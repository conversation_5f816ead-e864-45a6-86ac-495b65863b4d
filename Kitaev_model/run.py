#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
入口点脚本，用于启动Kitaev模型训练
"""

import os
import sys
from scripts import main

if __name__ == "__main__":
    # 直接将参数传递给main模块
    if len(sys.argv) > 1:
        main.main()
    else:
        print("使用方法: python run.py <Lx> <Ly> <Kx> <Ky> <Kz> <J> <hx> <hy> <hz> <Lambda>")
        print("例如: python run.py 4 4 1.0 1.0 1.0 0.0 0.1 0.1 0.1 0.1")
        sys.exit(1) 