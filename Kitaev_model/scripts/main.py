# 在main.py最开始添加环境配置
import os
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["JAX_PLATFORM_NAME"] = "gpu"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"

# 添加上级目录到Python路径
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import time
import json
import datetime
import jax
import numpy as np
import jax.numpy as jnp
import netket as nk

# 导入配置和模块
from configs.config import ModelConfig, TrainingConfig, SystemConfig
from src.runner import KitaevRunner
from src.utils.logging import log_message

def run_simulation(Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda):
    """
    执行单个Kitaev模型模拟
    
    Args:
        Lx, Ly: 晶格尺寸
        Kx, Ky, Kz: Kitaev相互作用强度
        J: <PERSON><PERSON>nberg相互作用强度
        hx, hy, hz: [111]方向磁场
        Lambda: plaquette算符的拉格朗日乘子
    """
    try:
        # 创建运行器并执行模拟
        runner = KitaevRunner(Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda)
        runner.setup_model()
        runner.run()
        
        log_message(runner.energy_log, f"Simulation completed successfully for parameters:")
        log_message(runner.energy_log, f"Lx={Lx}, Ly={Ly}, Kx={Kx}, Ky={Ky}, Kz={Kz}")
        log_message(runner.energy_log, f"J={J}, hx={hx}, hy={hy}, hz={hz}, Lambda={Lambda}")
        
    except Exception as e:
        print(f"模拟失败，参数: Lx={Lx}, Ly={Ly}, Kx={Kx}, Ky={Ky}, Kz={Kz}, J={J}, hx={hx}, hy={hy}, hz={hz}, Lambda={Lambda}")
        print(f"错误信息: {str(e)}")
        raise

def main():
    """主函数"""
    if len(sys.argv) != 11:
        print("使用方法: python main.py <Lx> <Ly> <Kx> <Ky> <Kz> <J> <hx> <hy> <hz> <Lambda>")
        print("例如: python main.py 4 4 1.0 1.0 1.0 0.0 0.1 0.1 0.1 0.1")
        sys.exit(1)

    # 解析命令行参数
    try:
        Lx = int(sys.argv[1])
        Ly = int(sys.argv[2])
        Kx = float(sys.argv[3])
        Ky = float(sys.argv[4])
        Kz = float(sys.argv[5])
        J = float(sys.argv[6])
        hx = float(sys.argv[7])
        hy = float(sys.argv[8])
        hz = float(sys.argv[9])
        Lambda = float(sys.argv[10])
    except ValueError as e:
        print(f"参数解析错误: {e}")
        print("请确保所有参数格式正确")
        sys.exit(1)

    # 验证参数合理性
    if Lx <= 0 or Ly <= 0:
        print("错误: 晶格尺寸必须为正整数")
        sys.exit(1)

    print("="*60)
    print("Kitaev-Heisenberg模型模拟开始")
    print("="*60)
    print(f"晶格尺寸: Lx={Lx}, Ly={Ly}")
    print(f"Kitaev相互作用: Kx={Kx}, Ky={Ky}, Kz={Kz}")
    print(f"Heisenberg相互作用: J={J}")
    print(f"磁场[111]: hx={hx}, hy={hy}, hz={hz}")
    print(f"拉格朗日乘子: Lambda={Lambda}")
    print("="*60)

    # 记录JAX设备信息
    print(f"JAX设备: {jax.devices()}")
    print(f"设备数量: {len(jax.devices())}")
    print("="*60)

    # 运行模拟
    start_time = time.time()
    run_simulation(Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda)
    end_time = time.time()
    
    print("="*60)
    print(f"模拟完成，总用时: {end_time - start_time:.2f} 秒")
    print("="*60)

if __name__ == "__main__":
    main() 