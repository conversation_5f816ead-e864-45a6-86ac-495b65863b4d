#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用于从config.py读取参数的辅助脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from configs.config import SystemConfig

def print_config_values():
    """打印所有配置值"""
    print(f"LX_VALUE='{SystemConfig.Lx}'")
    print(f"LY_VALUE='{SystemConfig.Ly}'")
    print(f"KX_VALUES='{' '.join(map(str, SystemConfig.KX_LIST))}'")
    print(f"KY_VALUES='{' '.join(map(str, SystemConfig.KY_LIST))}'")
    print(f"KZ_VALUES='{' '.join(map(str, SystemConfig.KZ_LIST))}'")
    print(f"J_VALUES='{' '.join(map(str, SystemConfig.J_LIST))}'")
    print(f"HX_VALUES='{' '.join(map(str, SystemConfig.HX_LIST))}'")
    print(f"HY_VALUES='{' '.join(map(str, SystemConfig.HY_LIST))}'")
    print(f"HZ_VALUES='{' '.join(map(str, SystemConfig.HZ_LIST))}'")
    print(f"LAMBDA_VALUES='{' '.join(map(str, SystemConfig.LAMBDA_LIST))}'")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        param_name = sys.argv[1].upper()
        if param_name == "LX":
            print(SystemConfig.Lx)
        elif param_name == "LY":
            print(SystemConfig.Ly)
        elif param_name == "KX_LIST":
            print(' '.join(map(str, SystemConfig.KX_LIST)))
        elif param_name == "KY_LIST":
            print(' '.join(map(str, SystemConfig.KY_LIST)))
        elif param_name == "KZ_LIST":
            print(' '.join(map(str, SystemConfig.KZ_LIST)))
        elif param_name == "J_LIST":
            print(' '.join(map(str, SystemConfig.J_LIST)))
        elif param_name == "HX_LIST":
            print(' '.join(map(str, SystemConfig.HX_LIST)))
        elif param_name == "HY_LIST":
            print(' '.join(map(str, SystemConfig.HY_LIST)))
        elif param_name == "HZ_LIST":
            print(' '.join(map(str, SystemConfig.HZ_LIST)))
        elif param_name == "LAMBDA_LIST":
            print(' '.join(map(str, SystemConfig.LAMBDA_LIST)))
        elif param_name == "ALL":
            print_config_values()
        else:
            print(f"Unknown parameter: {param_name}")
            sys.exit(1)
    else:
        print_config_values() 