# ==================== Kitaev模型训练任务配置文件 ====================
# 此文件包含Kitaev模型基础训练任务的所有参数配置

# ==================== 系统参数配置 ====================
# 晶格尺寸
LX_VALUES="4"
LY_VALUES="4"

# Kitaev相互作用参数
KX_VALUES="1.0"
KY_VALUES="1.0"
KZ_VALUES="1.0"

# Heisenberg相互作用参数
J_VALUES="0.0"

# 磁场参数
HX_VALUES="0.1"
HY_VALUES="0.1"
HZ_VALUES="0.1"

# 拉格朗日乘子
LAMBDA_VALUES="0.1"

# ==================== 训练超参数 ====================
# 学习率调度（余弦退火+热重启）
TRAIN_MAX_LR=0.1         # 最大学习率（重启时的学习率）
TRAIN_MIN_LR=0.001       # 最小学习率（周期结束时的学习率）
TRAIN_INITIAL_PERIOD=100 # 初始退火周期长度
TRAIN_PERIOD_MULT=2.0    # 周期倍增因子（每次重启后周期长度翻倍）
TRAIN_N_CYCLES=2         # 重启周期数

# 采样参数
TRAIN_N_SAMPLES=4096
TRAIN_CHUNK_SIZE=1024

# ==================== 模型参数 ====================
# cRBM模型参数组合（格式：每个组合为"alpha值"，空格分隔）
# 例如："4" 表示 alpha=4
# 示例：TRAIN_MODEL_CONFIGS="4 6 8"
TRAIN_MODEL_CONFIGS="4"

# 其他模型参数
TRAIN_DIAG_SHIFT=0.20
TRAIN_GRAD_CLIP=1.0

# ==================== Checkpoint配置 ====================
TRAIN_ENABLE_CHECKPOINT=true
TRAIN_CHECKPOINT_INTERVAL=500
TRAIN_RESUME_FROM_CHECKPOINT=""
TRAIN_KEEP_CHECKPOINT_HISTORY=true
