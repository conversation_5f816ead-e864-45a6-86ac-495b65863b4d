#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=2
#PBS -l walltime=22:00:00
#PBS -P 12004256
###PBS -P personal-s240076
#PBS -N kitaev-unified
#PBS -j oe

# ==================== 任务选择配置 ====================
# 设置要执行的任务类型，可以选择一个或多个：
# TRAIN     - 基础训练任务
#
# 示例：
# TASKS="TRAIN"                    # 只执行训练

TASKS="TRAIN"

# ==================== 读取配置文件 ====================
# 根据任务类型读取对应的配置文件
load_config() {
    local task_type=$1
    local config_file=""

    case $task_type in
        TRAIN)
            config_file="jobs/config/train.conf"
            ;;
        *)
            echo "错误: 未知任务类型: $task_type"
            return 1
            ;;
    esac

    if [ ! -f "$config_file" ]; then
        echo "错误: 配置文件 $config_file 不存在！"
        return 1
    fi

    echo "读取配置文件: $config_file"
    source "$config_file"
    return 0
}

# 进入工作目录（必须在读取配置文件之前）
cd $PBS_O_WORKDIR || exit $?

# 预加载所有可能用到的配置文件
echo "预加载配置文件..."
echo "当前工作目录: $(pwd)"
for task in $TASKS; do
    if ! load_config "$task"; then
        exit 1
    fi
done

# 记录作业开始时间和节点信息
echo "==================== Kitaev模型统一任务提交系统 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Selected tasks: $TASKS"
echo "GPU Information:"
nvidia-smi

# ==================== 环境配置 ====================
# 加载必要的模块
module load singularity

# Singularity容器路径
CONTAINER_PATH="/home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif"

# ==================== 通用函数定义 ====================

# 函数：构建checkpoint参数
build_checkpoint_args() {
    local enable=$1
    local interval=$2
    local keep_history=$3
    local resume_from=$4

    local args=""
    if [ "$enable" = "true" ]; then
        args="--enable_checkpoint --save_interval $interval"

        if [ "$keep_history" = "true" ]; then
            args="$args --keep_history"
        fi

        if [ -n "$resume_from" ]; then
            args="$args --resume_from $resume_from"
        fi
    fi

    echo "$args"
}

# 函数：运行单个模型配置的所有训练任务
run_train_model() {
    local alpha=$1
    local checkpoint_args=$2

    echo "==================== 模型参数: Alpha=$alpha ===================="

    for Lx in $LX_VALUES; do
        for Ly in $LY_VALUES; do
            for Kx in $KX_VALUES; do
                for Ky in $KY_VALUES; do
                    for Kz in $KZ_VALUES; do
                        for J in $J_VALUES; do
                            for hx in $HX_VALUES; do
                                for hy in $HY_VALUES; do
                                    for hz in $HZ_VALUES; do
                                        for Lambda in $LAMBDA_VALUES; do
                                            echo "Starting training Lx=$Lx, Ly=$Ly, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda, Alpha=$alpha at: $(date)"

                                            # 顺序执行训练任务
                                            singularity exec --nv -B /scratch,/app \
                                                $CONTAINER_PATH \
                                                python scripts/train.py $Lx $Ly $Kx $Ky $Kz $J $hx $hy $hz $Lambda \
                                                --n_samples $TRAIN_N_SAMPLES \
                                                --chunk_size $TRAIN_CHUNK_SIZE \
                                                --n_cycles $TRAIN_N_CYCLES \
                                                --initial_period $TRAIN_INITIAL_PERIOD \
                                                --period_mult $TRAIN_PERIOD_MULT \
                                                --max_lr $TRAIN_MAX_LR \
                                                --min_lr $TRAIN_MIN_LR \
                                                --alpha $alpha \
                                                --diag_shift $TRAIN_DIAG_SHIFT \
                                                --grad_clip $TRAIN_GRAD_CLIP \
                                                $checkpoint_args

                                            echo "Completed training job Lx=$Lx, Ly=$Ly, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda, Alpha=$alpha at: $(date)"
                                        done
                                    done
                                done
                            done
                        done
                    done
                done
            done
        done
    done

    echo "模型 Alpha=$alpha 的所有训练任务已完成！"
}

# 函数：运行训练任务
run_train() {
    echo "==================== 执行训练任务 ===================="

    # 重新加载训练配置（确保使用最新配置）
    load_config "TRAIN"

    # 构建checkpoint参数
    local checkpoint_args=$(build_checkpoint_args \
        "$TRAIN_ENABLE_CHECKPOINT" \
        "$TRAIN_CHECKPOINT_INTERVAL" \
        "$TRAIN_KEEP_CHECKPOINT_HISTORY" \
        "$TRAIN_RESUME_FROM_CHECKPOINT")

    echo "训练参数配置:"
    echo "Lx values: $LX_VALUES"
    echo "Ly values: $LY_VALUES"
    echo "Kx values: $KX_VALUES"
    echo "Ky values: $KY_VALUES"
    echo "Kz values: $KZ_VALUES"
    echo "J values: $J_VALUES"
    echo "hx values: $HX_VALUES"
    echo "hy values: $HY_VALUES"
    echo "hz values: $HZ_VALUES"
    echo "Lambda values: $LAMBDA_VALUES"
    echo "Model configs (Alpha): $TRAIN_MODEL_CONFIGS"
    echo "Model parallel: 串行执行"
    echo "Max learning rate: $TRAIN_MAX_LR"
    echo "Min learning rate: $TRAIN_MIN_LR"
    echo "Samples: $TRAIN_N_SAMPLES"
    echo "Checkpoint args: $checkpoint_args"

    # 将模型配置转换为数组
    model_configs_array=($TRAIN_MODEL_CONFIGS)
    total_models=${#model_configs_array[@]}

    echo "总共 $total_models 个模型配置，串行执行"

    # 串行处理所有模型配置
    for alpha in "${model_configs_array[@]}"; do
        echo "==================== 开始模型 Alpha=$alpha 的训练任务 ===================="

        # 串行运行该模型的所有训练任务
        run_train_model $alpha "$checkpoint_args"

        echo "模型 Alpha=$alpha 的训练任务已完成"
    done

    echo "所有训练任务已完成！"
}

# ==================== 主执行逻辑 ====================

# 串行执行选定的任务
for task in $TASKS; do
    case $task in
        TRAIN)
            echo "开始训练任务..."
            run_train
            echo "训练任务已完成"
            ;;
        *)
            echo "错误: 未知任务类型: $task"
            echo "可用任务类型: TRAIN"
            exit 1
            ;;
    esac
done

echo "==================== 所有任务完成 ===================="
echo "执行的任务: $TASKS"
echo "Job finished at: $(date)"