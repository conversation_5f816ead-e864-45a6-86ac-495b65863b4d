#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=2
#PBS -l walltime=22:00:00
#PBS -P 12004256
###PBS -P personal-s240076
#PBS -N kitaev-model
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load singularity

# 从config.py读取参数列表 - 使用专门的脚本避免污染
echo "Reading parameters from configs/config.py..."

# 使用专门的参数读取脚本
CONFIG_OUTPUT=$(singularity exec --nv -B /scratch,/app \
    /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
    python scripts/read_config.py ALL 2>/dev/null)

# 解析输出
eval "$CONFIG_OUTPUT"

# 从解析的变量中提取列表值
KX_VALUES="$KX_VALUES"
KY_VALUES="$KY_VALUES"
KZ_VALUES="$KZ_VALUES"
J_VALUES="$J_VALUES"
HX_VALUES="$HX_VALUES"
HY_VALUES="$HY_VALUES"
HZ_VALUES="$HZ_VALUES"
LAMBDA_VALUES="$LAMBDA_VALUES"

echo "Processing the following parameter combinations:"
echo "Lx value: $LX_VALUE"
echo "Ly value: $LY_VALUE"
echo "Kx values: $KX_VALUES"
echo "Ky values: $KY_VALUES"
echo "Kz values: $KZ_VALUES"
echo "J values: $J_VALUES"
echo "hx values: $HX_VALUES"
echo "hy values: $HY_VALUES"
echo "hz values: $HZ_VALUES"
echo "Lambda values: $LAMBDA_VALUES"

# 检查参数是否成功读取
if [ -z "$LX_VALUE" ] || [ -z "$LY_VALUE" ] || [ -z "$KX_VALUES" ] || [ -z "$KY_VALUES" ] || [ -z "$KZ_VALUES" ] || [ -z "$J_VALUES" ] || [ -z "$HX_VALUES" ] || [ -z "$HY_VALUES" ] || [ -z "$HZ_VALUES" ] || [ -z "$LAMBDA_VALUES" ]; then
    echo "Error: Failed to read parameters from config.py"
    echo "Please check the configuration file and Python environment"
    exit 1
fi

# 并行任务最大数量
max_tasks=6
current_tasks=0

for Kx in $KX_VALUES; do
    for Ky in $KY_VALUES; do
        for Kz in $KZ_VALUES; do
            for J in $J_VALUES; do
                for hx in $HX_VALUES; do
                    for hy in $HY_VALUES; do
                        for hz in $HZ_VALUES; do
                            for Lambda in $LAMBDA_VALUES; do
                                echo "Starting computation Lx=$LX_VALUE, Ly=$LY_VALUE, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda at: $(date)"
                                
                                # 提交任务到后台运行，使用run.py入口脚本，同样重定向stderr避免干扰
                                singularity exec --nv -B /scratch,/app \
                                    /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
                                    python run.py $LX_VALUE $LY_VALUE $Kx $Ky $Kz $J $hx $hy $hz $Lambda 2>&1 &
                                
                                current_tasks=$((current_tasks + 1))
                                
                                # 如果达到最大并行任务数，则等待这批任务全部结束，再继续提交
                                if [ $current_tasks -ge $max_tasks ]; then
                                    wait
                                    current_tasks=0
                                fi
                                
                                echo "Submitted job Lx=$LX_VALUE, Ly=$LY_VALUE, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda at: $(date)"
                            done
                        done
                    done
                done
            done
        done
    done
done

# 等待剩余任务
wait

echo "Job finished at: $(date)" 