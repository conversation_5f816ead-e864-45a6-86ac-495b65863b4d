class ModelConfig:
    """cRBM模型参数"""
    alpha = 4                    # RBM隐藏单元比例
    param_dtype = "complex128"   # 参数数据类型
    use_hidden_bias = True       # 是否使用隐藏偏置
    use_visible_bias = True      # 是否使用可见偏置
    
class TrainingConfig:
    """训练参数"""
    seed = 0
    learning_rate = 0.1          # 学习率
    n_iter = 20000              # 总迭代次数
    n_train = 1                 # 每次退火的训练步数
    n_samples = 2**13           # 样本数量
    n_discard_per_chain = 0     # 每条链丢弃的样本数
    chunk_size = 2**10          # 批处理大小
    temperature = 1.0           # 初始温度
    reference_energy = -6.396     # 手动设置的参考能量，若为None则不显示相对误差

class SystemConfig:
    """Kitaev系统参数"""
    # 系统大小
    Lx = 4
    Ly = 4
    
    # Kitaev相互作用参数
    Kx = 1.0
    Ky = 1.0 
    Kz = 1.0
    
    # Heisenberg相互作用
    J = 0.0
    
    # [111]磁场
    hx = 0.1
    hy = 0.1
    hz = 0.1
    
    # 拉格朗日乘子
    Lambda = 0.1
    
    # 自旋
    spin = 0.5
    
    # 参数扫描列表（用于批量计算）
    KX_LIST = [1.0]
    KY_LIST = [1.0] 
    KZ_LIST = [1.0]
    J_LIST = [0.0]
    HX_LIST = [0.1]
    HY_LIST = [0.1]
    HZ_LIST = [0.1]
    LAMBDA_LIST = [0.0]
    
    @staticmethod
    def get_n_sites(Lx, Ly):
        """获取总格点数"""
        return 2 * Lx * Ly  # 蜂窝晶格每个单元格有2个格点 