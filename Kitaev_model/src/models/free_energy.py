#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自定义VMC_SR驱动器，用于Kitaev模型的变分量子态优化
基于Shastry-Sutherland_Extra的实现，适配Kitaev模型
"""

import math
import time
import numpy as np
import jax
import jax.numpy as jnp
import netket as nk
import netket.optimizer as nk_opt
from netket.driver import ImprovedVMC_SR
from datetime import datetime
import pytz

from src.utils.logging import log_message


class CustomImprovedVMC_SR(ImprovedVMC_SR):
    """
    自定义改进的VMC_SR驱动器，添加完整的训练管理功能
    
    功能：
    1. 学习率余弦退火 + 热重启
    2. NaN检测和早停
    3. Checkpoint保存
    4. 详细日志输出
    """
    
    def __init__(self, initial_period=100, period_mult=2.0,
                 max_lr=None, min_lr=None,
                 base_lr=None,  # 新增：基础学习率
                 checkpoint_callback=None, checkpoint_interval=500, 
                 *args, **kwargs):
        """
        Args:
            initial_period: 初始重启周期
            period_mult: 周期倍增因子
            max_lr: 最大学习率（绝对值）
            min_lr: 最小学习率（绝对值）
            base_lr: 基础学习率（如果max_lr/min_lr未指定，则使用base_lr计算）
            checkpoint_callback: checkpoint回调函数
            checkpoint_interval: checkpoint保存间隔
        """
        super().__init__(*args, **kwargs)
        
        self.max_nan_count = 5  # 最大允许连续NaN次数
        
        # 学习率调度参数
        self.initial_period = initial_period
        self.period_mult = period_mult
        
        # 获取基础学习率（从优化器）
        self.base_lr = base_lr
        if self.base_lr is None:
            # 尝试从优化器获取
            self.base_lr = self._get_optimizer_lr()
        
        # 设置学习率范围
        if max_lr is not None and min_lr is not None:
            self.max_lr = max_lr
            self.min_lr = min_lr
        else:
            # 使用base_lr和因子计算
            self.max_lr = self.base_lr
            self.min_lr = self.base_lr * 0.01  # 默认衰减到1%
        
        # 记录重启信息
        self.current_restart = 0
        self.current_period = initial_period
        self.iter_since_restart = 0
        
        # Checkpoint相关
        self.checkpoint_callback = checkpoint_callback
        self.checkpoint_interval = checkpoint_interval
        
        # 当前学习率
        self.current_lr = self.max_lr
    
    def _get_optimizer_lr(self):
        """从优化器获取当前学习率"""
        # NetKet的SGD优化器使用 .learning_rate 属性
        if hasattr(self.optimizer, 'learning_rate'):
            return float(self.optimizer.learning_rate)
        elif hasattr(self.optimizer, '_lr'):
            return float(self.optimizer._lr)
        else:
            # 默认值
            return 0.001
    
    def _cosine_annealing_with_restart(self, iteration):
        """
        带热重启的余弦退火学习率调度
        
        lr(i) = lr_min + (lr_max - lr_min) * (1 + cos(π * i_cur / T_cur)) / 2
        
        Args:
            iteration: 当前迭代次数
        
        Returns:
            当前学习率（绝对值）
        """
        # 检查是否需要重启
        if self.iter_since_restart >= self.current_period:
            self.current_restart += 1
            self.iter_since_restart = 0
            # 更新下一个重启周期长度
            self.current_period = int(self.initial_period * (self.period_mult ** self.current_restart))
        
        cosine_factor = (1 + math.cos(math.pi * self.iter_since_restart / self.current_period)) / 2
        learning_rate = self.min_lr + (self.max_lr - self.min_lr) * cosine_factor
        
        self.iter_since_restart += 1
        return learning_rate
    
    def _update_learning_rate(self, learning_rate):
        """
        更新优化器的学习率
        
        Args:
            learning_rate: 新的学习率（绝对值）
        """
        self.current_lr = learning_rate
        
        # 更新优化器学习率
        if hasattr(self.optimizer, 'learning_rate'):
            # 对于NetKet的SGD优化器
            self.optimizer = self.optimizer.replace(learning_rate=learning_rate)
        elif hasattr(self.optimizer, '_lr'):
            self.optimizer._lr = learning_rate
    
    def run(self, n_iter, energy_log=None):
        """
        运行优化过程

        Args:
            n_iter: 总迭代次数
            energy_log: 日志文件路径
        """
        # 初始化统计
        nan_count = 0
        best_energy = float('inf')

        # 记录开始时间
        start_time = time.time()

        for i in range(n_iter):
            # 更新学习率
            current_lr = self._cosine_annealing_with_restart(i)
            self._update_learning_rate(current_lr)

            # 执行一步优化
            try:
                # 先计算能量用于日志记录
                energy_stats = self.state.expect(self.hamiltonian)

                # 检查NaN
                if jnp.isnan(energy_stats.mean):
                    nan_count += 1
                    if energy_log:
                        log_message(energy_log, f"⚠️  Iteration {i}: NaN detected ({nan_count}/{self.max_nan_count})")

                    if nan_count >= self.max_nan_count:
                        if energy_log:
                            log_message(energy_log, f"❌ Training stopped due to {self.max_nan_count} consecutive NaN values")
                        break
                    continue
                else:
                    nan_count = 0  # 重置NaN计数

                # 执行优化步骤（这会更新self.state）
                self.advance()

                # 更新最佳能量
                current_energy = float(energy_stats.mean)
                if current_energy < best_energy:
                    best_energy = current_energy

                # 记录日志
                if energy_log and (i % 10 == 0 or i < 100):
                    energy_var = float(energy_stats.variance) if hasattr(energy_stats, 'variance') else 0.0
                    log_message(energy_log,
                        f"Iter: {i:6d} | Energy: {current_energy:12.6f} ± {float(energy_stats.error_of_mean):8.6f} | "
                        f"Var: {energy_var:8.6f} | LR: {current_lr:.6f}")

                # 保存checkpoint
                if (self.checkpoint_callback and
                    self.checkpoint_interval > 0 and
                    (i + 1) % self.checkpoint_interval == 0):
                    self.checkpoint_callback(i + 1, energy_stats.mean, energy_stats.error_of_mean,
                                           getattr(energy_stats, 'variance', 0.0))

            except Exception as e:
                if energy_log:
                    log_message(energy_log, f"⚠️  Iteration {i}: Error occurred: {str(e)}")
                nan_count += 1
                if nan_count >= self.max_nan_count:
                    if energy_log:
                        log_message(energy_log, f"❌ Training stopped due to {self.max_nan_count} consecutive errors")
                    break

        # 记录最终统计
        end_time = time.time()
        if energy_log:
            log_message(energy_log, f"Training completed in {end_time - start_time:.1f}s")
            log_message(energy_log, f"Best energy achieved: {best_energy:.6f}")


class CustomFreeEnergyVMC_SR(CustomImprovedVMC_SR):
    """
    自定义自由能VMC_SR驱动器，实现F = E - TS的最小化
    """
    
    def __init__(self, temperature=1.0, *args, **kwargs):
        """
        Args:
            temperature: 温度参数
        """
        super().__init__(*args, **kwargs)
        self.temperature = temperature
    
    def advance(self):
        """
        执行一步自由能优化
        实现F = E - TS的最小化，其中S是von Neumann熵
        """
        # 计算能量和梯度
        energy, energy_grad = self.state.expect_and_grad(self.hamiltonian)
        
        # 计算熵梯度项（简化实现）
        # 这里使用一个简化的熵估计
        samples = self.state.samples
        log_psi = self.state.log_value(samples)
        entropy_grad = 2.0 * self.temperature * jnp.mean(jnp.real(log_psi)) * jnp.mean(jnp.real(log_psi))
        
        # 组合自由能梯度
        free_energy_grad = jax.tree_map(lambda eg: eg - entropy_grad, energy_grad)
        
        # 应用SR更新
        self.state = self.update_parameters(self.state, free_energy_grad)
        
        return self.state
