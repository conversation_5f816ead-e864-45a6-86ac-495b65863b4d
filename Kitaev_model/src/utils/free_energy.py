import time
import jax
import jax.numpy as jnp
from jax import tree_util
import datetime
from netket.optimizer.qgt import QGTJacobianDense
from src.utils.logging import log_message

def T_logp2(params, inputs, temperature, model):
    """计算熵梯度项 T * <(log ψ)²>"""
    variables = {"params": params}
    preds = model.apply(variables, inputs)
    return 2.0 * temperature * jnp.mean(jnp.real(preds) * jnp.real(preds))

def T_logp_2(params, inputs, temperature, model):
    """计算熵梯度项 T * <log ψ>²"""
    variables = {"params": params}
    preds = model.apply(variables, inputs)
    return 2.0 * temperature * jnp.mean(jnp.real(preds)) * jnp.mean(jnp.real(preds))

def custom_sr_free_energy(
    hamiltonian,
    vstate,
    lr,
    temperature,
    n_ann,
    n_train,
    energy_log,
    result_dir,
    Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda,
    reference_energy=None
):
    """
    自定义SR自由能优化函数
    实现F = E - TS的最小化，其中S是von Neumann熵
    
    Args:
        hamiltonian: 哈密顿量
        vstate: 变分量子态  
        lr: 学习率
        temperature: 初始温度
        n_ann: 退火迭代次数
        n_train: 每次迭代的训练次数
        energy_log: 日志文件路径
        result_dir: 结果目录
        reference_energy: 手动设置的参考能量，若为None则不计算相对误差
        其他参数: 用于保存文件名
    """
    import flax
    import netket as nk
    
    # 使用手动设置的参考能量
    if reference_energy is not None:
        log_message(energy_log, f"Using manual reference energy: {reference_energy:.8f}")
        exact_gs_energy = reference_energy
    else:
        log_message(energy_log, "No reference energy provided, relative error will not be displayed")
        exact_gs_energy = None
    
    for i in range(n_ann):
        # 指数退火温度衰减
        temperature_i = temperature * (jnp.exp(-i / 50.0) / 2.0)
        
        for j in range(n_train):
            # 不再保存状态文件 - 用户要求不存储 .mpack 文件
            
            # 计算能量和梯度
            energy, f = vstate.expect_and_grad(hamiltonian)
            variables = vstate.variables
            inputs0 = vstate.samples
            
            # 处理采样数据形状
            if len(inputs0.shape) == 3:
                inputs1 = jnp.reshape(inputs0, (1, inputs0.shape[0] * inputs0.shape[1], inputs0.shape[2]))
                inputs = inputs1[0]
            else:
                inputs = inputs0
            
            # 计算相对误差
            if exact_gs_energy is not None:
                relative_error = abs(energy.mean.real - exact_gs_energy) / abs(exact_gs_energy)
                # 改为百分数格式，保留6位小数
                error_msg = f", Rel_err: {relative_error*100:.6f}%"
            else:
                error_msg = ""
            
            # 记录能量信息 - 修改格式
            log_message(energy_log, 
                       f"Iteration: {i+1}/{n_ann}, Temp: {temperature_i:.4f}, "
                       f"Energy: {energy.mean.real:.6f} ± {energy.error_of_mean:.6f}, "
                       f"Variance: {energy.variance:.6f}{error_msg}")
            
            # 不再保存能量数据到文件 - 用户要求不存储 energy_data_* 文件
            
            # 计算量子几何张量
            G = vstate.quantum_geometric_tensor(QGTJacobianDense())
            
            # 计算熵梯度
            mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(variables["params"], inputs, temperature_i, vstate.model)
            mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(variables["params"], inputs, temperature_i, vstate.model)
            mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)
            
            # 合并能量梯度和熵梯度
            gamma_S = tree_util.tree_map(lambda x: -1.0 * jnp.conj(x), mT_grad_S)
            gamma_f = tree_util.tree_map(lambda x: -1.0 * x, f)
            gamma_tot = tree_util.tree_map(lambda x, y: x + y, gamma_f, gamma_S)
            
            # 求解更新方程
            dtheta, _ = G.solve(jax.scipy.sparse.linalg.cg, gamma_tot)
            
            # 更新参数
            vstate.parameters = tree_util.tree_map(
                lambda x, y: x + lr * y, vstate.parameters, dtheta
            )

def save_energy_data(result_dir, energy, iteration, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, exact_gs_energy=None):
    """保存能量数据到单个文件 - 已禁用，用户不需要此功能"""
    # 不再保存任何能量数据文件
    pass

def cleanup_energy_files(result_dir, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz):
    """清理已存在的能量文件"""
    import os
    
    base_filename = f"Kitaev_SL_Lx={Lx}_Ly={Ly}_Kx={Kx}_Ky={Ky}_Kz={Kz}_J={J}_hx={hx}_hy={hy}_hz={hz}_cRBM"
    
    # 清理旧的分散文件格式
    old_files_to_clean = [
        f"E_real_{base_filename}.dat",
        f"E_imag_{base_filename}.dat", 
        f"Iters_{base_filename}.dat",
        f"E_error_{base_filename}.dat",
        f"E_variance_{base_filename}.dat",
        f"E_rel_error_{base_filename}.dat"
    ]
    
    # 清理新的合并文件格式
    new_files_to_clean = [
        f"energy_data_{base_filename}.dat",
        f"E_ED_{base_filename}.dat"
    ]
    
    all_files_to_clean = old_files_to_clean + new_files_to_clean
    
    for filename in all_files_to_clean:
        filepath = f"{result_dir}/{filename}"
        if os.path.exists(filepath):
            os.remove(filepath)
            print(f"已删除文件: {filename}")
    
    # 清理旧的checkpoint文件
    import glob
    checkpoint_pattern = f"{result_dir}/cRBM_checkpoint_iter_*_{base_filename}.mpack"
    for filepath in glob.glob(checkpoint_pattern):
        os.remove(filepath)
        print(f"已删除检查点文件: {os.path.basename(filepath)}")
    
    # 清理旧的非checkpoint的mpack文件
    old_mpack_pattern = f"{result_dir}/cRBM_Lx={Lx}_Ly={Ly}_Kx={Kx}_Ky={Ky}_Kz=*_J={J}_hx={hx}_hy={hy}_hz={hz}_Lambda=*.mpack"
    for filepath in glob.glob(old_mpack_pattern):
        if "checkpoint" not in filepath:  # 不删除新的checkpoint文件
            os.remove(filepath)
            print(f"已删除旧状态文件: {os.path.basename(filepath)}") 