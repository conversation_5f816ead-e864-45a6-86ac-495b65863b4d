#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kitaev_model运行器模块
提供了一种更面向对象的方式来进行Kitaev模型模拟
"""

import os
import sys
import time
import json
import numpy as np
import jax
import jax.numpy as jnp
import netket as nk
import flax

from configs.config import ModelConfig, TrainingConfig, SystemConfig
from src.models.cRBM import cRBM
from src.utils.free_energy import custom_sr_free_energy, cleanup_energy_files
from src.utils.logging import log_message
from src.physics.kitaev import (
    create_honeycomb_lattice, 
    create_kitaev_hamiltonian, 
    get_symmetries,
    save_lattice_figure
)

class KitaevRunner:
    """Kitaev模型运行器类，提供面向对象的接口"""
    
    def __init__(self, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda, result_dir=None):
        """
        初始化运行器
        
        Args:
            Lx, Ly: 晶格尺寸
            Kx, Ky, Kz: Kitaev相互作用强度
            J: Heisenberg相互作用强度
            hx, hy, hz: [111]方向磁场
            Lambda: plaquette算符的拉格朗日乘子
            result_dir: 结果保存目录，如果为None则自动生成
        """
        self.Lx = Lx
        self.Ly = Ly
        self.Kx = Kx
        self.Ky = Ky
        self.Kz = Kz
        self.J = J
        self.hx = hx
        self.hy = hy
        self.hz = hz
        self.Lambda = Lambda
        
        # 创建结果目录，参考Shastry-Sutherland_model结构
        if result_dir is None:
            self.result_dir = f"results/Lx={Lx}_Ly={Ly}/Kx={Kx}_Ky={Ky}_Kz={Kz}/J={J}/h={hx}_{hy}_{hz}/Lambda={Lambda}"
        else:
            self.result_dir = result_dir
            
        self.training_dir = os.path.join(self.result_dir, "training")
        self.analysis_dir = os.path.join(self.result_dir, "analysis")
        
        # 创建所有必要的目录
        os.makedirs(self.training_dir, exist_ok=True)
        os.makedirs(self.analysis_dir, exist_ok=True)
        
        # 设置日志文件 - 训练日志保存在training目录
        self.energy_log = os.path.join(self.training_dir, 
                                      f"energy_Lx={Lx}_Ly={Ly}_Kx={Kx}_Ky={Ky}_Kz={Kz}_J={J}_h={hx}_{hy}_{hz}_Lambda={Lambda}.log")
        
        # 清理旧的能量文件
        cleanup_energy_files(self.training_dir, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz)
        
        # 初始化物理系统
        self.lattice = create_honeycomb_lattice(Lx, Ly)
        self.N = self.lattice.n_nodes
        
        # 保存晶格图像到training目录
        lattice_filename = os.path.join(self.training_dir, "Honeycomb_lattice.png")
        save_lattice_figure(self.lattice, lattice_filename)
        
        # 记录开始信息
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"Kitaev-Heisenberg Model Study")
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"System parameters: Lx={Lx}, Ly={Ly}, N={self.N}")
        log_message(self.energy_log, f"  - Kitaev interactions: Kx={Kx}, Ky={Ky}, Kz={Kz}")
        log_message(self.energy_log, f"  - Heisenberg interaction: J={J}")
        log_message(self.energy_log, f"  - Magnetic field [111]: hx={hx}, hy={hy}, hz={hz}")
        log_message(self.energy_log, f"  - Lagrange multiplier: Lambda={Lambda}")
    
    def setup_model(self):
        """设置模型和优化器"""
        # 创建Hamiltonian
        self.H, self.hi = create_kitaev_hamiltonian(
            self.lattice, self.Kx, self.Ky, self.Kz, self.J, 
            self.hx, self.hy, self.hz, self.Lambda
        )
        
        # 设置采样器
        self.sampler = nk.sampler.MetropolisLocal(
            hilbert=self.hi,
            n_chains=TrainingConfig.n_samples
        )
        
        # 设置cRBM模型
        model_no_symm = cRBM(
            Lx=self.Lx,
            Ly=self.Ly,
            alpha=ModelConfig.alpha,
            param_dtype=getattr(np, ModelConfig.param_dtype)
        )
        
        # 获取对称性
        symmetries = get_symmetries(self.lattice)
        
        # 使用对称性
        self.model = nk.nn.blocks.SymmExpSum(
            module=model_no_symm, 
            symm_group=symmetries, 
            character_id=0
        )
        
        # 创建变分量子态
        self.vqs = nk.vqs.MCState(
            sampler=self.sampler,
            model=self.model,
            n_samples=TrainingConfig.n_samples,
            n_discard_per_chain=TrainingConfig.n_discard_per_chain,
            chunk_size=TrainingConfig.chunk_size,
        )
        
        # 记录模型参数
        n_params = nk.jax.tree_size(self.vqs.parameters)
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Model parameters:")
        log_message(self.energy_log, f"  - cRBM alpha = {ModelConfig.alpha}")
        log_message(self.energy_log, f"  - Parameter dtype = {ModelConfig.param_dtype}")
        log_message(self.energy_log, f"  - Use hidden bias = {ModelConfig.use_hidden_bias}")
        log_message(self.energy_log, f"  - Use visible bias = {ModelConfig.use_visible_bias}")
        log_message(self.energy_log, f"  - Symmetries used: {len(symmetries)}")
        log_message(self.energy_log, f"  - Total parameters = {n_params}")
        
        # 记录训练参数
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Training parameters:")
        log_message(self.energy_log, f"  - Learning rate: {TrainingConfig.learning_rate}")
        log_message(self.energy_log, f"  - Total annealing steps: {TrainingConfig.n_iter}")
        log_message(self.energy_log, f"  - Samples: {TrainingConfig.n_samples}")
        log_message(self.energy_log, f"  - Discarded samples: {TrainingConfig.n_discard_per_chain}")
        log_message(self.energy_log, f"  - Chunk size: {TrainingConfig.chunk_size}")
        log_message(self.energy_log, f"  - Initial temperature: {TrainingConfig.temperature}")
        
        # 记录设备状态
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Device status:")
        log_message(self.energy_log, f"  - Number of devices: {len(jax.devices())}")
        log_message(self.energy_log, f"  - Device type: {jax.devices()[0].device_kind}")
        
        # 保存参数
        params_dict = {
            "seed": TrainingConfig.seed,
            "learning_rate": TrainingConfig.learning_rate,
            "n_iter": TrainingConfig.n_iter,
            "n_train": TrainingConfig.n_train,
            "n_samples": TrainingConfig.n_samples,
            "n_discard_per_chain": TrainingConfig.n_discard_per_chain,
            "chunk_size": TrainingConfig.chunk_size,
            "temperature": TrainingConfig.temperature,
            "alpha": ModelConfig.alpha,
            "param_dtype": ModelConfig.param_dtype,
            "Lx": self.Lx,
            "Ly": self.Ly,
            "N": self.N,
            "Kx": self.Kx,
            "Ky": self.Ky,
            "Kz": self.Kz,
            "J": self.J,
            "hx": self.hx,
            "hy": self.hy,
            "hz": self.hz,
            "Lambda": self.Lambda,
            "lattice_extent": [self.Lx, self.Ly],
            "lattice_pbc": [True, True]
        }
        
        params_file = os.path.join(self.training_dir, 
                                  f"parameters_Lx={self.Lx}_Ly={self.Ly}_Kx={self.Kx}_Ky={self.Ky}_Kz={self.Kz}_J={self.J}_h={self.hx}_{self.hy}_{self.hz}_Lambda={self.Lambda}.json")
        with open(params_file, "w") as f_out:
            json.dump(params_dict, f_out, indent=4)
        
        return self
    
    def run(self, n_iter=None, n_train=None):
        """
        运行模拟
        
        Args:
            n_iter: 迭代次数，如果为None则使用配置中的值
            n_train: 每次迭代的训练步数，如果为None则使用配置中的值
        """
        if n_iter is None:
            n_iter = TrainingConfig.n_iter
        if n_train is None:
            n_train = TrainingConfig.n_train
            
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Start training...")
        
        # 记录时间
        start = time.time()
        
        # 运行自由能优化
        custom_sr_free_energy(
            hamiltonian=self.H,
            vstate=self.vqs,
            lr=TrainingConfig.learning_rate,
            temperature=TrainingConfig.temperature,
            n_ann=n_iter,
            n_train=n_train,
            energy_log=self.energy_log,
            result_dir=self.training_dir,
            Lx=self.Lx, Ly=self.Ly, Kx=self.Kx, Ky=self.Ky, Kz=self.Kz,
            J=self.J, hx=self.hx, hy=self.hy, hz=self.hz, Lambda=self.Lambda,
            reference_energy=TrainingConfig.reference_energy
        )
        
        end = time.time()
        
        runtime = end - start
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"Training finished, total running time = {runtime:.2f} seconds")
        
        # 保存最终状态到training目录
        import pickle
        state_file = os.path.join(self.training_dir, 
                                 f"cRBM_final_Lx={self.Lx}_Ly={self.Ly}_Kx={self.Kx}_Ky={self.Ky}_Kz={self.Kz}_J={self.J}_h={self.hx}_{self.hy}_{self.hz}_Lambda={self.Lambda}.pkl")
        with open(state_file, "wb") as f_state:
            pickle.dump(self.vqs.parameters, f_state)
        log_message(self.energy_log, f"The trained quantum state parameters have been saved to: {state_file}")
        log_message(self.energy_log, "="*50)
        
        return self
    
    @classmethod
    def run_simulation(cls, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda):
        """
        静态方法：运行单个模拟
        
        Args:
            所有Kitaev模型参数
        """
        runner = cls(Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda)
        runner.setup_model()
        runner.run()
        return runner 