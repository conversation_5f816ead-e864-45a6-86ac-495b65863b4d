{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 神经网络层数外推分析\n", "\n", "本notebook用于分析不同神经网络层数对Neel序参量的影响，并进行层数外推分析。\n", "\n", "## 分析目标\n", "- 使用已训练的L=4和L=5系统下J2=1，J1=0.76的不同神经网络层数数据\n", "- 创建Neel序参量的层数外推图\n", "- X轴：1/Layer数量 (层数的倒数)\n", "- Y轴：Neel序参量值\n", "- 将L=4和L=5的数据点绘制在同一张图上"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pickle\n", "import pandas as pd\n", "from pathlib import Path\n", "import re\n", "from scipy.optimize import curve_fit\n", "from collections import defaultdict\n", "\n", "# 设置matplotlib参数\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['figure.figsize'] = (10, 8)\n", "plt.rcParams['font.size'] = 12"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载分析数据"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功加载数据文件: analysis_results.pkl\n", "数据包含的系统配置: [(4, 0.0), (4, 0.05), (4, 1.0), (5, 0.0), (5, 0.05), (5, 1.0)]\n"]}], "source": ["# 加载已保存的分析结果\n", "def load_analysis_data(filename=\"analysis_results.pkl\"):\n", "    \"\"\"加载分析结果数据\"\"\"\n", "    data_path = Path(filename)\n", "    if data_path.exists():\n", "        with open(data_path, 'rb') as f:\n", "            data = pickle.load(f)\n", "        print(f\"成功加载数据文件: {data_path}\")\n", "        return data\n", "    else:\n", "        print(f\"数据文件 {data_path} 不存在\")\n", "        return None\n", "\n", "# 加载数据\n", "analysis_data = load_analysis_data()\n", "if analysis_data:\n", "    print(f\"数据包含的系统配置: {list(analysis_data.keys())}\")\n", "else:\n", "    print(\"无法加载数据，请先运行order_analysis.py生成分析结果\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 提取层数信息和序参量数据"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["警告: L=4, J2=1.0, J1=0.76 的数据没有模型信息\n", "警告: L=5, J2=1.0, J1=0.76 的数据没有模型信息\n", "提取到的层数外推数据:\n"]}], "source": ["def extract_layer_info_from_model_name(model_name):\n", "    \"\"\"从模型名称中提取层数信息\n", "    \n", "    Args:\n", "        model_name: 模型名称，如 'model_L4F4', 'model_L6F4', 'model_L8F4'\n", "    \n", "    Returns:\n", "        int: 层数，如果无法解析则返回None\n", "    \"\"\"\n", "    # 匹配模式：model_L{layers}F{features}\n", "    match = re.search(r'model_L(\\d+)F\\d+', model_name)\n", "    if match:\n", "        return int(match.group(1))\n", "    return None\n", "\n", "def extract_layer_data_for_extrapolation(analysis_data, target_J2=1.00, target_J1=0.76):\n", "    \"\"\"提取用于层数外推的数据\n", "    \n", "    Args:\n", "        analysis_data: 分析数据字典\n", "        target_J2: 目标J2值\n", "        target_J1: 目标J1值\n", "    \n", "    Returns:\n", "        dict: 按L值组织的层数外推数据\n", "    \"\"\"\n", "    layer_data = defaultdict(list)\n", "    \n", "    for (L, J2), data_list in analysis_data.items():\n", "        if abs(J2 - target_J2) < 1e-6:  # 匹配J2值\n", "            for data_point in data_list:\n", "                if abs(data_point['J1'] - target_J1) < 1e-6:  # 匹配J1值\n", "                    # 检查是否有模型信息\n", "                    if 'model_results' in data_point:\n", "                        for model_name, model_data in data_point['model_results'].items():\n", "                            layers = extract_layer_info_from_model_name(model_name)\n", "                            if layers is not None:\n", "                                layer_data[L].append({\n", "                                    'layers': layers,\n", "                                    'inv_layers': 1.0 / layers,\n", "                                    'neel_ratio': model_data.get('neel_ratio', 0.0),\n", "                                    'af_order_param': model_data.get('af_order_param', 0.0),\n", "                                    'neel_ratio_std': model_data.get('neel_ratio_std', 0.0),\n", "                                    'af_order_param_std': model_data.get('af_order_param_std', 0.0),\n", "                                    'model_name': model_name\n", "                                })\n", "                    else:\n", "                        # 如果没有模型信息，可能是旧格式数据\n", "                        print(f\"警告: L={L}, J2={J2}, J1={data_point['J1']} 的数据没有模型信息\")\n", "    \n", "    # 按层数排序\n", "    for L in layer_data:\n", "        layer_data[L].sort(key=lambda x: x['layers'])\n", "    \n", "    return dict(layer_data)\n", "\n", "# 提取层数外推数据\n", "if analysis_data:\n", "    layer_extrapolation_data = extract_layer_data_for_extrapolation(analysis_data)\n", "    print(f\"提取到的层数外推数据:\")\n", "    for L, data_list in layer_extrapolation_data.items():\n", "        print(f\"  L={L}: {len(data_list)} 个层数配置\")\n", "        for data in data_list:\n", "            print(f\"    层数={data['layers']}, Neel比={data['neel_ratio']:.6f}, AF序参量={data['af_order_param']:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 检查数据结构并手动提取数据"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据结构检查:\n", "\n", "L=4, J2=1.0:\n", "  数据点 0: J1=0.76\n", "    可用字段: ['L', 'J2', 'J1', 'n_checkpoints', 'neel_ratio', 'neel_ratio_std', 'neel_ratio_values', 'dimer_ratio_0pi', 'dimer_ratio_0pi_std', 'dimer_ratio_0pi_values', 'dimer_ratio_pi0', 'dimer_ratio_pi0_std', 'dimer_ratio_pi0_values', 'dimer_ratio_avg', 'dimer_ratio_avg_std', 'dimer_ratio_avg_values', 'diag_dimer_ratio_0pi', 'diag_dimer_ratio_0pi_std', 'diag_dimer_ratio_0pi_values', 'diag_dimer_ratio_pi0', 'diag_dimer_ratio_pi0_std', 'diag_dimer_ratio_pi0_values', 'diag_dimer_ratio_avg', 'diag_dimer_ratio_avg_std', 'diag_dimer_ratio_avg_values', 'af_order_param', 'af_order_param_std', 'af_order_param_values', 'dimer_order_param', 'dimer_order_param_std', 'dimer_order_param_values', 'diag_dimer_order_param', 'diag_dimer_order_param_std', 'diag_dimer_order_param_values']\n", "    neel_ratio: 0.626114761638561\n", "    af_order_param: 0.09502446282710628\n", "\n", "L=5, J2=1.0:\n", "  数据点 0: J1=0.76\n", "    可用字段: ['L', 'J2', 'J1', 'n_checkpoints', 'neel_ratio', 'neel_ratio_std', 'neel_ratio_values', 'dimer_ratio_0pi', 'dimer_ratio_0pi_std', 'dimer_ratio_0pi_values', 'dimer_ratio_pi0', 'dimer_ratio_pi0_std', 'dimer_ratio_pi0_values', 'dimer_ratio_avg', 'dimer_ratio_avg_std', 'dimer_ratio_avg_values', 'diag_dimer_ratio_0pi', 'diag_dimer_ratio_0pi_std', 'diag_dimer_ratio_0pi_values', 'diag_dimer_ratio_pi0', 'diag_dimer_ratio_pi0_std', 'diag_dimer_ratio_pi0_values', 'diag_dimer_ratio_avg', 'diag_dimer_ratio_avg_std', 'diag_dimer_ratio_avg_values', 'af_order_param', 'af_order_param_std', 'af_order_param_values', 'dimer_order_param', 'dimer_order_param_std', 'dimer_order_param_values', 'diag_dimer_order_param', 'diag_dimer_order_param_std', 'diag_dimer_order_param_values']\n", "    neel_ratio: 0.6379087633886065\n", "    af_order_param: 0.0741027209875234\n"]}], "source": ["# 如果上面的自动提取没有成功，我们手动检查数据结构\n", "if analysis_data:\n", "    print(\"数据结构检查:\")\n", "    for key, data_list in analysis_data.items():\n", "        L, J2 = key\n", "        if J2 == 1.00:  # 只看J2=1.00的数据\n", "            print(f\"\\nL={L}, J2={J2}:\")\n", "            for i, data_point in enumerate(data_list):\n", "                if abs(data_point['J1'] - 0.76) < 1e-6:  # 只看J1=0.76的数据\n", "                    print(f\"  数据点 {i}: J1={data_point['J1']}\")\n", "                    print(f\"    可用字段: {list(data_point.keys())}\")\n", "                    if 'neel_ratio' in data_point:\n", "                        print(f\"    neel_ratio: {data_point['neel_ratio']}\")\n", "                    if 'af_order_param' in data_point:\n", "                        print(f\"    af_order_param: {data_point['af_order_param']}\")\n", "                    # 检查是否有checkpoint相关信息\n", "                    if 'checkpoint' in data_point:\n", "                        print(f\"    checkpoint: {data_point['checkpoint']}\")\n", "                    break  # 只看第一个匹配的数据点"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 直接从results目录提取层数数据"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["直接从results目录提取层数数据...\n", "L=4, 层数=4: AF序参量=0.094565, <PERSON>eel比=0.624754\n", "L=4, 层数=6: AF序参量=0.092425, <PERSON>eel比=0.613777\n", "L=4, 层数=8: AF序参量=0.071894, <PERSON>eel比=0.596710\n", "L=5, 层数=4: AF序参量=0.074655, Neel比=0.635142\n", "L=5, 层数=6: AF序参量=0.072619, Neel比=0.621475\n", "L=5, 层数=8: AF序参量=0.068676, <PERSON>eel比=0.595302\n", "\n", "成功提取到层数数据:\n", "  L=4: 3 个层数配置\n", "  L=5: 3 个层数配置\n"]}], "source": ["def extract_layer_data_from_results_directory(results_dir=\"../results\", target_J2=1.00, target_J1=0.76):\n", "    \"\"\"直接从results目录结构中提取层数数据\"\"\"\n", "    results_path = Path(results_dir)\n", "    layer_data = defaultdict(list)\n", "    \n", "    # 遍历L=4和L=5目录\n", "    for L in [4, 5]:\n", "        L_dir = results_path / f\"L={L}\"\n", "        if not L_dir.exists():\n", "            continue\n", "            \n", "        J2_dir = L_dir / f\"J2={target_J2:.2f}\"\n", "        if not J2_dir.exists():\n", "            continue\n", "            \n", "        J1_dir = J2_dir / f\"J1={target_J1:.2f}\"\n", "        if not J1_dir.exists():\n", "            continue\n", "            \n", "        # 查找不同层数的模型目录\n", "        for model_dir in J1_dir.iterdir():\n", "            if model_dir.is_dir() and model_dir.name.startswith('model_L'):\n", "                layers = extract_layer_info_from_model_name(model_dir.name)\n", "                if layers is not None:\n", "                    # 查找分析结果\n", "                    analysis_dir = model_dir / \"analysis\" / \"final_GCNN\"\n", "                    if analysis_dir.exists():\n", "                        # 查找spin数据文件\n", "                        spin_data_file = analysis_dir / \"spin\" / \"spin_data.npy\"\n", "                        if spin_data_file.exists():\n", "                            try:\n", "                                # 加载spin数据并计算序参量\n", "                                spin_data = np.load(spin_data_file, allow_pickle=True).item()\n", "                                \n", "                                # 提取k点和结构因子\n", "                                k_points_x = spin_data['metadata']['k_grid']['kx']\n", "                                k_points_y = spin_data['metadata']['k_grid']['ky']\n", "                                structure_factor = spin_data['structure_factor']['values']\n", "                                \n", "                                # 计算AF序参量 (S(π,π))\n", "                                # 理论基础：反铁磁序参量定义为S(π,π)，即(π,π)点的自旋结构因子\n", "                                # 这对应于Neel序的特征波矢，反映了相邻格点自旋反平行排列的程度\n", "                                pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))\n", "                                pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))\n", "                                af_order_param = structure_factor[pi_idx_y, pi_idx_x].real\n", "                                \n", "                                # 计算Neel关联比（使用标准方法：只取一个相邻点）\n", "                                # 理论基础：Neel关联比 = 1 - S(相邻点)/S(π,π)\n", "                                # 这个比值衡量了(π,π)峰相对于背景的突出程度，反映Neel序的强度\n", "                                # 按照标准方法，取x方向的相邻点\n", "                                idx_x_adj = pi_idx_x + 1 if pi_idx_x < len(k_points_x) - 1 else pi_idx_x - 1\n", "                                S_adj = structure_factor[pi_idx_y, idx_x_adj].real\n", "                                \n", "                                # 计算ratio：1 - S_adj / S_main\n", "                                neel_ratio = 1.0 - S_adj / af_order_param if abs(af_order_param) > 1e-10 else 0.0\n", "                                \n", "                                layer_data[L].append({\n", "                                    'layers': layers,\n", "                                    'inv_layers': 1.0 / layers,\n", "                                    'neel_ratio': neel_ratio,\n", "                                    'af_order_param': af_order_param,\n", "                                    'model_name': model_dir.name\n", "                                })\n", "                                \n", "                                print(f\"L={L}, 层数={layers}: AF序参量={af_order_param:.6f}, Neel比={neel_ratio:.6f}\")\n", "                                \n", "                            except Exception as e:\n", "                                print(f\"处理 {spin_data_file} 时出错: {e}\")\n", "    \n", "    # 按层数排序\n", "    for L in layer_data:\n", "        layer_data[L].sort(key=lambda x: x['layers'])\n", "    \n", "    return dict(layer_data)\n", "\n", "# 直接从results目录提取数据\n", "print(\"直接从results目录提取层数数据...\")\n", "layer_data_direct = extract_layer_data_from_results_directory()\n", "\n", "if layer_data_direct:\n", "    print(f\"\\n成功提取到层数数据:\")\n", "    for L, data_list in layer_data_direct.items():\n", "        print(f\"  L={L}: {len(data_list)} 个层数配置\")\n", "else:\n", "    print(\"未能提取到层数数据\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 绘制层数外推图"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Plotting AF order parameter layer extrapolation...\n", "L=4 Fit Results: slope=0.159123, intercept=0.057564, extrapolated=0.057564\n", "L=5 Fit Results: slope=0.044486, intercept=0.063951, extrapolated=0.063951\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def linear_function(x, a, b):\n", "    \"\"\"线性函数用于拟合\"\"\"\n", "    return a * x + b\n", "\n", "def plot_layer_extrapolation(layer_data, parameter='af_order_param', title_suffix='AF Order Parameter'):\n", "    \"\"\"绘制层数外推图\n", "    \n", "    Args:\n", "        layer_data: 层数数据字典\n", "        parameter: 要绘制的参数名称\n", "        title_suffix: 图标题后缀\n", "    \"\"\"\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    colors = ['blue', 'red', 'green', 'orange']\n", "    markers = ['o', 's', '^', 'D']\n", "    \n", "    extrapolated_values = {}\n", "    \n", "    for i, (L, data_list) in enumerate(layer_data.items()):\n", "        if not data_list:\n", "            continue\n", "            \n", "        # 提取数据\n", "        inv_layers = [d['inv_layers'] for d in data_list]\n", "        values = [d[parameter] for d in data_list]\n", "        layers = [d['layers'] for d in data_list]\n", "        \n", "        color = colors[i % len(colors)]\n", "        marker = markers[i % len(markers)]\n", "        \n", "        # 绘制数据点\n", "        plt.scatter(inv_layers, values, color=color, marker=marker, s=100, \n", "                   label=f'L={L} Data Points', alpha=0.8, edgecolors='black', linewidth=1)\n", "        \n", "        # 添加层数标签\n", "        for j, (x, y, layer) in enumerate(zip(inv_layers, values, layers)):\n", "            plt.annotate(f'{layer} layers', (x, y), xytext=(5, 5), textcoords='offset points',\n", "                        fontsize=10, alpha=0.7)\n", "        \n", "        # 线性拟合\n", "        if len(inv_layers) >= 2:\n", "            try:\n", "                popt, pcov = curve_fit(linear_function, inv_layers, values)\n", "                a, b = popt\n", "                \n", "                # 生成拟合线\n", "                x_fit = np.linspace(0, max(inv_layers) * 1.1, 100)\n", "                y_fit = linear_function(x_fit, a, b)\n", "                \n", "                plt.plot(x_fit, y_fit, color=color, linestyle='--', alpha=0.7,\n", "                        label=f'L={L} Fit: y={a:.4f}x+{b:.4f}')\n", "                \n", "                # 计算外推值 (1/layers -> 0)\n", "                extrapolated_value = b\n", "                extrapolated_values[L] = extrapolated_value\n", "                \n", "                # 标记外推点\n", "                plt.scatter([0], [extrapolated_value], color=color, marker='*', s=200,\n", "                           edgecolors='black', linewidth=2, alpha=0.9)\n", "                plt.annotate(f'L={L} Extrapolated\\n{extrapolated_value:.6f}', \n", "                           (0, extrapolated_value), xytext=(10, 10), \n", "                           textcoords='offset points', fontsize=10, \n", "                           bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.3))\n", "                \n", "                print(f\"L={L} Fit Results: slope={a:.6f}, intercept={b:.6f}, extrapolated={extrapolated_value:.6f}\")\n", "                \n", "            except Exception as e:\n", "                print(f\"L={L} Fit Failed: {e}\")\n", "    \n", "    plt.xlabel('1/Number of Layers', fontsize=14)\n", "    plt.ylabel(f'{title_suffix}', fontsize=14)\n", "    plt.title(f'Neural Network Layer Extrapolation Analysis - {title_suffix}\\n(J2=1.00, J1=0.76)', fontsize=16)\n", "    plt.legend(fontsize=12, loc='best')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 设置坐标轴范围\n", "    plt.xlim(-0.02, max([max([d['inv_layers'] for d in data_list]) for data_list in layer_data.values()]) * 1.1)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 注释掉文件保存，避免生成外部文件\n", "    # filename = f'layer_extrapolation_{parameter}_J2_1.00_J1_0.76.png'\n", "    # plt.savefig(filename, dpi=300, bbox_inches='tight')\n", "    # print(f\"Figure saved as: {filename}\")\n", "    \n", "    plt.show()\n", "    \n", "    return extrapolated_values\n", "\n", "# 绘制AF序参量的层数外推图\n", "if layer_data_direct:\n", "    print(\"Plotting AF order parameter layer extrapolation...\")\n", "    af_extrapolated = plot_layer_extrapolation(layer_data_direct, 'af_order_param', 'AF Order Parameter')\n", "    \n", "    # 注释掉Neel关联比的层数外推分析\n", "    # print(\"\\nPlotting Neel correlation ratio layer extrapolation...\")\n", "    # neel_extrapolated = plot_layer_extrapolation(layer_data_direct, 'neel_ratio', 'Neel Correlation Ratio')\n", "else:\n", "    print(\"No layer data available for plotting\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 外推结果总结"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "层数外推分析结果总结\n", "============================================================\n", "分析参数: J2=1.00, J1=0.76\n", "\n", "原始数据:\n", "\n", "L=4系统:\n", "  4层: AF序参量=0.094565, <PERSON>eel比=0.624754\n", "  6层: AF序参量=0.092425, <PERSON><PERSON>比=0.613777\n", "  8层: AF序参量=0.071894, <PERSON>eel比=0.596710\n", "\n", "L=5系统:\n", "  4层: AF序参量=0.074655, Neel比=0.635142\n", "  6层: AF序参量=0.072619, <PERSON>eel比=0.621475\n", "  8层: AF序参量=0.068676, <PERSON>eel比=0.595302\n", "\n", "AF序参量外推结果 (层数→∞):\n", "  L=4: 0.057564\n", "  L=5: 0.063951\n", "\n", "注意: 外推值是通过线性拟合1/层数 vs 序参量得到的截距值\n", "这代表了在无限层数极限下的序参量估计值\n"]}], "source": ["# 总结外推结果\n", "if layer_data_direct:\n", "    print(\"=\"*60)\n", "    print(\"层数外推分析结果总结\")\n", "    print(\"=\"*60)\n", "    print(f\"分析参数: J2=1.00, J1=0.76\")\n", "    print()\n", "    \n", "    # 显示原始数据\n", "    print(\"原始数据:\")\n", "    for L, data_list in layer_data_direct.items():\n", "        print(f\"\\nL={L}系统:\")\n", "        for data in data_list:\n", "            print(f\"  {data['layers']}层: AF序参量={data['af_order_param']:.6f}, Neel比={data['neel_ratio']:.6f}\")\n", "    \n", "    # 显示外推结果\n", "    if 'af_extrapolated' in locals() and af_extrapolated:\n", "        print(\"\\nAF序参量外推结果 (层数→∞):\")\n", "        for L, value in af_extrapolated.items():\n", "            print(f\"  L={L}: {value:.6f}\")\n", "    \n", "    # 删除Neel关联比外推结果显示\n", "    # if 'neel_extrapolated' in locals() and neel_extrapolated:\n", "    #     print(\"\\nNeel关联比外推结果 (层数→∞):\")\n", "    #     for L, value in neel_extrapolated.items():\n", "    #         print(f\"  L={L}: {value:.6f}\")\n", "    \n", "    print(\"\\n注意: 外推值是通过线性拟合1/层数 vs 序参量得到的截距值\")\n", "    print(\"这代表了在无限层数极限下的序参量估计值\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Energy Variance外推分析\n", "\n", "本节分析Energy Variance与Neel序参量的关系，并进行外推分析。\n", "\n", "### 分析目标\n", "- 提取J2=1.00, J1=0.76, model_L4F4的训练数据\n", "- 分别对L=4和L=5系统，取iter>1000的检查点\n", "- 横轴：Energy Variance，纵轴：Neel序参量\n", "- 进行外推分析，探索Energy Variance→0时的序参量值"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["提取Energy Variance数据...\n", "L=4: 提取到 1251 个数据点 (iter >= 1000), per site归一化因子: 64\n", "L=5: 提取到 1251 个数据点 (iter >= 1000), per site归一化因子: 100\n", "\n", "成功提取Energy Variance数据:\n", "  L=4: 1251 个数据点\n", "    迭代范围: 1000 - 2250\n", "    能量方差范围(per site): 0.001952 - 0.027366\n", "  L=5: 1251 个数据点\n", "    迭代范围: 1000 - 2250\n", "    能量方差范围(per site): 0.002705 - 0.120079\n"]}], "source": ["def parse_training_log(log_file_path, min_iter=1000):\n", "    \"\"\"\n", "    解析训练日志文件，提取energy variance数据\n", "    \n", "    Args:\n", "        log_file_path: 训练日志文件路径\n", "        min_iter: 最小迭代次数阈值\n", "    \n", "    Returns:\n", "        dict: 包含迭代次数、能量方差等信息的字典\n", "    \"\"\"\n", "    energy_variance_data = []\n", "    \n", "    try:\n", "        with open(log_file_path, 'r') as f:\n", "            for line in f:\n", "                # 匹配训练日志格式：[Iter  XXX/XXXX] ... | E_var: X.XXXX | ...\n", "                if '| E_var:' in line and '| E:' in line:\n", "                    # 提取迭代次数\n", "                    iter_match = re.search(r'\\[Iter\\s+(\\d+)/\\d+\\]', line)\n", "                    if not iter_match:\n", "                        continue\n", "                    \n", "                    iteration = int(iter_match.group(1))\n", "                    if iteration < min_iter:\n", "                        continue\n", "                    \n", "                    # 提取能量\n", "                    energy_match = re.search(r'\\| E:\\s*([-+]?\\d+\\.\\d+)', line)\n", "                    if not energy_match:\n", "                        continue\n", "                    energy = float(energy_match.group(1))\n", "                    \n", "                    # 提取能量方差\n", "                    var_match = re.search(r'\\| E_var:\\s*(\\d+\\.\\d+)', line)\n", "                    if not var_match:\n", "                        continue\n", "                    energy_var = float(var_match.group(1))\n", "                    \n", "                    # 提取能量误差\n", "                    err_match = re.search(r'\\| E_err:\\s*(\\d+\\.\\d+)', line)\n", "                    energy_err = float(err_match.group(1)) if err_match else 0.0\n", "                    \n", "                    energy_variance_data.append({\n", "                        'iteration': iteration,\n", "                        'energy': energy,\n", "                        'energy_variance': energy_var,\n", "                        'energy_error': energy_err\n", "                    })\n", "    \n", "    except Exception as e:\n", "        print(f\"解析日志文件 {log_file_path} 时出错: {e}\")\n", "        return []\n", "    \n", "    return energy_variance_data\n", "\n", "def extract_energy_variance_data(results_dir=\"../results\", target_J2=1.00, target_J1=0.76, \n", "                                model_name=\"model_L4F4\", min_iter=1000):\n", "    \"\"\"\n", "    提取energy variance数据并进行per site归一化\n", "    \n", "    Args:\n", "        results_dir: 结果目录\n", "        target_J2: 目标J2值\n", "        target_J1: 目标J1值\n", "        model_name: 模型名称\n", "        min_iter: 最小迭代次数\n", "    \n", "    Returns:\n", "        dict: 按L值组织的energy variance数据（已进行per site归一化）\n", "    \"\"\"\n", "    results_path = Path(results_dir)\n", "    energy_data = defaultdict(list)\n", "    \n", "    # 遍历L=4和L=5目录\n", "    for L in [4, 5]:\n", "        L_dir = results_path / f\"L={L}\"\n", "        if not L_dir.exists():\n", "            continue\n", "            \n", "        J2_dir = L_dir / f\"J2={target_J2:.2f}\"\n", "        if not J2_dir.exists():\n", "            continue\n", "            \n", "        J1_dir = J2_dir / f\"J1={target_J1:.2f}\"\n", "        if not J1_dir.exists():\n", "            continue\n", "            \n", "        model_dir = J1_dir / model_name\n", "        if not model_dir.exists():\n", "            continue\n", "            \n", "        # 计算per site归一化因子\n", "        # L=4: 4×4×4 = 64 sites\n", "        # L=5: 5×5×4 = 100 sites\n", "        n_sites = L * L * 4\n", "        \n", "        # 解析训练日志\n", "        train_log = model_dir / \"training\" / \"train.log\"\n", "        if train_log.exists():\n", "            log_data = parse_training_log(train_log, min_iter)\n", "            if log_data:\n", "                # 对energy variance进行per site归一化\n", "                for data_point in log_data:\n", "                    data_point['energy_variance'] = data_point['energy_variance'] / n_sites\n", "                    data_point['energy'] = data_point['energy'] / n_sites  # 能量也进行per site归一化\n", "                    data_point['energy_error'] = data_point['energy_error'] / n_sites  # 误差也进行per site归一化\n", "                \n", "                energy_data[L] = log_data\n", "                print(f\"L={L}: 提取到 {len(log_data)} 个数据点 (iter >= {min_iter}), per site归一化因子: {n_sites}\")\n", "            else:\n", "                print(f\"L={L}: 未找到有效的训练数据\")\n", "        else:\n", "            print(f\"L={L}: 训练日志文件不存在: {train_log}\")\n", "    \n", "    return dict(energy_data)\n", "\n", "# 提取energy variance数据\n", "print(\"提取Energy Variance数据...\")\n", "energy_variance_data = extract_energy_variance_data()\n", "\n", "if energy_variance_data:\n", "    print(f\"\\n成功提取Energy Variance数据:\")\n", "    for L, data_list in energy_variance_data.items():\n", "        if data_list:\n", "            print(f\"  L={L}: {len(data_list)} 个数据点\")\n", "            print(f\"    迭代范围: {data_list[0]['iteration']} - {data_list[-1]['iteration']}\")\n", "            print(f\"    能量方差范围(per site): {min(d['energy_variance'] for d in data_list):.6f} - {max(d['energy_variance'] for d in data_list):.6f}\")\n", "else:\n", "    print(\"未能提取到Energy Variance数据\")\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["从checkpoint获取序参量数据...\n", "L=4, iter=1400: AF序参量=0.095792, <PERSON>eel比=0.628697\n", "L=4, iter=1200: AF序参量=0.095545, Neel比=0.628222\n", "L=4, iter=2000: AF序参量=0.095157, Neel比=0.626991\n", "L=4, iter=1000: AF序参量=0.097524, Neel比=0.638803\n", "L=4, iter=2200: AF序参量=0.095139, Neel比=0.626870\n", "L=4, iter=1600: AF序参量=0.095952, <PERSON>eel比=0.632692\n", "L=4, iter=1800: AF序参量=0.096141, <PERSON>eel比=0.630794\n", "L=5, iter=1400: AF序参量=0.075593, Neel比=0.643424\n", "L=5, iter=1200: AF序参量=0.076638, Neel比=0.647726\n", "L=5, iter=2000: AF序参量=0.074536, Neel比=0.635863\n", "L=5, iter=1000: AF序参量=0.078168, Neel比=0.658036\n", "L=5, iter=2200: AF序参量=0.072280, Neel比=0.624708\n", "L=5, iter=1600: AF序参量=0.073772, <PERSON>eel比=0.639187\n", "L=5, iter=1800: AF序参量=0.073449, Neel比=0.634622\n", "\n", "成功获取checkpoint序参量数据:\n", "  L=4: 7 个checkpoint\n", "    迭代范围: 1000 - 2200\n", "  L=5: 7 个checkpoint\n", "    迭代范围: 1000 - 2200\n"]}], "source": ["def get_checkpoint_order_parameters(results_dir=\"../results\", target_J2=1.00, target_J1=0.76, \n", "                                   model_name=\"model_L4F4\", min_iter=1000):\n", "    \"\"\"\n", "    从checkpoint分析结果中获取序参量数据\n", "    \n", "    Args:\n", "        results_dir: 结果目录\n", "        target_J2: 目标J2值\n", "        target_J1: 目标J1值\n", "        model_name: 模型名称\n", "        min_iter: 最小迭代次数\n", "    \n", "    Returns:\n", "        dict: 按L值组织的序参量数据\n", "    \"\"\"\n", "    results_path = Path(results_dir)\n", "    order_param_data = defaultdict(dict)\n", "    \n", "    # 遍历L=4和L=5目录\n", "    for L in [4, 5]:\n", "        L_dir = results_path / f\"L={L}\"\n", "        if not L_dir.exists():\n", "            continue\n", "            \n", "        J2_dir = L_dir / f\"J2={target_J2:.2f}\"\n", "        if not J2_dir.exists():\n", "            continue\n", "            \n", "        J1_dir = J2_dir / f\"J1={target_J1:.2f}\"\n", "        if not J1_dir.exists():\n", "            continue\n", "            \n", "        model_dir = J1_dir / model_name\n", "        if not model_dir.exists():\n", "            continue\n", "            \n", "        # 查找分析目录中的checkpoint结果\n", "        analysis_base_dir = model_dir / \"analysis\"\n", "        if not analysis_base_dir.exists():\n", "            continue\n", "            \n", "        # 遍历所有checkpoint分析结果\n", "        for checkpoint_dir in analysis_base_dir.iterdir():\n", "            if not checkpoint_dir.is_dir():\n", "                continue\n", "                \n", "            # 提取checkpoint迭代次数\n", "            checkpoint_name = checkpoint_dir.name\n", "            if checkpoint_name == \"final_GCNN\":\n", "                # 对于final checkpoint，我们跳过，因为不知道确切的迭代次数\n", "                continue\n", "            elif checkpoint_name.startswith(\"checkpoint_iter_\"):\n", "                # 提取迭代次数\n", "                iter_match = re.search(r'checkpoint_iter_(\\d+)', checkpoint_name)\n", "                if iter_match:\n", "                    iteration = int(iter_match.group(1))\n", "                    if iteration < min_iter:\n", "                        continue\n", "                else:\n", "                    continue\n", "            else:\n", "                continue\n", "            \n", "            # 查找spin数据文件\n", "            spin_data_file = checkpoint_dir / \"spin\" / \"spin_data.npy\"\n", "            if spin_data_file.exists():\n", "                try:\n", "                    # 加载spin数据并计算序参量\n", "                    spin_data = np.load(spin_data_file, allow_pickle=True).item()\n", "                    \n", "                    # 提取k点和结构因子\n", "                    k_points_x = spin_data['metadata']['k_grid']['kx']\n", "                    k_points_y = spin_data['metadata']['k_grid']['ky']\n", "                    structure_factor = spin_data['structure_factor']['values']\n", "                    \n", "                    # 计算AF序参量 (S(π,π))\n", "                    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))\n", "                    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))\n", "                    af_order_param = structure_factor[pi_idx_y, pi_idx_x].real\n", "                    \n", "                    # 计算Neel关联比\n", "                    idx_x_adj = pi_idx_x + 1 if pi_idx_x < len(k_points_x) - 1 else pi_idx_x - 1\n", "                    S_adj = structure_factor[pi_idx_y, idx_x_adj].real\n", "                    neel_ratio = 1.0 - S_adj / af_order_param if abs(af_order_param) > 1e-10 else 0.0\n", "                    \n", "                    order_param_data[L][iteration] = {\n", "                        'af_order_param': af_order_param,\n", "                        'neel_ratio': neel_ratio,\n", "                        'checkpoint_name': checkpoint_name\n", "                    }\n", "                    \n", "                    print(f\"L={L}, iter={iteration}: AF序参量={af_order_param:.6f}, Neel比={neel_ratio:.6f}\")\n", "                    \n", "                except Exception as e:\n", "                    print(f\"处理 {spin_data_file} 时出错: {e}\")\n", "    \n", "    return dict(order_param_data)\n", "\n", "# 获取checkpoint序参量数据\n", "print(\"从checkpoint获取序参量数据...\")\n", "checkpoint_order_data = get_checkpoint_order_parameters()\n", "\n", "if checkpoint_order_data:\n", "    print(f\"\\n成功获取checkpoint序参量数据:\")\n", "    for L, iter_data in checkpoint_order_data.items():\n", "        print(f\"  L={L}: {len(iter_data)} 个checkpoint\")\n", "        if iter_data:\n", "            iterations = sorted(iter_data.keys())\n", "            print(f\"    迭代范围: {iterations[0]} - {iterations[-1]}\")\n", "else:\n", "    print(\"未能获取到checkpoint序参量数据\")\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["合并Energy Variance和序参量数据...\n", "L=4: 合并得到 7 个数据点\n", "  Energy Variance范围: 0.0022 - 0.0046\n", "  AF序参量范围: 0.095139 - 0.097524\n", "L=5: 合并得到 7 个数据点\n", "  Energy Variance范围: 0.0027 - 0.0074\n", "  AF序参量范围: 0.072280 - 0.078168\n", "\n", "成功合并数据:\n", "  L=4: 7 个有效数据点\n", "  L=5: 7 个有效数据点\n"]}], "source": ["def merge_energy_variance_and_order_data(energy_data, order_data):\n", "    \"\"\"\n", "    合并energy variance数据和序参量数据\n", "    \n", "    Args:\n", "        energy_data: energy variance数据\n", "        order_data: 序参量数据\n", "    \n", "    Returns:\n", "        dict: 合并后的数据\n", "    \"\"\"\n", "    merged_data = defaultdict(list)\n", "    \n", "    for L in energy_data.keys():\n", "        if L not in order_data:\n", "            continue\n", "            \n", "        energy_list = energy_data[L]\n", "        order_dict = order_data[L]\n", "        \n", "        # 创建迭代次数到energy variance的映射\n", "        iter_to_energy = {d['iteration']: d for d in energy_list}\n", "        \n", "        # 合并数据\n", "        for iteration, order_info in order_dict.items():\n", "            if iteration in iter_to_energy:\n", "                energy_info = iter_to_energy[iteration]\n", "                \n", "                merged_data[L].append({\n", "                    'iteration': iteration,\n", "                    'energy_variance': energy_info['energy_variance'],\n", "                    'energy': energy_info['energy'],\n", "                    'energy_error': energy_info['energy_error'],\n", "                    'af_order_param': order_info['af_order_param'],\n", "                    'neel_ratio': order_info['neel_ratio'],\n", "                    'checkpoint_name': order_info['checkpoint_name']\n", "                })\n", "        \n", "        # 按energy variance排序\n", "        merged_data[L].sort(key=lambda x: x['energy_variance'])\n", "        \n", "        print(f\"L={L}: 合并得到 {len(merged_data[L])} 个数据点\")\n", "        if merged_data[L]:\n", "            print(f\"  Energy Variance范围: {merged_data[L][0]['energy_variance']:.4f} - {merged_data[L][-1]['energy_variance']:.4f}\")\n", "            print(f\"  AF序参量范围: {min(d['af_order_param'] for d in merged_data[L]):.6f} - {max(d['af_order_param'] for d in merged_data[L]):.6f}\")\n", "    \n", "    return dict(merged_data)\n", "\n", "# 合并数据\n", "print(\"合并Energy Variance和序参量数据...\")\n", "if energy_variance_data and checkpoint_order_data:\n", "    merged_extrapolation_data = merge_energy_variance_and_order_data(energy_variance_data, checkpoint_order_data)\n", "    \n", "    if merged_extrapolation_data:\n", "        print(f\"\\n成功合并数据:\")\n", "        for L, data_list in merged_extrapolation_data.items():\n", "            print(f\"  L={L}: {len(data_list)} 个有效数据点\")\n", "    else:\n", "        print(\"合并后没有有效数据点\")\n", "else:\n", "    print(\"缺少必要的数据进行合并\")\n", "    merged_extrapolation_data = {}\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["绘制AF序参量的Energy Variance外推图...\n", "L=4 Fit Results: slope=0.950296, intercept=0.092810, extrapolated=0.092810\n", "L=5 Fit Results: slope=0.689970, intercept=0.071727, extrapolated=0.071727\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_energy_variance_extrapolation(merged_data, parameter='af_order_param', title_suffix='AF Order Parameter'):\n", "    \"\"\"\n", "    绘制Energy Variance外推图\n", "    \n", "    Args:\n", "        merged_data: 合并的数据字典\n", "        parameter: 要绘制的参数名称\n", "        title_suffix: 图标题后缀\n", "    \"\"\"\n", "    if not merged_data:\n", "        print(\"没有数据可以绘制\")\n", "        return {}\n", "    \n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    colors = ['blue', 'red', 'green', 'orange']\n", "    markers = ['o', 's', '^', 'D']\n", "    \n", "    extrapolated_values = {}\n", "    \n", "    for i, (L, data_list) in enumerate(merged_data.items()):\n", "        if not data_list:\n", "            continue\n", "            \n", "        # 提取数据\n", "        energy_variances = [d['energy_variance'] for d in data_list]\n", "        values = [d[parameter] for d in data_list]\n", "        iterations = [d['iteration'] for d in data_list]\n", "        \n", "        color = colors[i % len(colors)]\n", "        marker = markers[i % len(markers)]\n", "        \n", "        # 绘制数据点\n", "        plt.scatter(energy_variances, values, color=color, marker=marker, s=100, \n", "                   label=f'L={L} Data Points', alpha=0.8, edgecolors='black', linewidth=1)\n", "        \n", "        # 添加迭代次数标签\n", "        for j, (x, y, iter_num) in enumerate(zip(energy_variances, values, iterations)):\n", "            plt.annotate(f'{iter_num}', (x, y), xytext=(5, 5), textcoords='offset points',\n", "                        fontsize=8, alpha=0.7)\n", "        \n", "        # 线性拟合 (对于Energy Variance → 0的外推)\n", "        if len(energy_variances) >= 2:\n", "            try:\n", "                # 使用线性拟合: y = a * x + b\n", "                popt, pcov = curve_fit(linear_function, energy_variances, values)\n", "                a, b = popt\n", "                \n", "                # 生成拟合线\n", "                x_fit = np.linspace(0, max(energy_variances) * 1.1, 100)\n", "                y_fit = linear_function(x_fit, a, b)\n", "                \n", "                plt.plot(x_fit, y_fit, color=color, linestyle='--', alpha=0.7,\n", "                        label=f'L={L} Fit: y={a:.4f}x+{b:.6f}')\n", "                \n", "                # 计算外推值 (Energy Variance → 0)\n", "                extrapolated_value = b\n", "                extrapolated_values[L] = extrapolated_value\n", "                \n", "                # 标记外推点\n", "                plt.scatter([0], [extrapolated_value], color=color, marker='*', s=200,\n", "                           edgecolors='black', linewidth=2, alpha=0.9)\n", "                plt.annotate(f'L={L} Extrapolated\\\\n{extrapolated_value:.6f}', \n", "                           (0, extrapolated_value), xytext=(10, 10), \n", "                           textcoords='offset points', fontsize=10, \n", "                           bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.3))\n", "                \n", "                print(f\"L={L} Fit Results: slope={a:.6f}, intercept={b:.6f}, extrapolated={extrapolated_value:.6f}\")\n", "                \n", "            except Exception as e:\n", "                print(f\"L={L} Fit Failed: {e}\")\n", "    \n", "    plt.xlabel('Energy Variance (per site)', fontsize=14)\n", "    plt.ylabel(f'{title_suffix}', fontsize=14)\n", "    plt.title(f'Energy Variance Extrapolation Analysis - {title_suffix}\\\\n(J2=1.00, J1=0.76, model_L4F4, per site normalized)', fontsize=16)\n", "    plt.legend(fontsize=12, loc='best')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 设置坐标轴范围\n", "    if merged_data:\n", "        max_variance = max([max([d['energy_variance'] for d in data_list]) for data_list in merged_data.values()])\n", "        plt.xlim(0, max_variance * 1.1)  # Energy Variance不能为负，从0开始\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 注释掉文件保存，避免生成外部文件\n", "    # filename = f'energy_variance_extrapolation_{parameter}_J2_1.00_J1_0.76.png'\n", "    # plt.savefig(filename, dpi=300, bbox_inches='tight')\n", "    # print(f\"Figure saved as: {filename}\")\n", "    \n", "    plt.show()\n", "    \n", "    return extrapolated_values\n", "\n", "# 绘制Energy Variance外推图\n", "if merged_extrapolation_data:\n", "    print(\"绘制AF序参量的Energy Variance外推图...\")\n", "    af_ev_extrapolated = plot_energy_variance_extrapolation(merged_extrapolation_data, 'af_order_param', 'AF Order Parameter')\n", "    \n", "    # 删除Neel关联比的Energy Variance外推分析\n", "    # print(\"\\\\n绘制Neel关联比的Energy Variance外推图...\")\n", "    # neel_ev_extrapolated = plot_energy_variance_extrapolation(merged_extrapolation_data, 'neel_ratio', 'Neel Correlation Ratio')\n", "else:\n", "    print(\"没有数据可以绘制Energy Variance外推图\")\n"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}