import time
import math
import jax
import jax.numpy as jnp
from jax import tree_util
from netket.experimental.driver import VMC_SR
from src.utils.logging import log_message

# ============================================================================
# 工具函数
# ============================================================================

def clip_gradients(grad, max_norm):
    """
    梯度裁剪函数，防止梯度爆炸
    
    Args:
        grad: 梯度树
        max_norm: 最大梯度范数
    
    Returns:
        裁剪后的梯度
    """
    # 计算所有梯度的L2范数
    total_norm = jnp.sqrt(sum([jnp.sum(jnp.square(x)) for x in jax.tree_leaves(grad)]))
    # 如果总范数超过max_norm，则计算缩放因子，否则为1.0
    clip_coef = jnp.where(total_norm > max_norm, max_norm / (total_norm + 1e-6), 1.0)
    # 对所有梯度乘以缩放因子
    return jax.tree_map(lambda x: x * clip_coef, grad)


# ============================================================================
# 方案A: 改进的能量最小化驱动器（推荐方案）
# ============================================================================

class ImprovedVMC_SR(VMC_SR):
    """
    改进的VMC_SR驱动器
    
    特点：
    1. 标准能量最小化（不是自由能）
    2. 梯度裁剪，提高训练稳定性
    3. 学习率余弦退火调度
    4. NaN检测和自动恢复
    
    优势：
    - 能量方差小，训练稳定
    - 结果易于解释和验证
    - 直接优化基态能量
    """
    
    def __init__(self, clip_norm=1.0, use_lr_schedule=True, *args, **kwargs):
        """
        Args:
            clip_norm: 梯度裁剪阈值
            use_lr_schedule: 是否使用学习率调度
            *args, **kwargs: 传递给VMC_SR的参数
        """
        # 提取hamiltonian参数，因为VMC_SR需要它作为第一个位置参数
        hamiltonian = kwargs.pop('hamiltonian', None)
        if hamiltonian is None and args:
            hamiltonian = args[0]
            args = args[1:]
        
        # 调用父类构造函数
        super().__init__(hamiltonian, *args, **kwargs)
        
        self.clip_norm = clip_norm
        self.use_lr_schedule = use_lr_schedule
        self.hamiltonian = hamiltonian
        
        # 学习率调度相关
        self.initial_lr = None
        self.current_lr = None
    
    def _step_with_state(self, state):
        """
        执行一步优化，添加梯度裁剪
        """
        # 调用父类方法获取标准能量梯度
        new_state = super()._step_with_state(state)
        
        # 对梯度进行裁剪
        clipped_grad = clip_gradients(new_state.gradient, self.clip_norm)
        
        # 使用裁剪后的梯度更新参数
        params = new_state.parameters
        new_params = self.optimizer.update(clipped_grad, params)
        new_state = new_state.replace(parameters=new_params)
        
        return new_state


class CustomImprovedVMC_SR(ImprovedVMC_SR):
    """
    自定义改进的VMC_SR驱动器，添加完整的训练管理功能
    
    功能：
    1. 学习率余弦退火 + 热重启
    2. NaN检测和早停
    3. Checkpoint保存
    4. 详细日志输出
    """
    
    def __init__(self, initial_period=100, period_mult=2.0,
                 max_lr=None, min_lr=None,
                 base_lr=None,  # 新增：基础学习率
                 checkpoint_callback=None, checkpoint_interval=500, 
                 *args, **kwargs):
        """
        Args:
            initial_period: 初始重启周期
            period_mult: 周期倍增因子
            max_lr: 最大学习率（绝对值）
            min_lr: 最小学习率（绝对值）
            base_lr: 基础学习率（如果max_lr/min_lr未指定，则使用base_lr计算）
            checkpoint_callback: checkpoint回调函数
            checkpoint_interval: checkpoint保存间隔
        """
        super().__init__(*args, **kwargs)
        
        self.max_nan_count = 5  # 最大允许连续NaN次数
        
        # 学习率调度参数
        self.initial_period = initial_period
        self.period_mult = period_mult
        
        # 获取基础学习率（从优化器）
        self.base_lr = base_lr
        if self.base_lr is None:
            # 尝试从优化器获取
            self.base_lr = self._get_optimizer_lr()
        
        # 设置学习率范围
        if max_lr is not None and min_lr is not None:
            self.max_lr = max_lr
            self.min_lr = min_lr
        else:
            # 使用base_lr和因子计算
            self.max_lr = self.base_lr
            self.min_lr = self.base_lr * 0.01  # 默认衰减到1%
        
        # 记录重启信息
        self.current_restart = 0
        self.current_period = initial_period
        self.iter_since_restart = 0
        
        # Checkpoint相关
        self.checkpoint_callback = checkpoint_callback
        self.checkpoint_interval = checkpoint_interval
        
        # 当前学习率
        self.current_lr = self.max_lr
    
    def _get_optimizer_lr(self):
        """从优化器获取当前学习率"""
        # NetKet的SGD优化器使用 .learning_rate 属性
        if hasattr(self.optimizer, 'learning_rate'):
            return float(self.optimizer.learning_rate)
        elif hasattr(self.optimizer, '_lr'):
            return float(self.optimizer._lr)
        else:
            # 默认值
            return 0.001
    
    def _cosine_annealing_with_restart(self, iteration):
        """
        带热重启的余弦退火学习率调度
        
        lr(i) = lr_min + (lr_max - lr_min) * (1 + cos(π * i_cur / T_cur)) / 2
        
        Args:
            iteration: 当前迭代次数
        
        Returns:
            当前学习率（绝对值）
        """
        # 检查是否需要重启
        if self.iter_since_restart >= self.current_period:
            self.current_restart += 1
            self.iter_since_restart = 0
            # 更新下一个重启周期长度
            self.current_period = int(self.initial_period * (self.period_mult ** self.current_restart))
        
        cosine_factor = (1 + math.cos(math.pi * self.iter_since_restart / self.current_period)) / 2
        learning_rate = self.min_lr + (self.max_lr - self.min_lr) * cosine_factor
        
        self.iter_since_restart += 1
        return learning_rate
    
    def _update_learning_rate(self, learning_rate):
        """
        更新优化器的学习率
        
        Args:
            learning_rate: 新的学习率（绝对值）
        """
        self.current_lr = learning_rate
        
        # 更新优化器学习率
        # NetKet的SGD优化器使用 .learning_rate 属性
        if hasattr(self.optimizer, 'learning_rate'):
            self.optimizer.learning_rate = learning_rate
        elif hasattr(self.optimizer, '_lr'):
            self.optimizer._lr = learning_rate
    
    def run(self, n_iter, energy_log):
        """
        运行优化
        
        输出信息：
        - Energy: 能量期望值（优化目标）
        - E_var: 能量方差（应该较小）
        - E_err: 能量误差
        - LR: 当前学习率
        
        Args:
            n_iter: 迭代次数
            energy_log: 日志文件路径
        """
        nan_count = 0  # 连续NaN计数器
        
        # 记录开始训练的分隔线
        log_message(energy_log, "=" * 102)
        
        for i in range(n_iter):
            # 使用余弦退火更新学习率
            prev_lr = self.current_lr  # 使用上一步的学习率
            current_lr = self._cosine_annealing_with_restart(i)
            
            # 更新学习率
            if self.use_lr_schedule:
                self._update_learning_rate(current_lr)
            
            # 检测重启（学习率突然增大）
            if current_lr > prev_lr * 1.5 and i > 0:
                log_message(energy_log, f"🔄 RESTART #{self.current_restart} | Period: {self.current_period}")
            
            # 执行一步优化
            self.advance(1)
            
            # 获取能量统计信息
            energy_stats = self.estimate(self.hamiltonian)
            energy_mean = energy_stats.mean
            energy_var = energy_stats.variance
            energy_error = energy_stats.error_of_mean
            
            # 检查是否为NaN
            if jnp.isnan(energy_mean):
                nan_count += 1
                log_message(energy_log, f"⚠️  NaN Energy at iter {i+1}/{n_iter} ({nan_count}/{self.max_nan_count})")
                
                # 如果连续NaN次数达到阈值，停止训练
                if nan_count >= self.max_nan_count:
                    log_message(energy_log, f"❌ Stopping: {self.max_nan_count} consecutive NaN values")
                    break
            else:
                # Energy正常，重置NaN计数器
                nan_count = 0
                
                # 构建日志信息
                restart_info = f"R{self.current_restart}[{self.iter_since_restart-1}/{self.current_period}]"
                
                # 获取当前学习率
                lr_str = f"LR: {self.current_lr:.6f}" if self.current_lr is not None else "LR: N/A"
                
                log_message(
                    energy_log,
                    f"[Iter {i+1:4d}/{n_iter}] {restart_info:13s} | {lr_str} | "
                    f"E: {jnp.real(energy_mean):11.6f} | E_var: {energy_var:10.4f} | "
                    f"E_err: {energy_error:10.6f}"
                )
                
                # 保存checkpoint
                if (self.checkpoint_callback is not None and
                    self.checkpoint_interval > 0 and
                    (i + 1) % self.checkpoint_interval == 0):
                    self.checkpoint_callback(i + 1, energy_mean, energy_error, energy_var)
        
        # 训练结束总结
        log_message(energy_log, "=" * 102)
        if nan_count >= self.max_nan_count:
            log_message(energy_log, "❌ TRAINING TERMINATED: Persistent NaN values")
        else:
            log_message(energy_log, f"✅ Training completed successfully")
            log_message(energy_log, f"Total restarts: {self.current_restart}")
            
            # 输出最终结果
            final_energy = self.estimate(self.hamiltonian)
            log_message(energy_log, f"Final Energy: {jnp.real(final_energy.mean):.8f} ± {final_energy.error_of_mean:.8f}")
            log_message(energy_log, f"Final Variance: {final_energy.variance:.6f}")
        log_message(energy_log, "=" * 102)
        
        return self


# ============================================================================
# 备选方案：自由能优化（如果需要有限温度）
# ============================================================================

def T_logp2(params, inputs, temperature, model_instance):
    """计算熵梯度的第一项"""
    variables = {"params": params}
    preds = model_instance.apply(variables, inputs)
    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)

def T_logp_2(params, inputs, temperature, model_instance):
    """计算熵梯度的第二项"""
    variables = {"params": params}
    preds = model_instance.apply(variables, inputs)
    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2


class FreeEnergyVMC_SR(VMC_SR):
    """
    自由能优化驱动器（备选方案）
    
    优化目标：min F = E - T*S
    
    注意：由于优化的是自由能而非能量，能量方差会较大，这是正常的。
    适用于需要研究有限温度性质的场景。
    """
    
    def __init__(self, temperature, clip_norm=1.0, *args, **kwargs):
        hamiltonian = kwargs.pop('hamiltonian', None)
        if hamiltonian is None and args:
            hamiltonian = args[0]
            args = args[1:]
        
        super().__init__(hamiltonian, *args, **kwargs)
        
        self.init_temperature = temperature
        self.temperature = temperature
        self.clip_norm = clip_norm
        self.hamiltonian = hamiltonian
    
    def _step_with_state(self, state):
        new_state = super()._step_with_state(state)
        params = new_state.parameters
        inputs = new_state.samples
        
        # 计算熵梯度
        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)
        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)
        mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)
        
        # 自由能梯度 = 能量梯度 - 温度*熵梯度
        total_grad = tree_util.tree_map(lambda g_e, g_s: g_e - g_s,
                                          new_state.gradient, mT_grad_S)
        
        # 梯度裁剪
        total_grad = clip_gradients(total_grad, self.clip_norm)
        
        new_params = self.optimizer.update(total_grad, params)
        new_state = new_state.replace(parameters=new_params)
        return new_state
    
    def _compute_entropy(self, params, samples):
        """计算熵 S = -<log|ψ|²>"""
        variables = {"params": params}
        log_psi = self.variational_state.model.apply(variables, samples)
        entropy = -2.0 * jnp.mean(jnp.real(log_psi))
        return entropy


class CustomFreeEnergyVMC_SR(FreeEnergyVMC_SR):
    """
    自定义自由能优化驱动器（备选方案）
    
    功能：
    1. 温度余弦退火 + 热重启
    2. NaN检测和早停
    3. Checkpoint保存
    4. 详细日志输出（包含自由能F、能量E、熵S）
    """
    
    def __init__(self, initial_period=100, period_mult=2.0,
                 max_temperature=1.0, min_temperature=0.01,
                 checkpoint_callback=None, checkpoint_interval=500, 
                 *args, **kwargs):
        """
        Args:
            initial_period: 初始重启周期
            period_mult: 周期倍增因子
            max_temperature: 最大温度
            min_temperature: 最小温度
            checkpoint_callback: checkpoint回调函数
            checkpoint_interval: checkpoint保存间隔
        """
        # 设置初始温度为最大温度
        kwargs['temperature'] = max_temperature
        super().__init__(*args, **kwargs)
        self.max_nan_count = 5  # 最大允许连续NaN次数
        
        # 温度调度参数
        self.initial_period = initial_period
        self.period_mult = period_mult
        self.max_temperature = max_temperature
        self.min_temperature = min_temperature
        
        # 记录重启信息
        self.current_restart = 0
        self.current_period = initial_period
        self.iter_since_restart = 0
        
        # Checkpoint相关
        self.checkpoint_callback = checkpoint_callback
        self.checkpoint_interval = checkpoint_interval
    
    def _cosine_annealing_with_restart(self, iteration):
        """
        带热重启的余弦退火温度调度
        
        T(i) = T_min + (T_max - T_min) * (1 + cos(π * i_cur / T_cur)) / 2
        
        Args:
            iteration: 当前迭代次数
        
        Returns:
            当前温度
        """
        # 检查是否需要重启
        if self.iter_since_restart >= self.current_period:
            self.current_restart += 1
            self.iter_since_restart = 0
            # 更新下一个重启周期长度
            self.current_period = int(self.initial_period * (self.period_mult ** self.current_restart))
        
        cosine_factor = (1 + math.cos(math.pi * self.iter_since_restart / self.current_period)) / 2
        temperature = self.min_temperature + (self.max_temperature - self.min_temperature) * cosine_factor
        
        self.iter_since_restart += 1
        return temperature
    
    def run(self, n_iter, energy_log):
        """
        运行自由能优化
        
        输出信息：
        - F: 自由能 F = E - T·S（优化目标）
        - E: 能量期望值
        - S: 熵
        - T: 温度
        - E_var: 能量方差（会较大，这是正常的）
        
        Args:
            n_iter: 迭代次数
            energy_log: 日志文件路径
        """
        nan_count = 0  # 连续NaN计数器
        
        # 记录开始信息
        log_message(energy_log, "=" * 102)
        log_message(energy_log, "开始训练 - 自由能优化方案")
        log_message(energy_log, f"优化目标: 最小化自由能 F = E - T·S")
        log_message(energy_log, f"总迭代数: {n_iter}")
        log_message(energy_log, f"温度范围: {self.min_temperature} - {self.max_temperature}")
        log_message(energy_log, f"梯度裁剪阈值: {self.clip_norm}")
        log_message(energy_log, f"注意: E_var 会较大是正常的（因为优化的是F而非E）")
        log_message(energy_log, "=" * 100)
        
        for i in range(n_iter):
            # 使用余弦退火更新温度
            prev_temperature = self.temperature
            self.temperature = self._cosine_annealing_with_restart(i)
            
            # 检测重启（温度突然增大）
            if self.temperature > prev_temperature * 1.5 and i > 0:
                log_message(energy_log, f"🔄 RESTART #{self.current_restart} | Period: {self.current_period}")
            
            # 执行一步优化
            self.advance(1)
            
            # 获取能量统计信息
            energy_stats = self.estimate(self.hamiltonian)
            energy_mean = energy_stats.mean
            energy_var = energy_stats.variance
            energy_error = energy_stats.error_of_mean
            
            # 计算熵和自由能
            params = self.state.parameters
            samples = self.state.samples
            entropy = self._compute_entropy(params, samples)
            free_energy = energy_mean - self.temperature * entropy
            
            # 检查是否为NaN
            if jnp.isnan(free_energy) or jnp.isnan(energy_mean):
                nan_count += 1
                log_message(energy_log, f"⚠️  NaN detected at iter {i+1}/{n_iter} ({nan_count}/{self.max_nan_count})")
                
                # 如果连续NaN次数达到阈值，停止训练
                if nan_count >= self.max_nan_count:
                    log_message(energy_log, f"❌ Stopping: {self.max_nan_count} consecutive NaN values")
                    break
            else:
                # 正常，重置NaN计数器
                nan_count = 0
                
                # 构建日志信息
                restart_info = f"R{self.current_restart}[{self.iter_since_restart-1}/{self.current_period}]"
                
                log_message(
                    energy_log,
                    f"[Iter {i+1:4d}/{n_iter}] {restart_info:15s} | T: {self.temperature:.4f} | "
                    f"F: {jnp.real(free_energy):10.6f} | E: {jnp.real(energy_mean):10.6f} | "
                    f"S: {jnp.real(entropy):8.4f} | E_var: {energy_var:8.4f}"
                )
                
                # 保存checkpoint
                if (self.checkpoint_callback is not None and
                    self.checkpoint_interval > 0 and
                    (i + 1) % self.checkpoint_interval == 0):
                    self.checkpoint_callback(i + 1, free_energy, energy_mean, entropy)
        
        # 训练结束总结
        log_message(energy_log, "=" * 100)
        if nan_count >= self.max_nan_count:
            log_message(energy_log, "❌ TRAINING TERMINATED: Persistent NaN values")
        else:
            log_message(energy_log, f"✅ Training completed successfully")
            log_message(energy_log, f"Total restarts: {self.current_restart}")
            
            # 输出最终结果
            final_energy = self.estimate(self.hamiltonian)
            final_params = self.state.parameters
            final_samples = self.state.samples
            final_entropy = self._compute_entropy(final_params, final_samples)
            final_free_energy = final_energy.mean - self.temperature * final_entropy
            
            log_message(energy_log, f"Final Free Energy: {jnp.real(final_free_energy):.8f}")
            log_message(energy_log, f"Final Energy: {jnp.real(final_energy.mean):.8f} ± {final_energy.error_of_mean:.8f}")
            log_message(energy_log, f"Final Entropy: {jnp.real(final_entropy):.8f}")
            log_message(energy_log, f"Final Variance: {final_energy.variance:.6f}")
        log_message(energy_log, "=" * 100)
        
        return self
