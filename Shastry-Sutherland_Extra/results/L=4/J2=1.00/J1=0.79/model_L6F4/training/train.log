[2025-10-06 20:27:55] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.78/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 20:27:55]   - 迭代次数: final
[2025-10-06 20:27:55]   - 能量: -27.952841-0.001402j ± 0.004881, Var: 0.097573
[2025-10-06 20:27:55]   - 时间戳: 2025-10-06T20:27:44.542033+08:00
[2025-10-06 20:28:13] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 20:28:13] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 20:28:13] ======================================================================================================
[2025-10-06 20:28:13] GCNN for Shastry-Sutherland Model
[2025-10-06 20:28:13] ======================================================================================================
[2025-10-06 20:28:13] System parameters:
[2025-10-06 20:28:13]   - System size: L=4, N=64
[2025-10-06 20:28:13]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-10-06 20:28:13] ------------------------------------------------------------------------------------------------------
[2025-10-06 20:28:13] Model parameters:
[2025-10-06 20:28:13]   - Number of layers = 6
[2025-10-06 20:28:13]   - Number of features = 4
[2025-10-06 20:28:13]   - Total parameters = 20780
[2025-10-06 20:28:13] ------------------------------------------------------------------------------------------------------
[2025-10-06 20:28:13] Training parameters:
[2025-10-06 20:28:13]   - Total iterations: 1050
[2025-10-06 20:28:13]   - Annealing cycles: 3
[2025-10-06 20:28:13]   - Initial period: 150
[2025-10-06 20:28:13]   - Period multiplier: 2.0
[2025-10-06 20:28:13]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 20:28:13]   - Samples: 4096
[2025-10-06 20:28:13]   - Discarded samples: 0
[2025-10-06 20:28:13]   - Chunk size: 4096
[2025-10-06 20:28:13]   - Diagonal shift: 0.15
[2025-10-06 20:28:13]   - Gradient clipping: 1.0
[2025-10-06 20:28:13]   - Checkpoint enabled: interval=100
[2025-10-06 20:28:13]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.79/model_L6F4/training/checkpoints
[2025-10-06 20:28:13] ------------------------------------------------------------------------------------------------------
[2025-10-06 20:28:13] Device status:
[2025-10-06 20:28:13]   - Devices model: NVIDIA H200 NVL
[2025-10-06 20:28:13]   - Number of devices: 1
[2025-10-06 20:28:13]   - Sharding: True
[2025-10-06 20:28:14] ======================================================================================================
[2025-10-06 20:28:57] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -28.369378 | E_var:     0.2458 | E_err:   0.007747
[2025-10-06 20:29:23] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -28.362464 | E_var:     0.1786 | E_err:   0.006604
[2025-10-06 20:29:27] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -28.360424 | E_var:     0.1280 | E_err:   0.005590
[2025-10-06 20:29:30] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -28.360975 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 20:29:34] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -28.358959 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 20:29:38] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -28.363019 | E_var:     0.1082 | E_err:   0.005141
[2025-10-06 20:29:41] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -28.367468 | E_var:     0.1132 | E_err:   0.005257
[2025-10-06 20:29:45] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -28.360153 | E_var:     0.0855 | E_err:   0.004570
[2025-10-06 20:29:49] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -28.361980 | E_var:     0.1084 | E_err:   0.005144
[2025-10-06 20:29:52] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -28.356848 | E_var:     0.1401 | E_err:   0.005848
[2025-10-06 20:29:56] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -28.358300 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 20:30:00] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -28.369212 | E_var:     0.1550 | E_err:   0.006152
[2025-10-06 20:30:03] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -28.366255 | E_var:     0.0802 | E_err:   0.004425
[2025-10-06 20:30:07] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -28.357769 | E_var:     0.1165 | E_err:   0.005334
[2025-10-06 20:30:11] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -28.358728 | E_var:     0.0976 | E_err:   0.004882
[2025-10-06 20:30:14] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -28.359266 | E_var:     0.0955 | E_err:   0.004828
[2025-10-06 20:30:18] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -28.366386 | E_var:     0.0994 | E_err:   0.004927
[2025-10-06 20:30:22] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -28.365332 | E_var:     0.1330 | E_err:   0.005698
[2025-10-06 20:30:25] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -28.365050 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 20:30:29] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -28.369063 | E_var:     0.1201 | E_err:   0.005415
[2025-10-06 20:30:33] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -28.355183 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 20:30:36] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -28.371852 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 20:30:40] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -28.366694 | E_var:     0.1075 | E_err:   0.005124
[2025-10-06 20:30:44] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -28.359646 | E_var:     0.0891 | E_err:   0.004663
[2025-10-06 20:30:47] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -28.362421 | E_var:     0.1054 | E_err:   0.005074
[2025-10-06 20:30:51] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -28.367481 | E_var:     0.0844 | E_err:   0.004539
[2025-10-06 20:30:55] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -28.368578 | E_var:     0.0980 | E_err:   0.004893
[2025-10-06 20:30:58] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -28.371276 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 20:31:02] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -28.353819 | E_var:     0.2548 | E_err:   0.007888
[2025-10-06 20:31:06] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -28.366101 | E_var:     0.0894 | E_err:   0.004671
[2025-10-06 20:31:09] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -28.366939 | E_var:     0.1326 | E_err:   0.005690
[2025-10-06 20:31:13] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -28.370824 | E_var:     0.1048 | E_err:   0.005058
[2025-10-06 20:31:17] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -28.369935 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 20:31:20] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -28.366574 | E_var:     0.0977 | E_err:   0.004884
[2025-10-06 20:31:24] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -28.363479 | E_var:     0.0973 | E_err:   0.004874
[2025-10-06 20:31:28] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -28.361543 | E_var:     0.1177 | E_err:   0.005360
[2025-10-06 20:31:31] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -28.371719 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 20:31:35] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -28.366869 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 20:31:39] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -28.368283 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 20:31:42] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -28.364088 | E_var:     0.0951 | E_err:   0.004818
[2025-10-06 20:31:46] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -28.360692 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 20:31:50] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -28.373316 | E_var:     0.3246 | E_err:   0.008902
[2025-10-06 20:31:53] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -28.364184 | E_var:     0.0851 | E_err:   0.004557
[2025-10-06 20:31:57] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -28.364293 | E_var:     0.1138 | E_err:   0.005271
[2025-10-06 20:32:01] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -28.361203 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 20:32:04] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -28.368644 | E_var:     0.0969 | E_err:   0.004865
[2025-10-06 20:32:08] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -28.365280 | E_var:     0.1118 | E_err:   0.005225
[2025-10-06 20:32:12] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -28.366711 | E_var:     0.0894 | E_err:   0.004672
[2025-10-06 20:32:15] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -28.366577 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 20:32:19] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -28.369122 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 20:32:23] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -28.353474 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 20:32:26] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -28.365100 | E_var:     0.0886 | E_err:   0.004652
[2025-10-06 20:32:30] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -28.367998 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 20:32:34] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -28.372351 | E_var:     0.2079 | E_err:   0.007125
[2025-10-06 20:32:37] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -28.373048 | E_var:     0.1265 | E_err:   0.005557
[2025-10-06 20:32:41] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -28.363137 | E_var:     0.1151 | E_err:   0.005301
[2025-10-06 20:32:45] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -28.363488 | E_var:     0.1187 | E_err:   0.005384
[2025-10-06 20:32:48] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -28.373704 | E_var:     0.0952 | E_err:   0.004822
[2025-10-06 20:32:52] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -28.359006 | E_var:     0.1396 | E_err:   0.005838
[2025-10-06 20:32:56] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -28.370802 | E_var:     0.1070 | E_err:   0.005112
[2025-10-06 20:32:59] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -28.366299 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 20:33:03] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -28.365819 | E_var:     0.0732 | E_err:   0.004228
[2025-10-06 20:33:07] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -28.364775 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 20:33:10] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -28.354724 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 20:33:14] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -28.361575 | E_var:     0.1373 | E_err:   0.005790
[2025-10-06 20:33:18] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -28.365343 | E_var:     0.0862 | E_err:   0.004586
[2025-10-06 20:33:21] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -28.357172 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 20:33:25] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -28.361486 | E_var:     0.1626 | E_err:   0.006300
[2025-10-06 20:33:29] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -28.362351 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 20:33:32] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -28.357497 | E_var:     0.1580 | E_err:   0.006211
[2025-10-06 20:33:36] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -28.361990 | E_var:     0.1238 | E_err:   0.005498
[2025-10-06 20:33:40] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -28.368531 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 20:33:43] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -28.365994 | E_var:     0.0950 | E_err:   0.004815
[2025-10-06 20:33:47] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -28.358618 | E_var:     0.1036 | E_err:   0.005030
[2025-10-06 20:33:50] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -28.368178 | E_var:     0.1017 | E_err:   0.004982
[2025-10-06 20:33:54] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -28.364031 | E_var:     0.0996 | E_err:   0.004930
[2025-10-06 20:33:58] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -28.371114 | E_var:     0.0993 | E_err:   0.004923
[2025-10-06 20:34:01] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -28.362578 | E_var:     0.0931 | E_err:   0.004768
[2025-10-06 20:34:05] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -28.362253 | E_var:     0.1074 | E_err:   0.005120
[2025-10-06 20:34:09] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -28.364939 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 20:34:12] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -28.361209 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 20:34:16] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -28.359914 | E_var:     0.1019 | E_err:   0.004988
[2025-10-06 20:34:20] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -28.365897 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 20:34:23] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -28.361020 | E_var:     0.1281 | E_err:   0.005592
[2025-10-06 20:34:27] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -28.369153 | E_var:     0.0820 | E_err:   0.004473
[2025-10-06 20:34:31] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -28.359821 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 20:34:34] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -28.360067 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 20:34:38] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -28.359647 | E_var:     0.1502 | E_err:   0.006055
[2025-10-06 20:34:42] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -28.361785 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 20:34:45] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -28.363068 | E_var:     0.1315 | E_err:   0.005666
[2025-10-06 20:34:49] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -28.372069 | E_var:     0.1530 | E_err:   0.006111
[2025-10-06 20:34:53] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -28.365342 | E_var:     0.1030 | E_err:   0.005015
[2025-10-06 20:34:56] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -28.371728 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 20:35:00] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -28.363616 | E_var:     0.0955 | E_err:   0.004829
[2025-10-06 20:35:04] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -28.351494 | E_var:     0.0923 | E_err:   0.004747
[2025-10-06 20:35:07] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -28.364388 | E_var:     0.1095 | E_err:   0.005170
[2025-10-06 20:35:11] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -28.376429 | E_var:     0.1131 | E_err:   0.005254
[2025-10-06 20:35:15] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -28.370415 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 20:35:18] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -28.362894 | E_var:     0.0909 | E_err:   0.004711
[2025-10-06 20:35:22] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -28.360824 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 20:35:22] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 20:35:26] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -28.361834 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 20:35:30] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -28.359733 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 20:35:33] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -28.361131 | E_var:     0.1110 | E_err:   0.005206
[2025-10-06 20:35:37] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -28.363510 | E_var:     0.1385 | E_err:   0.005814
[2025-10-06 20:35:40] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -28.372044 | E_var:     0.0845 | E_err:   0.004543
[2025-10-06 20:35:44] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -28.368929 | E_var:     0.1074 | E_err:   0.005120
[2025-10-06 20:35:48] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -28.368795 | E_var:     0.1004 | E_err:   0.004951
[2025-10-06 20:35:51] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -28.370166 | E_var:     0.0937 | E_err:   0.004784
[2025-10-06 20:35:55] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -28.364364 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 20:35:59] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -28.362329 | E_var:     0.0914 | E_err:   0.004723
[2025-10-06 20:36:02] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -28.354915 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 20:36:06] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -28.364635 | E_var:     0.1013 | E_err:   0.004973
[2025-10-06 20:36:10] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -28.360501 | E_var:     0.0804 | E_err:   0.004432
[2025-10-06 20:36:13] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -28.366943 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 20:36:17] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -28.362328 | E_var:     0.1453 | E_err:   0.005955
[2025-10-06 20:36:21] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -28.363548 | E_var:     0.0919 | E_err:   0.004736
[2025-10-06 20:36:24] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -28.371357 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 20:36:28] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -28.364879 | E_var:     0.1638 | E_err:   0.006323
[2025-10-06 20:36:32] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -28.368466 | E_var:     0.0798 | E_err:   0.004413
[2025-10-06 20:36:35] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -28.358506 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 20:36:39] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -28.369558 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 20:36:43] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -28.349882 | E_var:     0.1363 | E_err:   0.005768
[2025-10-06 20:36:46] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -28.376956 | E_var:     0.2083 | E_err:   0.007132
[2025-10-06 20:36:50] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -28.349773 | E_var:     0.0887 | E_err:   0.004652
[2025-10-06 20:36:54] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -28.360580 | E_var:     0.1091 | E_err:   0.005162
[2025-10-06 20:36:57] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -28.367165 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 20:37:01] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -28.370154 | E_var:     0.1312 | E_err:   0.005659
[2025-10-06 20:37:05] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -28.362896 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 20:37:08] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -28.370279 | E_var:     0.0833 | E_err:   0.004508
[2025-10-06 20:37:12] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -28.362597 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 20:37:16] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -28.360246 | E_var:     0.1177 | E_err:   0.005360
[2025-10-06 20:37:19] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -28.365949 | E_var:     0.4445 | E_err:   0.010418
[2025-10-06 20:37:23] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -28.369876 | E_var:     0.1214 | E_err:   0.005445
[2025-10-06 20:37:27] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -28.372468 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 20:37:30] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -28.364943 | E_var:     0.0965 | E_err:   0.004855
[2025-10-06 20:37:34] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -28.354939 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 20:37:38] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -28.363312 | E_var:     0.0989 | E_err:   0.004914
[2025-10-06 20:37:41] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -28.367313 | E_var:     0.0932 | E_err:   0.004769
[2025-10-06 20:37:45] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -28.361212 | E_var:     0.1486 | E_err:   0.006024
[2025-10-06 20:37:49] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -28.361632 | E_var:     0.2186 | E_err:   0.007306
[2025-10-06 20:37:52] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -28.368167 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 20:37:56] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -28.358259 | E_var:     0.1186 | E_err:   0.005381
[2025-10-06 20:38:00] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -28.360719 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 20:38:03] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -28.358567 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 20:38:07] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -28.367122 | E_var:     0.0926 | E_err:   0.004756
[2025-10-06 20:38:11] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -28.368437 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 20:38:14] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -28.368660 | E_var:     0.1055 | E_err:   0.005075
[2025-10-06 20:38:18] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -28.360706 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 20:38:22] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -28.357902 | E_var:     0.1957 | E_err:   0.006913
[2025-10-06 20:38:25] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -28.367770 | E_var:     0.1069 | E_err:   0.005110
[2025-10-06 20:38:25] 🔄 RESTART #1 | Period: 300
[2025-10-06 20:38:29] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -28.354974 | E_var:     0.1190 | E_err:   0.005390
[2025-10-06 20:38:33] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -28.372648 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 20:38:36] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -28.363909 | E_var:     0.1455 | E_err:   0.005961
[2025-10-06 20:38:40] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -28.367333 | E_var:     0.1655 | E_err:   0.006357
[2025-10-06 20:38:44] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -28.369911 | E_var:     0.0812 | E_err:   0.004451
[2025-10-06 20:38:47] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -28.362838 | E_var:     0.1024 | E_err:   0.005000
[2025-10-06 20:38:51] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -28.357104 | E_var:     0.1005 | E_err:   0.004953
[2025-10-06 20:38:55] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -28.367617 | E_var:     0.0993 | E_err:   0.004923
[2025-10-06 20:38:58] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -28.363145 | E_var:     0.1060 | E_err:   0.005088
[2025-10-06 20:39:02] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -28.363665 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 20:39:06] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -28.352755 | E_var:     0.0748 | E_err:   0.004272
[2025-10-06 20:39:09] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -28.366101 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 20:39:13] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -28.371030 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 20:39:17] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -28.359444 | E_var:     0.0719 | E_err:   0.004191
[2025-10-06 20:39:20] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -28.365127 | E_var:     0.0988 | E_err:   0.004911
[2025-10-06 20:39:24] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -28.358139 | E_var:     0.1103 | E_err:   0.005189
[2025-10-06 20:39:28] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -28.368052 | E_var:     0.1258 | E_err:   0.005543
[2025-10-06 20:39:31] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -28.364459 | E_var:     0.0874 | E_err:   0.004620
[2025-10-06 20:39:35] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -28.359705 | E_var:     0.0917 | E_err:   0.004730
[2025-10-06 20:39:39] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -28.374179 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 20:39:42] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -28.370995 | E_var:     0.0981 | E_err:   0.004894
[2025-10-06 20:39:46] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -28.365881 | E_var:     0.0927 | E_err:   0.004757
[2025-10-06 20:39:50] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -28.369413 | E_var:     0.1032 | E_err:   0.005018
[2025-10-06 20:39:53] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -28.361365 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 20:39:57] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -28.361641 | E_var:     0.1097 | E_err:   0.005174
[2025-10-06 20:40:01] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -28.373206 | E_var:     0.0951 | E_err:   0.004818
[2025-10-06 20:40:04] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -28.368001 | E_var:     0.1103 | E_err:   0.005189
[2025-10-06 20:40:08] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -28.367613 | E_var:     0.0800 | E_err:   0.004421
[2025-10-06 20:40:12] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -28.361538 | E_var:     0.0831 | E_err:   0.004506
[2025-10-06 20:40:15] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -28.365039 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 20:40:19] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -28.361427 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 20:40:23] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -28.353293 | E_var:     0.1854 | E_err:   0.006727
[2025-10-06 20:40:26] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -28.360132 | E_var:     0.1924 | E_err:   0.006854
[2025-10-06 20:40:30] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -28.357222 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 20:40:34] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -28.361466 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 20:40:37] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -28.368482 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 20:40:41] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -28.374195 | E_var:     0.1237 | E_err:   0.005495
[2025-10-06 20:40:45] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -28.366193 | E_var:     0.0959 | E_err:   0.004840
[2025-10-06 20:40:48] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -28.368043 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 20:40:52] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -28.364543 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 20:40:56] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -28.377025 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 20:40:59] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -28.365070 | E_var:     0.2964 | E_err:   0.008506
[2025-10-06 20:41:03] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -28.356598 | E_var:     0.1122 | E_err:   0.005233
[2025-10-06 20:41:07] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -28.369169 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 20:41:10] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -28.368453 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 20:41:14] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -28.360817 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 20:41:18] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -28.370012 | E_var:     0.0849 | E_err:   0.004552
[2025-10-06 20:41:21] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -28.363917 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 20:41:25] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -28.359938 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 20:41:29] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -28.374720 | E_var:     0.1052 | E_err:   0.005069
[2025-10-06 20:41:29] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 20:41:32] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -28.365966 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 20:41:36] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -28.371028 | E_var:     0.1160 | E_err:   0.005321
[2025-10-06 20:41:40] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -28.363668 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 20:41:43] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -28.365303 | E_var:     0.0785 | E_err:   0.004379
[2025-10-06 20:41:47] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -28.377301 | E_var:     0.1659 | E_err:   0.006365
[2025-10-06 20:41:51] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -28.363026 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 20:41:54] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -28.365947 | E_var:     0.0790 | E_err:   0.004391
[2025-10-06 20:41:58] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -28.370784 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 20:42:02] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -28.374302 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 20:42:05] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -28.361503 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 20:42:09] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -28.364171 | E_var:     0.0767 | E_err:   0.004328
[2025-10-06 20:42:13] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -28.364727 | E_var:     0.0907 | E_err:   0.004707
[2025-10-06 20:42:16] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -28.363423 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 20:42:20] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -28.371842 | E_var:     0.0933 | E_err:   0.004773
[2025-10-06 20:42:24] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -28.363042 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 20:42:27] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -28.364844 | E_var:     0.0823 | E_err:   0.004484
[2025-10-06 20:42:31] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -28.357449 | E_var:     0.0979 | E_err:   0.004889
[2025-10-06 20:42:35] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -28.365926 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 20:42:38] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -28.356739 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 20:42:42] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -28.361679 | E_var:     0.0982 | E_err:   0.004897
[2025-10-06 20:42:46] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -28.367736 | E_var:     0.0841 | E_err:   0.004530
[2025-10-06 20:42:49] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -28.367532 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 20:42:53] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -28.359152 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 20:42:57] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -28.360640 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 20:43:00] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -28.369165 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 20:43:04] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -28.358543 | E_var:     0.0916 | E_err:   0.004730
[2025-10-06 20:43:08] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -28.362612 | E_var:     0.0958 | E_err:   0.004835
[2025-10-06 20:43:11] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -28.369419 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 20:43:15] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -28.367346 | E_var:     0.0744 | E_err:   0.004263
[2025-10-06 20:43:19] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -28.370133 | E_var:     0.1950 | E_err:   0.006900
[2025-10-06 20:43:22] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -28.361657 | E_var:     0.0677 | E_err:   0.004067
[2025-10-06 20:43:26] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -28.365727 | E_var:     0.1064 | E_err:   0.005097
[2025-10-06 20:43:30] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -28.359279 | E_var:     0.0756 | E_err:   0.004297
[2025-10-06 20:43:33] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -28.359424 | E_var:     0.1771 | E_err:   0.006576
[2025-10-06 20:43:37] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -28.366993 | E_var:     0.2223 | E_err:   0.007367
[2025-10-06 20:43:41] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -28.360779 | E_var:     0.0813 | E_err:   0.004454
[2025-10-06 20:43:44] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -28.369269 | E_var:     0.1326 | E_err:   0.005691
[2025-10-06 20:43:48] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -28.367437 | E_var:     0.0759 | E_err:   0.004304
[2025-10-06 20:43:52] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -28.368810 | E_var:     0.0978 | E_err:   0.004887
[2025-10-06 20:43:55] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -28.365797 | E_var:     0.0684 | E_err:   0.004085
[2025-10-06 20:43:59] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -28.371839 | E_var:     0.1088 | E_err:   0.005154
[2025-10-06 20:44:03] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -28.365606 | E_var:     0.1020 | E_err:   0.004991
[2025-10-06 20:44:06] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -28.354346 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 20:44:10] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -28.370046 | E_var:     0.1057 | E_err:   0.005081
[2025-10-06 20:44:13] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -28.358195 | E_var:     0.1041 | E_err:   0.005041
[2025-10-06 20:44:17] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -28.368625 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 20:44:21] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -28.366775 | E_var:     0.0690 | E_err:   0.004104
[2025-10-06 20:44:24] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -28.363423 | E_var:     0.0963 | E_err:   0.004849
[2025-10-06 20:44:28] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -28.358356 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 20:44:32] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -28.362245 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 20:44:35] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -28.366057 | E_var:     0.1173 | E_err:   0.005351
[2025-10-06 20:44:39] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -28.362608 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 20:44:43] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -28.372421 | E_var:     0.0955 | E_err:   0.004828
[2025-10-06 20:44:46] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -28.364756 | E_var:     0.1027 | E_err:   0.005007
[2025-10-06 20:44:50] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -28.361012 | E_var:     0.1088 | E_err:   0.005155
[2025-10-06 20:44:54] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -28.363147 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 20:44:57] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -28.364039 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 20:45:01] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -28.369514 | E_var:     0.1034 | E_err:   0.005024
[2025-10-06 20:45:05] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -28.360450 | E_var:     0.1160 | E_err:   0.005321
[2025-10-06 20:45:08] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -28.370030 | E_var:     0.0946 | E_err:   0.004807
[2025-10-06 20:45:12] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -28.353544 | E_var:     0.0898 | E_err:   0.004682
[2025-10-06 20:45:16] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -28.361150 | E_var:     0.0856 | E_err:   0.004573
[2025-10-06 20:45:19] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -28.370256 | E_var:     0.1172 | E_err:   0.005350
[2025-10-06 20:45:23] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -28.369332 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 20:45:27] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -28.371792 | E_var:     0.1178 | E_err:   0.005363
[2025-10-06 20:45:30] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -28.362060 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 20:45:34] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -28.367730 | E_var:     0.1118 | E_err:   0.005225
[2025-10-06 20:45:38] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -28.367350 | E_var:     0.0920 | E_err:   0.004739
[2025-10-06 20:45:41] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -28.373795 | E_var:     0.1121 | E_err:   0.005230
[2025-10-06 20:45:45] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -28.359485 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 20:45:49] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -28.364007 | E_var:     0.0927 | E_err:   0.004759
[2025-10-06 20:45:52] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -28.360864 | E_var:     0.1232 | E_err:   0.005485
[2025-10-06 20:45:56] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -28.373098 | E_var:     0.1650 | E_err:   0.006346
[2025-10-06 20:46:00] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -28.369212 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 20:46:03] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -28.364389 | E_var:     0.1064 | E_err:   0.005098
[2025-10-06 20:46:07] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -28.351882 | E_var:     0.1486 | E_err:   0.006023
[2025-10-06 20:46:11] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -28.358780 | E_var:     0.1201 | E_err:   0.005416
[2025-10-06 20:46:14] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -28.367553 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 20:46:18] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -28.369067 | E_var:     0.1857 | E_err:   0.006733
[2025-10-06 20:46:22] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -28.357241 | E_var:     0.1705 | E_err:   0.006452
[2025-10-06 20:46:25] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -28.362180 | E_var:     0.1116 | E_err:   0.005219
[2025-10-06 20:46:29] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -28.366733 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 20:46:33] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -28.364396 | E_var:     0.0976 | E_err:   0.004882
[2025-10-06 20:46:36] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -28.361377 | E_var:     0.0918 | E_err:   0.004735
[2025-10-06 20:46:40] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -28.366421 | E_var:     0.0772 | E_err:   0.004340
[2025-10-06 20:46:44] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -28.372208 | E_var:     0.1157 | E_err:   0.005315
[2025-10-06 20:46:47] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -28.365656 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 20:46:51] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -28.368283 | E_var:     0.0886 | E_err:   0.004650
[2025-10-06 20:46:55] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -28.357753 | E_var:     0.0832 | E_err:   0.004506
[2025-10-06 20:46:58] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -28.361733 | E_var:     0.1087 | E_err:   0.005150
[2025-10-06 20:47:02] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -28.374396 | E_var:     0.2001 | E_err:   0.006990
[2025-10-06 20:47:06] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -28.363086 | E_var:     0.1021 | E_err:   0.004992
[2025-10-06 20:47:09] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -28.360087 | E_var:     0.0972 | E_err:   0.004873
[2025-10-06 20:47:13] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -28.364142 | E_var:     0.1260 | E_err:   0.005547
[2025-10-06 20:47:17] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -28.361453 | E_var:     0.1094 | E_err:   0.005168
[2025-10-06 20:47:20] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -28.373039 | E_var:     0.0911 | E_err:   0.004715
[2025-10-06 20:47:24] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -28.375180 | E_var:     0.0917 | E_err:   0.004732
[2025-10-06 20:47:28] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -28.364568 | E_var:     0.0990 | E_err:   0.004916
[2025-10-06 20:47:31] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -28.369460 | E_var:     0.0900 | E_err:   0.004688
[2025-10-06 20:47:35] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -28.367917 | E_var:     0.0921 | E_err:   0.004742
[2025-10-06 20:47:35] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 20:47:39] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -28.367636 | E_var:     0.1088 | E_err:   0.005154
[2025-10-06 20:47:42] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -28.361557 | E_var:     0.1094 | E_err:   0.005167
[2025-10-06 20:47:46] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -28.359619 | E_var:     0.1200 | E_err:   0.005412
[2025-10-06 20:47:50] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -28.374431 | E_var:     0.0897 | E_err:   0.004681
[2025-10-06 20:47:53] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -28.355373 | E_var:     0.3129 | E_err:   0.008740
[2025-10-06 20:47:57] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -28.367037 | E_var:     0.0836 | E_err:   0.004517
[2025-10-06 20:48:01] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -28.364747 | E_var:     0.0867 | E_err:   0.004601
[2025-10-06 20:48:04] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -28.363975 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 20:48:08] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -28.365543 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 20:48:12] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -28.359469 | E_var:     0.0833 | E_err:   0.004511
[2025-10-06 20:48:15] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -28.368328 | E_var:     0.0821 | E_err:   0.004477
[2025-10-06 20:48:19] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -28.372741 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 20:48:23] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -28.367690 | E_var:     0.0877 | E_err:   0.004628
[2025-10-06 20:48:26] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -28.361971 | E_var:     0.1190 | E_err:   0.005391
[2025-10-06 20:48:30] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -28.361891 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 20:48:34] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -28.366100 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 20:48:37] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -28.364266 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 20:48:41] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -28.368112 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 20:48:45] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -28.369125 | E_var:     0.1385 | E_err:   0.005814
[2025-10-06 20:48:48] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -28.365752 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 20:48:52] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -28.363841 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 20:48:56] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -28.365455 | E_var:     0.0980 | E_err:   0.004892
[2025-10-06 20:48:59] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -28.370264 | E_var:     0.0792 | E_err:   0.004396
[2025-10-06 20:49:03] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -28.366725 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 20:49:07] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -28.367921 | E_var:     0.1291 | E_err:   0.005615
[2025-10-06 20:49:10] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -28.370182 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 20:49:14] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -28.358358 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 20:49:18] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -28.365977 | E_var:     0.1135 | E_err:   0.005264
[2025-10-06 20:49:21] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -28.375082 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 20:49:25] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -28.365605 | E_var:     0.1290 | E_err:   0.005611
[2025-10-06 20:49:29] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -28.362976 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 20:49:32] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -28.366294 | E_var:     0.1093 | E_err:   0.005165
[2025-10-06 20:49:36] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -28.367345 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 20:49:40] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -28.356472 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 20:49:43] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -28.371377 | E_var:     0.0948 | E_err:   0.004810
[2025-10-06 20:49:47] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -28.364437 | E_var:     0.1345 | E_err:   0.005731
[2025-10-06 20:49:51] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -28.370260 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 20:49:54] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -28.375865 | E_var:     0.1698 | E_err:   0.006439
[2025-10-06 20:49:58] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -28.358458 | E_var:     0.1121 | E_err:   0.005231
[2025-10-06 20:50:02] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -28.374113 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 20:50:05] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -28.368525 | E_var:     0.0745 | E_err:   0.004265
[2025-10-06 20:50:09] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -28.370320 | E_var:     0.0945 | E_err:   0.004803
[2025-10-06 20:50:13] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -28.370128 | E_var:     0.1021 | E_err:   0.004994
[2025-10-06 20:50:16] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -28.366353 | E_var:     0.0891 | E_err:   0.004664
[2025-10-06 20:50:20] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -28.365337 | E_var:     0.1193 | E_err:   0.005396
[2025-10-06 20:50:24] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -28.370794 | E_var:     0.1394 | E_err:   0.005834
[2025-10-06 20:50:27] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -28.365165 | E_var:     0.1178 | E_err:   0.005363
[2025-10-06 20:50:31] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -28.361821 | E_var:     0.1006 | E_err:   0.004956
[2025-10-06 20:50:35] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -28.364549 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 20:50:38] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -28.363141 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 20:50:42] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -28.368368 | E_var:     0.0791 | E_err:   0.004396
[2025-10-06 20:50:46] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -28.362353 | E_var:     0.1144 | E_err:   0.005284
[2025-10-06 20:50:49] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -28.374253 | E_var:     0.1940 | E_err:   0.006881
[2025-10-06 20:50:53] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -28.367750 | E_var:     0.1118 | E_err:   0.005224
[2025-10-06 20:50:57] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -28.361032 | E_var:     0.1192 | E_err:   0.005394
[2025-10-06 20:51:00] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -28.361219 | E_var:     0.0891 | E_err:   0.004664
[2025-10-06 20:51:04] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -28.373279 | E_var:     0.1288 | E_err:   0.005608
[2025-10-06 20:51:08] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -28.364592 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 20:51:11] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -28.356821 | E_var:     0.0974 | E_err:   0.004878
[2025-10-06 20:51:15] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -28.373503 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 20:51:19] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -28.358901 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 20:51:22] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -28.362870 | E_var:     0.1127 | E_err:   0.005246
[2025-10-06 20:51:26] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -28.362768 | E_var:     0.0981 | E_err:   0.004893
[2025-10-06 20:51:30] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -28.370727 | E_var:     0.0987 | E_err:   0.004910
[2025-10-06 20:51:33] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -28.365275 | E_var:     0.1177 | E_err:   0.005361
[2025-10-06 20:51:37] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -28.368512 | E_var:     0.0922 | E_err:   0.004745
[2025-10-06 20:51:41] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -28.365852 | E_var:     0.1043 | E_err:   0.005045
[2025-10-06 20:51:44] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -28.357101 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 20:51:48] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -28.361678 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 20:51:51] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -28.369039 | E_var:     0.0972 | E_err:   0.004870
[2025-10-06 20:51:55] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -28.375596 | E_var:     0.1086 | E_err:   0.005148
[2025-10-06 20:51:59] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -28.371880 | E_var:     0.0801 | E_err:   0.004423
[2025-10-06 20:52:02] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -28.364949 | E_var:     0.1511 | E_err:   0.006073
[2025-10-06 20:52:06] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -28.362839 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 20:52:10] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -28.371935 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 20:52:13] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -28.374607 | E_var:     0.1020 | E_err:   0.004989
[2025-10-06 20:52:17] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -28.368163 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 20:52:21] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -28.361564 | E_var:     0.0891 | E_err:   0.004665
[2025-10-06 20:52:24] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -28.359432 | E_var:     0.0931 | E_err:   0.004769
[2025-10-06 20:52:28] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -28.369730 | E_var:     0.1657 | E_err:   0.006360
[2025-10-06 20:52:32] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -28.373170 | E_var:     0.1336 | E_err:   0.005712
[2025-10-06 20:52:35] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -28.363855 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 20:52:39] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -28.355501 | E_var:     0.1149 | E_err:   0.005296
[2025-10-06 20:52:43] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -28.355175 | E_var:     0.1411 | E_err:   0.005870
[2025-10-06 20:52:46] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -28.365913 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 20:52:50] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -28.367045 | E_var:     0.0798 | E_err:   0.004414
[2025-10-06 20:52:54] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -28.371066 | E_var:     0.0968 | E_err:   0.004861
[2025-10-06 20:52:57] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -28.361478 | E_var:     0.1689 | E_err:   0.006422
[2025-10-06 20:53:01] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -28.358672 | E_var:     0.0967 | E_err:   0.004860
[2025-10-06 20:53:05] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -28.367598 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 20:53:08] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -28.368593 | E_var:     0.2328 | E_err:   0.007538
[2025-10-06 20:53:12] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -28.375956 | E_var:     0.0959 | E_err:   0.004837
[2025-10-06 20:53:16] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -28.359127 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 20:53:19] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -28.355153 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 20:53:23] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -28.362798 | E_var:     0.1110 | E_err:   0.005206
[2025-10-06 20:53:27] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -28.368451 | E_var:     0.1124 | E_err:   0.005239
[2025-10-06 20:53:30] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -28.353099 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 20:53:34] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -28.366582 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 20:53:38] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -28.363985 | E_var:     0.1145 | E_err:   0.005286
[2025-10-06 20:53:41] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -28.367822 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 20:53:42] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 20:53:45] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -28.357955 | E_var:     0.1154 | E_err:   0.005307
[2025-10-06 20:53:49] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -28.362504 | E_var:     0.1427 | E_err:   0.005902
[2025-10-06 20:53:53] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -28.367149 | E_var:     0.1088 | E_err:   0.005155
[2025-10-06 20:53:56] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -28.374402 | E_var:     0.1659 | E_err:   0.006363
[2025-10-06 20:54:00] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -28.357850 | E_var:     0.1210 | E_err:   0.005436
[2025-10-06 20:54:04] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -28.367674 | E_var:     0.0969 | E_err:   0.004863
[2025-10-06 20:54:07] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -28.369216 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 20:54:11] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -28.373354 | E_var:     0.1065 | E_err:   0.005100
[2025-10-06 20:54:15] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -28.365469 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 20:54:18] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -28.360260 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 20:54:22] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -28.360672 | E_var:     0.1128 | E_err:   0.005248
[2025-10-06 20:54:26] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -28.377664 | E_var:     0.1071 | E_err:   0.005112
[2025-10-06 20:54:29] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -28.363098 | E_var:     0.1007 | E_err:   0.004959
[2025-10-06 20:54:33] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -28.368866 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 20:54:37] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -28.363785 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 20:54:40] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -28.365202 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 20:54:44] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -28.366884 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 20:54:47] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -28.359953 | E_var:     0.1193 | E_err:   0.005397
[2025-10-06 20:54:51] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -28.365636 | E_var:     0.0973 | E_err:   0.004875
[2025-10-06 20:54:55] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -28.372135 | E_var:     0.0970 | E_err:   0.004867
[2025-10-06 20:54:58] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -28.366242 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 20:55:02] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -28.360483 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 20:55:06] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -28.368716 | E_var:     0.0888 | E_err:   0.004656
[2025-10-06 20:55:09] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -28.371866 | E_var:     0.1066 | E_err:   0.005102
[2025-10-06 20:55:13] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -28.360718 | E_var:     0.0876 | E_err:   0.004623
[2025-10-06 20:55:17] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -28.366276 | E_var:     0.0795 | E_err:   0.004405
[2025-10-06 20:55:20] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -28.364017 | E_var:     0.0888 | E_err:   0.004656
[2025-10-06 20:55:24] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -28.367654 | E_var:     0.2150 | E_err:   0.007245
[2025-10-06 20:55:28] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -28.367707 | E_var:     0.1225 | E_err:   0.005469
[2025-10-06 20:55:31] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -28.355540 | E_var:     0.1374 | E_err:   0.005792
[2025-10-06 20:55:35] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -28.364699 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 20:55:39] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -28.369237 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 20:55:42] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -28.365978 | E_var:     0.0992 | E_err:   0.004921
[2025-10-06 20:55:46] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -28.362822 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 20:55:50] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -28.372539 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 20:55:53] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -28.363904 | E_var:     0.1614 | E_err:   0.006277
[2025-10-06 20:55:57] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -28.371579 | E_var:     0.0979 | E_err:   0.004889
[2025-10-06 20:56:01] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -28.365322 | E_var:     0.0774 | E_err:   0.004348
[2025-10-06 20:56:04] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -28.366448 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 20:56:08] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -28.363213 | E_var:     0.0996 | E_err:   0.004932
[2025-10-06 20:56:12] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -28.370689 | E_var:     0.0989 | E_err:   0.004915
[2025-10-06 20:56:15] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -28.360708 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 20:56:19] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -28.369923 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 20:56:23] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -28.364092 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 20:56:26] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -28.361170 | E_var:     0.1006 | E_err:   0.004956
[2025-10-06 20:56:30] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -28.360284 | E_var:     0.0924 | E_err:   0.004751
[2025-10-06 20:56:34] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -28.351413 | E_var:     0.1392 | E_err:   0.005829
[2025-10-06 20:56:37] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -28.365870 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 20:56:41] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -28.369079 | E_var:     0.1031 | E_err:   0.005017
[2025-10-06 20:56:45] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -28.355720 | E_var:     0.0872 | E_err:   0.004613
[2025-10-06 20:56:45] 🔄 RESTART #2 | Period: 600
[2025-10-06 20:56:48] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -28.360081 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 20:56:52] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -28.367329 | E_var:     0.1004 | E_err:   0.004950
[2025-10-06 20:56:56] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -28.367839 | E_var:     0.0907 | E_err:   0.004706
[2025-10-06 20:56:59] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -28.368692 | E_var:     0.1080 | E_err:   0.005135
[2025-10-06 20:57:03] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -28.367702 | E_var:     0.0757 | E_err:   0.004298
[2025-10-06 20:57:07] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -28.368472 | E_var:     0.1500 | E_err:   0.006052
[2025-10-06 20:57:10] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -28.365274 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 20:57:14] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -28.364518 | E_var:     0.0971 | E_err:   0.004869
[2025-10-06 20:57:18] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -28.359154 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 20:57:21] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -28.373876 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 20:57:25] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -28.366410 | E_var:     0.1013 | E_err:   0.004973
[2025-10-06 20:57:29] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -28.363062 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 20:57:32] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -28.357192 | E_var:     0.1294 | E_err:   0.005620
[2025-10-06 20:57:36] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -28.360739 | E_var:     0.0896 | E_err:   0.004678
[2025-10-06 20:57:40] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -28.365494 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 20:57:43] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -28.369843 | E_var:     0.0959 | E_err:   0.004838
[2025-10-06 20:57:47] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -28.371140 | E_var:     0.1099 | E_err:   0.005181
[2025-10-06 20:57:51] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -28.365181 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 20:57:54] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -28.361424 | E_var:     0.1726 | E_err:   0.006492
[2025-10-06 20:57:58] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -28.364629 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 20:58:02] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -28.353172 | E_var:     0.1475 | E_err:   0.006001
[2025-10-06 20:58:05] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -28.356108 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 20:58:09] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -28.358119 | E_var:     0.1083 | E_err:   0.005142
[2025-10-06 20:58:13] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -28.357556 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 20:58:16] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -28.362483 | E_var:     0.1022 | E_err:   0.004994
[2025-10-06 20:58:20] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -28.370465 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 20:58:24] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -28.362239 | E_var:     0.1038 | E_err:   0.005033
[2025-10-06 20:58:27] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -28.380159 | E_var:     0.2593 | E_err:   0.007956
[2025-10-06 20:58:31] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -28.367939 | E_var:     0.1036 | E_err:   0.005030
[2025-10-06 20:58:35] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -28.371020 | E_var:     0.1708 | E_err:   0.006457
[2025-10-06 20:58:38] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -28.362908 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 20:58:42] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -28.370016 | E_var:     0.0867 | E_err:   0.004600
[2025-10-06 20:58:46] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -28.364529 | E_var:     0.0790 | E_err:   0.004392
[2025-10-06 20:58:49] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -28.355004 | E_var:     0.1083 | E_err:   0.005143
[2025-10-06 20:58:53] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -28.366463 | E_var:     0.1209 | E_err:   0.005433
[2025-10-06 20:58:57] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -28.362942 | E_var:     0.0757 | E_err:   0.004299
[2025-10-06 20:59:00] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -28.362088 | E_var:     0.1153 | E_err:   0.005305
[2025-10-06 20:59:04] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -28.360887 | E_var:     0.0951 | E_err:   0.004819
[2025-10-06 20:59:08] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -28.359550 | E_var:     0.0785 | E_err:   0.004378
[2025-10-06 20:59:11] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -28.369461 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 20:59:15] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -28.361756 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 20:59:19] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -28.365303 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 20:59:22] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -28.355785 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 20:59:26] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -28.366574 | E_var:     0.0863 | E_err:   0.004591
[2025-10-06 20:59:30] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -28.367616 | E_var:     0.1114 | E_err:   0.005216
[2025-10-06 20:59:33] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -28.354327 | E_var:     0.5350 | E_err:   0.011428
[2025-10-06 20:59:37] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -28.364845 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 20:59:41] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -28.363589 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 20:59:44] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -28.365764 | E_var:     0.0867 | E_err:   0.004600
[2025-10-06 20:59:48] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -28.362724 | E_var:     0.0892 | E_err:   0.004666
[2025-10-06 20:59:48] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 20:59:52] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -28.371763 | E_var:     0.0920 | E_err:   0.004739
[2025-10-06 20:59:55] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -28.374154 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 20:59:59] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -28.367798 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 21:00:03] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -28.362227 | E_var:     0.0691 | E_err:   0.004106
[2025-10-06 21:00:06] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -28.365960 | E_var:     0.0843 | E_err:   0.004536
[2025-10-06 21:00:10] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -28.355566 | E_var:     0.0978 | E_err:   0.004886
[2025-10-06 21:00:14] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -28.367003 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 21:00:17] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -28.369333 | E_var:     0.0878 | E_err:   0.004631
[2025-10-06 21:00:21] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -28.355586 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 21:00:25] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -28.364062 | E_var:     0.0971 | E_err:   0.004868
[2025-10-06 21:00:28] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -28.366187 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 21:00:32] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -28.366013 | E_var:     0.1074 | E_err:   0.005121
[2025-10-06 21:00:36] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -28.368684 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 21:00:39] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -28.371238 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 21:00:43] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -28.364667 | E_var:     0.1159 | E_err:   0.005321
[2025-10-06 21:00:47] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -28.367482 | E_var:     0.1039 | E_err:   0.005037
[2025-10-06 21:00:50] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -28.364706 | E_var:     0.1013 | E_err:   0.004973
[2025-10-06 21:00:54] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -28.369404 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 21:00:58] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -28.370087 | E_var:     0.0886 | E_err:   0.004651
[2025-10-06 21:01:01] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -28.361923 | E_var:     0.1019 | E_err:   0.004987
[2025-10-06 21:01:05] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -28.369539 | E_var:     0.0793 | E_err:   0.004399
[2025-10-06 21:01:09] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -28.364881 | E_var:     0.0869 | E_err:   0.004605
[2025-10-06 21:01:12] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -28.365756 | E_var:     0.0875 | E_err:   0.004621
[2025-10-06 21:01:16] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -28.357038 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 21:01:20] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -28.372835 | E_var:     0.1208 | E_err:   0.005430
[2025-10-06 21:01:23] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -28.366426 | E_var:     0.0777 | E_err:   0.004354
[2025-10-06 21:01:27] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -28.357134 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 21:01:31] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -28.361091 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 21:01:34] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -28.359092 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 21:01:38] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -28.364064 | E_var:     0.0894 | E_err:   0.004671
[2025-10-06 21:01:42] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -28.358687 | E_var:     0.1611 | E_err:   0.006272
[2025-10-06 21:01:45] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -28.362086 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 21:01:49] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -28.364519 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 21:01:53] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -28.365340 | E_var:     0.0874 | E_err:   0.004620
[2025-10-06 21:01:56] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -28.368343 | E_var:     0.1032 | E_err:   0.005018
[2025-10-06 21:02:00] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -28.363141 | E_var:     0.0979 | E_err:   0.004888
[2025-10-06 21:02:04] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -28.373756 | E_var:     0.1020 | E_err:   0.004989
[2025-10-06 21:02:07] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -28.365591 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 21:02:11] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -28.368738 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 21:02:15] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -28.365149 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 21:02:18] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -28.371578 | E_var:     0.1012 | E_err:   0.004971
[2025-10-06 21:02:22] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -28.372820 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 21:02:26] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -28.370093 | E_var:     0.1176 | E_err:   0.005358
[2025-10-06 21:02:29] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -28.362040 | E_var:     0.1251 | E_err:   0.005526
[2025-10-06 21:02:33] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -28.366926 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 21:02:37] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -28.367592 | E_var:     0.1557 | E_err:   0.006165
[2025-10-06 21:02:40] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -28.352631 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 21:02:44] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -28.362255 | E_var:     0.0949 | E_err:   0.004814
[2025-10-06 21:02:48] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -28.368548 | E_var:     0.1071 | E_err:   0.005114
[2025-10-06 21:02:51] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -28.372507 | E_var:     0.0940 | E_err:   0.004792
[2025-10-06 21:02:55] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -28.370430 | E_var:     0.1322 | E_err:   0.005680
[2025-10-06 21:02:59] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -28.370738 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 21:03:02] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -28.359507 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 21:03:06] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -28.369913 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 21:03:10] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -28.364633 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 21:03:13] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -28.369266 | E_var:     0.2668 | E_err:   0.008070
[2025-10-06 21:03:17] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -28.359770 | E_var:     0.1283 | E_err:   0.005596
[2025-10-06 21:03:21] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -28.356403 | E_var:     0.0920 | E_err:   0.004739
[2025-10-06 21:03:24] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -28.372698 | E_var:     0.1407 | E_err:   0.005860
[2025-10-06 21:03:28] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -28.369030 | E_var:     0.1559 | E_err:   0.006170
[2025-10-06 21:03:32] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -28.365401 | E_var:     0.1391 | E_err:   0.005828
[2025-10-06 21:03:35] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -28.363916 | E_var:     0.0916 | E_err:   0.004728
[2025-10-06 21:03:39] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -28.360969 | E_var:     0.1963 | E_err:   0.006923
[2025-10-06 21:03:43] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -28.367758 | E_var:     0.1115 | E_err:   0.005216
[2025-10-06 21:03:46] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -28.359101 | E_var:     0.0791 | E_err:   0.004396
[2025-10-06 21:03:50] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -28.362720 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 21:03:54] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -28.367236 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 21:03:57] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -28.359546 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 21:04:01] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -28.367428 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 21:04:05] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -28.361135 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 21:04:08] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -28.366976 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 21:04:12] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -28.357018 | E_var:     0.1361 | E_err:   0.005764
[2025-10-06 21:04:16] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -28.363533 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 21:04:19] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -28.367894 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 21:04:23] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -28.365084 | E_var:     0.1123 | E_err:   0.005236
[2025-10-06 21:04:27] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -28.366839 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 21:04:30] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -28.369360 | E_var:     0.0819 | E_err:   0.004472
[2025-10-06 21:04:34] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -28.363113 | E_var:     0.0969 | E_err:   0.004865
[2025-10-06 21:04:38] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -28.370744 | E_var:     0.0827 | E_err:   0.004493
[2025-10-06 21:04:41] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -28.358942 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 21:04:45] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -28.365404 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 21:04:49] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -28.364570 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 21:04:52] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -28.364106 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 21:04:56] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -28.363019 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 21:05:00] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -28.366397 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 21:05:03] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -28.368908 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 21:05:07] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -28.364716 | E_var:     0.0931 | E_err:   0.004769
[2025-10-06 21:05:11] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -28.377848 | E_var:     0.1695 | E_err:   0.006432
[2025-10-06 21:05:14] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -28.358579 | E_var:     0.1248 | E_err:   0.005521
[2025-10-06 21:05:18] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -28.367442 | E_var:     0.0770 | E_err:   0.004336
[2025-10-06 21:05:22] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -28.362086 | E_var:     0.1961 | E_err:   0.006919
[2025-10-06 21:05:25] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -28.357170 | E_var:     0.1367 | E_err:   0.005778
[2025-10-06 21:05:29] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -28.360628 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 21:05:33] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -28.364493 | E_var:     0.0841 | E_err:   0.004530
[2025-10-06 21:05:36] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -28.377709 | E_var:     0.1297 | E_err:   0.005628
[2025-10-06 21:05:40] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -28.368410 | E_var:     0.1004 | E_err:   0.004950
[2025-10-06 21:05:44] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -28.374732 | E_var:     0.0931 | E_err:   0.004768
[2025-10-06 21:05:47] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -28.371467 | E_var:     0.1033 | E_err:   0.005021
[2025-10-06 21:05:51] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -28.366983 | E_var:     0.1117 | E_err:   0.005222
[2025-10-06 21:05:55] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -28.360349 | E_var:     0.0977 | E_err:   0.004884
[2025-10-06 21:06:00] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 21:06:03] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -28.368503 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 21:06:07] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -28.360842 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 21:06:11] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -28.360606 | E_var:     0.1300 | E_err:   0.005634
[2025-10-06 21:06:14] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -28.378011 | E_var:     0.2508 | E_err:   0.007825
[2025-10-06 21:06:18] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -28.365723 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 21:06:22] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -28.368953 | E_var:     0.0925 | E_err:   0.004751
[2025-10-06 21:06:25] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -28.361873 | E_var:     0.0970 | E_err:   0.004868
[2025-10-06 21:06:29] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -28.367989 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 21:06:33] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -28.367751 | E_var:     0.0953 | E_err:   0.004825
[2025-10-06 21:06:36] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -28.366418 | E_var:     0.1052 | E_err:   0.005069
[2025-10-06 21:06:40] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -28.368632 | E_var:     0.1024 | E_err:   0.005001
[2025-10-06 21:06:44] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -28.371759 | E_var:     0.1220 | E_err:   0.005457
[2025-10-06 21:06:47] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -28.361854 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 21:06:51] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -28.353492 | E_var:     0.1003 | E_err:   0.004949
[2025-10-06 21:06:55] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -28.360183 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 21:06:58] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -28.360050 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 21:07:02] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -28.360619 | E_var:     0.1586 | E_err:   0.006223
[2025-10-06 21:07:06] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -28.357626 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 21:07:09] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -28.356961 | E_var:     0.1241 | E_err:   0.005503
[2025-10-06 21:07:13] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -28.365712 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 21:07:17] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -28.359222 | E_var:     0.1276 | E_err:   0.005580
[2025-10-06 21:07:20] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -28.359651 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 21:07:24] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -28.365148 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 21:07:28] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -28.356849 | E_var:     0.1124 | E_err:   0.005239
[2025-10-06 21:07:31] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -28.365725 | E_var:     0.0975 | E_err:   0.004878
[2025-10-06 21:07:35] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -28.364440 | E_var:     0.0890 | E_err:   0.004660
[2025-10-06 21:07:39] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -28.361278 | E_var:     0.1007 | E_err:   0.004959
[2025-10-06 21:07:42] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -28.364093 | E_var:     0.0924 | E_err:   0.004750
[2025-10-06 21:07:46] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -28.354661 | E_var:     0.0999 | E_err:   0.004940
[2025-10-06 21:07:50] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -28.371398 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 21:07:53] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -28.362262 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 21:07:57] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -28.361083 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 21:08:00] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -28.353270 | E_var:     0.1447 | E_err:   0.005944
[2025-10-06 21:08:04] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -28.370814 | E_var:     0.1254 | E_err:   0.005532
[2025-10-06 21:08:08] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -28.360996 | E_var:     0.1094 | E_err:   0.005168
[2025-10-06 21:08:11] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -28.369595 | E_var:     0.0771 | E_err:   0.004339
[2025-10-06 21:08:15] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -28.364643 | E_var:     0.1325 | E_err:   0.005687
[2025-10-06 21:08:19] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -28.364431 | E_var:     0.0830 | E_err:   0.004502
[2025-10-06 21:08:22] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -28.360131 | E_var:     0.0891 | E_err:   0.004664
[2025-10-06 21:08:26] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -28.364262 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 21:08:30] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -28.372799 | E_var:     0.0957 | E_err:   0.004834
[2025-10-06 21:08:33] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -28.361185 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 21:08:37] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -28.375901 | E_var:     0.1226 | E_err:   0.005470
[2025-10-06 21:08:41] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -28.360711 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 21:08:44] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -28.359504 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 21:08:48] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -28.363659 | E_var:     0.1479 | E_err:   0.006008
[2025-10-06 21:08:52] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -28.364461 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 21:08:55] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -28.369426 | E_var:     0.1309 | E_err:   0.005654
[2025-10-06 21:08:59] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -28.358762 | E_var:     0.1127 | E_err:   0.005246
[2025-10-06 21:09:03] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -28.362452 | E_var:     0.1061 | E_err:   0.005089
[2025-10-06 21:09:06] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -28.355421 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 21:09:10] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -28.360512 | E_var:     0.1109 | E_err:   0.005204
[2025-10-06 21:09:14] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -28.361127 | E_var:     0.1076 | E_err:   0.005124
[2025-10-06 21:09:17] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -28.359627 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 21:09:21] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -28.359677 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 21:09:25] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -28.375072 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 21:09:28] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -28.370637 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 21:09:32] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -28.372550 | E_var:     0.1468 | E_err:   0.005986
[2025-10-06 21:09:36] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -28.364316 | E_var:     0.0849 | E_err:   0.004552
[2025-10-06 21:09:39] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -28.365969 | E_var:     0.0818 | E_err:   0.004469
[2025-10-06 21:09:43] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -28.365668 | E_var:     0.1035 | E_err:   0.005026
[2025-10-06 21:09:47] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -28.361612 | E_var:     0.1357 | E_err:   0.005755
[2025-10-06 21:09:50] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -28.354116 | E_var:     0.1582 | E_err:   0.006215
[2025-10-06 21:09:54] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -28.360364 | E_var:     0.1088 | E_err:   0.005154
[2025-10-06 21:09:58] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -28.364861 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 21:10:01] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -28.371717 | E_var:     0.0974 | E_err:   0.004875
[2025-10-06 21:10:05] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -28.367843 | E_var:     0.1233 | E_err:   0.005486
[2025-10-06 21:10:09] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -28.371052 | E_var:     0.0805 | E_err:   0.004432
[2025-10-06 21:10:12] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -28.359988 | E_var:     0.1038 | E_err:   0.005033
[2025-10-06 21:10:16] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -28.367853 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 21:10:20] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -28.374396 | E_var:     0.0963 | E_err:   0.004849
[2025-10-06 21:10:23] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -28.366086 | E_var:     0.1113 | E_err:   0.005212
[2025-10-06 21:10:27] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -28.363564 | E_var:     0.1197 | E_err:   0.005407
[2025-10-06 21:10:31] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -28.360701 | E_var:     0.0859 | E_err:   0.004580
[2025-10-06 21:10:34] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -28.362415 | E_var:     0.3272 | E_err:   0.008937
[2025-10-06 21:10:38] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -28.365078 | E_var:     0.1280 | E_err:   0.005591
[2025-10-06 21:10:42] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -28.372634 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 21:10:45] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -28.367735 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 21:10:49] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -28.379083 | E_var:     0.1282 | E_err:   0.005595
[2025-10-06 21:10:53] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -28.367528 | E_var:     0.1403 | E_err:   0.005853
[2025-10-06 21:10:56] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -28.366094 | E_var:     0.1242 | E_err:   0.005506
[2025-10-06 21:11:00] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -28.366838 | E_var:     0.1012 | E_err:   0.004971
[2025-10-06 21:11:04] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -28.364939 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 21:11:07] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -28.362189 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 21:11:11] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -28.372679 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 21:11:15] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -28.357230 | E_var:     0.0840 | E_err:   0.004529
[2025-10-06 21:11:18] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -28.368189 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 21:11:22] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -28.372504 | E_var:     0.1269 | E_err:   0.005566
[2025-10-06 21:11:26] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -28.357170 | E_var:     0.0849 | E_err:   0.004552
[2025-10-06 21:11:29] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -28.367075 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 21:11:33] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -28.361932 | E_var:     0.0989 | E_err:   0.004913
[2025-10-06 21:11:37] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -28.359318 | E_var:     0.1415 | E_err:   0.005878
[2025-10-06 21:11:40] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -28.369761 | E_var:     0.0941 | E_err:   0.004792
[2025-10-06 21:11:44] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -28.359471 | E_var:     0.1463 | E_err:   0.005977
[2025-10-06 21:11:48] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -28.363791 | E_var:     0.1245 | E_err:   0.005512
[2025-10-06 21:11:51] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -28.373582 | E_var:     0.0835 | E_err:   0.004516
[2025-10-06 21:11:55] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -28.367548 | E_var:     0.0852 | E_err:   0.004562
[2025-10-06 21:11:59] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -28.370523 | E_var:     0.8530 | E_err:   0.014431
[2025-10-06 21:12:02] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -28.365841 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 21:12:06] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -28.363800 | E_var:     0.1009 | E_err:   0.004962
[2025-10-06 21:12:06] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 21:12:10] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -28.367753 | E_var:     0.0711 | E_err:   0.004166
[2025-10-06 21:12:13] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -28.367332 | E_var:     0.0772 | E_err:   0.004342
[2025-10-06 21:12:17] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -28.362558 | E_var:     0.0785 | E_err:   0.004378
[2025-10-06 21:12:21] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -28.368294 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 21:12:24] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -28.362549 | E_var:     0.1372 | E_err:   0.005787
[2025-10-06 21:12:28] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -28.367216 | E_var:     0.0899 | E_err:   0.004684
[2025-10-06 21:12:32] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -28.361010 | E_var:     0.1130 | E_err:   0.005254
[2025-10-06 21:12:35] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -28.368662 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 21:12:39] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -28.360525 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 21:12:43] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -28.362101 | E_var:     0.1439 | E_err:   0.005928
[2025-10-06 21:12:46] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -28.363365 | E_var:     0.1088 | E_err:   0.005154
[2025-10-06 21:12:50] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -28.363928 | E_var:     0.1168 | E_err:   0.005341
[2025-10-06 21:12:54] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -28.368188 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 21:12:57] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -28.363082 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 21:13:01] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -28.359680 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 21:13:05] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -28.369133 | E_var:     0.0898 | E_err:   0.004682
[2025-10-06 21:13:08] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -28.365835 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 21:13:12] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -28.368534 | E_var:     0.1161 | E_err:   0.005323
[2025-10-06 21:13:16] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -28.369781 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 21:13:19] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -28.375735 | E_var:     0.0895 | E_err:   0.004676
[2025-10-06 21:13:23] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -28.357949 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 21:13:27] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -28.368855 | E_var:     0.1328 | E_err:   0.005694
[2025-10-06 21:13:30] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -28.360363 | E_var:     0.1077 | E_err:   0.005127
[2025-10-06 21:13:34] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -28.368789 | E_var:     0.0746 | E_err:   0.004268
[2025-10-06 21:13:38] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -28.368865 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 21:13:41] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -28.373052 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 21:13:45] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -28.366878 | E_var:     0.1294 | E_err:   0.005620
[2025-10-06 21:13:49] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -28.371685 | E_var:     0.1026 | E_err:   0.005006
[2025-10-06 21:13:52] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -28.355888 | E_var:     0.0987 | E_err:   0.004910
[2025-10-06 21:13:56] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -28.370214 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 21:14:00] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -28.370337 | E_var:     0.1098 | E_err:   0.005177
[2025-10-06 21:14:03] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -28.360936 | E_var:     0.1122 | E_err:   0.005235
[2025-10-06 21:14:07] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -28.374518 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 21:14:11] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -28.365455 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 21:14:14] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -28.361932 | E_var:     0.1048 | E_err:   0.005058
[2025-10-06 21:14:18] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -28.376145 | E_var:     0.1469 | E_err:   0.005989
[2025-10-06 21:14:22] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -28.359383 | E_var:     0.0952 | E_err:   0.004820
[2025-10-06 21:14:25] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -28.363341 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 21:14:29] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -28.357428 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 21:14:33] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -28.368550 | E_var:     0.3109 | E_err:   0.008712
[2025-10-06 21:14:36] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -28.367936 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 21:14:40] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -28.370079 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 21:14:44] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -28.367769 | E_var:     0.1061 | E_err:   0.005089
[2025-10-06 21:14:47] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -28.371568 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 21:14:51] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -28.361478 | E_var:     0.1049 | E_err:   0.005061
[2025-10-06 21:14:54] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -28.368476 | E_var:     0.0886 | E_err:   0.004650
[2025-10-06 21:14:58] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -28.361820 | E_var:     0.0982 | E_err:   0.004898
[2025-10-06 21:15:02] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -28.368205 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 21:15:05] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -28.364872 | E_var:     0.1110 | E_err:   0.005205
[2025-10-06 21:15:09] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -28.365737 | E_var:     0.1207 | E_err:   0.005428
[2025-10-06 21:15:13] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -28.357567 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 21:15:16] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -28.359635 | E_var:     0.1176 | E_err:   0.005358
[2025-10-06 21:15:20] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -28.367728 | E_var:     0.1026 | E_err:   0.005006
[2025-10-06 21:15:24] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -28.362576 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 21:15:27] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -28.355211 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 21:15:31] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -28.370646 | E_var:     0.0735 | E_err:   0.004236
[2025-10-06 21:15:35] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -28.367822 | E_var:     0.1022 | E_err:   0.004995
[2025-10-06 21:15:38] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -28.363481 | E_var:     0.0922 | E_err:   0.004745
[2025-10-06 21:15:42] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -28.369207 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 21:15:46] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -28.360847 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 21:15:49] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -28.373518 | E_var:     0.1460 | E_err:   0.005971
[2025-10-06 21:15:53] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -28.362755 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 21:15:57] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -28.369741 | E_var:     0.0852 | E_err:   0.004560
[2025-10-06 21:16:00] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -28.362224 | E_var:     0.0987 | E_err:   0.004909
[2025-10-06 21:16:04] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -28.366498 | E_var:     0.0777 | E_err:   0.004354
[2025-10-06 21:16:08] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -28.373827 | E_var:     0.0890 | E_err:   0.004660
[2025-10-06 21:16:11] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -28.369191 | E_var:     0.0971 | E_err:   0.004868
[2025-10-06 21:16:15] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -28.369063 | E_var:     0.0809 | E_err:   0.004444
[2025-10-06 21:16:19] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -28.366824 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 21:16:22] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -28.363448 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 21:16:26] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -28.367792 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 21:16:30] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -28.359517 | E_var:     0.1678 | E_err:   0.006401
[2025-10-06 21:16:33] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -28.366107 | E_var:     0.1060 | E_err:   0.005088
[2025-10-06 21:16:37] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -28.375193 | E_var:     0.1213 | E_err:   0.005443
[2025-10-06 21:16:41] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -28.362647 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 21:16:44] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -28.370370 | E_var:     0.1455 | E_err:   0.005959
[2025-10-06 21:16:48] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -28.360885 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 21:16:52] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -28.371838 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 21:16:55] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -28.377394 | E_var:     0.1067 | E_err:   0.005105
[2025-10-06 21:16:59] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -28.371937 | E_var:     0.1004 | E_err:   0.004950
[2025-10-06 21:17:03] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -28.355339 | E_var:     0.1762 | E_err:   0.006559
[2025-10-06 21:17:06] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -28.362343 | E_var:     0.1100 | E_err:   0.005182
[2025-10-06 21:17:10] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -28.368127 | E_var:     0.1035 | E_err:   0.005028
[2025-10-06 21:17:14] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -28.363086 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 21:17:17] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -28.369107 | E_var:     0.0893 | E_err:   0.004668
[2025-10-06 21:17:21] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -28.373089 | E_var:     0.1349 | E_err:   0.005740
[2025-10-06 21:17:25] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -28.369900 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 21:17:28] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -28.365242 | E_var:     0.0880 | E_err:   0.004635
[2025-10-06 21:17:32] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -28.359964 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 21:17:36] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -28.369560 | E_var:     0.1465 | E_err:   0.005981
[2025-10-06 21:17:39] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -28.370846 | E_var:     0.0905 | E_err:   0.004700
[2025-10-06 21:17:43] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -28.366986 | E_var:     0.0992 | E_err:   0.004921
[2025-10-06 21:17:47] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -28.357780 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 21:17:50] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -28.370612 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 21:17:54] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -28.359259 | E_var:     0.1351 | E_err:   0.005744
[2025-10-06 21:17:58] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -28.364184 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 21:18:01] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -28.361891 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 21:18:05] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -28.366936 | E_var:     0.0892 | E_err:   0.004668
[2025-10-06 21:18:09] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -28.370283 | E_var:     0.0948 | E_err:   0.004810
[2025-10-06 21:18:12] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -28.353637 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 21:18:12] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 21:18:16] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -28.365417 | E_var:     0.1236 | E_err:   0.005494
[2025-10-06 21:18:20] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -28.366398 | E_var:     0.1040 | E_err:   0.005040
[2025-10-06 21:18:23] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -28.373352 | E_var:     0.1203 | E_err:   0.005420
[2025-10-06 21:18:27] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -28.366627 | E_var:     0.0926 | E_err:   0.004754
[2025-10-06 21:18:31] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -28.370672 | E_var:     0.0972 | E_err:   0.004872
[2025-10-06 21:18:34] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -28.364797 | E_var:     0.1272 | E_err:   0.005573
[2025-10-06 21:18:38] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -28.363946 | E_var:     0.0801 | E_err:   0.004421
[2025-10-06 21:18:42] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -28.370905 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 21:18:45] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -28.381161 | E_var:     0.0827 | E_err:   0.004493
[2025-10-06 21:18:49] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -28.365543 | E_var:     0.0727 | E_err:   0.004213
[2025-10-06 21:18:53] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -28.359441 | E_var:     0.1540 | E_err:   0.006132
[2025-10-06 21:18:56] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -28.364635 | E_var:     0.1355 | E_err:   0.005752
[2025-10-06 21:19:00] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -28.370049 | E_var:     0.1000 | E_err:   0.004942
[2025-10-06 21:19:04] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -28.369181 | E_var:     0.0728 | E_err:   0.004215
[2025-10-06 21:19:07] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -28.368550 | E_var:     0.0721 | E_err:   0.004197
[2025-10-06 21:19:11] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -28.373990 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 21:19:15] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -28.364521 | E_var:     0.1592 | E_err:   0.006235
[2025-10-06 21:19:18] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -28.371503 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 21:19:22] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -28.365023 | E_var:     0.0886 | E_err:   0.004652
[2025-10-06 21:19:26] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -28.361583 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 21:19:29] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -28.374097 | E_var:     0.1005 | E_err:   0.004953
[2025-10-06 21:19:33] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -28.362204 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 21:19:37] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -28.361753 | E_var:     0.0826 | E_err:   0.004490
[2025-10-06 21:19:40] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -28.364344 | E_var:     0.1195 | E_err:   0.005400
[2025-10-06 21:19:44] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -28.364431 | E_var:     0.2430 | E_err:   0.007702
[2025-10-06 21:19:48] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -28.370777 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 21:19:51] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -28.370009 | E_var:     0.0800 | E_err:   0.004418
[2025-10-06 21:19:55] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -28.365024 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 21:19:59] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -28.367971 | E_var:     0.1045 | E_err:   0.005052
[2025-10-06 21:20:02] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -28.366690 | E_var:     0.0885 | E_err:   0.004648
[2025-10-06 21:20:06] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -28.355869 | E_var:     0.1108 | E_err:   0.005200
[2025-10-06 21:20:10] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -28.364510 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 21:20:13] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -28.374197 | E_var:     0.1354 | E_err:   0.005749
[2025-10-06 21:20:17] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -28.369047 | E_var:     0.0877 | E_err:   0.004627
[2025-10-06 21:20:21] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -28.361664 | E_var:     0.2230 | E_err:   0.007379
[2025-10-06 21:20:24] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -28.371771 | E_var:     0.0930 | E_err:   0.004765
[2025-10-06 21:20:28] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -28.367789 | E_var:     0.0738 | E_err:   0.004246
[2025-10-06 21:20:32] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -28.366239 | E_var:     0.0950 | E_err:   0.004816
[2025-10-06 21:20:35] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -28.361463 | E_var:     0.1366 | E_err:   0.005775
[2025-10-06 21:20:39] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -28.362030 | E_var:     0.0775 | E_err:   0.004350
[2025-10-06 21:20:43] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -28.357923 | E_var:     0.1253 | E_err:   0.005531
[2025-10-06 21:20:46] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -28.369712 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 21:20:50] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -28.358328 | E_var:     0.1190 | E_err:   0.005390
[2025-10-06 21:20:54] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -28.364252 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 21:20:57] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -28.359223 | E_var:     0.1768 | E_err:   0.006570
[2025-10-06 21:21:01] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -28.367005 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 21:21:05] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -28.372844 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 21:21:08] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -28.367002 | E_var:     0.0848 | E_err:   0.004551
[2025-10-06 21:21:12] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -28.369963 | E_var:     0.0798 | E_err:   0.004415
[2025-10-06 21:21:16] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -28.362275 | E_var:     0.1377 | E_err:   0.005799
[2025-10-06 21:21:19] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -28.364068 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 21:21:23] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -28.370063 | E_var:     0.0744 | E_err:   0.004261
[2025-10-06 21:21:27] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -28.367080 | E_var:     0.1154 | E_err:   0.005307
[2025-10-06 21:21:30] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -28.364170 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 21:21:34] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -28.366388 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 21:21:37] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -28.372176 | E_var:     0.0952 | E_err:   0.004821
[2025-10-06 21:21:41] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -28.365392 | E_var:     0.0847 | E_err:   0.004547
[2025-10-06 21:21:45] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -28.365149 | E_var:     0.0806 | E_err:   0.004437
[2025-10-06 21:21:48] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -28.366365 | E_var:     0.0703 | E_err:   0.004144
[2025-10-06 21:21:52] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -28.366262 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 21:21:56] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -28.365074 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 21:21:59] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -28.369464 | E_var:     0.0871 | E_err:   0.004610
[2025-10-06 21:22:03] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -28.366089 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 21:22:07] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -28.363146 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 21:22:10] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -28.369249 | E_var:     0.1028 | E_err:   0.005010
[2025-10-06 21:22:14] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -28.362014 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 21:22:18] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -28.365048 | E_var:     0.0883 | E_err:   0.004644
[2025-10-06 21:22:21] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -28.372458 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 21:22:25] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -28.360261 | E_var:     0.1179 | E_err:   0.005366
[2025-10-06 21:22:29] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -28.363692 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 21:22:32] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -28.366397 | E_var:     0.1023 | E_err:   0.004997
[2025-10-06 21:22:36] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -28.360245 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 21:22:40] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -28.368826 | E_var:     0.1307 | E_err:   0.005648
[2025-10-06 21:22:43] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -28.366668 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 21:22:47] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -28.368777 | E_var:     0.0774 | E_err:   0.004348
[2025-10-06 21:22:51] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -28.364718 | E_var:     0.0989 | E_err:   0.004913
[2025-10-06 21:22:54] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -28.362479 | E_var:     0.0726 | E_err:   0.004209
[2025-10-06 21:22:58] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -28.373031 | E_var:     0.0775 | E_err:   0.004351
[2025-10-06 21:23:02] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -28.363656 | E_var:     0.1601 | E_err:   0.006252
[2025-10-06 21:23:05] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -28.360793 | E_var:     0.0892 | E_err:   0.004666
[2025-10-06 21:23:09] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -28.366853 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 21:23:13] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -28.368520 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 21:23:16] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -28.367515 | E_var:     0.1038 | E_err:   0.005035
[2025-10-06 21:23:20] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -28.357816 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 21:23:24] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -28.363349 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 21:23:27] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -28.365498 | E_var:     0.0874 | E_err:   0.004618
[2025-10-06 21:23:31] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -28.367667 | E_var:     0.0840 | E_err:   0.004528
[2025-10-06 21:23:35] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -28.358304 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 21:23:38] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -28.365362 | E_var:     0.0840 | E_err:   0.004529
[2025-10-06 21:23:42] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -28.369962 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 21:23:46] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -28.369314 | E_var:     0.0793 | E_err:   0.004401
[2025-10-06 21:23:49] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -28.362638 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 21:23:53] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -28.361534 | E_var:     0.1671 | E_err:   0.006387
[2025-10-06 21:23:57] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -28.369174 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 21:24:00] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -28.371480 | E_var:     0.0742 | E_err:   0.004255
[2025-10-06 21:24:04] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -28.363627 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 21:24:08] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -28.367245 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 21:24:11] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -28.358598 | E_var:     0.1100 | E_err:   0.005181
[2025-10-06 21:24:15] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -28.364617 | E_var:     0.0847 | E_err:   0.004547
[2025-10-06 21:24:19] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -28.361355 | E_var:     0.0837 | E_err:   0.004520
[2025-10-06 21:24:19] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 21:24:22] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -28.360223 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 21:24:26] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -28.358981 | E_var:     0.0920 | E_err:   0.004740
[2025-10-06 21:24:30] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -28.366730 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 21:24:33] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -28.367205 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 21:24:37] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -28.366500 | E_var:     0.0725 | E_err:   0.004208
[2025-10-06 21:24:41] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -28.374837 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 21:24:44] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -28.353975 | E_var:     0.0746 | E_err:   0.004268
[2025-10-06 21:24:48] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -28.367993 | E_var:     0.0967 | E_err:   0.004858
[2025-10-06 21:24:52] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -28.370249 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 21:24:55] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -28.377497 | E_var:     0.0764 | E_err:   0.004320
[2025-10-06 21:24:59] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -28.363214 | E_var:     0.1043 | E_err:   0.005046
[2025-10-06 21:25:03] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -28.365091 | E_var:     0.0798 | E_err:   0.004414
[2025-10-06 21:25:06] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -28.359879 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 21:25:10] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -28.367664 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 21:25:14] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -28.370601 | E_var:     0.1262 | E_err:   0.005550
[2025-10-06 21:25:17] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -28.358565 | E_var:     0.1530 | E_err:   0.006112
[2025-10-06 21:25:21] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -28.360875 | E_var:     0.0987 | E_err:   0.004910
[2025-10-06 21:25:25] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -28.364773 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 21:25:28] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -28.363228 | E_var:     0.0766 | E_err:   0.004324
[2025-10-06 21:25:32] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -28.366658 | E_var:     0.0917 | E_err:   0.004732
[2025-10-06 21:25:36] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -28.370632 | E_var:     0.2376 | E_err:   0.007616
[2025-10-06 21:25:39] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -28.366858 | E_var:     0.0729 | E_err:   0.004217
[2025-10-06 21:25:43] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -28.374301 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 21:25:47] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -28.366718 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 21:25:50] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -28.365914 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 21:25:54] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -28.353370 | E_var:     0.0888 | E_err:   0.004655
[2025-10-06 21:25:58] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -28.372376 | E_var:     0.1281 | E_err:   0.005592
[2025-10-06 21:26:01] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -28.367614 | E_var:     0.0835 | E_err:   0.004516
[2025-10-06 21:26:05] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -28.366157 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 21:26:09] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -28.364687 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 21:26:12] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -28.365883 | E_var:     0.0893 | E_err:   0.004668
[2025-10-06 21:26:16] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -28.360183 | E_var:     0.0833 | E_err:   0.004511
[2025-10-06 21:26:20] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -28.367506 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 21:26:23] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -28.362852 | E_var:     0.0943 | E_err:   0.004797
[2025-10-06 21:26:27] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -28.366846 | E_var:     0.0858 | E_err:   0.004578
[2025-10-06 21:26:31] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -28.367062 | E_var:     0.0865 | E_err:   0.004594
[2025-10-06 21:26:34] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -28.364227 | E_var:     0.1566 | E_err:   0.006183
[2025-10-06 21:26:38] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -28.362878 | E_var:     0.1511 | E_err:   0.006075
[2025-10-06 21:26:41] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -28.370621 | E_var:     0.1019 | E_err:   0.004988
[2025-10-06 21:26:45] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -28.370765 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 21:26:49] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -28.358119 | E_var:     0.2228 | E_err:   0.007375
[2025-10-06 21:26:52] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -28.368335 | E_var:     0.0965 | E_err:   0.004853
[2025-10-06 21:26:56] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -28.366736 | E_var:     0.0987 | E_err:   0.004909
[2025-10-06 21:27:00] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -28.371280 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 21:27:03] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -28.361989 | E_var:     0.1181 | E_err:   0.005369
[2025-10-06 21:27:07] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -28.365735 | E_var:     0.1181 | E_err:   0.005370
[2025-10-06 21:27:11] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -28.362183 | E_var:     0.0670 | E_err:   0.004045
[2025-10-06 21:27:14] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -28.369595 | E_var:     0.0727 | E_err:   0.004212
[2025-10-06 21:27:18] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -28.372510 | E_var:     0.1191 | E_err:   0.005391
[2025-10-06 21:27:22] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -28.361669 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 21:27:25] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -28.374923 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 21:27:29] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -28.370310 | E_var:     0.1562 | E_err:   0.006176
[2025-10-06 21:27:33] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -28.359086 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 21:27:36] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -28.356346 | E_var:     0.1078 | E_err:   0.005131
[2025-10-06 21:27:40] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -28.368458 | E_var:     0.1115 | E_err:   0.005218
[2025-10-06 21:27:44] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -28.363836 | E_var:     0.2431 | E_err:   0.007703
[2025-10-06 21:27:47] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -28.370425 | E_var:     0.0994 | E_err:   0.004926
[2025-10-06 21:27:51] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -28.365724 | E_var:     0.1248 | E_err:   0.005520
[2025-10-06 21:27:55] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -28.372919 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 21:27:58] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -28.361706 | E_var:     0.1074 | E_err:   0.005121
[2025-10-06 21:28:02] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -28.363161 | E_var:     0.1575 | E_err:   0.006201
[2025-10-06 21:28:06] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -28.371015 | E_var:     0.0772 | E_err:   0.004340
[2025-10-06 21:28:09] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -28.367158 | E_var:     0.1048 | E_err:   0.005058
[2025-10-06 21:28:13] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -28.370353 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 21:28:17] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -28.366980 | E_var:     0.1028 | E_err:   0.005010
[2025-10-06 21:28:20] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -28.369067 | E_var:     0.0957 | E_err:   0.004835
[2025-10-06 21:28:24] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -28.367104 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 21:28:28] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -28.365889 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 21:28:31] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -28.369530 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 21:28:35] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -28.358561 | E_var:     0.1034 | E_err:   0.005025
[2025-10-06 21:28:39] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -28.374746 | E_var:     0.0954 | E_err:   0.004825
[2025-10-06 21:28:42] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -28.371510 | E_var:     0.0909 | E_err:   0.004712
[2025-10-06 21:28:46] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -28.365227 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 21:28:50] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -28.361256 | E_var:     0.1373 | E_err:   0.005790
[2025-10-06 21:28:53] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -28.364985 | E_var:     0.1041 | E_err:   0.005040
[2025-10-06 21:28:57] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -28.363651 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 21:29:01] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -28.370186 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 21:29:04] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -28.367148 | E_var:     0.1150 | E_err:   0.005299
[2025-10-06 21:29:08] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -28.359949 | E_var:     0.1110 | E_err:   0.005206
[2025-10-06 21:29:12] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -28.361451 | E_var:     0.1140 | E_err:   0.005276
[2025-10-06 21:29:15] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -28.368639 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 21:29:19] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -28.364582 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 21:29:23] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -28.366567 | E_var:     0.0854 | E_err:   0.004566
[2025-10-06 21:29:26] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -28.368734 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 21:29:30] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -28.357342 | E_var:     0.1614 | E_err:   0.006276
[2025-10-06 21:29:34] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -28.368684 | E_var:     0.1003 | E_err:   0.004949
[2025-10-06 21:29:37] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -28.364495 | E_var:     0.0869 | E_err:   0.004605
[2025-10-06 21:29:41] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -28.364698 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 21:29:45] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -28.365798 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 21:29:48] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -28.366549 | E_var:     0.1284 | E_err:   0.005598
[2025-10-06 21:29:52] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -28.366615 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 21:29:56] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -28.364877 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 21:29:59] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -28.366117 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 21:30:03] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -28.364971 | E_var:     0.0931 | E_err:   0.004768
[2025-10-06 21:30:07] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -28.361745 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 21:30:10] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -28.368728 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 21:30:14] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -28.369898 | E_var:     0.0963 | E_err:   0.004850
[2025-10-06 21:30:18] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -28.364398 | E_var:     0.1124 | E_err:   0.005239
[2025-10-06 21:30:21] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -28.361414 | E_var:     0.0966 | E_err:   0.004856
[2025-10-06 21:30:25] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -28.379140 | E_var:     0.0933 | E_err:   0.004774
[2025-10-06 21:30:25] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 21:30:29] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -28.373206 | E_var:     0.1099 | E_err:   0.005181
[2025-10-06 21:30:32] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -28.372881 | E_var:     0.0945 | E_err:   0.004803
[2025-10-06 21:30:36] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -28.367573 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 21:30:40] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -28.369894 | E_var:     0.0677 | E_err:   0.004065
[2025-10-06 21:30:43] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -28.363364 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 21:30:47] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -28.358927 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 21:30:51] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -28.360100 | E_var:     0.0744 | E_err:   0.004263
[2025-10-06 21:30:54] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -28.364600 | E_var:     0.0783 | E_err:   0.004372
[2025-10-06 21:30:58] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -28.364168 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 21:31:02] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -28.367432 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 21:31:05] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -28.375449 | E_var:     0.1396 | E_err:   0.005838
[2025-10-06 21:31:09] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -28.362869 | E_var:     0.0945 | E_err:   0.004804
[2025-10-06 21:31:13] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -28.369495 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 21:31:16] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -28.367961 | E_var:     0.0892 | E_err:   0.004666
[2025-10-06 21:31:20] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -28.360060 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 21:31:23] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -28.363185 | E_var:     0.0924 | E_err:   0.004750
[2025-10-06 21:31:27] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -28.370541 | E_var:     0.1037 | E_err:   0.005032
[2025-10-06 21:31:31] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -28.360706 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 21:31:34] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -28.367548 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 21:31:38] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -28.366819 | E_var:     0.1024 | E_err:   0.004999
[2025-10-06 21:31:42] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -28.359918 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 21:31:45] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -28.369685 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 21:31:49] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -28.358158 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 21:31:53] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -28.359268 | E_var:     0.0820 | E_err:   0.004473
[2025-10-06 21:31:56] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -28.366244 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 21:32:00] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -28.361293 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 21:32:04] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -28.363281 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 21:32:07] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -28.371357 | E_var:     0.1038 | E_err:   0.005034
[2025-10-06 21:32:11] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -28.363813 | E_var:     0.1121 | E_err:   0.005231
[2025-10-06 21:32:15] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -28.367527 | E_var:     0.1079 | E_err:   0.005132
[2025-10-06 21:32:18] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -28.355939 | E_var:     0.0997 | E_err:   0.004932
[2025-10-06 21:32:22] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -28.368643 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 21:32:26] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -28.365693 | E_var:     0.0897 | E_err:   0.004679
[2025-10-06 21:32:29] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -28.363216 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 21:32:33] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -28.369068 | E_var:     0.1253 | E_err:   0.005530
[2025-10-06 21:32:37] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -28.364690 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 21:32:40] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -28.364704 | E_var:     0.1009 | E_err:   0.004962
[2025-10-06 21:32:44] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -28.367978 | E_var:     0.1281 | E_err:   0.005593
[2025-10-06 21:32:48] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -28.366878 | E_var:     0.0975 | E_err:   0.004880
[2025-10-06 21:32:51] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -28.365711 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 21:32:55] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -28.366943 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 21:32:59] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -28.360891 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 21:33:02] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -28.365747 | E_var:     0.0878 | E_err:   0.004630
[2025-10-06 21:33:06] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -28.364711 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 21:33:10] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -28.362665 | E_var:     0.0787 | E_err:   0.004384
[2025-10-06 21:33:13] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -28.360574 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 21:33:17] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -28.360012 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 21:33:21] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -28.367730 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 21:33:24] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -28.368600 | E_var:     0.0756 | E_err:   0.004297
[2025-10-06 21:33:28] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -28.363602 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 21:33:28] ======================================================================================================
[2025-10-06 21:33:28] ✅ Training completed successfully
[2025-10-06 21:33:28] Total restarts: 2
[2025-10-06 21:33:29] Final Energy: -28.36360207 ± 0.00493805
[2025-10-06 21:33:29] Final Variance: 0.099878
[2025-10-06 21:33:29] ======================================================================================================
[2025-10-06 21:33:29] ======================================================================================================
[2025-10-06 21:33:29] Training completed | Runtime: 3916.2s
[2025-10-06 21:33:30] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 21:33:31] ======================================================================================================
