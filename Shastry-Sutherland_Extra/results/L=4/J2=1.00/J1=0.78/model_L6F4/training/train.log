[2025-10-06 19:22:28] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.77/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 19:22:28]   - 迭代次数: final
[2025-10-06 19:22:28]   - 能量: -27.542210+0.000172j ± 0.006596, Var: 0.178198
[2025-10-06 19:22:28]   - 时间戳: 2025-10-06T19:22:10.346052+08:00
[2025-10-06 19:22:46] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 19:22:46] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 19:22:46] ======================================================================================================
[2025-10-06 19:22:46] GCNN for Shastry-Sutherland Model
[2025-10-06 19:22:46] ======================================================================================================
[2025-10-06 19:22:46] System parameters:
[2025-10-06 19:22:46]   - System size: L=4, N=64
[2025-10-06 19:22:46]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-10-06 19:22:46] ------------------------------------------------------------------------------------------------------
[2025-10-06 19:22:46] Model parameters:
[2025-10-06 19:22:46]   - Number of layers = 6
[2025-10-06 19:22:46]   - Number of features = 4
[2025-10-06 19:22:46]   - Total parameters = 20780
[2025-10-06 19:22:46] ------------------------------------------------------------------------------------------------------
[2025-10-06 19:22:46] Training parameters:
[2025-10-06 19:22:46]   - Total iterations: 1050
[2025-10-06 19:22:46]   - Annealing cycles: 3
[2025-10-06 19:22:46]   - Initial period: 150
[2025-10-06 19:22:46]   - Period multiplier: 2.0
[2025-10-06 19:22:46]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 19:22:46]   - Samples: 4096
[2025-10-06 19:22:46]   - Discarded samples: 0
[2025-10-06 19:22:46]   - Chunk size: 4096
[2025-10-06 19:22:46]   - Diagonal shift: 0.15
[2025-10-06 19:22:46]   - Gradient clipping: 1.0
[2025-10-06 19:22:46]   - Checkpoint enabled: interval=100
[2025-10-06 19:22:46]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.78/model_L6F4/training/checkpoints
[2025-10-06 19:22:46] ------------------------------------------------------------------------------------------------------
[2025-10-06 19:22:46] Device status:
[2025-10-06 19:22:46]   - Devices model: NVIDIA H200 NVL
[2025-10-06 19:22:46]   - Number of devices: 1
[2025-10-06 19:22:46]   - Sharding: True
[2025-10-06 19:22:47] ======================================================================================================
[2025-10-06 19:23:30] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -27.940990 | E_var:     0.3933 | E_err:   0.009799
[2025-10-06 19:23:56] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -27.949575 | E_var:     0.1880 | E_err:   0.006775
[2025-10-06 19:24:00] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -27.959027 | E_var:     0.1642 | E_err:   0.006331
[2025-10-06 19:24:04] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -27.947475 | E_var:     0.1758 | E_err:   0.006552
[2025-10-06 19:24:07] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -27.950083 | E_var:     0.1207 | E_err:   0.005427
[2025-10-06 19:24:11] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -27.954231 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 19:24:15] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -27.967762 | E_var:     0.1441 | E_err:   0.005931
[2025-10-06 19:24:18] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -27.946934 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 19:24:22] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -27.948621 | E_var:     0.1228 | E_err:   0.005475
[2025-10-06 19:24:26] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -27.953321 | E_var:     0.1911 | E_err:   0.006830
[2025-10-06 19:24:29] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -27.951811 | E_var:     0.1089 | E_err:   0.005155
[2025-10-06 19:24:33] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -27.955297 | E_var:     0.1597 | E_err:   0.006245
[2025-10-06 19:24:36] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -27.959708 | E_var:     0.1380 | E_err:   0.005805
[2025-10-06 19:24:40] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -27.963225 | E_var:     0.1524 | E_err:   0.006099
[2025-10-06 19:24:44] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -27.961956 | E_var:     0.1224 | E_err:   0.005465
[2025-10-06 19:24:47] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -27.955969 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 19:24:51] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -27.944393 | E_var:     0.1102 | E_err:   0.005186
[2025-10-06 19:24:55] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -27.948494 | E_var:     0.1138 | E_err:   0.005271
[2025-10-06 19:24:58] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -27.953317 | E_var:     0.1108 | E_err:   0.005202
[2025-10-06 19:25:02] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -27.963037 | E_var:     0.1024 | E_err:   0.005000
[2025-10-06 19:25:06] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -27.949967 | E_var:     0.1229 | E_err:   0.005477
[2025-10-06 19:25:09] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -27.944957 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 19:25:13] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -27.952272 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 19:25:17] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -27.955413 | E_var:     0.1550 | E_err:   0.006152
[2025-10-06 19:25:20] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -27.955909 | E_var:     0.1027 | E_err:   0.005006
[2025-10-06 19:25:24] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -27.952422 | E_var:     0.1473 | E_err:   0.005997
[2025-10-06 19:25:28] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -27.948883 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 19:25:31] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -27.942448 | E_var:     0.1306 | E_err:   0.005647
[2025-10-06 19:25:35] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -27.950475 | E_var:     0.1084 | E_err:   0.005144
[2025-10-06 19:25:39] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -27.957328 | E_var:     0.1471 | E_err:   0.005994
[2025-10-06 19:25:42] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -27.948695 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 19:25:46] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -27.967408 | E_var:     0.1315 | E_err:   0.005666
[2025-10-06 19:25:49] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -27.959641 | E_var:     0.1428 | E_err:   0.005904
[2025-10-06 19:25:53] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -27.959006 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 19:25:57] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -27.954559 | E_var:     0.1254 | E_err:   0.005533
[2025-10-06 19:26:00] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -27.950866 | E_var:     0.1179 | E_err:   0.005365
[2025-10-06 19:26:04] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -27.944818 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 19:26:08] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -27.956966 | E_var:     0.1136 | E_err:   0.005266
[2025-10-06 19:26:11] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -27.949554 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 19:26:15] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -27.953377 | E_var:     0.1238 | E_err:   0.005497
[2025-10-06 19:26:19] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -27.948837 | E_var:     0.2747 | E_err:   0.008189
[2025-10-06 19:26:22] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -27.953333 | E_var:     0.1616 | E_err:   0.006282
[2025-10-06 19:26:26] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -27.966905 | E_var:     0.1177 | E_err:   0.005360
[2025-10-06 19:26:30] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -27.951631 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 19:26:33] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -27.958440 | E_var:     0.1531 | E_err:   0.006113
[2025-10-06 19:26:37] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -27.953823 | E_var:     0.4956 | E_err:   0.010999
[2025-10-06 19:26:41] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -27.946099 | E_var:     0.1460 | E_err:   0.005970
[2025-10-06 19:26:44] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -27.942395 | E_var:     0.1466 | E_err:   0.005982
[2025-10-06 19:26:48] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -27.960649 | E_var:     0.1425 | E_err:   0.005897
[2025-10-06 19:26:52] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -27.954877 | E_var:     0.1569 | E_err:   0.006190
[2025-10-06 19:26:55] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -27.962888 | E_var:     0.1275 | E_err:   0.005580
[2025-10-06 19:26:59] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -27.949601 | E_var:     0.1113 | E_err:   0.005213
[2025-10-06 19:27:02] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -27.959845 | E_var:     0.1091 | E_err:   0.005160
[2025-10-06 19:27:06] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -27.943418 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 19:27:10] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -27.959851 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 19:27:13] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -27.952229 | E_var:     0.1369 | E_err:   0.005781
[2025-10-06 19:27:17] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -27.953482 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 19:27:21] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -27.953420 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 19:27:24] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -27.948996 | E_var:     0.1341 | E_err:   0.005721
[2025-10-06 19:27:28] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -27.957109 | E_var:     0.1407 | E_err:   0.005860
[2025-10-06 19:27:32] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -27.953189 | E_var:     0.1197 | E_err:   0.005406
[2025-10-06 19:27:35] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -27.956559 | E_var:     0.0872 | E_err:   0.004613
[2025-10-06 19:27:39] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -27.943179 | E_var:     0.1404 | E_err:   0.005856
[2025-10-06 19:27:43] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -27.952347 | E_var:     0.1109 | E_err:   0.005202
[2025-10-06 19:27:46] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -27.949239 | E_var:     0.2551 | E_err:   0.007892
[2025-10-06 19:27:50] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -27.955305 | E_var:     0.2270 | E_err:   0.007444
[2025-10-06 19:27:54] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -27.955777 | E_var:     0.1243 | E_err:   0.005508
[2025-10-06 19:27:57] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -27.946578 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 19:28:01] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -27.954252 | E_var:     0.1500 | E_err:   0.006053
[2025-10-06 19:28:05] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -27.956261 | E_var:     0.1455 | E_err:   0.005961
[2025-10-06 19:28:08] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -27.946291 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 19:28:12] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -27.943224 | E_var:     0.1511 | E_err:   0.006074
[2025-10-06 19:28:16] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -27.956070 | E_var:     0.1428 | E_err:   0.005904
[2025-10-06 19:28:19] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -27.956291 | E_var:     0.1413 | E_err:   0.005874
[2025-10-06 19:28:23] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -27.953461 | E_var:     0.1160 | E_err:   0.005321
[2025-10-06 19:28:26] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -27.957011 | E_var:     0.1061 | E_err:   0.005089
[2025-10-06 19:28:30] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -27.955490 | E_var:     0.1031 | E_err:   0.005016
[2025-10-06 19:28:34] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -27.958587 | E_var:     0.0845 | E_err:   0.004541
[2025-10-06 19:28:37] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -27.958784 | E_var:     0.1265 | E_err:   0.005558
[2025-10-06 19:28:41] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -27.954684 | E_var:     0.1110 | E_err:   0.005207
[2025-10-06 19:28:45] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -27.961258 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 19:28:48] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -27.955039 | E_var:     0.1062 | E_err:   0.005091
[2025-10-06 19:28:52] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -27.960781 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 19:28:56] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -27.962702 | E_var:     0.1138 | E_err:   0.005272
[2025-10-06 19:28:59] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -27.948641 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 19:29:03] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -27.953881 | E_var:     0.1280 | E_err:   0.005589
[2025-10-06 19:29:07] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -27.941350 | E_var:     0.1234 | E_err:   0.005489
[2025-10-06 19:29:10] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -27.950685 | E_var:     0.1212 | E_err:   0.005440
[2025-10-06 19:29:14] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -27.954741 | E_var:     0.1183 | E_err:   0.005375
[2025-10-06 19:29:18] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -27.946449 | E_var:     0.1052 | E_err:   0.005067
[2025-10-06 19:29:21] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -27.951692 | E_var:     0.1379 | E_err:   0.005802
[2025-10-06 19:29:25] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -27.963001 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 19:29:29] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -27.956184 | E_var:     0.1201 | E_err:   0.005415
[2025-10-06 19:29:32] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -27.942685 | E_var:     0.1643 | E_err:   0.006334
[2025-10-06 19:29:36] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -27.955283 | E_var:     0.1387 | E_err:   0.005819
[2025-10-06 19:29:39] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -27.957128 | E_var:     0.1200 | E_err:   0.005413
[2025-10-06 19:29:43] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -27.946686 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 19:29:47] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -27.955971 | E_var:     0.1144 | E_err:   0.005285
[2025-10-06 19:29:50] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -27.955480 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 19:29:54] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -27.960087 | E_var:     0.1213 | E_err:   0.005442
[2025-10-06 19:29:54] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 19:29:58] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -27.953540 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 19:30:01] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -27.952459 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 19:30:05] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -27.945026 | E_var:     0.1860 | E_err:   0.006738
[2025-10-06 19:30:09] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -27.956245 | E_var:     0.1451 | E_err:   0.005952
[2025-10-06 19:30:12] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -27.945928 | E_var:     0.1837 | E_err:   0.006697
[2025-10-06 19:30:16] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -27.939059 | E_var:     0.1229 | E_err:   0.005478
[2025-10-06 19:30:20] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -27.959505 | E_var:     0.1269 | E_err:   0.005566
[2025-10-06 19:30:23] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -27.950197 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 19:30:27] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -27.960105 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 19:30:31] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -27.958993 | E_var:     0.1178 | E_err:   0.005363
[2025-10-06 19:30:34] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -27.946756 | E_var:     0.1207 | E_err:   0.005429
[2025-10-06 19:30:38] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -27.961259 | E_var:     0.1244 | E_err:   0.005512
[2025-10-06 19:30:42] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -27.948195 | E_var:     0.1094 | E_err:   0.005169
[2025-10-06 19:30:45] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -27.953456 | E_var:     0.1318 | E_err:   0.005671
[2025-10-06 19:30:49] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -27.948979 | E_var:     0.1574 | E_err:   0.006199
[2025-10-06 19:30:53] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -27.950412 | E_var:     0.1143 | E_err:   0.005281
[2025-10-06 19:30:56] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -27.949882 | E_var:     0.1201 | E_err:   0.005416
[2025-10-06 19:31:00] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -27.949370 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 19:31:03] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -27.954476 | E_var:     0.1000 | E_err:   0.004940
[2025-10-06 19:31:07] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -27.953860 | E_var:     0.1386 | E_err:   0.005816
[2025-10-06 19:31:11] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -27.952363 | E_var:     0.1229 | E_err:   0.005478
[2025-10-06 19:31:14] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -27.954637 | E_var:     0.1682 | E_err:   0.006408
[2025-10-06 19:31:18] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -27.948002 | E_var:     0.1354 | E_err:   0.005749
[2025-10-06 19:31:22] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -27.962032 | E_var:     0.1344 | E_err:   0.005729
[2025-10-06 19:31:25] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -27.945253 | E_var:     0.2441 | E_err:   0.007719
[2025-10-06 19:31:29] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -27.948740 | E_var:     0.1419 | E_err:   0.005886
[2025-10-06 19:31:33] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -27.954104 | E_var:     0.3097 | E_err:   0.008696
[2025-10-06 19:31:36] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -27.957609 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 19:31:40] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -27.958517 | E_var:     0.1729 | E_err:   0.006497
[2025-10-06 19:31:44] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -27.953755 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 19:31:47] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -27.954931 | E_var:     0.1406 | E_err:   0.005859
[2025-10-06 19:31:51] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -27.949836 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 19:31:55] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -27.953930 | E_var:     0.0980 | E_err:   0.004890
[2025-10-06 19:31:58] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -27.944944 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 19:32:02] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -27.962447 | E_var:     0.1308 | E_err:   0.005650
[2025-10-06 19:32:05] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -27.953633 | E_var:     0.1448 | E_err:   0.005945
[2025-10-06 19:32:09] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -27.967295 | E_var:     0.0969 | E_err:   0.004864
[2025-10-06 19:32:13] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -27.956191 | E_var:     0.1123 | E_err:   0.005237
[2025-10-06 19:32:16] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -27.954830 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 19:32:20] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -27.961825 | E_var:     0.1715 | E_err:   0.006470
[2025-10-06 19:32:24] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -27.956268 | E_var:     0.1206 | E_err:   0.005426
[2025-10-06 19:32:27] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -27.965955 | E_var:     0.1490 | E_err:   0.006031
[2025-10-06 19:32:31] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -27.953411 | E_var:     0.1163 | E_err:   0.005328
[2025-10-06 19:32:35] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -27.953113 | E_var:     0.1080 | E_err:   0.005135
[2025-10-06 19:32:38] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -27.948757 | E_var:     0.1049 | E_err:   0.005060
[2025-10-06 19:32:42] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -27.945217 | E_var:     0.1408 | E_err:   0.005863
[2025-10-06 19:32:46] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -27.951789 | E_var:     0.1206 | E_err:   0.005425
[2025-10-06 19:32:49] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -27.964968 | E_var:     0.1198 | E_err:   0.005408
[2025-10-06 19:32:53] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -27.951847 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 19:32:57] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -27.963950 | E_var:     0.1272 | E_err:   0.005573
[2025-10-06 19:32:57] 🔄 RESTART #1 | Period: 300
[2025-10-06 19:33:00] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -27.949689 | E_var:     0.1304 | E_err:   0.005643
[2025-10-06 19:33:04] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -27.951763 | E_var:     0.1300 | E_err:   0.005633
[2025-10-06 19:33:08] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -27.960245 | E_var:     0.1497 | E_err:   0.006045
[2025-10-06 19:33:11] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -27.952240 | E_var:     0.0990 | E_err:   0.004918
[2025-10-06 19:33:15] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -27.950400 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 19:33:18] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -27.955607 | E_var:     0.1231 | E_err:   0.005482
[2025-10-06 19:33:22] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -27.945214 | E_var:     0.1485 | E_err:   0.006022
[2025-10-06 19:33:26] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -27.953232 | E_var:     0.1227 | E_err:   0.005473
[2025-10-06 19:33:29] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -27.955639 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 19:33:33] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -27.950987 | E_var:     0.1403 | E_err:   0.005853
[2025-10-06 19:33:37] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -27.951062 | E_var:     0.1112 | E_err:   0.005211
[2025-10-06 19:33:40] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -27.947447 | E_var:     0.1038 | E_err:   0.005034
[2025-10-06 19:33:44] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -27.944218 | E_var:     0.1105 | E_err:   0.005194
[2025-10-06 19:33:48] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -27.962339 | E_var:     0.1266 | E_err:   0.005560
[2025-10-06 19:33:51] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -27.954565 | E_var:     0.1257 | E_err:   0.005539
[2025-10-06 19:33:55] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -27.946635 | E_var:     0.1526 | E_err:   0.006103
[2025-10-06 19:33:59] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -27.955860 | E_var:     0.1904 | E_err:   0.006818
[2025-10-06 19:34:02] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -27.958972 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 19:34:06] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -27.952398 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 19:34:09] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -27.950306 | E_var:     0.0964 | E_err:   0.004852
[2025-10-06 19:34:13] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -27.951417 | E_var:     0.1083 | E_err:   0.005141
[2025-10-06 19:34:17] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -27.954848 | E_var:     0.1017 | E_err:   0.004983
[2025-10-06 19:34:20] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -27.946664 | E_var:     0.1064 | E_err:   0.005096
[2025-10-06 19:34:24] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -27.945280 | E_var:     0.1091 | E_err:   0.005160
[2025-10-06 19:34:28] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -27.950621 | E_var:     0.1460 | E_err:   0.005970
[2025-10-06 19:34:31] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -27.952868 | E_var:     0.1126 | E_err:   0.005243
[2025-10-06 19:34:35] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -27.954794 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 19:34:39] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -27.942982 | E_var:     0.1342 | E_err:   0.005724
[2025-10-06 19:34:42] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -27.958870 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 19:34:46] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -27.954660 | E_var:     0.0968 | E_err:   0.004863
[2025-10-06 19:34:50] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -27.946086 | E_var:     0.1178 | E_err:   0.005362
[2025-10-06 19:34:53] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -27.955486 | E_var:     0.1511 | E_err:   0.006074
[2025-10-06 19:34:57] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -27.957357 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 19:35:01] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -27.953144 | E_var:     0.1388 | E_err:   0.005820
[2025-10-06 19:35:04] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -27.962806 | E_var:     0.1079 | E_err:   0.005134
[2025-10-06 19:35:08] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -27.948507 | E_var:     0.1174 | E_err:   0.005353
[2025-10-06 19:35:12] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -27.952376 | E_var:     0.1347 | E_err:   0.005735
[2025-10-06 19:35:15] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -27.954434 | E_var:     0.1661 | E_err:   0.006368
[2025-10-06 19:35:19] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -27.951284 | E_var:     0.1736 | E_err:   0.006511
[2025-10-06 19:35:23] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -27.957742 | E_var:     0.1718 | E_err:   0.006476
[2025-10-06 19:35:26] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -27.956798 | E_var:     0.1240 | E_err:   0.005501
[2025-10-06 19:35:30] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -27.962705 | E_var:     0.1067 | E_err:   0.005105
[2025-10-06 19:35:33] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -27.948382 | E_var:     0.1014 | E_err:   0.004976
[2025-10-06 19:35:37] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -27.951991 | E_var:     0.1167 | E_err:   0.005339
[2025-10-06 19:35:41] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -27.954727 | E_var:     0.1166 | E_err:   0.005335
[2025-10-06 19:35:44] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -27.955506 | E_var:     0.1339 | E_err:   0.005719
[2025-10-06 19:35:48] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -27.956418 | E_var:     0.1070 | E_err:   0.005111
[2025-10-06 19:35:52] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -27.950850 | E_var:     0.1062 | E_err:   0.005091
[2025-10-06 19:35:55] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -27.961100 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 19:35:59] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -27.948098 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 19:35:59] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 19:36:03] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -27.952200 | E_var:     0.1647 | E_err:   0.006342
[2025-10-06 19:36:06] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -27.954453 | E_var:     0.1213 | E_err:   0.005443
[2025-10-06 19:36:10] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -27.949802 | E_var:     0.1175 | E_err:   0.005356
[2025-10-06 19:36:14] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -27.957130 | E_var:     0.1001 | E_err:   0.004944
[2025-10-06 19:36:17] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -27.948326 | E_var:     0.1216 | E_err:   0.005448
[2025-10-06 19:36:21] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -27.957076 | E_var:     0.1113 | E_err:   0.005212
[2025-10-06 19:36:25] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -27.951785 | E_var:     0.1510 | E_err:   0.006072
[2025-10-06 19:36:28] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -27.957776 | E_var:     0.1156 | E_err:   0.005312
[2025-10-06 19:36:32] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -27.958782 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 19:36:36] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -27.958037 | E_var:     0.1024 | E_err:   0.004999
[2025-10-06 19:36:39] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -27.959819 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 19:36:43] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -27.949702 | E_var:     0.1131 | E_err:   0.005256
[2025-10-06 19:36:47] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -27.947931 | E_var:     0.1060 | E_err:   0.005086
[2025-10-06 19:36:50] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -27.957242 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 19:36:54] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -27.962431 | E_var:     0.1177 | E_err:   0.005360
[2025-10-06 19:36:58] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -27.965252 | E_var:     0.1932 | E_err:   0.006867
[2025-10-06 19:37:01] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -27.946244 | E_var:     0.1202 | E_err:   0.005416
[2025-10-06 19:37:05] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -27.948257 | E_var:     0.1102 | E_err:   0.005186
[2025-10-06 19:37:08] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -27.954099 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 19:37:12] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -27.953756 | E_var:     0.1224 | E_err:   0.005467
[2025-10-06 19:37:16] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -27.963480 | E_var:     0.1359 | E_err:   0.005761
[2025-10-06 19:37:19] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -27.954866 | E_var:     0.1144 | E_err:   0.005286
[2025-10-06 19:37:23] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -27.961265 | E_var:     0.1214 | E_err:   0.005443
[2025-10-06 19:37:27] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -27.949759 | E_var:     0.0986 | E_err:   0.004905
[2025-10-06 19:37:30] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -27.956362 | E_var:     0.0988 | E_err:   0.004911
[2025-10-06 19:37:34] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -27.952983 | E_var:     0.1315 | E_err:   0.005666
[2025-10-06 19:37:38] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -27.959976 | E_var:     0.1068 | E_err:   0.005105
[2025-10-06 19:37:41] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -27.954599 | E_var:     0.1102 | E_err:   0.005186
[2025-10-06 19:37:45] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -27.962147 | E_var:     0.1261 | E_err:   0.005548
[2025-10-06 19:37:49] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -27.956621 | E_var:     0.1108 | E_err:   0.005202
[2025-10-06 19:37:52] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -27.950177 | E_var:     0.1284 | E_err:   0.005600
[2025-10-06 19:37:56] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -27.956989 | E_var:     0.0997 | E_err:   0.004933
[2025-10-06 19:38:00] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -27.951341 | E_var:     0.1143 | E_err:   0.005283
[2025-10-06 19:38:03] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -27.953360 | E_var:     0.1208 | E_err:   0.005432
[2025-10-06 19:38:07] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -27.948599 | E_var:     0.1132 | E_err:   0.005257
[2025-10-06 19:38:11] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -27.952438 | E_var:     0.1365 | E_err:   0.005772
[2025-10-06 19:38:14] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -27.960258 | E_var:     0.1378 | E_err:   0.005800
[2025-10-06 19:38:18] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -27.959963 | E_var:     0.1749 | E_err:   0.006534
[2025-10-06 19:38:21] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -27.951896 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 19:38:25] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -27.954877 | E_var:     0.0986 | E_err:   0.004905
[2025-10-06 19:38:29] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -27.938526 | E_var:     0.1065 | E_err:   0.005099
[2025-10-06 19:38:32] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -27.953565 | E_var:     0.0937 | E_err:   0.004783
[2025-10-06 19:38:36] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -27.954052 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 19:38:40] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -27.955662 | E_var:     0.1334 | E_err:   0.005708
[2025-10-06 19:38:43] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -27.959235 | E_var:     0.1326 | E_err:   0.005691
[2025-10-06 19:38:47] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -27.954981 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 19:38:51] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -27.958939 | E_var:     0.1080 | E_err:   0.005136
[2025-10-06 19:38:54] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -27.954879 | E_var:     0.1716 | E_err:   0.006473
[2025-10-06 19:38:58] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -27.953805 | E_var:     0.0911 | E_err:   0.004715
[2025-10-06 19:39:02] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -27.951889 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 19:39:05] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -27.955452 | E_var:     0.1228 | E_err:   0.005475
[2025-10-06 19:39:09] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -27.953729 | E_var:     0.1013 | E_err:   0.004972
[2025-10-06 19:39:13] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -27.948883 | E_var:     0.1190 | E_err:   0.005391
[2025-10-06 19:39:16] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -27.951769 | E_var:     0.1483 | E_err:   0.006017
[2025-10-06 19:39:20] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -27.951207 | E_var:     0.1240 | E_err:   0.005502
[2025-10-06 19:39:24] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -27.967115 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 19:39:27] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -27.951943 | E_var:     0.2154 | E_err:   0.007251
[2025-10-06 19:39:31] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -27.960528 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 19:39:35] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -27.952396 | E_var:     0.1263 | E_err:   0.005554
[2025-10-06 19:39:38] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -27.948211 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 19:39:42] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -27.959448 | E_var:     0.1140 | E_err:   0.005277
[2025-10-06 19:39:45] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -27.950408 | E_var:     0.1190 | E_err:   0.005390
[2025-10-06 19:39:49] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -27.954604 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 19:39:53] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -27.955535 | E_var:     0.1597 | E_err:   0.006245
[2025-10-06 19:39:56] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -27.953733 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 19:40:00] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -27.952482 | E_var:     0.1147 | E_err:   0.005292
[2025-10-06 19:40:04] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -27.954543 | E_var:     0.1349 | E_err:   0.005740
[2025-10-06 19:40:07] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -27.952955 | E_var:     0.0923 | E_err:   0.004747
[2025-10-06 19:40:11] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -27.959025 | E_var:     0.1055 | E_err:   0.005074
[2025-10-06 19:40:15] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -27.957640 | E_var:     0.0795 | E_err:   0.004404
[2025-10-06 19:40:18] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -27.950381 | E_var:     0.1128 | E_err:   0.005247
[2025-10-06 19:40:22] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -27.949497 | E_var:     0.1214 | E_err:   0.005445
[2025-10-06 19:40:26] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -27.955948 | E_var:     0.0956 | E_err:   0.004830
[2025-10-06 19:40:29] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -27.948944 | E_var:     0.1229 | E_err:   0.005478
[2025-10-06 19:40:33] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -27.958175 | E_var:     0.1259 | E_err:   0.005543
[2025-10-06 19:40:37] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -27.952487 | E_var:     0.1231 | E_err:   0.005482
[2025-10-06 19:40:40] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -27.953286 | E_var:     0.1031 | E_err:   0.005016
[2025-10-06 19:40:44] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -27.947778 | E_var:     0.1164 | E_err:   0.005330
[2025-10-06 19:40:48] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -27.951362 | E_var:     0.1048 | E_err:   0.005058
[2025-10-06 19:40:51] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -27.952620 | E_var:     0.1267 | E_err:   0.005562
[2025-10-06 19:40:55] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -27.957644 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 19:40:58] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -27.962129 | E_var:     0.0996 | E_err:   0.004932
[2025-10-06 19:41:02] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -27.953160 | E_var:     0.1288 | E_err:   0.005607
[2025-10-06 19:41:06] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -27.954653 | E_var:     0.1032 | E_err:   0.005018
[2025-10-06 19:41:09] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -27.960277 | E_var:     0.1046 | E_err:   0.005054
[2025-10-06 19:41:13] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -27.957369 | E_var:     0.1711 | E_err:   0.006463
[2025-10-06 19:41:17] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -27.954142 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 19:41:20] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -27.949410 | E_var:     0.1014 | E_err:   0.004974
[2025-10-06 19:41:24] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -27.950803 | E_var:     0.1157 | E_err:   0.005315
[2025-10-06 19:41:28] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -27.947272 | E_var:     0.1298 | E_err:   0.005629
[2025-10-06 19:41:31] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -27.948845 | E_var:     0.1285 | E_err:   0.005602
[2025-10-06 19:41:35] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -27.946901 | E_var:     0.1189 | E_err:   0.005387
[2025-10-06 19:41:39] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -27.945040 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 19:41:42] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -27.954185 | E_var:     0.0985 | E_err:   0.004903
[2025-10-06 19:41:46] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -27.951150 | E_var:     0.1362 | E_err:   0.005766
[2025-10-06 19:41:50] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -27.951063 | E_var:     0.1023 | E_err:   0.004997
[2025-10-06 19:41:53] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -27.961950 | E_var:     0.1037 | E_err:   0.005032
[2025-10-06 19:41:57] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -27.958081 | E_var:     0.1421 | E_err:   0.005889
[2025-10-06 19:42:01] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -27.967221 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 19:42:04] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -27.956299 | E_var:     0.1813 | E_err:   0.006653
[2025-10-06 19:42:04] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 19:42:08] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -27.952061 | E_var:     0.2356 | E_err:   0.007585
[2025-10-06 19:42:12] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -27.960814 | E_var:     0.0974 | E_err:   0.004875
[2025-10-06 19:42:15] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -27.950804 | E_var:     0.1523 | E_err:   0.006098
[2025-10-06 19:42:19] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -27.961517 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 19:42:23] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -27.950286 | E_var:     0.1165 | E_err:   0.005333
[2025-10-06 19:42:26] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -27.959221 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 19:42:30] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -27.950669 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 19:42:33] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -27.961587 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 19:42:37] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -27.952443 | E_var:     0.1003 | E_err:   0.004949
[2025-10-06 19:42:41] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -27.949662 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 19:42:44] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -27.960562 | E_var:     0.1644 | E_err:   0.006335
[2025-10-06 19:42:48] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -27.959319 | E_var:     0.1010 | E_err:   0.004965
[2025-10-06 19:42:52] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -27.946719 | E_var:     0.0973 | E_err:   0.004873
[2025-10-06 19:42:55] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -27.956994 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 19:42:59] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -27.953045 | E_var:     0.1765 | E_err:   0.006564
[2025-10-06 19:43:03] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -27.962178 | E_var:     0.2092 | E_err:   0.007146
[2025-10-06 19:43:06] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -27.943525 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 19:43:10] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -27.963030 | E_var:     0.1239 | E_err:   0.005500
[2025-10-06 19:43:14] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -27.946843 | E_var:     0.1193 | E_err:   0.005397
[2025-10-06 19:43:17] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -27.946442 | E_var:     0.2804 | E_err:   0.008273
[2025-10-06 19:43:21] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -27.957969 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 19:43:25] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -27.953021 | E_var:     0.1279 | E_err:   0.005589
[2025-10-06 19:43:28] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -27.948057 | E_var:     0.0960 | E_err:   0.004842
[2025-10-06 19:43:32] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -27.958553 | E_var:     0.1797 | E_err:   0.006624
[2025-10-06 19:43:36] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -27.945473 | E_var:     0.1340 | E_err:   0.005720
[2025-10-06 19:43:39] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -27.959982 | E_var:     0.1235 | E_err:   0.005491
[2025-10-06 19:43:43] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -27.962332 | E_var:     0.1321 | E_err:   0.005680
[2025-10-06 19:43:47] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -27.953189 | E_var:     0.1072 | E_err:   0.005115
[2025-10-06 19:43:50] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -27.954427 | E_var:     0.1130 | E_err:   0.005253
[2025-10-06 19:43:54] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -27.948346 | E_var:     0.1679 | E_err:   0.006402
[2025-10-06 19:43:57] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -27.951532 | E_var:     0.1151 | E_err:   0.005302
[2025-10-06 19:44:01] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -27.946242 | E_var:     0.1329 | E_err:   0.005695
[2025-10-06 19:44:05] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -27.967305 | E_var:     0.1142 | E_err:   0.005280
[2025-10-06 19:44:08] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -27.957774 | E_var:     0.1394 | E_err:   0.005834
[2025-10-06 19:44:12] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -27.947505 | E_var:     0.1270 | E_err:   0.005569
[2025-10-06 19:44:16] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -27.950042 | E_var:     0.1228 | E_err:   0.005476
[2025-10-06 19:44:19] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -27.951854 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 19:44:23] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -27.966487 | E_var:     0.2341 | E_err:   0.007560
[2025-10-06 19:44:27] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -27.944118 | E_var:     0.1038 | E_err:   0.005034
[2025-10-06 19:44:30] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -27.953576 | E_var:     0.1087 | E_err:   0.005150
[2025-10-06 19:44:34] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -27.952426 | E_var:     0.1092 | E_err:   0.005164
[2025-10-06 19:44:38] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -27.954939 | E_var:     0.1181 | E_err:   0.005370
[2025-10-06 19:44:41] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -27.960482 | E_var:     0.1090 | E_err:   0.005160
[2025-10-06 19:44:45] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -27.942828 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 19:44:49] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -27.951334 | E_var:     0.1431 | E_err:   0.005910
[2025-10-06 19:44:52] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -27.956489 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 19:44:56] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -27.948504 | E_var:     0.1093 | E_err:   0.005165
[2025-10-06 19:45:00] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -27.961341 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 19:45:03] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -27.957174 | E_var:     0.1063 | E_err:   0.005095
[2025-10-06 19:45:07] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -27.959482 | E_var:     0.1582 | E_err:   0.006215
[2025-10-06 19:45:10] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -27.947683 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 19:45:14] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -27.957193 | E_var:     0.1289 | E_err:   0.005611
[2025-10-06 19:45:18] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -27.952739 | E_var:     0.1090 | E_err:   0.005158
[2025-10-06 19:45:21] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -27.953097 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 19:45:25] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -27.954037 | E_var:     0.1360 | E_err:   0.005761
[2025-10-06 19:45:29] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -27.960478 | E_var:     0.1087 | E_err:   0.005151
[2025-10-06 19:45:32] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -27.950220 | E_var:     0.1467 | E_err:   0.005986
[2025-10-06 19:45:36] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -27.957208 | E_var:     0.1056 | E_err:   0.005078
[2025-10-06 19:45:40] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -27.952180 | E_var:     0.1069 | E_err:   0.005110
[2025-10-06 19:45:43] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -27.958795 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 19:45:47] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -27.960557 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 19:45:51] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -27.954673 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 19:45:54] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -27.956711 | E_var:     0.1235 | E_err:   0.005491
[2025-10-06 19:45:58] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -27.950111 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 19:46:02] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -27.959529 | E_var:     0.1157 | E_err:   0.005316
[2025-10-06 19:46:05] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -27.954771 | E_var:     0.1504 | E_err:   0.006060
[2025-10-06 19:46:09] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -27.951782 | E_var:     0.0836 | E_err:   0.004518
[2025-10-06 19:46:13] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -27.973589 | E_var:     0.1466 | E_err:   0.005983
[2025-10-06 19:46:16] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -27.960031 | E_var:     0.1151 | E_err:   0.005302
[2025-10-06 19:46:20] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -27.953169 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 19:46:24] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -27.965702 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 19:46:27] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -27.958789 | E_var:     0.1481 | E_err:   0.006014
[2025-10-06 19:46:31] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -27.963851 | E_var:     0.3035 | E_err:   0.008608
[2025-10-06 19:46:34] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -27.959601 | E_var:     0.1030 | E_err:   0.005016
[2025-10-06 19:46:38] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -27.961327 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 19:46:42] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -27.951092 | E_var:     0.1366 | E_err:   0.005776
[2025-10-06 19:46:45] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -27.958767 | E_var:     0.1035 | E_err:   0.005028
[2025-10-06 19:46:49] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -27.963847 | E_var:     0.1184 | E_err:   0.005375
[2025-10-06 19:46:53] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -27.955133 | E_var:     0.1224 | E_err:   0.005468
[2025-10-06 19:46:56] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -27.950103 | E_var:     0.0989 | E_err:   0.004914
[2025-10-06 19:47:00] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -27.957360 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 19:47:04] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -27.953081 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 19:47:07] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -27.957177 | E_var:     0.0950 | E_err:   0.004815
[2025-10-06 19:47:11] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -27.963510 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 19:47:15] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -27.954199 | E_var:     0.1814 | E_err:   0.006654
[2025-10-06 19:47:18] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -27.950442 | E_var:     0.1031 | E_err:   0.005018
[2025-10-06 19:47:22] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -27.950128 | E_var:     0.0985 | E_err:   0.004903
[2025-10-06 19:47:26] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -27.960607 | E_var:     0.1153 | E_err:   0.005305
[2025-10-06 19:47:29] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -27.966272 | E_var:     0.1316 | E_err:   0.005668
[2025-10-06 19:47:33] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -27.959954 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 19:47:37] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -27.960177 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 19:47:40] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -27.954537 | E_var:     0.1518 | E_err:   0.006088
[2025-10-06 19:47:44] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -27.954223 | E_var:     0.1157 | E_err:   0.005315
[2025-10-06 19:47:47] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -27.951244 | E_var:     0.1078 | E_err:   0.005131
[2025-10-06 19:47:51] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -27.954627 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 19:47:55] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -27.961739 | E_var:     0.1300 | E_err:   0.005634
[2025-10-06 19:47:58] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -27.957205 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 19:48:02] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -27.960568 | E_var:     0.1225 | E_err:   0.005468
[2025-10-06 19:48:06] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -27.958171 | E_var:     0.1397 | E_err:   0.005840
[2025-10-06 19:48:09] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -27.956297 | E_var:     0.1192 | E_err:   0.005394
[2025-10-06 19:48:09] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 19:48:13] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -27.954284 | E_var:     0.1140 | E_err:   0.005276
[2025-10-06 19:48:17] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -27.954683 | E_var:     0.1178 | E_err:   0.005364
[2025-10-06 19:48:20] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -27.959364 | E_var:     0.0967 | E_err:   0.004858
[2025-10-06 19:48:24] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -27.955403 | E_var:     0.1045 | E_err:   0.005052
[2025-10-06 19:48:28] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -27.953347 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 19:48:31] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -27.957397 | E_var:     0.1169 | E_err:   0.005342
[2025-10-06 19:48:35] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -27.964698 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 19:48:39] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -27.956737 | E_var:     0.0906 | E_err:   0.004704
[2025-10-06 19:48:42] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -27.962888 | E_var:     0.1117 | E_err:   0.005222
[2025-10-06 19:48:46] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -27.950576 | E_var:     0.1514 | E_err:   0.006079
[2025-10-06 19:48:50] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -27.952687 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 19:48:53] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -27.954791 | E_var:     0.1275 | E_err:   0.005580
[2025-10-06 19:48:57] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -27.957139 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 19:49:01] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -27.955245 | E_var:     0.0921 | E_err:   0.004743
[2025-10-06 19:49:04] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -27.950835 | E_var:     0.1164 | E_err:   0.005331
[2025-10-06 19:49:08] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -27.965593 | E_var:     0.1272 | E_err:   0.005572
[2025-10-06 19:49:12] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -27.954737 | E_var:     0.1131 | E_err:   0.005256
[2025-10-06 19:49:15] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -27.948874 | E_var:     0.1261 | E_err:   0.005548
[2025-10-06 19:49:19] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -27.952880 | E_var:     0.1083 | E_err:   0.005143
[2025-10-06 19:49:22] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -27.951270 | E_var:     0.1124 | E_err:   0.005239
[2025-10-06 19:49:26] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -27.947191 | E_var:     0.1112 | E_err:   0.005211
[2025-10-06 19:49:30] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -27.953479 | E_var:     0.1378 | E_err:   0.005799
[2025-10-06 19:49:33] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -27.949447 | E_var:     0.1624 | E_err:   0.006296
[2025-10-06 19:49:37] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -27.962111 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 19:49:41] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -27.967069 | E_var:     0.1688 | E_err:   0.006420
[2025-10-06 19:49:44] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -27.959655 | E_var:     0.2676 | E_err:   0.008083
[2025-10-06 19:49:48] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -27.960385 | E_var:     0.2657 | E_err:   0.008054
[2025-10-06 19:49:52] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -27.960874 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 19:49:55] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -27.955851 | E_var:     0.0905 | E_err:   0.004700
[2025-10-06 19:49:59] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -27.952219 | E_var:     0.1011 | E_err:   0.004969
[2025-10-06 19:50:03] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -27.950119 | E_var:     0.1263 | E_err:   0.005552
[2025-10-06 19:50:06] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -27.953929 | E_var:     0.1391 | E_err:   0.005827
[2025-10-06 19:50:10] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -27.946743 | E_var:     0.1341 | E_err:   0.005721
[2025-10-06 19:50:14] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -27.954877 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 19:50:17] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -27.950316 | E_var:     0.1172 | E_err:   0.005350
[2025-10-06 19:50:21] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -27.962515 | E_var:     0.1254 | E_err:   0.005532
[2025-10-06 19:50:25] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -27.955342 | E_var:     0.1189 | E_err:   0.005389
[2025-10-06 19:50:28] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -27.957023 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 19:50:32] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -27.958627 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 19:50:36] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -27.947976 | E_var:     0.1891 | E_err:   0.006794
[2025-10-06 19:50:39] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -27.956003 | E_var:     0.1000 | E_err:   0.004942
[2025-10-06 19:50:43] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -27.956730 | E_var:     0.1313 | E_err:   0.005662
[2025-10-06 19:50:46] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -27.962149 | E_var:     0.1227 | E_err:   0.005474
[2025-10-06 19:50:50] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -27.962179 | E_var:     0.1158 | E_err:   0.005318
[2025-10-06 19:50:54] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -27.953292 | E_var:     0.1162 | E_err:   0.005327
[2025-10-06 19:50:57] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -27.956041 | E_var:     0.1244 | E_err:   0.005512
[2025-10-06 19:51:01] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -27.956193 | E_var:     0.1069 | E_err:   0.005109
[2025-10-06 19:51:05] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -27.963555 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 19:51:08] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -27.954541 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 19:51:12] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -27.950420 | E_var:     0.1201 | E_err:   0.005414
[2025-10-06 19:51:12] 🔄 RESTART #2 | Period: 600
[2025-10-06 19:51:16] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -27.954966 | E_var:     0.1049 | E_err:   0.005061
[2025-10-06 19:51:19] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -27.956422 | E_var:     0.0957 | E_err:   0.004835
[2025-10-06 19:51:23] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -27.954707 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 19:51:27] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -27.953810 | E_var:     0.1117 | E_err:   0.005221
[2025-10-06 19:51:30] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -27.949597 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 19:51:34] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -27.955377 | E_var:     0.0999 | E_err:   0.004937
[2025-10-06 19:51:38] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -27.952471 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 19:51:41] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -27.946286 | E_var:     0.1200 | E_err:   0.005412
[2025-10-06 19:51:45] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -27.948833 | E_var:     0.1201 | E_err:   0.005414
[2025-10-06 19:51:49] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -27.950769 | E_var:     0.1317 | E_err:   0.005670
[2025-10-06 19:51:52] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -27.954954 | E_var:     0.1012 | E_err:   0.004971
[2025-10-06 19:51:56] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -27.949014 | E_var:     0.1013 | E_err:   0.004974
[2025-10-06 19:51:59] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -27.953646 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 19:52:03] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -27.952962 | E_var:     0.0970 | E_err:   0.004867
[2025-10-06 19:52:07] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -27.959433 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 19:52:10] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -27.961014 | E_var:     0.1325 | E_err:   0.005687
[2025-10-06 19:52:14] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -27.955021 | E_var:     0.1433 | E_err:   0.005916
[2025-10-06 19:52:18] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -27.954727 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 19:52:21] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -27.957615 | E_var:     0.1319 | E_err:   0.005674
[2025-10-06 19:52:25] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -27.955803 | E_var:     0.0790 | E_err:   0.004393
[2025-10-06 19:52:29] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -27.956216 | E_var:     0.0980 | E_err:   0.004891
[2025-10-06 19:52:32] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -27.955440 | E_var:     0.0881 | E_err:   0.004639
[2025-10-06 19:52:36] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -27.951758 | E_var:     0.1167 | E_err:   0.005338
[2025-10-06 19:52:40] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -27.948887 | E_var:     0.1104 | E_err:   0.005192
[2025-10-06 19:52:43] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -27.956908 | E_var:     0.1117 | E_err:   0.005222
[2025-10-06 19:52:47] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -27.943697 | E_var:     0.1807 | E_err:   0.006642
[2025-10-06 19:52:51] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -27.964361 | E_var:     0.0930 | E_err:   0.004766
[2025-10-06 19:52:54] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -27.943004 | E_var:     0.1181 | E_err:   0.005371
[2025-10-06 19:52:58] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -27.950928 | E_var:     0.1102 | E_err:   0.005187
[2025-10-06 19:53:02] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -27.954812 | E_var:     0.1175 | E_err:   0.005356
[2025-10-06 19:53:05] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -27.953836 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 19:53:09] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -27.952726 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 19:53:12] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -27.948668 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 19:53:16] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -27.951987 | E_var:     0.1035 | E_err:   0.005028
[2025-10-06 19:53:20] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -27.957471 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 19:53:23] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -27.953117 | E_var:     0.1165 | E_err:   0.005333
[2025-10-06 19:53:27] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -27.960945 | E_var:     0.1278 | E_err:   0.005586
[2025-10-06 19:53:31] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -27.951786 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 19:53:34] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -27.950589 | E_var:     0.1170 | E_err:   0.005344
[2025-10-06 19:53:38] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -27.950660 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 19:53:42] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -27.954997 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 19:53:45] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -27.958696 | E_var:     0.0866 | E_err:   0.004599
[2025-10-06 19:53:49] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -27.944548 | E_var:     0.1375 | E_err:   0.005793
[2025-10-06 19:53:53] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -27.945611 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 19:53:56] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -27.963090 | E_var:     0.1644 | E_err:   0.006335
[2025-10-06 19:54:00] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -27.959981 | E_var:     0.1012 | E_err:   0.004971
[2025-10-06 19:54:04] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -27.961608 | E_var:     0.1835 | E_err:   0.006693
[2025-10-06 19:54:07] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -27.950529 | E_var:     0.1045 | E_err:   0.005050
[2025-10-06 19:54:11] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -27.968938 | E_var:     0.1216 | E_err:   0.005448
[2025-10-06 19:54:15] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -27.955458 | E_var:     0.1143 | E_err:   0.005282
[2025-10-06 19:54:15] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 19:54:18] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -27.957187 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 19:54:22] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -27.956196 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 19:54:26] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -27.959548 | E_var:     0.1151 | E_err:   0.005302
[2025-10-06 19:54:29] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -27.960666 | E_var:     0.0900 | E_err:   0.004688
[2025-10-06 19:54:33] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -27.955481 | E_var:     0.1018 | E_err:   0.004985
[2025-10-06 19:54:37] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -27.958832 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 19:54:40] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -27.953129 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 19:54:44] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -27.959602 | E_var:     0.1164 | E_err:   0.005330
[2025-10-06 19:54:48] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -27.952995 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 19:54:51] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -27.952213 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 19:54:55] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -27.953758 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 19:54:58] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -27.954177 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 19:55:02] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -27.951359 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 19:55:06] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -27.958174 | E_var:     0.0942 | E_err:   0.004796
[2025-10-06 19:55:09] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -27.953459 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 19:55:13] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -27.953494 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 19:55:17] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -27.955608 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 19:55:20] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -27.951457 | E_var:     0.1176 | E_err:   0.005358
[2025-10-06 19:55:24] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -27.957723 | E_var:     0.1606 | E_err:   0.006261
[2025-10-06 19:55:28] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -27.959946 | E_var:     0.1796 | E_err:   0.006621
[2025-10-06 19:55:31] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -27.945173 | E_var:     0.1114 | E_err:   0.005216
[2025-10-06 19:55:35] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -27.956072 | E_var:     0.1038 | E_err:   0.005033
[2025-10-06 19:55:39] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -27.953984 | E_var:     0.1031 | E_err:   0.005018
[2025-10-06 19:55:42] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -27.958004 | E_var:     0.0911 | E_err:   0.004715
[2025-10-06 19:55:46] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -27.961904 | E_var:     0.0952 | E_err:   0.004822
[2025-10-06 19:55:50] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -27.950740 | E_var:     0.1024 | E_err:   0.005000
[2025-10-06 19:55:53] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -27.961532 | E_var:     0.0955 | E_err:   0.004829
[2025-10-06 19:55:57] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -27.948729 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 19:56:01] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -27.954141 | E_var:     0.0975 | E_err:   0.004880
[2025-10-06 19:56:04] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -27.950106 | E_var:     0.1206 | E_err:   0.005425
[2025-10-06 19:56:08] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -27.945802 | E_var:     0.0983 | E_err:   0.004898
[2025-10-06 19:56:11] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -27.952014 | E_var:     0.1541 | E_err:   0.006133
[2025-10-06 19:56:15] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -27.943435 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 19:56:19] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -27.948155 | E_var:     0.1210 | E_err:   0.005436
[2025-10-06 19:56:22] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -27.953502 | E_var:     0.1115 | E_err:   0.005217
[2025-10-06 19:56:26] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -27.954166 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 19:56:30] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -27.953883 | E_var:     0.1173 | E_err:   0.005351
[2025-10-06 19:56:33] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -27.950257 | E_var:     0.0902 | E_err:   0.004694
[2025-10-06 19:56:37] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -27.950697 | E_var:     0.1366 | E_err:   0.005775
[2025-10-06 19:56:41] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -27.958102 | E_var:     0.0843 | E_err:   0.004538
[2025-10-06 19:56:44] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -27.953503 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 19:56:48] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -27.952225 | E_var:     0.1243 | E_err:   0.005510
[2025-10-06 19:56:52] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -27.951285 | E_var:     0.1346 | E_err:   0.005733
[2025-10-06 19:56:55] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -27.953454 | E_var:     0.1377 | E_err:   0.005797
[2025-10-06 19:56:59] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -27.956256 | E_var:     0.1169 | E_err:   0.005342
[2025-10-06 19:57:03] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -27.959650 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 19:57:06] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -27.966366 | E_var:     0.1047 | E_err:   0.005057
[2025-10-06 19:57:10] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -27.959864 | E_var:     0.1205 | E_err:   0.005423
[2025-10-06 19:57:13] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -27.965105 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 19:57:17] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -27.951571 | E_var:     0.1360 | E_err:   0.005763
[2025-10-06 19:57:21] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -27.963197 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 19:57:24] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -27.956637 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 19:57:28] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -27.955378 | E_var:     0.1273 | E_err:   0.005574
[2025-10-06 19:57:32] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -27.953469 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 19:57:35] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -27.951376 | E_var:     0.1109 | E_err:   0.005203
[2025-10-06 19:57:39] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -27.959551 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 19:57:43] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -27.953181 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 19:57:46] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -27.946984 | E_var:     0.1628 | E_err:   0.006304
[2025-10-06 19:57:50] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -27.959219 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 19:57:54] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -27.955065 | E_var:     0.0941 | E_err:   0.004794
[2025-10-06 19:57:57] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -27.955549 | E_var:     0.1154 | E_err:   0.005307
[2025-10-06 19:58:01] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -27.964020 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 19:58:05] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -27.955521 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 19:58:08] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -27.957241 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 19:58:12] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -27.958786 | E_var:     0.0977 | E_err:   0.004885
[2025-10-06 19:58:16] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -27.954070 | E_var:     0.1218 | E_err:   0.005452
[2025-10-06 19:58:19] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -27.953565 | E_var:     0.1195 | E_err:   0.005401
[2025-10-06 19:58:23] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -27.957226 | E_var:     0.1165 | E_err:   0.005332
[2025-10-06 19:58:27] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -27.957167 | E_var:     0.0968 | E_err:   0.004862
[2025-10-06 19:58:30] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -27.953352 | E_var:     0.1365 | E_err:   0.005772
[2025-10-06 19:58:34] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -27.951219 | E_var:     0.1234 | E_err:   0.005490
[2025-10-06 19:58:37] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -27.950356 | E_var:     0.1179 | E_err:   0.005364
[2025-10-06 19:58:41] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -27.942715 | E_var:     0.1433 | E_err:   0.005914
[2025-10-06 19:58:45] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -27.956466 | E_var:     0.1153 | E_err:   0.005306
[2025-10-06 19:58:48] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -27.953637 | E_var:     0.1251 | E_err:   0.005526
[2025-10-06 19:58:52] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -27.951929 | E_var:     0.0981 | E_err:   0.004894
[2025-10-06 19:58:56] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -27.952384 | E_var:     0.1929 | E_err:   0.006862
[2025-10-06 19:58:59] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -27.951667 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 19:59:03] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -27.954268 | E_var:     0.1270 | E_err:   0.005568
[2025-10-06 19:59:07] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -27.954671 | E_var:     0.1131 | E_err:   0.005256
[2025-10-06 19:59:10] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -27.956524 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 19:59:14] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -27.957537 | E_var:     0.1111 | E_err:   0.005209
[2025-10-06 19:59:18] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -27.953422 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 19:59:21] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -27.954372 | E_var:     0.1094 | E_err:   0.005168
[2025-10-06 19:59:25] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -27.954271 | E_var:     0.1171 | E_err:   0.005348
[2025-10-06 19:59:29] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -27.952495 | E_var:     0.0966 | E_err:   0.004856
[2025-10-06 19:59:32] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -27.962322 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 19:59:36] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -27.965554 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 19:59:40] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -27.949882 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 19:59:43] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -27.954606 | E_var:     0.0956 | E_err:   0.004830
[2025-10-06 19:59:47] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -27.961562 | E_var:     0.1697 | E_err:   0.006438
[2025-10-06 19:59:50] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -27.962593 | E_var:     0.1052 | E_err:   0.005067
[2025-10-06 19:59:54] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -27.948805 | E_var:     0.1119 | E_err:   0.005228
[2025-10-06 19:59:58] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -27.958199 | E_var:     0.0985 | E_err:   0.004903
[2025-10-06 20:00:01] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -27.958398 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 20:00:05] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -27.951460 | E_var:     0.1241 | E_err:   0.005503
[2025-10-06 20:00:09] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -27.952745 | E_var:     0.0997 | E_err:   0.004935
[2025-10-06 20:00:12] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -27.943385 | E_var:     0.1055 | E_err:   0.005075
[2025-10-06 20:00:16] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -27.950657 | E_var:     0.1086 | E_err:   0.005148
[2025-10-06 20:00:20] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -27.958246 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 20:00:20] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 20:00:24] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -27.959836 | E_var:     0.1938 | E_err:   0.006878
[2025-10-06 20:00:27] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -27.950520 | E_var:     0.1233 | E_err:   0.005486
[2025-10-06 20:00:31] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -27.953493 | E_var:     0.1637 | E_err:   0.006322
[2025-10-06 20:00:34] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -27.951214 | E_var:     0.1596 | E_err:   0.006241
[2025-10-06 20:00:38] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -27.960041 | E_var:     0.1273 | E_err:   0.005575
[2025-10-06 20:00:42] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -27.960539 | E_var:     0.1001 | E_err:   0.004944
[2025-10-06 20:00:45] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -27.959463 | E_var:     0.1055 | E_err:   0.005074
[2025-10-06 20:00:49] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -27.953273 | E_var:     0.1038 | E_err:   0.005035
[2025-10-06 20:00:53] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -27.956407 | E_var:     0.0915 | E_err:   0.004727
[2025-10-06 20:00:56] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -27.958698 | E_var:     0.1491 | E_err:   0.006033
[2025-10-06 20:01:00] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -27.954643 | E_var:     0.1074 | E_err:   0.005120
[2025-10-06 20:01:04] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -27.959405 | E_var:     0.0965 | E_err:   0.004853
[2025-10-06 20:01:07] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -27.957578 | E_var:     0.1338 | E_err:   0.005714
[2025-10-06 20:01:11] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -27.956591 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 20:01:15] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -27.960375 | E_var:     0.1126 | E_err:   0.005243
[2025-10-06 20:01:18] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -27.956195 | E_var:     0.0859 | E_err:   0.004580
[2025-10-06 20:01:22] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -27.954690 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 20:01:26] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -27.965265 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 20:01:29] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -27.953235 | E_var:     0.0964 | E_err:   0.004852
[2025-10-06 20:01:33] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -27.960803 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 20:01:36] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -27.958508 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 20:01:40] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -27.966192 | E_var:     0.1898 | E_err:   0.006808
[2025-10-06 20:01:44] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -27.962944 | E_var:     0.1131 | E_err:   0.005254
[2025-10-06 20:01:47] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -27.954458 | E_var:     0.1049 | E_err:   0.005060
[2025-10-06 20:01:51] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -27.952262 | E_var:     0.1365 | E_err:   0.005773
[2025-10-06 20:01:55] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -27.957909 | E_var:     0.1706 | E_err:   0.006454
[2025-10-06 20:01:58] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -27.957905 | E_var:     0.1070 | E_err:   0.005111
[2025-10-06 20:02:02] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -27.967977 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 20:02:06] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -27.953837 | E_var:     0.1498 | E_err:   0.006047
[2025-10-06 20:02:09] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -27.941998 | E_var:     0.1198 | E_err:   0.005408
[2025-10-06 20:02:13] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -27.958839 | E_var:     0.1368 | E_err:   0.005779
[2025-10-06 20:02:17] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -27.960078 | E_var:     0.1265 | E_err:   0.005557
[2025-10-06 20:02:20] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -27.956718 | E_var:     0.1070 | E_err:   0.005111
[2025-10-06 20:02:24] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -27.947806 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 20:02:28] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -27.956587 | E_var:     0.0906 | E_err:   0.004702
[2025-10-06 20:02:31] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -27.949776 | E_var:     0.1029 | E_err:   0.005011
[2025-10-06 20:02:35] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -27.957952 | E_var:     0.1431 | E_err:   0.005910
[2025-10-06 20:02:38] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -27.957847 | E_var:     0.1065 | E_err:   0.005098
[2025-10-06 20:02:42] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -27.952539 | E_var:     0.1414 | E_err:   0.005875
[2025-10-06 20:02:46] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -27.950611 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 20:02:49] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -27.958167 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 20:02:53] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -27.961792 | E_var:     0.1009 | E_err:   0.004962
[2025-10-06 20:02:57] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -27.948596 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 20:03:00] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -27.954622 | E_var:     0.1123 | E_err:   0.005237
[2025-10-06 20:03:04] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -27.963157 | E_var:     0.1136 | E_err:   0.005267
[2025-10-06 20:03:08] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -27.952942 | E_var:     0.1085 | E_err:   0.005146
[2025-10-06 20:03:11] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -27.961522 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 20:03:15] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -27.945952 | E_var:     0.1602 | E_err:   0.006254
[2025-10-06 20:03:19] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -27.958477 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 20:03:22] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -27.960670 | E_var:     0.1205 | E_err:   0.005425
[2025-10-06 20:03:26] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -27.946933 | E_var:     0.1026 | E_err:   0.005005
[2025-10-06 20:03:30] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -27.962267 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 20:03:33] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -27.947308 | E_var:     0.0970 | E_err:   0.004867
[2025-10-06 20:03:37] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -27.947208 | E_var:     0.1162 | E_err:   0.005326
[2025-10-06 20:03:41] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -27.954314 | E_var:     0.0995 | E_err:   0.004928
[2025-10-06 20:03:44] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -27.964581 | E_var:     0.5585 | E_err:   0.011677
[2025-10-06 20:03:48] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -27.948756 | E_var:     0.1440 | E_err:   0.005929
[2025-10-06 20:03:51] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -27.961298 | E_var:     0.1829 | E_err:   0.006682
[2025-10-06 20:03:55] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -27.946870 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 20:03:59] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -27.947589 | E_var:     0.1833 | E_err:   0.006690
[2025-10-06 20:04:02] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -27.959934 | E_var:     0.1240 | E_err:   0.005503
[2025-10-06 20:04:06] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -27.953950 | E_var:     0.0947 | E_err:   0.004807
[2025-10-06 20:04:10] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -27.949149 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 20:04:13] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -27.954216 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 20:04:17] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -27.959515 | E_var:     0.1110 | E_err:   0.005205
[2025-10-06 20:04:21] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -27.951905 | E_var:     0.1087 | E_err:   0.005152
[2025-10-06 20:04:24] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -27.956481 | E_var:     0.1092 | E_err:   0.005164
[2025-10-06 20:04:28] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -27.955699 | E_var:     0.1318 | E_err:   0.005672
[2025-10-06 20:04:32] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -27.955861 | E_var:     0.1287 | E_err:   0.005605
[2025-10-06 20:04:35] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -27.959235 | E_var:     0.1131 | E_err:   0.005254
[2025-10-06 20:04:39] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -27.969459 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 20:04:43] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -27.955878 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 20:04:46] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -27.950248 | E_var:     0.1427 | E_err:   0.005903
[2025-10-06 20:04:50] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -27.964938 | E_var:     0.0805 | E_err:   0.004434
[2025-10-06 20:04:54] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -27.966302 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 20:04:57] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -27.955817 | E_var:     0.0978 | E_err:   0.004886
[2025-10-06 20:05:01] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -27.960799 | E_var:     0.1240 | E_err:   0.005503
[2025-10-06 20:05:05] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -27.958612 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 20:05:08] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -27.957756 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 20:05:12] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -27.952808 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 20:05:15] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -27.950728 | E_var:     0.1190 | E_err:   0.005389
[2025-10-06 20:05:19] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -27.953511 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 20:05:23] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -27.957910 | E_var:     0.0953 | E_err:   0.004825
[2025-10-06 20:05:26] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -27.958297 | E_var:     0.1277 | E_err:   0.005583
[2025-10-06 20:05:30] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -27.951853 | E_var:     0.1176 | E_err:   0.005358
[2025-10-06 20:05:34] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -27.960438 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 20:05:37] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -27.959013 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 20:05:41] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -27.960464 | E_var:     0.1161 | E_err:   0.005324
[2025-10-06 20:05:45] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -27.958938 | E_var:     0.5100 | E_err:   0.011158
[2025-10-06 20:05:48] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -27.954796 | E_var:     0.0926 | E_err:   0.004754
[2025-10-06 20:05:52] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -27.950654 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 20:05:56] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -27.958727 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 20:05:59] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -27.956451 | E_var:     0.0925 | E_err:   0.004751
[2025-10-06 20:06:03] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -27.981998 | E_var:     4.4110 | E_err:   0.032816
[2025-10-06 20:06:07] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -27.943063 | E_var:     0.2138 | E_err:   0.007225
[2025-10-06 20:06:10] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -27.966717 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 20:06:14] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -27.961964 | E_var:     0.3616 | E_err:   0.009396
[2025-10-06 20:06:17] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -27.965442 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 20:06:21] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -27.965553 | E_var:     0.1011 | E_err:   0.004968
[2025-10-06 20:06:25] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -27.961256 | E_var:     0.1070 | E_err:   0.005111
[2025-10-06 20:06:25] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 20:06:29] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -27.952823 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 20:06:32] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -27.953028 | E_var:     0.1119 | E_err:   0.005228
[2025-10-06 20:06:36] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -27.957881 | E_var:     0.1211 | E_err:   0.005437
[2025-10-06 20:06:39] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -27.948974 | E_var:     0.1177 | E_err:   0.005360
[2025-10-06 20:06:43] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -27.961104 | E_var:     0.0993 | E_err:   0.004925
[2025-10-06 20:06:47] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -27.954399 | E_var:     0.1180 | E_err:   0.005368
[2025-10-06 20:06:50] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -27.956996 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 20:06:54] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -27.955139 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 20:06:58] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -27.960490 | E_var:     0.1116 | E_err:   0.005219
[2025-10-06 20:07:01] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -27.952097 | E_var:     0.1004 | E_err:   0.004950
[2025-10-06 20:07:05] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -27.962399 | E_var:     0.1566 | E_err:   0.006183
[2025-10-06 20:07:09] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -27.959011 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 20:07:12] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -27.956824 | E_var:     0.1184 | E_err:   0.005376
[2025-10-06 20:07:16] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -27.964527 | E_var:     0.1287 | E_err:   0.005604
[2025-10-06 20:07:20] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -27.952883 | E_var:     0.2238 | E_err:   0.007393
[2025-10-06 20:07:23] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -27.955461 | E_var:     0.0759 | E_err:   0.004306
[2025-10-06 20:07:27] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -27.948864 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 20:07:31] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -27.956845 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 20:07:34] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -27.961076 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 20:07:38] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -27.950177 | E_var:     0.1135 | E_err:   0.005263
[2025-10-06 20:07:41] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -27.953742 | E_var:     0.1201 | E_err:   0.005415
[2025-10-06 20:07:45] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -27.955390 | E_var:     0.1177 | E_err:   0.005359
[2025-10-06 20:07:49] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -27.956536 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 20:07:52] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -27.963659 | E_var:     0.2345 | E_err:   0.007566
[2025-10-06 20:07:56] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -27.949078 | E_var:     0.1213 | E_err:   0.005441
[2025-10-06 20:08:00] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -27.955479 | E_var:     0.1074 | E_err:   0.005120
[2025-10-06 20:08:03] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -27.957424 | E_var:     0.1166 | E_err:   0.005334
[2025-10-06 20:08:07] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -27.953023 | E_var:     0.1212 | E_err:   0.005441
[2025-10-06 20:08:11] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -27.953884 | E_var:     0.0997 | E_err:   0.004933
[2025-10-06 20:08:14] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -27.948152 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 20:08:18] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -27.956951 | E_var:     0.1068 | E_err:   0.005105
[2025-10-06 20:08:22] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -27.950112 | E_var:     0.0969 | E_err:   0.004863
[2025-10-06 20:08:25] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -27.950596 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 20:08:29] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -27.960884 | E_var:     0.1005 | E_err:   0.004952
[2025-10-06 20:08:33] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -27.958721 | E_var:     0.1302 | E_err:   0.005639
[2025-10-06 20:08:36] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -27.956917 | E_var:     0.1397 | E_err:   0.005841
[2025-10-06 20:08:40] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -27.963626 | E_var:     0.1307 | E_err:   0.005649
[2025-10-06 20:08:43] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -27.959875 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 20:08:47] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -27.961226 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 20:08:51] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -27.947312 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 20:08:54] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -27.955599 | E_var:     0.1035 | E_err:   0.005027
[2025-10-06 20:08:58] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -27.961185 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 20:09:02] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -27.960740 | E_var:     0.0872 | E_err:   0.004615
[2025-10-06 20:09:05] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -27.950023 | E_var:     0.0951 | E_err:   0.004819
[2025-10-06 20:09:09] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -27.959786 | E_var:     0.1772 | E_err:   0.006577
[2025-10-06 20:09:13] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -27.954538 | E_var:     0.0878 | E_err:   0.004630
[2025-10-06 20:09:16] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -27.961421 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 20:09:20] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -27.947896 | E_var:     0.2083 | E_err:   0.007131
[2025-10-06 20:09:24] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -27.950651 | E_var:     0.1144 | E_err:   0.005284
[2025-10-06 20:09:27] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -27.951122 | E_var:     0.1186 | E_err:   0.005380
[2025-10-06 20:09:31] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -27.951333 | E_var:     0.0972 | E_err:   0.004870
[2025-10-06 20:09:35] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -27.949917 | E_var:     0.1050 | E_err:   0.005064
[2025-10-06 20:09:38] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -27.957490 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 20:09:42] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -27.957485 | E_var:     0.1201 | E_err:   0.005414
[2025-10-06 20:09:45] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -27.945683 | E_var:     0.1089 | E_err:   0.005156
[2025-10-06 20:09:49] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -27.956484 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 20:09:53] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -27.950274 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 20:09:56] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -27.965353 | E_var:     0.1210 | E_err:   0.005436
[2025-10-06 20:10:00] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -27.958911 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 20:10:04] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -27.954975 | E_var:     0.0971 | E_err:   0.004868
[2025-10-06 20:10:07] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -27.956278 | E_var:     0.1005 | E_err:   0.004953
[2025-10-06 20:10:11] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -27.961305 | E_var:     0.1437 | E_err:   0.005923
[2025-10-06 20:10:15] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -27.944572 | E_var:     0.1932 | E_err:   0.006868
[2025-10-06 20:10:18] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -27.955924 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 20:10:22] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -27.955150 | E_var:     0.1080 | E_err:   0.005134
[2025-10-06 20:10:26] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -27.954730 | E_var:     0.0959 | E_err:   0.004839
[2025-10-06 20:10:29] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -27.947118 | E_var:     0.1298 | E_err:   0.005630
[2025-10-06 20:10:33] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -27.964610 | E_var:     0.0963 | E_err:   0.004850
[2025-10-06 20:10:37] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -27.955346 | E_var:     0.1038 | E_err:   0.005035
[2025-10-06 20:10:40] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -27.961740 | E_var:     0.1744 | E_err:   0.006525
[2025-10-06 20:10:44] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -27.959432 | E_var:     0.1014 | E_err:   0.004977
[2025-10-06 20:10:47] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -27.954240 | E_var:     0.1069 | E_err:   0.005109
[2025-10-06 20:10:51] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -27.959604 | E_var:     0.1413 | E_err:   0.005874
[2025-10-06 20:10:55] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -27.951393 | E_var:     0.0865 | E_err:   0.004595
[2025-10-06 20:10:58] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -27.952374 | E_var:     0.1252 | E_err:   0.005529
[2025-10-06 20:11:02] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -27.957585 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 20:11:06] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -27.957539 | E_var:     0.1045 | E_err:   0.005050
[2025-10-06 20:11:09] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -27.956724 | E_var:     0.1106 | E_err:   0.005197
[2025-10-06 20:11:13] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -27.963282 | E_var:     0.1131 | E_err:   0.005256
[2025-10-06 20:11:17] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -27.963220 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 20:11:20] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -27.943345 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 20:11:24] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -27.955385 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 20:11:28] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -27.949692 | E_var:     0.1214 | E_err:   0.005443
[2025-10-06 20:11:31] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -27.952375 | E_var:     0.1683 | E_err:   0.006410
[2025-10-06 20:11:35] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -27.954299 | E_var:     0.0983 | E_err:   0.004900
[2025-10-06 20:11:39] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -27.956468 | E_var:     0.1085 | E_err:   0.005148
[2025-10-06 20:11:42] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -27.953140 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 20:11:46] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -27.955194 | E_var:     0.1856 | E_err:   0.006731
[2025-10-06 20:11:49] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -27.953607 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 20:11:53] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -27.945201 | E_var:     0.1345 | E_err:   0.005730
[2025-10-06 20:11:57] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -27.962388 | E_var:     0.1312 | E_err:   0.005659
[2025-10-06 20:12:00] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -27.958613 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 20:12:04] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -27.954671 | E_var:     0.1007 | E_err:   0.004959
[2025-10-06 20:12:08] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -27.957696 | E_var:     0.0930 | E_err:   0.004766
[2025-10-06 20:12:11] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -27.952350 | E_var:     0.1049 | E_err:   0.005060
[2025-10-06 20:12:15] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -27.957945 | E_var:     0.1153 | E_err:   0.005306
[2025-10-06 20:12:19] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -27.959544 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 20:12:22] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -27.952047 | E_var:     0.1755 | E_err:   0.006546
[2025-10-06 20:12:26] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -27.951746 | E_var:     0.1035 | E_err:   0.005028
[2025-10-06 20:12:30] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -27.953057 | E_var:     0.1018 | E_err:   0.004985
[2025-10-06 20:12:30] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 20:12:33] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -27.952332 | E_var:     0.1155 | E_err:   0.005310
[2025-10-06 20:12:37] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -27.954032 | E_var:     0.1288 | E_err:   0.005608
[2025-10-06 20:12:41] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -27.954118 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 20:12:44] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -27.963474 | E_var:     0.1023 | E_err:   0.004997
[2025-10-06 20:12:48] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -27.942053 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 20:12:51] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -27.956932 | E_var:     0.1255 | E_err:   0.005536
[2025-10-06 20:12:55] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -27.961380 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 20:12:59] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -27.959470 | E_var:     0.1135 | E_err:   0.005264
[2025-10-06 20:13:02] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -27.957106 | E_var:     0.1150 | E_err:   0.005299
[2025-10-06 20:13:06] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -27.956593 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 20:13:10] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -27.952385 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 20:13:13] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -27.973006 | E_var:     0.2188 | E_err:   0.007308
[2025-10-06 20:13:17] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -27.949653 | E_var:     0.1211 | E_err:   0.005438
[2025-10-06 20:13:21] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -27.950042 | E_var:     0.1007 | E_err:   0.004957
[2025-10-06 20:13:24] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -27.954952 | E_var:     0.1351 | E_err:   0.005742
[2025-10-06 20:13:28] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -27.960091 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 20:13:32] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -27.961872 | E_var:     0.1805 | E_err:   0.006639
[2025-10-06 20:13:35] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -27.954328 | E_var:     0.1426 | E_err:   0.005901
[2025-10-06 20:13:39] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -27.960367 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 20:13:43] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -27.961079 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 20:13:46] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -27.968232 | E_var:     0.1246 | E_err:   0.005516
[2025-10-06 20:13:50] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -27.952404 | E_var:     0.1386 | E_err:   0.005816
[2025-10-06 20:13:54] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -27.959411 | E_var:     0.1376 | E_err:   0.005797
[2025-10-06 20:13:57] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -27.961957 | E_var:     0.1209 | E_err:   0.005433
[2025-10-06 20:14:01] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -27.962774 | E_var:     0.1579 | E_err:   0.006210
[2025-10-06 20:14:04] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -27.958319 | E_var:     0.1084 | E_err:   0.005145
[2025-10-06 20:14:08] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -27.956737 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 20:14:12] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -27.962176 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 20:14:15] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -27.958475 | E_var:     0.1166 | E_err:   0.005336
[2025-10-06 20:14:19] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -27.964826 | E_var:     0.1172 | E_err:   0.005350
[2025-10-06 20:14:23] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -27.950543 | E_var:     0.1267 | E_err:   0.005561
[2025-10-06 20:14:26] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -27.957807 | E_var:     0.0893 | E_err:   0.004668
[2025-10-06 20:14:30] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -27.954499 | E_var:     0.2018 | E_err:   0.007019
[2025-10-06 20:14:34] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -27.958154 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 20:14:37] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -27.964298 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 20:14:41] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -27.958169 | E_var:     0.1575 | E_err:   0.006202
[2025-10-06 20:14:45] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -27.964307 | E_var:     0.4225 | E_err:   0.010156
[2025-10-06 20:14:48] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -27.956778 | E_var:     0.1021 | E_err:   0.004994
[2025-10-06 20:14:52] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -27.948677 | E_var:     0.1322 | E_err:   0.005682
[2025-10-06 20:14:56] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -27.965587 | E_var:     0.1043 | E_err:   0.005046
[2025-10-06 20:14:59] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -27.953229 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 20:15:03] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -27.953365 | E_var:     0.1190 | E_err:   0.005389
[2025-10-06 20:15:06] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -27.958090 | E_var:     0.6446 | E_err:   0.012544
[2025-10-06 20:15:10] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -27.954858 | E_var:     0.1415 | E_err:   0.005877
[2025-10-06 20:15:14] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -27.960886 | E_var:     0.0896 | E_err:   0.004678
[2025-10-06 20:15:17] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -27.958674 | E_var:     0.1019 | E_err:   0.004988
[2025-10-06 20:15:21] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -27.960287 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 20:15:25] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -27.953120 | E_var:     0.1826 | E_err:   0.006677
[2025-10-06 20:15:28] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -27.955182 | E_var:     0.1186 | E_err:   0.005380
[2025-10-06 20:15:32] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -27.962288 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 20:15:36] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -27.953552 | E_var:     0.4716 | E_err:   0.010730
[2025-10-06 20:15:39] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -27.952331 | E_var:     0.1213 | E_err:   0.005442
[2025-10-06 20:15:43] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -27.944971 | E_var:     0.1102 | E_err:   0.005188
[2025-10-06 20:15:47] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -27.951523 | E_var:     0.1582 | E_err:   0.006214
[2025-10-06 20:15:50] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -27.957042 | E_var:     0.1798 | E_err:   0.006626
[2025-10-06 20:15:54] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -27.960598 | E_var:     0.1407 | E_err:   0.005861
[2025-10-06 20:15:58] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -27.961708 | E_var:     0.1195 | E_err:   0.005402
[2025-10-06 20:16:01] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -27.953008 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 20:16:05] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -27.956464 | E_var:     0.1551 | E_err:   0.006154
[2025-10-06 20:16:08] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -27.961815 | E_var:     0.1492 | E_err:   0.006035
[2025-10-06 20:16:12] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -27.960739 | E_var:     0.1232 | E_err:   0.005485
[2025-10-06 20:16:16] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -27.960510 | E_var:     0.1132 | E_err:   0.005258
[2025-10-06 20:16:19] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -27.947816 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 20:16:23] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -27.961569 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 20:16:27] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -27.952321 | E_var:     0.1751 | E_err:   0.006539
[2025-10-06 20:16:30] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -27.948952 | E_var:     0.0909 | E_err:   0.004710
[2025-10-06 20:16:34] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -27.958403 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 20:16:38] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -27.958382 | E_var:     0.1256 | E_err:   0.005537
[2025-10-06 20:16:41] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -27.948078 | E_var:     0.1081 | E_err:   0.005136
[2025-10-06 20:16:45] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -27.962153 | E_var:     0.1240 | E_err:   0.005501
[2025-10-06 20:16:49] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -27.963777 | E_var:     0.1514 | E_err:   0.006080
[2025-10-06 20:16:52] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -27.958910 | E_var:     0.1294 | E_err:   0.005621
[2025-10-06 20:16:56] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -27.958753 | E_var:     0.1195 | E_err:   0.005401
[2025-10-06 20:17:00] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -27.961597 | E_var:     0.1303 | E_err:   0.005641
[2025-10-06 20:17:03] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -27.959248 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 20:17:07] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -27.966433 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 20:17:10] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -27.958914 | E_var:     0.1218 | E_err:   0.005452
[2025-10-06 20:17:14] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -27.956128 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 20:17:18] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -27.951628 | E_var:     0.1472 | E_err:   0.005995
[2025-10-06 20:17:21] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -27.958372 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 20:17:25] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -27.955902 | E_var:     0.0889 | E_err:   0.004660
[2025-10-06 20:17:29] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -27.959669 | E_var:     0.0980 | E_err:   0.004892
[2025-10-06 20:17:32] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -27.956561 | E_var:     0.1389 | E_err:   0.005824
[2025-10-06 20:17:36] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -27.949734 | E_var:     0.1507 | E_err:   0.006065
[2025-10-06 20:17:40] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -27.964895 | E_var:     0.1108 | E_err:   0.005202
[2025-10-06 20:17:43] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -27.960445 | E_var:     0.1482 | E_err:   0.006015
[2025-10-06 20:17:47] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -27.958556 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 20:17:51] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -27.943878 | E_var:     0.0866 | E_err:   0.004599
[2025-10-06 20:17:54] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -27.958483 | E_var:     0.3522 | E_err:   0.009273
[2025-10-06 20:17:58] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -27.956223 | E_var:     0.1114 | E_err:   0.005216
[2025-10-06 20:18:02] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -27.952649 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 20:18:05] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -27.957121 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 20:18:09] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -27.948408 | E_var:     0.1137 | E_err:   0.005269
[2025-10-06 20:18:12] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -27.953887 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 20:18:16] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -27.951758 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 20:18:20] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -27.958111 | E_var:     0.1031 | E_err:   0.005017
[2025-10-06 20:18:23] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -27.952717 | E_var:     0.0834 | E_err:   0.004511
[2025-10-06 20:18:27] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -27.962049 | E_var:     0.2099 | E_err:   0.007158
[2025-10-06 20:18:31] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -27.957890 | E_var:     0.1379 | E_err:   0.005802
[2025-10-06 20:18:34] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -27.965314 | E_var:     0.0981 | E_err:   0.004893
[2025-10-06 20:18:34] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 20:18:38] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -27.955701 | E_var:     0.4979 | E_err:   0.011025
[2025-10-06 20:18:42] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -27.954839 | E_var:     0.0855 | E_err:   0.004568
[2025-10-06 20:18:45] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -27.954229 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 20:18:49] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -27.951056 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 20:18:53] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -27.951835 | E_var:     0.1153 | E_err:   0.005305
[2025-10-06 20:18:56] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -27.956024 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 20:19:00] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -27.950885 | E_var:     0.0941 | E_err:   0.004793
[2025-10-06 20:19:04] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -27.961652 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 20:19:07] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -27.959430 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 20:19:11] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -27.957152 | E_var:     0.1073 | E_err:   0.005117
[2025-10-06 20:19:15] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -27.955844 | E_var:     0.0892 | E_err:   0.004668
[2025-10-06 20:19:18] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -27.948844 | E_var:     0.2110 | E_err:   0.007177
[2025-10-06 20:19:22] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -27.956245 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 20:19:26] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -27.960439 | E_var:     0.1272 | E_err:   0.005572
[2025-10-06 20:19:29] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -27.960482 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 20:19:33] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -27.959944 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 20:19:36] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -27.955864 | E_var:     0.0991 | E_err:   0.004918
[2025-10-06 20:19:40] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -27.948265 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 20:19:44] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -27.950791 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 20:19:47] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -27.953679 | E_var:     0.1175 | E_err:   0.005355
[2025-10-06 20:19:51] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -27.952863 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 20:19:55] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -27.962897 | E_var:     0.1089 | E_err:   0.005157
[2025-10-06 20:19:58] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -27.951528 | E_var:     0.0981 | E_err:   0.004894
[2025-10-06 20:20:02] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -27.953889 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 20:20:06] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -27.954753 | E_var:     0.0998 | E_err:   0.004935
[2025-10-06 20:20:09] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -27.952960 | E_var:     0.0997 | E_err:   0.004933
[2025-10-06 20:20:13] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -27.952629 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 20:20:17] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -27.954656 | E_var:     0.1074 | E_err:   0.005122
[2025-10-06 20:20:20] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -27.951913 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 20:20:24] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -27.954342 | E_var:     0.2279 | E_err:   0.007459
[2025-10-06 20:20:28] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -27.955739 | E_var:     0.1160 | E_err:   0.005322
[2025-10-06 20:20:31] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -27.962807 | E_var:     0.1431 | E_err:   0.005911
[2025-10-06 20:20:35] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -27.955374 | E_var:     0.1157 | E_err:   0.005314
[2025-10-06 20:20:38] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -27.963508 | E_var:     0.1021 | E_err:   0.004994
[2025-10-06 20:20:42] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -27.956577 | E_var:     0.0921 | E_err:   0.004742
[2025-10-06 20:20:46] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -27.960465 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 20:20:49] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -27.954345 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 20:20:53] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -27.963711 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 20:20:57] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -27.965589 | E_var:     0.1658 | E_err:   0.006362
[2025-10-06 20:21:00] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -27.967028 | E_var:     0.2840 | E_err:   0.008327
[2025-10-06 20:21:04] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -27.960364 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 20:21:08] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -27.959651 | E_var:     0.1286 | E_err:   0.005604
[2025-10-06 20:21:11] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -27.955201 | E_var:     0.1179 | E_err:   0.005365
[2025-10-06 20:21:15] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -27.955175 | E_var:     0.1098 | E_err:   0.005178
[2025-10-06 20:21:19] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -27.951759 | E_var:     0.0984 | E_err:   0.004900
[2025-10-06 20:21:22] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -27.958516 | E_var:     0.1063 | E_err:   0.005095
[2025-10-06 20:21:26] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -27.954798 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 20:21:30] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -27.959522 | E_var:     0.0938 | E_err:   0.004786
[2025-10-06 20:21:33] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -27.959672 | E_var:     0.0928 | E_err:   0.004761
[2025-10-06 20:21:37] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -27.956863 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 20:21:40] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -27.950152 | E_var:     0.1379 | E_err:   0.005803
[2025-10-06 20:21:44] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -27.957345 | E_var:     0.0994 | E_err:   0.004926
[2025-10-06 20:21:48] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -27.959799 | E_var:     0.1091 | E_err:   0.005162
[2025-10-06 20:21:51] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -27.957752 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 20:21:55] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -27.964926 | E_var:     0.1181 | E_err:   0.005371
[2025-10-06 20:21:59] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -27.952618 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 20:22:02] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -27.959015 | E_var:     0.1420 | E_err:   0.005889
[2025-10-06 20:22:06] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -27.953074 | E_var:     0.1384 | E_err:   0.005813
[2025-10-06 20:22:10] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -27.946439 | E_var:     0.1173 | E_err:   0.005351
[2025-10-06 20:22:13] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -27.957479 | E_var:     0.1121 | E_err:   0.005231
[2025-10-06 20:22:17] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -27.959974 | E_var:     0.0979 | E_err:   0.004889
[2025-10-06 20:22:21] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -27.956502 | E_var:     0.0867 | E_err:   0.004601
[2025-10-06 20:22:24] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -27.948952 | E_var:     0.1454 | E_err:   0.005958
[2025-10-06 20:22:28] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -27.952235 | E_var:     0.1017 | E_err:   0.004982
[2025-10-06 20:22:32] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -27.959692 | E_var:     0.0969 | E_err:   0.004865
[2025-10-06 20:22:35] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -27.962951 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 20:22:39] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -27.964371 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 20:22:42] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -27.944861 | E_var:     0.0969 | E_err:   0.004863
[2025-10-06 20:22:46] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -27.953793 | E_var:     0.0967 | E_err:   0.004860
[2025-10-06 20:22:50] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -27.962231 | E_var:     0.1035 | E_err:   0.005026
[2025-10-06 20:22:53] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -27.955188 | E_var:     0.1043 | E_err:   0.005047
[2025-10-06 20:22:57] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -27.962327 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 20:23:01] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -27.960672 | E_var:     0.0975 | E_err:   0.004879
[2025-10-06 20:23:04] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -27.951131 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 20:23:08] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -27.947094 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 20:23:12] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -27.962924 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 20:23:15] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -27.950594 | E_var:     0.0963 | E_err:   0.004849
[2025-10-06 20:23:19] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -27.955070 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 20:23:23] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -27.953059 | E_var:     0.0824 | E_err:   0.004486
[2025-10-06 20:23:26] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -27.966446 | E_var:     0.1127 | E_err:   0.005244
[2025-10-06 20:23:30] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -27.952396 | E_var:     0.1195 | E_err:   0.005402
[2025-10-06 20:23:34] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -27.960318 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 20:23:37] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -27.955734 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 20:23:41] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -27.953006 | E_var:     0.1014 | E_err:   0.004976
[2025-10-06 20:23:45] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -27.964278 | E_var:     0.1061 | E_err:   0.005091
[2025-10-06 20:23:48] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -27.960720 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 20:23:52] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -27.950566 | E_var:     0.1276 | E_err:   0.005581
[2025-10-06 20:23:55] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -27.959986 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 20:23:59] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -27.951568 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 20:24:03] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -27.947043 | E_var:     0.1813 | E_err:   0.006653
[2025-10-06 20:24:06] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -27.948090 | E_var:     0.1771 | E_err:   0.006575
[2025-10-06 20:24:10] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -27.962496 | E_var:     0.1153 | E_err:   0.005306
[2025-10-06 20:24:14] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -27.956207 | E_var:     0.0849 | E_err:   0.004552
[2025-10-06 20:24:17] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -27.952516 | E_var:     0.0959 | E_err:   0.004838
[2025-10-06 20:24:21] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -27.954202 | E_var:     0.1309 | E_err:   0.005653
[2025-10-06 20:24:25] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -27.958118 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 20:24:28] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -27.954549 | E_var:     0.0992 | E_err:   0.004921
[2025-10-06 20:24:32] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -27.956969 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 20:24:36] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -27.958318 | E_var:     0.0996 | E_err:   0.004930
[2025-10-06 20:24:39] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -27.952741 | E_var:     0.1224 | E_err:   0.005466
[2025-10-06 20:24:39] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 20:24:43] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -27.960941 | E_var:     0.1331 | E_err:   0.005701
[2025-10-06 20:24:47] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -27.956575 | E_var:     0.1296 | E_err:   0.005625
[2025-10-06 20:24:50] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -27.956127 | E_var:     0.1119 | E_err:   0.005227
[2025-10-06 20:24:54] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -27.952774 | E_var:     0.1043 | E_err:   0.005046
[2025-10-06 20:24:58] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -27.946443 | E_var:     0.1450 | E_err:   0.005950
[2025-10-06 20:25:01] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -27.953701 | E_var:     0.1442 | E_err:   0.005932
[2025-10-06 20:25:05] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -27.957210 | E_var:     0.1899 | E_err:   0.006810
[2025-10-06 20:25:08] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -27.957812 | E_var:     0.2520 | E_err:   0.007844
[2025-10-06 20:25:12] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -27.961652 | E_var:     0.1087 | E_err:   0.005151
[2025-10-06 20:25:16] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -27.953238 | E_var:     0.1258 | E_err:   0.005543
[2025-10-06 20:25:19] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -27.958985 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 20:25:23] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -27.955600 | E_var:     0.1217 | E_err:   0.005451
[2025-10-06 20:25:27] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -27.955515 | E_var:     0.1205 | E_err:   0.005423
[2025-10-06 20:25:30] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -27.954578 | E_var:     0.0843 | E_err:   0.004537
[2025-10-06 20:25:34] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -27.960087 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 20:25:38] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -27.964408 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 20:25:41] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -27.947145 | E_var:     0.1377 | E_err:   0.005799
[2025-10-06 20:25:45] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -27.959552 | E_var:     0.0951 | E_err:   0.004817
[2025-10-06 20:25:49] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -27.953820 | E_var:     0.1268 | E_err:   0.005563
[2025-10-06 20:25:52] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -27.952220 | E_var:     0.1071 | E_err:   0.005114
[2025-10-06 20:25:56] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -27.960952 | E_var:     0.1048 | E_err:   0.005057
[2025-10-06 20:26:00] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -27.966002 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 20:26:03] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -27.957874 | E_var:     0.0861 | E_err:   0.004585
[2025-10-06 20:26:07] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -27.962879 | E_var:     0.1169 | E_err:   0.005342
[2025-10-06 20:26:10] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -27.955503 | E_var:     0.1079 | E_err:   0.005133
[2025-10-06 20:26:14] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -27.955372 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 20:26:18] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -27.958337 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 20:26:21] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -27.956144 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 20:26:25] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -27.956547 | E_var:     0.1425 | E_err:   0.005899
[2025-10-06 20:26:29] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -27.960891 | E_var:     0.1028 | E_err:   0.005010
[2025-10-06 20:26:32] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -27.963603 | E_var:     0.1011 | E_err:   0.004969
[2025-10-06 20:26:36] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -27.956407 | E_var:     0.1616 | E_err:   0.006281
[2025-10-06 20:26:40] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -27.965140 | E_var:     0.1136 | E_err:   0.005266
[2025-10-06 20:26:43] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -27.960164 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 20:26:47] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -27.971433 | E_var:     0.1486 | E_err:   0.006022
[2025-10-06 20:26:51] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -27.961309 | E_var:     0.1180 | E_err:   0.005366
[2025-10-06 20:26:54] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -27.950299 | E_var:     0.2287 | E_err:   0.007472
[2025-10-06 20:26:58] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -27.961331 | E_var:     0.1639 | E_err:   0.006325
[2025-10-06 20:27:01] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -27.958256 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 20:27:05] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -27.958862 | E_var:     0.1157 | E_err:   0.005314
[2025-10-06 20:27:09] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -27.952167 | E_var:     0.0901 | E_err:   0.004689
[2025-10-06 20:27:12] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -27.953515 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 20:27:16] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -27.956947 | E_var:     0.0861 | E_err:   0.004585
[2025-10-06 20:27:20] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -27.952392 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 20:27:23] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -27.953500 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 20:27:27] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -27.952819 | E_var:     0.1150 | E_err:   0.005298
[2025-10-06 20:27:31] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -27.958587 | E_var:     0.1039 | E_err:   0.005035
[2025-10-06 20:27:34] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -27.965313 | E_var:     0.1137 | E_err:   0.005269
[2025-10-06 20:27:38] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -27.954001 | E_var:     0.1343 | E_err:   0.005726
[2025-10-06 20:27:42] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -27.952841 | E_var:     0.0976 | E_err:   0.004881
[2025-10-06 20:27:42] ======================================================================================================
[2025-10-06 20:27:42] ✅ Training completed successfully
[2025-10-06 20:27:42] Total restarts: 2
[2025-10-06 20:27:43] Final Energy: -27.95284137 ± 0.00488074
[2025-10-06 20:27:43] Final Variance: 0.097573
[2025-10-06 20:27:43] ======================================================================================================
[2025-10-06 20:27:43] ======================================================================================================
[2025-10-06 20:27:43] Training completed | Runtime: 3896.8s
[2025-10-06 20:27:44] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 20:27:44] ======================================================================================================
