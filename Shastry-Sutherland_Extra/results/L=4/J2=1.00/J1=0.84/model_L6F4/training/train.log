[2025-10-07 01:56:52] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.83/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 01:56:52]   - 迭代次数: final
[2025-10-07 01:56:52]   - 能量: -30.014236-0.000168j ± 0.003959, Var: 0.064187
[2025-10-07 01:56:52]   - 时间戳: 2025-10-07T01:56:42.888048+08:00
[2025-10-07 01:57:10] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 01:57:10] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 01:57:10] ======================================================================================================
[2025-10-07 01:57:10] GCNN for Shastry-Sutherland Model
[2025-10-07 01:57:10] ======================================================================================================
[2025-10-07 01:57:10] System parameters:
[2025-10-07 01:57:10]   - System size: L=4, N=64
[2025-10-07 01:57:10]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-10-07 01:57:10] ------------------------------------------------------------------------------------------------------
[2025-10-07 01:57:10] Model parameters:
[2025-10-07 01:57:10]   - Number of layers = 6
[2025-10-07 01:57:10]   - Number of features = 4
[2025-10-07 01:57:10]   - Total parameters = 20780
[2025-10-07 01:57:10] ------------------------------------------------------------------------------------------------------
[2025-10-07 01:57:10] Training parameters:
[2025-10-07 01:57:10]   - Total iterations: 1050
[2025-10-07 01:57:10]   - Annealing cycles: 3
[2025-10-07 01:57:10]   - Initial period: 150
[2025-10-07 01:57:10]   - Period multiplier: 2.0
[2025-10-07 01:57:10]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 01:57:10]   - Samples: 4096
[2025-10-07 01:57:10]   - Discarded samples: 0
[2025-10-07 01:57:10]   - Chunk size: 4096
[2025-10-07 01:57:10]   - Diagonal shift: 0.15
[2025-10-07 01:57:10]   - Gradient clipping: 1.0
[2025-10-07 01:57:10]   - Checkpoint enabled: interval=100
[2025-10-07 01:57:10]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.84/model_L6F4/training/checkpoints
[2025-10-07 01:57:10] ------------------------------------------------------------------------------------------------------
[2025-10-07 01:57:10] Device status:
[2025-10-07 01:57:10]   - Devices model: NVIDIA H200 NVL
[2025-10-07 01:57:10]   - Number of devices: 1
[2025-10-07 01:57:10]   - Sharding: True
[2025-10-07 01:57:11] ======================================================================================================
[2025-10-07 01:57:54] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -30.432366 | E_var:     0.2220 | E_err:   0.007362
[2025-10-07 01:58:20] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -30.426790 | E_var:     0.1064 | E_err:   0.005096
[2025-10-07 01:58:24] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -30.423241 | E_var:     0.0792 | E_err:   0.004397
[2025-10-07 01:58:27] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -30.423340 | E_var:     0.0684 | E_err:   0.004087
[2025-10-07 01:58:31] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -30.423647 | E_var:     0.0795 | E_err:   0.004406
[2025-10-07 01:58:35] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -30.424059 | E_var:     0.0590 | E_err:   0.003794
[2025-10-07 01:58:38] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -30.429798 | E_var:     0.0627 | E_err:   0.003912
[2025-10-07 01:58:42] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -30.425665 | E_var:     0.0768 | E_err:   0.004329
[2025-10-07 01:58:46] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -30.423182 | E_var:     0.0686 | E_err:   0.004092
[2025-10-07 01:58:49] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -30.429405 | E_var:     0.0574 | E_err:   0.003743
[2025-10-07 01:58:53] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -30.423986 | E_var:     0.0540 | E_err:   0.003630
[2025-10-07 01:58:57] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -30.424580 | E_var:     0.0595 | E_err:   0.003812
[2025-10-07 01:59:00] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -30.427184 | E_var:     0.0506 | E_err:   0.003514
[2025-10-07 01:59:04] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -30.424271 | E_var:     0.0568 | E_err:   0.003723
[2025-10-07 01:59:08] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -30.432700 | E_var:     0.0738 | E_err:   0.004245
[2025-10-07 01:59:11] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -30.423618 | E_var:     0.0598 | E_err:   0.003822
[2025-10-07 01:59:15] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -30.430129 | E_var:     0.0605 | E_err:   0.003844
[2025-10-07 01:59:19] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -30.427856 | E_var:     0.0483 | E_err:   0.003433
[2025-10-07 01:59:22] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -30.426732 | E_var:     0.0561 | E_err:   0.003699
[2025-10-07 01:59:26] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -30.428872 | E_var:     0.0589 | E_err:   0.003791
[2025-10-07 01:59:30] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -30.418068 | E_var:     0.0590 | E_err:   0.003796
[2025-10-07 01:59:33] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -30.419584 | E_var:     0.0875 | E_err:   0.004623
[2025-10-07 01:59:37] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -30.431697 | E_var:     0.0703 | E_err:   0.004142
[2025-10-07 01:59:41] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -30.422512 | E_var:     0.0588 | E_err:   0.003790
[2025-10-07 01:59:44] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -30.424807 | E_var:     0.0761 | E_err:   0.004309
[2025-10-07 01:59:48] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -30.425589 | E_var:     0.0649 | E_err:   0.003982
[2025-10-07 01:59:52] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -30.424997 | E_var:     0.1279 | E_err:   0.005587
[2025-10-07 01:59:55] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -30.431110 | E_var:     0.0914 | E_err:   0.004724
[2025-10-07 01:59:59] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -30.425707 | E_var:     0.0586 | E_err:   0.003783
[2025-10-07 02:00:03] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -30.427966 | E_var:     0.0589 | E_err:   0.003791
[2025-10-07 02:00:06] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -30.432866 | E_var:     0.0797 | E_err:   0.004410
[2025-10-07 02:00:10] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -30.425284 | E_var:     0.0838 | E_err:   0.004522
[2025-10-07 02:00:14] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -30.427642 | E_var:     0.0641 | E_err:   0.003957
[2025-10-07 02:00:17] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -30.424514 | E_var:     0.0640 | E_err:   0.003953
[2025-10-07 02:00:21] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -30.429149 | E_var:     0.0685 | E_err:   0.004088
[2025-10-07 02:00:25] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -30.429082 | E_var:     0.0495 | E_err:   0.003476
[2025-10-07 02:00:28] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -30.423044 | E_var:     0.0594 | E_err:   0.003807
[2025-10-07 02:00:32] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -30.424241 | E_var:     0.0623 | E_err:   0.003900
[2025-10-07 02:00:36] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -30.427705 | E_var:     0.0796 | E_err:   0.004409
[2025-10-07 02:00:39] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -30.422998 | E_var:     0.0726 | E_err:   0.004211
[2025-10-07 02:00:43] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -30.425875 | E_var:     0.0580 | E_err:   0.003762
[2025-10-07 02:00:47] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -30.427449 | E_var:     0.0611 | E_err:   0.003861
[2025-10-07 02:00:50] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -30.426709 | E_var:     0.0911 | E_err:   0.004716
[2025-10-07 02:00:54] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -30.424329 | E_var:     0.0677 | E_err:   0.004067
[2025-10-07 02:00:58] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -30.423802 | E_var:     0.0715 | E_err:   0.004179
[2025-10-07 02:01:01] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -30.431425 | E_var:     0.0617 | E_err:   0.003880
[2025-10-07 02:01:05] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -30.422589 | E_var:     0.0625 | E_err:   0.003907
[2025-10-07 02:01:09] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -30.429985 | E_var:     0.0549 | E_err:   0.003661
[2025-10-07 02:01:12] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -30.424496 | E_var:     0.0742 | E_err:   0.004255
[2025-10-07 02:01:16] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -30.422195 | E_var:     0.0705 | E_err:   0.004150
[2025-10-07 02:01:20] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -30.420813 | E_var:     0.0737 | E_err:   0.004241
[2025-10-07 02:01:23] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -30.416309 | E_var:     0.0571 | E_err:   0.003735
[2025-10-07 02:01:27] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -30.421621 | E_var:     0.0902 | E_err:   0.004693
[2025-10-07 02:01:31] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -30.428328 | E_var:     0.0561 | E_err:   0.003700
[2025-10-07 02:01:34] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -30.424675 | E_var:     0.0866 | E_err:   0.004597
[2025-10-07 02:01:38] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -30.427388 | E_var:     0.1280 | E_err:   0.005590
[2025-10-07 02:01:42] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -30.428998 | E_var:     0.0840 | E_err:   0.004529
[2025-10-07 02:01:45] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -30.424162 | E_var:     0.0515 | E_err:   0.003547
[2025-10-07 02:01:49] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -30.427968 | E_var:     0.0562 | E_err:   0.003703
[2025-10-07 02:01:53] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -30.419246 | E_var:     0.0599 | E_err:   0.003824
[2025-10-07 02:01:56] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -30.422059 | E_var:     0.0515 | E_err:   0.003544
[2025-10-07 02:02:00] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -30.422980 | E_var:     0.0644 | E_err:   0.003966
[2025-10-07 02:02:03] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -30.425354 | E_var:     0.0811 | E_err:   0.004450
[2025-10-07 02:02:07] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -30.430577 | E_var:     0.0635 | E_err:   0.003938
[2025-10-07 02:02:11] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -30.423934 | E_var:     0.0619 | E_err:   0.003887
[2025-10-07 02:02:14] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -30.421224 | E_var:     0.0508 | E_err:   0.003522
[2025-10-07 02:02:18] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -30.424339 | E_var:     0.0806 | E_err:   0.004436
[2025-10-07 02:02:22] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -30.421886 | E_var:     0.0714 | E_err:   0.004176
[2025-10-07 02:02:25] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -30.427512 | E_var:     0.0662 | E_err:   0.004021
[2025-10-07 02:02:29] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -30.430042 | E_var:     0.0790 | E_err:   0.004391
[2025-10-07 02:02:33] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -30.429325 | E_var:     0.0844 | E_err:   0.004539
[2025-10-07 02:02:36] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -30.427184 | E_var:     0.0581 | E_err:   0.003767
[2025-10-07 02:02:40] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -30.427833 | E_var:     0.0591 | E_err:   0.003798
[2025-10-07 02:02:44] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -30.434423 | E_var:     0.0514 | E_err:   0.003543
[2025-10-07 02:02:47] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -30.428510 | E_var:     0.0739 | E_err:   0.004247
[2025-10-07 02:02:51] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -30.423686 | E_var:     0.0747 | E_err:   0.004271
[2025-10-07 02:02:55] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -30.424337 | E_var:     0.0790 | E_err:   0.004392
[2025-10-07 02:02:58] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -30.423739 | E_var:     0.0749 | E_err:   0.004276
[2025-10-07 02:03:02] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -30.430341 | E_var:     0.0847 | E_err:   0.004548
[2025-10-07 02:03:06] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -30.423530 | E_var:     0.0798 | E_err:   0.004414
[2025-10-07 02:03:09] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -30.429291 | E_var:     0.0870 | E_err:   0.004609
[2025-10-07 02:03:13] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -30.423170 | E_var:     0.0500 | E_err:   0.003494
[2025-10-07 02:03:17] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -30.424865 | E_var:     0.0643 | E_err:   0.003961
[2025-10-07 02:03:20] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -30.426927 | E_var:     0.0530 | E_err:   0.003598
[2025-10-07 02:03:24] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -30.424757 | E_var:     0.0480 | E_err:   0.003424
[2025-10-07 02:03:28] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -30.427479 | E_var:     0.0811 | E_err:   0.004450
[2025-10-07 02:03:31] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -30.429241 | E_var:     0.0713 | E_err:   0.004174
[2025-10-07 02:03:35] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -30.430037 | E_var:     0.0608 | E_err:   0.003853
[2025-10-07 02:03:39] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -30.426801 | E_var:     0.0828 | E_err:   0.004495
[2025-10-07 02:03:42] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -30.424269 | E_var:     0.0540 | E_err:   0.003630
[2025-10-07 02:03:46] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -30.426531 | E_var:     0.0537 | E_err:   0.003621
[2025-10-07 02:03:50] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -30.432386 | E_var:     0.0696 | E_err:   0.004123
[2025-10-07 02:03:53] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -30.417872 | E_var:     0.0554 | E_err:   0.003677
[2025-10-07 02:03:57] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -30.428930 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 02:04:01] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -30.429260 | E_var:     0.0653 | E_err:   0.003994
[2025-10-07 02:04:04] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -30.423570 | E_var:     0.0624 | E_err:   0.003903
[2025-10-07 02:04:08] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -30.426705 | E_var:     0.0655 | E_err:   0.003998
[2025-10-07 02:04:12] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -30.424123 | E_var:     0.0579 | E_err:   0.003760
[2025-10-07 02:04:15] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -30.428226 | E_var:     0.0650 | E_err:   0.003984
[2025-10-07 02:04:19] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -30.432806 | E_var:     0.0627 | E_err:   0.003913
[2025-10-07 02:04:19] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 02:04:23] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -30.422043 | E_var:     0.5362 | E_err:   0.011441
[2025-10-07 02:04:26] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -30.426114 | E_var:     0.0545 | E_err:   0.003647
[2025-10-07 02:04:30] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -30.428085 | E_var:     0.0610 | E_err:   0.003860
[2025-10-07 02:04:34] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -30.423522 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 02:04:37] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -30.431479 | E_var:     0.0516 | E_err:   0.003548
[2025-10-07 02:04:41] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -30.427323 | E_var:     0.0524 | E_err:   0.003576
[2025-10-07 02:04:45] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -30.424370 | E_var:     0.0932 | E_err:   0.004769
[2025-10-07 02:04:48] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -30.431055 | E_var:     0.0584 | E_err:   0.003776
[2025-10-07 02:04:52] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -30.429296 | E_var:     0.0546 | E_err:   0.003650
[2025-10-07 02:04:56] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -30.428550 | E_var:     0.0577 | E_err:   0.003755
[2025-10-07 02:04:59] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -30.420639 | E_var:     0.0562 | E_err:   0.003703
[2025-10-07 02:05:03] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -30.425069 | E_var:     0.0538 | E_err:   0.003623
[2025-10-07 02:05:07] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -30.425742 | E_var:     0.0643 | E_err:   0.003962
[2025-10-07 02:05:10] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -30.433637 | E_var:     0.0617 | E_err:   0.003881
[2025-10-07 02:05:14] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -30.427955 | E_var:     0.0660 | E_err:   0.004015
[2025-10-07 02:05:18] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -30.435101 | E_var:     0.0654 | E_err:   0.003997
[2025-10-07 02:05:21] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -30.425890 | E_var:     0.0614 | E_err:   0.003872
[2025-10-07 02:05:25] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -30.424008 | E_var:     0.0626 | E_err:   0.003908
[2025-10-07 02:05:29] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -30.428420 | E_var:     0.0558 | E_err:   0.003691
[2025-10-07 02:05:32] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -30.428208 | E_var:     0.0447 | E_err:   0.003303
[2025-10-07 02:05:36] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -30.431502 | E_var:     0.0564 | E_err:   0.003712
[2025-10-07 02:05:40] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -30.422341 | E_var:     0.0529 | E_err:   0.003595
[2025-10-07 02:05:43] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -30.423031 | E_var:     0.0638 | E_err:   0.003946
[2025-10-07 02:05:47] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -30.422332 | E_var:     0.0529 | E_err:   0.003594
[2025-10-07 02:05:51] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -30.423579 | E_var:     0.0718 | E_err:   0.004188
[2025-10-07 02:05:54] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -30.432120 | E_var:     0.0586 | E_err:   0.003781
[2025-10-07 02:05:58] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -30.420929 | E_var:     0.0614 | E_err:   0.003871
[2025-10-07 02:06:02] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -30.431339 | E_var:     0.0506 | E_err:   0.003515
[2025-10-07 02:06:05] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -30.426176 | E_var:     0.0824 | E_err:   0.004486
[2025-10-07 02:06:09] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -30.423866 | E_var:     0.0629 | E_err:   0.003920
[2025-10-07 02:06:13] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -30.427315 | E_var:     0.0766 | E_err:   0.004324
[2025-10-07 02:06:16] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -30.421732 | E_var:     0.0504 | E_err:   0.003506
[2025-10-07 02:06:20] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -30.428043 | E_var:     0.0570 | E_err:   0.003732
[2025-10-07 02:06:24] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -30.426935 | E_var:     0.0533 | E_err:   0.003607
[2025-10-07 02:06:27] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -30.422671 | E_var:     0.0514 | E_err:   0.003543
[2025-10-07 02:06:31] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -30.423834 | E_var:     0.0616 | E_err:   0.003878
[2025-10-07 02:06:35] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -30.423803 | E_var:     0.0942 | E_err:   0.004796
[2025-10-07 02:06:38] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -30.431026 | E_var:     0.0680 | E_err:   0.004074
[2025-10-07 02:06:42] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -30.424157 | E_var:     0.1169 | E_err:   0.005342
[2025-10-07 02:06:46] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -30.426354 | E_var:     0.0769 | E_err:   0.004333
[2025-10-07 02:06:49] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -30.433601 | E_var:     0.0542 | E_err:   0.003638
[2025-10-07 02:06:53] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -30.429900 | E_var:     0.0564 | E_err:   0.003711
[2025-10-07 02:06:57] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -30.423920 | E_var:     0.0629 | E_err:   0.003919
[2025-10-07 02:07:00] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -30.429128 | E_var:     0.0588 | E_err:   0.003788
[2025-10-07 02:07:04] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -30.421653 | E_var:     0.0582 | E_err:   0.003770
[2025-10-07 02:07:08] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -30.428814 | E_var:     0.0651 | E_err:   0.003987
[2025-10-07 02:07:11] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -30.425412 | E_var:     0.0671 | E_err:   0.004046
[2025-10-07 02:07:15] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -30.426993 | E_var:     0.0580 | E_err:   0.003763
[2025-10-07 02:07:19] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -30.426206 | E_var:     0.0616 | E_err:   0.003878
[2025-10-07 02:07:22] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -30.429347 | E_var:     0.0707 | E_err:   0.004155
[2025-10-07 02:07:22] 🔄 RESTART #1 | Period: 300
[2025-10-07 02:07:26] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -30.424384 | E_var:     0.0520 | E_err:   0.003562
[2025-10-07 02:07:30] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -30.432195 | E_var:     0.0597 | E_err:   0.003819
[2025-10-07 02:07:33] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -30.422584 | E_var:     0.0555 | E_err:   0.003680
[2025-10-07 02:07:37] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -30.428590 | E_var:     0.0603 | E_err:   0.003836
[2025-10-07 02:07:41] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -30.429702 | E_var:     0.0755 | E_err:   0.004294
[2025-10-07 02:07:44] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -30.420299 | E_var:     0.0550 | E_err:   0.003664
[2025-10-07 02:07:48] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -30.425277 | E_var:     0.0607 | E_err:   0.003850
[2025-10-07 02:07:52] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -30.430959 | E_var:     0.0868 | E_err:   0.004604
[2025-10-07 02:07:55] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -30.424279 | E_var:     0.0624 | E_err:   0.003902
[2025-10-07 02:07:59] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -30.424276 | E_var:     0.0754 | E_err:   0.004292
[2025-10-07 02:08:03] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -30.429366 | E_var:     0.0594 | E_err:   0.003808
[2025-10-07 02:08:06] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -30.431646 | E_var:     0.0726 | E_err:   0.004210
[2025-10-07 02:08:10] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -30.425577 | E_var:     0.0580 | E_err:   0.003763
[2025-10-07 02:08:14] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -30.423998 | E_var:     0.0708 | E_err:   0.004158
[2025-10-07 02:08:17] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -30.422568 | E_var:     0.0523 | E_err:   0.003572
[2025-10-07 02:08:21] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -30.426537 | E_var:     0.0729 | E_err:   0.004218
[2025-10-07 02:08:24] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -30.425918 | E_var:     0.0768 | E_err:   0.004330
[2025-10-07 02:08:28] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -30.426726 | E_var:     0.0728 | E_err:   0.004215
[2025-10-07 02:08:32] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -30.422056 | E_var:     0.0585 | E_err:   0.003780
[2025-10-07 02:08:35] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -30.421835 | E_var:     0.0786 | E_err:   0.004382
[2025-10-07 02:08:39] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -30.423346 | E_var:     0.0565 | E_err:   0.003714
[2025-10-07 02:08:43] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -30.434759 | E_var:     0.0537 | E_err:   0.003620
[2025-10-07 02:08:46] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -30.428832 | E_var:     0.0752 | E_err:   0.004284
[2025-10-07 02:08:50] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -30.426970 | E_var:     0.0577 | E_err:   0.003753
[2025-10-07 02:08:54] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -30.427310 | E_var:     0.0718 | E_err:   0.004187
[2025-10-07 02:08:57] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -30.423993 | E_var:     0.0570 | E_err:   0.003731
[2025-10-07 02:09:01] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -30.426252 | E_var:     0.0464 | E_err:   0.003366
[2025-10-07 02:09:05] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -30.426739 | E_var:     0.0588 | E_err:   0.003788
[2025-10-07 02:09:08] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -30.427463 | E_var:     0.0620 | E_err:   0.003890
[2025-10-07 02:09:12] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -30.430162 | E_var:     0.0676 | E_err:   0.004062
[2025-10-07 02:09:16] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -30.422734 | E_var:     0.0603 | E_err:   0.003837
[2025-10-07 02:09:19] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -30.425648 | E_var:     0.0576 | E_err:   0.003749
[2025-10-07 02:09:23] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -30.420700 | E_var:     0.0576 | E_err:   0.003749
[2025-10-07 02:09:27] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -30.432434 | E_var:     0.0746 | E_err:   0.004268
[2025-10-07 02:09:30] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -30.420615 | E_var:     0.1015 | E_err:   0.004978
[2025-10-07 02:09:34] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -30.429525 | E_var:     0.0579 | E_err:   0.003760
[2025-10-07 02:09:38] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -30.421309 | E_var:     0.0594 | E_err:   0.003807
[2025-10-07 02:09:41] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -30.428737 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 02:09:45] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -30.423049 | E_var:     0.0419 | E_err:   0.003199
[2025-10-07 02:09:49] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -30.431079 | E_var:     0.0700 | E_err:   0.004134
[2025-10-07 02:09:52] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -30.427387 | E_var:     0.0557 | E_err:   0.003688
[2025-10-07 02:09:56] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -30.428634 | E_var:     0.0567 | E_err:   0.003719
[2025-10-07 02:10:00] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -30.421858 | E_var:     0.0552 | E_err:   0.003671
[2025-10-07 02:10:03] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -30.422199 | E_var:     0.0614 | E_err:   0.003873
[2025-10-07 02:10:07] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -30.429549 | E_var:     0.0702 | E_err:   0.004141
[2025-10-07 02:10:11] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -30.427286 | E_var:     0.0560 | E_err:   0.003698
[2025-10-07 02:10:14] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -30.424441 | E_var:     0.0620 | E_err:   0.003892
[2025-10-07 02:10:18] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -30.426054 | E_var:     0.0606 | E_err:   0.003845
[2025-10-07 02:10:22] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -30.426917 | E_var:     0.0622 | E_err:   0.003895
[2025-10-07 02:10:25] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -30.416289 | E_var:     0.0631 | E_err:   0.003924
[2025-10-07 02:10:25] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 02:10:29] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -30.432944 | E_var:     0.0525 | E_err:   0.003581
[2025-10-07 02:10:33] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -30.424762 | E_var:     0.0946 | E_err:   0.004807
[2025-10-07 02:10:36] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -30.425601 | E_var:     0.0734 | E_err:   0.004234
[2025-10-07 02:10:40] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -30.434529 | E_var:     0.1617 | E_err:   0.006283
[2025-10-07 02:10:44] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -30.427739 | E_var:     0.0624 | E_err:   0.003902
[2025-10-07 02:10:47] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -30.430108 | E_var:     0.0512 | E_err:   0.003537
[2025-10-07 02:10:51] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -30.423473 | E_var:     0.0616 | E_err:   0.003878
[2025-10-07 02:10:55] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -30.424400 | E_var:     0.0511 | E_err:   0.003532
[2025-10-07 02:10:58] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -30.426608 | E_var:     0.0687 | E_err:   0.004095
[2025-10-07 02:11:02] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -30.425664 | E_var:     0.0497 | E_err:   0.003483
[2025-10-07 02:11:06] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -30.427868 | E_var:     0.0591 | E_err:   0.003798
[2025-10-07 02:11:09] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -30.426264 | E_var:     0.0799 | E_err:   0.004416
[2025-10-07 02:11:13] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -30.421713 | E_var:     0.0602 | E_err:   0.003835
[2025-10-07 02:11:17] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -30.419291 | E_var:     0.0748 | E_err:   0.004274
[2025-10-07 02:11:20] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -30.428338 | E_var:     0.0661 | E_err:   0.004017
[2025-10-07 02:11:24] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -30.426644 | E_var:     0.0666 | E_err:   0.004032
[2025-10-07 02:11:28] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -30.427955 | E_var:     0.0635 | E_err:   0.003938
[2025-10-07 02:11:31] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -30.423904 | E_var:     0.0765 | E_err:   0.004323
[2025-10-07 02:11:35] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -30.422300 | E_var:     0.0720 | E_err:   0.004194
[2025-10-07 02:11:39] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -30.428265 | E_var:     0.0577 | E_err:   0.003752
[2025-10-07 02:11:42] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -30.422718 | E_var:     0.0687 | E_err:   0.004096
[2025-10-07 02:11:46] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -30.422161 | E_var:     0.1583 | E_err:   0.006216
[2025-10-07 02:11:50] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -30.421892 | E_var:     0.0741 | E_err:   0.004255
[2025-10-07 02:11:53] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -30.425296 | E_var:     0.0680 | E_err:   0.004073
[2025-10-07 02:11:57] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -30.420412 | E_var:     0.1311 | E_err:   0.005657
[2025-10-07 02:12:01] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -30.423032 | E_var:     0.0516 | E_err:   0.003549
[2025-10-07 02:12:04] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -30.429890 | E_var:     0.0947 | E_err:   0.004808
[2025-10-07 02:12:08] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -30.422094 | E_var:     0.0794 | E_err:   0.004404
[2025-10-07 02:12:12] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -30.426882 | E_var:     0.0705 | E_err:   0.004150
[2025-10-07 02:12:15] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -30.426566 | E_var:     0.0572 | E_err:   0.003737
[2025-10-07 02:12:19] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -30.426919 | E_var:     0.0671 | E_err:   0.004049
[2025-10-07 02:12:23] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -30.425435 | E_var:     0.0907 | E_err:   0.004706
[2025-10-07 02:12:26] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -30.431085 | E_var:     0.0573 | E_err:   0.003739
[2025-10-07 02:12:30] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -30.420196 | E_var:     0.0770 | E_err:   0.004337
[2025-10-07 02:12:34] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -30.422986 | E_var:     0.0504 | E_err:   0.003509
[2025-10-07 02:12:37] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -30.425236 | E_var:     0.0551 | E_err:   0.003669
[2025-10-07 02:12:41] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -30.426052 | E_var:     0.0764 | E_err:   0.004318
[2025-10-07 02:12:45] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -30.428933 | E_var:     0.0665 | E_err:   0.004028
[2025-10-07 02:12:48] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -30.430127 | E_var:     0.0520 | E_err:   0.003563
[2025-10-07 02:12:52] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -30.421199 | E_var:     0.0610 | E_err:   0.003859
[2025-10-07 02:12:56] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -30.426016 | E_var:     0.0532 | E_err:   0.003604
[2025-10-07 02:12:59] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -30.428407 | E_var:     0.0649 | E_err:   0.003980
[2025-10-07 02:13:03] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -30.424926 | E_var:     0.0540 | E_err:   0.003630
[2025-10-07 02:13:07] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -30.427016 | E_var:     0.0913 | E_err:   0.004721
[2025-10-07 02:13:10] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -30.426367 | E_var:     0.0618 | E_err:   0.003885
[2025-10-07 02:13:14] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -30.427737 | E_var:     0.0700 | E_err:   0.004135
[2025-10-07 02:13:18] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -30.429889 | E_var:     0.0769 | E_err:   0.004332
[2025-10-07 02:13:21] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -30.419758 | E_var:     0.0608 | E_err:   0.003853
[2025-10-07 02:13:25] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -30.420023 | E_var:     0.0608 | E_err:   0.003852
[2025-10-07 02:13:29] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -30.429948 | E_var:     0.0732 | E_err:   0.004226
[2025-10-07 02:13:32] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -30.431058 | E_var:     0.0753 | E_err:   0.004288
[2025-10-07 02:13:36] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -30.423621 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 02:13:40] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -30.425112 | E_var:     0.0568 | E_err:   0.003725
[2025-10-07 02:13:43] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -30.425292 | E_var:     0.0702 | E_err:   0.004140
[2025-10-07 02:13:47] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -30.429155 | E_var:     0.0482 | E_err:   0.003431
[2025-10-07 02:13:51] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -30.419337 | E_var:     0.0536 | E_err:   0.003619
[2025-10-07 02:13:54] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -30.428122 | E_var:     0.0523 | E_err:   0.003574
[2025-10-07 02:13:58] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -30.427300 | E_var:     0.0593 | E_err:   0.003804
[2025-10-07 02:14:02] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -30.428538 | E_var:     0.0548 | E_err:   0.003658
[2025-10-07 02:14:05] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -30.428881 | E_var:     0.0566 | E_err:   0.003717
[2025-10-07 02:14:09] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -30.432755 | E_var:     0.0537 | E_err:   0.003622
[2025-10-07 02:14:12] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -30.425796 | E_var:     0.0807 | E_err:   0.004439
[2025-10-07 02:14:16] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -30.427120 | E_var:     0.0580 | E_err:   0.003764
[2025-10-07 02:14:20] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -30.425767 | E_var:     0.0699 | E_err:   0.004130
[2025-10-07 02:14:23] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -30.431522 | E_var:     0.0564 | E_err:   0.003711
[2025-10-07 02:14:27] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -30.421983 | E_var:     0.0519 | E_err:   0.003560
[2025-10-07 02:14:31] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -30.422853 | E_var:     0.0699 | E_err:   0.004131
[2025-10-07 02:14:34] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -30.429451 | E_var:     0.0467 | E_err:   0.003377
[2025-10-07 02:14:38] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -30.427978 | E_var:     0.0647 | E_err:   0.003973
[2025-10-07 02:14:42] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -30.416547 | E_var:     0.0930 | E_err:   0.004766
[2025-10-07 02:14:45] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -30.429638 | E_var:     0.0886 | E_err:   0.004652
[2025-10-07 02:14:49] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -30.423387 | E_var:     0.0578 | E_err:   0.003758
[2025-10-07 02:14:53] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -30.427779 | E_var:     0.0654 | E_err:   0.003994
[2025-10-07 02:14:56] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -30.426359 | E_var:     0.0552 | E_err:   0.003671
[2025-10-07 02:15:00] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -30.422446 | E_var:     0.0588 | E_err:   0.003787
[2025-10-07 02:15:04] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -30.421769 | E_var:     0.0653 | E_err:   0.003993
[2025-10-07 02:15:07] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -30.429381 | E_var:     0.0634 | E_err:   0.003936
[2025-10-07 02:15:11] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -30.427297 | E_var:     0.0564 | E_err:   0.003712
[2025-10-07 02:15:15] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -30.422047 | E_var:     0.0554 | E_err:   0.003678
[2025-10-07 02:15:18] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -30.427359 | E_var:     0.1337 | E_err:   0.005713
[2025-10-07 02:15:22] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -30.426358 | E_var:     0.0547 | E_err:   0.003656
[2025-10-07 02:15:26] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -30.434138 | E_var:     0.0642 | E_err:   0.003960
[2025-10-07 02:15:29] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -30.429928 | E_var:     0.0495 | E_err:   0.003478
[2025-10-07 02:15:33] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -30.426168 | E_var:     0.0868 | E_err:   0.004602
[2025-10-07 02:15:37] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -30.424872 | E_var:     0.0951 | E_err:   0.004818
[2025-10-07 02:15:40] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -30.427222 | E_var:     0.0516 | E_err:   0.003549
[2025-10-07 02:15:44] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -30.419417 | E_var:     0.1178 | E_err:   0.005363
[2025-10-07 02:15:48] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -30.422629 | E_var:     0.0542 | E_err:   0.003638
[2025-10-07 02:15:51] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -30.427035 | E_var:     0.0787 | E_err:   0.004385
[2025-10-07 02:15:55] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -30.425034 | E_var:     0.0505 | E_err:   0.003511
[2025-10-07 02:15:59] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -30.427606 | E_var:     0.0517 | E_err:   0.003552
[2025-10-07 02:16:02] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -30.427341 | E_var:     0.0762 | E_err:   0.004314
[2025-10-07 02:16:06] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -30.425222 | E_var:     0.0516 | E_err:   0.003550
[2025-10-07 02:16:10] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -30.432057 | E_var:     0.0578 | E_err:   0.003757
[2025-10-07 02:16:13] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -30.436086 | E_var:     0.0803 | E_err:   0.004427
[2025-10-07 02:16:17] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -30.426292 | E_var:     0.0638 | E_err:   0.003948
[2025-10-07 02:16:21] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -30.426522 | E_var:     0.0552 | E_err:   0.003673
[2025-10-07 02:16:24] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -30.433863 | E_var:     0.0674 | E_err:   0.004057
[2025-10-07 02:16:28] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -30.421312 | E_var:     0.0545 | E_err:   0.003648
[2025-10-07 02:16:32] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -30.423392 | E_var:     0.0622 | E_err:   0.003895
[2025-10-07 02:16:32] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 02:16:35] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -30.424012 | E_var:     0.0781 | E_err:   0.004366
[2025-10-07 02:16:39] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -30.427802 | E_var:     0.0629 | E_err:   0.003919
[2025-10-07 02:16:43] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -30.424814 | E_var:     0.0586 | E_err:   0.003782
[2025-10-07 02:16:46] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -30.428431 | E_var:     0.0547 | E_err:   0.003655
[2025-10-07 02:16:50] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -30.426147 | E_var:     0.0605 | E_err:   0.003845
[2025-10-07 02:16:54] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -30.423358 | E_var:     0.1139 | E_err:   0.005273
[2025-10-07 02:16:57] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -30.424564 | E_var:     0.0544 | E_err:   0.003646
[2025-10-07 02:17:01] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -30.428423 | E_var:     0.0947 | E_err:   0.004809
[2025-10-07 02:17:05] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -30.424730 | E_var:     0.0578 | E_err:   0.003757
[2025-10-07 02:17:08] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -30.430795 | E_var:     0.0674 | E_err:   0.004057
[2025-10-07 02:17:12] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -30.433347 | E_var:     0.0583 | E_err:   0.003773
[2025-10-07 02:17:16] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -30.431701 | E_var:     0.0529 | E_err:   0.003594
[2025-10-07 02:17:19] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -30.430089 | E_var:     0.0622 | E_err:   0.003898
[2025-10-07 02:17:23] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -30.420850 | E_var:     0.0627 | E_err:   0.003914
[2025-10-07 02:17:27] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -30.422570 | E_var:     0.0694 | E_err:   0.004115
[2025-10-07 02:17:30] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -30.424988 | E_var:     0.0550 | E_err:   0.003665
[2025-10-07 02:17:34] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -30.426031 | E_var:     0.1798 | E_err:   0.006625
[2025-10-07 02:17:37] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -30.427997 | E_var:     0.0565 | E_err:   0.003714
[2025-10-07 02:17:41] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -30.425653 | E_var:     0.0632 | E_err:   0.003927
[2025-10-07 02:17:45] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -30.423476 | E_var:     0.0720 | E_err:   0.004191
[2025-10-07 02:17:48] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -30.427244 | E_var:     0.0606 | E_err:   0.003848
[2025-10-07 02:17:52] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -30.425229 | E_var:     0.0752 | E_err:   0.004285
[2025-10-07 02:17:56] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -30.424359 | E_var:     0.0523 | E_err:   0.003574
[2025-10-07 02:17:59] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -30.424403 | E_var:     0.0693 | E_err:   0.004114
[2025-10-07 02:18:03] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -30.430427 | E_var:     0.0588 | E_err:   0.003788
[2025-10-07 02:18:07] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -30.421932 | E_var:     0.0550 | E_err:   0.003663
[2025-10-07 02:18:10] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -30.424462 | E_var:     0.0627 | E_err:   0.003913
[2025-10-07 02:18:14] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -30.422768 | E_var:     0.0543 | E_err:   0.003640
[2025-10-07 02:18:18] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -30.425246 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 02:18:21] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -30.430009 | E_var:     0.0499 | E_err:   0.003489
[2025-10-07 02:18:25] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -30.426360 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 02:18:29] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -30.430552 | E_var:     0.0835 | E_err:   0.004515
[2025-10-07 02:18:32] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -30.428720 | E_var:     0.0503 | E_err:   0.003504
[2025-10-07 02:18:36] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -30.428421 | E_var:     0.0671 | E_err:   0.004047
[2025-10-07 02:18:40] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -30.428080 | E_var:     0.0469 | E_err:   0.003384
[2025-10-07 02:18:43] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -30.428748 | E_var:     0.0649 | E_err:   0.003982
[2025-10-07 02:18:47] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -30.422548 | E_var:     0.0523 | E_err:   0.003575
[2025-10-07 02:18:51] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -30.421976 | E_var:     0.0605 | E_err:   0.003844
[2025-10-07 02:18:54] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -30.437411 | E_var:     0.0602 | E_err:   0.003835
[2025-10-07 02:18:58] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -30.423474 | E_var:     0.0927 | E_err:   0.004758
[2025-10-07 02:19:02] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -30.431046 | E_var:     0.0944 | E_err:   0.004800
[2025-10-07 02:19:05] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -30.426516 | E_var:     0.0679 | E_err:   0.004071
[2025-10-07 02:19:09] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -30.416980 | E_var:     0.0929 | E_err:   0.004764
[2025-10-07 02:19:13] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -30.427870 | E_var:     0.0562 | E_err:   0.003706
[2025-10-07 02:19:16] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -30.431445 | E_var:     0.0597 | E_err:   0.003817
[2025-10-07 02:19:20] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -30.421521 | E_var:     0.0507 | E_err:   0.003517
[2025-10-07 02:19:24] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -30.428419 | E_var:     0.0643 | E_err:   0.003962
[2025-10-07 02:19:27] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -30.426649 | E_var:     0.0843 | E_err:   0.004537
[2025-10-07 02:19:31] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -30.425669 | E_var:     0.0626 | E_err:   0.003909
[2025-10-07 02:19:35] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -30.421099 | E_var:     0.0490 | E_err:   0.003459
[2025-10-07 02:19:38] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -30.422305 | E_var:     0.0578 | E_err:   0.003757
[2025-10-07 02:19:42] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -30.433927 | E_var:     0.0716 | E_err:   0.004181
[2025-10-07 02:19:46] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -30.433704 | E_var:     0.1014 | E_err:   0.004976
[2025-10-07 02:19:49] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -30.426054 | E_var:     0.0814 | E_err:   0.004458
[2025-10-07 02:19:53] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -30.420488 | E_var:     0.0613 | E_err:   0.003868
[2025-10-07 02:19:57] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -30.421961 | E_var:     0.0804 | E_err:   0.004431
[2025-10-07 02:20:00] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -30.422645 | E_var:     0.0556 | E_err:   0.003683
[2025-10-07 02:20:04] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -30.430351 | E_var:     0.0587 | E_err:   0.003785
[2025-10-07 02:20:08] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -30.429017 | E_var:     0.0505 | E_err:   0.003511
[2025-10-07 02:20:11] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -30.431193 | E_var:     0.0605 | E_err:   0.003842
[2025-10-07 02:20:15] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -30.431311 | E_var:     0.0633 | E_err:   0.003930
[2025-10-07 02:20:19] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -30.422256 | E_var:     0.0506 | E_err:   0.003515
[2025-10-07 02:20:22] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -30.426499 | E_var:     0.0542 | E_err:   0.003636
[2025-10-07 02:20:26] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -30.422895 | E_var:     0.0685 | E_err:   0.004089
[2025-10-07 02:20:30] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -30.428525 | E_var:     0.0550 | E_err:   0.003663
[2025-10-07 02:20:33] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -30.425959 | E_var:     0.1023 | E_err:   0.004997
[2025-10-07 02:20:37] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -30.428132 | E_var:     0.0535 | E_err:   0.003612
[2025-10-07 02:20:41] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -30.426566 | E_var:     0.0739 | E_err:   0.004247
[2025-10-07 02:20:44] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -30.419217 | E_var:     0.0900 | E_err:   0.004687
[2025-10-07 02:20:48] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -30.422359 | E_var:     0.0499 | E_err:   0.003491
[2025-10-07 02:20:52] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -30.432124 | E_var:     0.0574 | E_err:   0.003744
[2025-10-07 02:20:55] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -30.429378 | E_var:     0.0613 | E_err:   0.003868
[2025-10-07 02:20:59] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -30.422399 | E_var:     0.0586 | E_err:   0.003781
[2025-10-07 02:21:03] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -30.432055 | E_var:     0.0544 | E_err:   0.003643
[2025-10-07 02:21:06] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -30.427823 | E_var:     0.0406 | E_err:   0.003150
[2025-10-07 02:21:10] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -30.426782 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 02:21:14] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -30.420192 | E_var:     0.0848 | E_err:   0.004551
[2025-10-07 02:21:17] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -30.429709 | E_var:     0.0526 | E_err:   0.003584
[2025-10-07 02:21:21] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -30.424038 | E_var:     0.0879 | E_err:   0.004632
[2025-10-07 02:21:25] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -30.433919 | E_var:     0.1425 | E_err:   0.005898
[2025-10-07 02:21:28] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -30.431735 | E_var:     0.0628 | E_err:   0.003917
[2025-10-07 02:21:32] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -30.423331 | E_var:     0.0652 | E_err:   0.003990
[2025-10-07 02:21:36] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -30.427893 | E_var:     0.0809 | E_err:   0.004445
[2025-10-07 02:21:39] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -30.426681 | E_var:     0.0794 | E_err:   0.004403
[2025-10-07 02:21:43] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -30.428668 | E_var:     0.0672 | E_err:   0.004052
[2025-10-07 02:21:47] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -30.426803 | E_var:     0.0675 | E_err:   0.004059
[2025-10-07 02:21:50] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -30.425865 | E_var:     0.0692 | E_err:   0.004111
[2025-10-07 02:21:54] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -30.420429 | E_var:     0.0601 | E_err:   0.003831
[2025-10-07 02:21:58] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -30.428677 | E_var:     0.0618 | E_err:   0.003886
[2025-10-07 02:22:01] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -30.426586 | E_var:     0.0576 | E_err:   0.003749
[2025-10-07 02:22:05] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -30.415775 | E_var:     0.0965 | E_err:   0.004854
[2025-10-07 02:22:09] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -30.423333 | E_var:     0.0573 | E_err:   0.003741
[2025-10-07 02:22:12] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -30.432358 | E_var:     0.0541 | E_err:   0.003634
[2025-10-07 02:22:16] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -30.423905 | E_var:     0.0515 | E_err:   0.003546
[2025-10-07 02:22:20] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -30.422141 | E_var:     0.0539 | E_err:   0.003628
[2025-10-07 02:22:23] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -30.421191 | E_var:     0.0649 | E_err:   0.003982
[2025-10-07 02:22:27] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -30.426981 | E_var:     0.0560 | E_err:   0.003698
[2025-10-07 02:22:31] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -30.419678 | E_var:     0.0499 | E_err:   0.003489
[2025-10-07 02:22:34] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -30.429169 | E_var:     0.0664 | E_err:   0.004025
[2025-10-07 02:22:38] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -30.425376 | E_var:     0.0733 | E_err:   0.004229
[2025-10-07 02:22:38] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 02:22:42] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -30.426728 | E_var:     0.0577 | E_err:   0.003754
[2025-10-07 02:22:45] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -30.431738 | E_var:     0.0605 | E_err:   0.003844
[2025-10-07 02:22:49] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -30.425147 | E_var:     0.0466 | E_err:   0.003373
[2025-10-07 02:22:53] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -30.426560 | E_var:     0.0668 | E_err:   0.004038
[2025-10-07 02:22:56] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -30.419365 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 02:23:00] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -30.425910 | E_var:     0.0537 | E_err:   0.003620
[2025-10-07 02:23:04] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -30.421211 | E_var:     0.0702 | E_err:   0.004140
[2025-10-07 02:23:07] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -30.428547 | E_var:     0.0674 | E_err:   0.004056
[2025-10-07 02:23:11] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -30.426764 | E_var:     0.0588 | E_err:   0.003790
[2025-10-07 02:23:14] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -30.421835 | E_var:     0.0585 | E_err:   0.003780
[2025-10-07 02:23:18] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -30.424610 | E_var:     0.0581 | E_err:   0.003766
[2025-10-07 02:23:22] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -30.424436 | E_var:     0.0757 | E_err:   0.004300
[2025-10-07 02:23:25] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -30.427108 | E_var:     0.0474 | E_err:   0.003403
[2025-10-07 02:23:29] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -30.421725 | E_var:     0.0657 | E_err:   0.004006
[2025-10-07 02:23:33] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -30.423347 | E_var:     0.1025 | E_err:   0.005003
[2025-10-07 02:23:36] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -30.425627 | E_var:     0.0647 | E_err:   0.003973
[2025-10-07 02:23:40] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -30.428729 | E_var:     0.0794 | E_err:   0.004403
[2025-10-07 02:23:44] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -30.428107 | E_var:     0.0705 | E_err:   0.004148
[2025-10-07 02:23:47] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -30.428176 | E_var:     0.0429 | E_err:   0.003236
[2025-10-07 02:23:51] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -30.427871 | E_var:     0.0586 | E_err:   0.003781
[2025-10-07 02:23:55] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -30.424782 | E_var:     0.0610 | E_err:   0.003860
[2025-10-07 02:23:58] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -30.424108 | E_var:     0.0552 | E_err:   0.003673
[2025-10-07 02:24:02] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -30.425873 | E_var:     0.0546 | E_err:   0.003650
[2025-10-07 02:24:06] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -30.430802 | E_var:     0.1013 | E_err:   0.004973
[2025-10-07 02:24:09] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -30.433455 | E_var:     0.0531 | E_err:   0.003602
[2025-10-07 02:24:13] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -30.425845 | E_var:     0.0681 | E_err:   0.004077
[2025-10-07 02:24:17] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -30.426343 | E_var:     0.0556 | E_err:   0.003684
[2025-10-07 02:24:20] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -30.445216 | E_var:     0.6202 | E_err:   0.012306
[2025-10-07 02:24:24] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -30.424162 | E_var:     0.0727 | E_err:   0.004213
[2025-10-07 02:24:28] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -30.427257 | E_var:     0.0767 | E_err:   0.004326
[2025-10-07 02:24:31] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -30.426497 | E_var:     0.0788 | E_err:   0.004386
[2025-10-07 02:24:35] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -30.421080 | E_var:     0.0737 | E_err:   0.004241
[2025-10-07 02:24:39] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -30.421039 | E_var:     0.0579 | E_err:   0.003760
[2025-10-07 02:24:42] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -30.429546 | E_var:     0.0481 | E_err:   0.003425
[2025-10-07 02:24:46] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -30.421972 | E_var:     0.0584 | E_err:   0.003777
[2025-10-07 02:24:50] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -30.421582 | E_var:     0.0636 | E_err:   0.003941
[2025-10-07 02:24:53] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -30.424776 | E_var:     0.0629 | E_err:   0.003919
[2025-10-07 02:24:57] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -30.431883 | E_var:     0.0672 | E_err:   0.004051
[2025-10-07 02:25:01] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -30.422882 | E_var:     0.0610 | E_err:   0.003860
[2025-10-07 02:25:04] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -30.426556 | E_var:     0.0580 | E_err:   0.003762
[2025-10-07 02:25:08] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -30.421804 | E_var:     0.0687 | E_err:   0.004097
[2025-10-07 02:25:12] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -30.427432 | E_var:     0.0628 | E_err:   0.003915
[2025-10-07 02:25:15] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -30.430355 | E_var:     0.0650 | E_err:   0.003984
[2025-10-07 02:25:19] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -30.420972 | E_var:     0.0639 | E_err:   0.003950
[2025-10-07 02:25:23] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -30.432404 | E_var:     0.0654 | E_err:   0.003996
[2025-10-07 02:25:26] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -30.424025 | E_var:     0.0588 | E_err:   0.003787
[2025-10-07 02:25:30] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -30.420849 | E_var:     0.0495 | E_err:   0.003476
[2025-10-07 02:25:34] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -30.428471 | E_var:     0.0632 | E_err:   0.003927
[2025-10-07 02:25:37] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -30.417693 | E_var:     0.0523 | E_err:   0.003572
[2025-10-07 02:25:41] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -30.426433 | E_var:     0.0572 | E_err:   0.003737
[2025-10-07 02:25:41] 🔄 RESTART #2 | Period: 600
[2025-10-07 02:25:45] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -30.429819 | E_var:     0.0622 | E_err:   0.003896
[2025-10-07 02:25:48] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -30.431129 | E_var:     0.0660 | E_err:   0.004015
[2025-10-07 02:25:52] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -30.433927 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 02:25:56] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -30.425590 | E_var:     0.0580 | E_err:   0.003764
[2025-10-07 02:25:59] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -30.425572 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 02:26:03] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -30.431156 | E_var:     0.0662 | E_err:   0.004021
[2025-10-07 02:26:07] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -30.424347 | E_var:     0.0523 | E_err:   0.003572
[2025-10-07 02:26:10] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -30.426867 | E_var:     0.0521 | E_err:   0.003566
[2025-10-07 02:26:14] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -30.427085 | E_var:     0.0534 | E_err:   0.003611
[2025-10-07 02:26:18] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -30.429811 | E_var:     0.0582 | E_err:   0.003770
[2025-10-07 02:26:21] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -30.430485 | E_var:     0.0545 | E_err:   0.003649
[2025-10-07 02:26:25] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -30.426755 | E_var:     0.0552 | E_err:   0.003671
[2025-10-07 02:26:29] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -30.428071 | E_var:     0.1018 | E_err:   0.004985
[2025-10-07 02:26:32] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -30.426362 | E_var:     0.0668 | E_err:   0.004038
[2025-10-07 02:26:36] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -30.423459 | E_var:     0.0483 | E_err:   0.003434
[2025-10-07 02:26:40] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -30.425302 | E_var:     0.0647 | E_err:   0.003975
[2025-10-07 02:26:43] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -30.435077 | E_var:     0.0675 | E_err:   0.004060
[2025-10-07 02:26:47] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -30.427573 | E_var:     0.0746 | E_err:   0.004268
[2025-10-07 02:26:51] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -30.424060 | E_var:     0.0536 | E_err:   0.003617
[2025-10-07 02:26:54] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -30.427775 | E_var:     0.0519 | E_err:   0.003559
[2025-10-07 02:26:58] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -30.421488 | E_var:     0.0605 | E_err:   0.003842
[2025-10-07 02:27:02] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -30.428520 | E_var:     0.0529 | E_err:   0.003594
[2025-10-07 02:27:05] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -30.425324 | E_var:     0.0445 | E_err:   0.003296
[2025-10-07 02:27:09] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -30.426778 | E_var:     0.0564 | E_err:   0.003712
[2025-10-07 02:27:13] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -30.423625 | E_var:     0.0893 | E_err:   0.004670
[2025-10-07 02:27:16] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -30.425891 | E_var:     0.0586 | E_err:   0.003782
[2025-10-07 02:27:20] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -30.423813 | E_var:     0.0535 | E_err:   0.003613
[2025-10-07 02:27:24] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -30.427708 | E_var:     0.0584 | E_err:   0.003775
[2025-10-07 02:27:27] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -30.422733 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 02:27:31] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -30.427336 | E_var:     0.0708 | E_err:   0.004156
[2025-10-07 02:27:35] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -30.434663 | E_var:     0.0565 | E_err:   0.003715
[2025-10-07 02:27:38] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -30.421303 | E_var:     0.0629 | E_err:   0.003919
[2025-10-07 02:27:42] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -30.428115 | E_var:     0.0571 | E_err:   0.003733
[2025-10-07 02:27:46] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -30.426195 | E_var:     0.0692 | E_err:   0.004111
[2025-10-07 02:27:49] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -30.429982 | E_var:     0.0607 | E_err:   0.003851
[2025-10-07 02:27:53] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -30.428569 | E_var:     0.0608 | E_err:   0.003854
[2025-10-07 02:27:57] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -30.429954 | E_var:     0.0710 | E_err:   0.004164
[2025-10-07 02:28:00] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -30.419449 | E_var:     0.0700 | E_err:   0.004134
[2025-10-07 02:28:04] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -30.424720 | E_var:     0.0971 | E_err:   0.004869
[2025-10-07 02:28:07] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -30.424127 | E_var:     0.0548 | E_err:   0.003657
[2025-10-07 02:28:11] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -30.429568 | E_var:     0.0619 | E_err:   0.003887
[2025-10-07 02:28:15] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -30.424297 | E_var:     0.0535 | E_err:   0.003615
[2025-10-07 02:28:18] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -30.418583 | E_var:     0.0807 | E_err:   0.004438
[2025-10-07 02:28:22] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -30.430753 | E_var:     0.0547 | E_err:   0.003655
[2025-10-07 02:28:26] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -30.422703 | E_var:     0.0619 | E_err:   0.003887
[2025-10-07 02:28:29] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -30.415612 | E_var:     0.0835 | E_err:   0.004515
[2025-10-07 02:28:33] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -30.426443 | E_var:     0.0523 | E_err:   0.003573
[2025-10-07 02:28:37] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -30.433156 | E_var:     0.0780 | E_err:   0.004365
[2025-10-07 02:28:40] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -30.435213 | E_var:     0.0682 | E_err:   0.004079
[2025-10-07 02:28:44] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -30.420762 | E_var:     0.0564 | E_err:   0.003711
[2025-10-07 02:28:44] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 02:28:48] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -30.429069 | E_var:     0.0598 | E_err:   0.003821
[2025-10-07 02:28:52] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -30.425961 | E_var:     0.0516 | E_err:   0.003551
[2025-10-07 02:28:55] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -30.420640 | E_var:     0.0686 | E_err:   0.004092
[2025-10-07 02:28:59] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -30.423174 | E_var:     0.0545 | E_err:   0.003647
[2025-10-07 02:29:02] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -30.425412 | E_var:     0.0905 | E_err:   0.004701
[2025-10-07 02:29:06] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -30.419537 | E_var:     0.1001 | E_err:   0.004943
[2025-10-07 02:29:10] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -30.423071 | E_var:     0.0565 | E_err:   0.003714
[2025-10-07 02:29:13] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -30.424620 | E_var:     0.0605 | E_err:   0.003844
[2025-10-07 02:29:17] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -30.429872 | E_var:     0.0571 | E_err:   0.003733
[2025-10-07 02:29:21] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -30.424182 | E_var:     0.0563 | E_err:   0.003709
[2025-10-07 02:29:24] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -30.425882 | E_var:     0.0926 | E_err:   0.004756
[2025-10-07 02:29:28] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -30.424333 | E_var:     0.0491 | E_err:   0.003463
[2025-10-07 02:29:32] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -30.430978 | E_var:     0.0491 | E_err:   0.003464
[2025-10-07 02:29:35] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -30.424951 | E_var:     0.0735 | E_err:   0.004236
[2025-10-07 02:29:39] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -30.433023 | E_var:     0.0583 | E_err:   0.003771
[2025-10-07 02:29:43] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -30.436145 | E_var:     0.0590 | E_err:   0.003795
[2025-10-07 02:29:46] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -30.423006 | E_var:     0.0610 | E_err:   0.003859
[2025-10-07 02:29:50] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -30.427037 | E_var:     0.0675 | E_err:   0.004061
[2025-10-07 02:29:54] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -30.427779 | E_var:     0.0545 | E_err:   0.003647
[2025-10-07 02:29:57] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -30.425465 | E_var:     0.0649 | E_err:   0.003979
[2025-10-07 02:30:01] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -30.430111 | E_var:     0.1014 | E_err:   0.004976
[2025-10-07 02:30:05] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -30.431816 | E_var:     0.0606 | E_err:   0.003846
[2025-10-07 02:30:08] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -30.430478 | E_var:     0.0597 | E_err:   0.003817
[2025-10-07 02:30:12] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -30.425027 | E_var:     0.0730 | E_err:   0.004220
[2025-10-07 02:30:16] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -30.419692 | E_var:     0.0604 | E_err:   0.003839
[2025-10-07 02:30:19] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -30.426559 | E_var:     0.0947 | E_err:   0.004809
[2025-10-07 02:30:23] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -30.425172 | E_var:     0.0519 | E_err:   0.003558
[2025-10-07 02:30:27] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -30.429506 | E_var:     0.0668 | E_err:   0.004039
[2025-10-07 02:30:30] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -30.425909 | E_var:     0.0546 | E_err:   0.003650
[2025-10-07 02:30:34] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -30.432966 | E_var:     0.0890 | E_err:   0.004662
[2025-10-07 02:30:38] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -30.418434 | E_var:     0.0687 | E_err:   0.004094
[2025-10-07 02:30:41] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -30.423298 | E_var:     0.0641 | E_err:   0.003956
[2025-10-07 02:30:45] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -30.429937 | E_var:     0.0478 | E_err:   0.003416
[2025-10-07 02:30:49] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -30.425893 | E_var:     0.0684 | E_err:   0.004085
[2025-10-07 02:30:52] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -30.424640 | E_var:     0.0581 | E_err:   0.003767
[2025-10-07 02:30:56] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -30.431202 | E_var:     0.0662 | E_err:   0.004021
[2025-10-07 02:31:00] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -30.429217 | E_var:     0.0777 | E_err:   0.004355
[2025-10-07 02:31:05] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -30.422880 | E_var:     0.1318 | E_err:   0.005672
[2025-10-07 02:31:19] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -30.425679 | E_var:     0.0708 | E_err:   0.004157
[2025-10-07 02:31:23] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -30.421103 | E_var:     0.0599 | E_err:   0.003823
[2025-10-07 02:31:27] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -30.429218 | E_var:     0.0788 | E_err:   0.004387
[2025-10-07 02:31:30] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -30.424393 | E_var:     0.0523 | E_err:   0.003573
[2025-10-07 02:31:34] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -30.422413 | E_var:     0.0536 | E_err:   0.003618
[2025-10-07 02:31:38] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -30.422738 | E_var:     0.0531 | E_err:   0.003600
[2025-10-07 02:31:41] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -30.425283 | E_var:     0.0584 | E_err:   0.003776
[2025-10-07 02:31:45] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -30.428056 | E_var:     0.0682 | E_err:   0.004080
[2025-10-07 02:31:49] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -30.427085 | E_var:     0.0557 | E_err:   0.003688
[2025-10-07 02:31:52] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -30.429088 | E_var:     0.0479 | E_err:   0.003419
[2025-10-07 02:31:56] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -30.427054 | E_var:     0.0504 | E_err:   0.003507
[2025-10-07 02:32:00] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -30.425520 | E_var:     0.0541 | E_err:   0.003634
[2025-10-07 02:32:03] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -30.426132 | E_var:     0.0872 | E_err:   0.004614
[2025-10-07 02:32:07] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -30.429636 | E_var:     0.0523 | E_err:   0.003573
[2025-10-07 02:32:11] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -30.426924 | E_var:     0.0622 | E_err:   0.003896
[2025-10-07 02:32:14] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -30.428185 | E_var:     0.0553 | E_err:   0.003675
[2025-10-07 02:32:18] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -30.427986 | E_var:     0.0577 | E_err:   0.003753
[2025-10-07 02:32:22] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -30.423629 | E_var:     0.0571 | E_err:   0.003732
[2025-10-07 02:32:25] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -30.426070 | E_var:     0.0585 | E_err:   0.003780
[2025-10-07 02:32:29] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -30.428352 | E_var:     0.0893 | E_err:   0.004669
[2025-10-07 02:32:33] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -30.428867 | E_var:     0.0550 | E_err:   0.003664
[2025-10-07 02:32:36] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -30.423009 | E_var:     0.0585 | E_err:   0.003780
[2025-10-07 02:32:40] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -30.425618 | E_var:     0.0498 | E_err:   0.003487
[2025-10-07 02:32:44] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -30.421773 | E_var:     0.0599 | E_err:   0.003824
[2025-10-07 02:32:47] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -30.423212 | E_var:     0.0463 | E_err:   0.003363
[2025-10-07 02:32:51] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -30.428573 | E_var:     0.0740 | E_err:   0.004251
[2025-10-07 02:32:55] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -30.434418 | E_var:     0.0606 | E_err:   0.003847
[2025-10-07 02:32:58] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -30.432406 | E_var:     0.0729 | E_err:   0.004217
[2025-10-07 02:33:02] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -30.424225 | E_var:     0.1025 | E_err:   0.005002
[2025-10-07 02:33:06] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -30.429062 | E_var:     0.0704 | E_err:   0.004146
[2025-10-07 02:33:09] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -30.424807 | E_var:     0.0553 | E_err:   0.003674
[2025-10-07 02:33:13] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -30.427064 | E_var:     0.0507 | E_err:   0.003518
[2025-10-07 02:33:17] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -30.432287 | E_var:     0.0663 | E_err:   0.004022
[2025-10-07 02:33:20] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -30.427842 | E_var:     0.0642 | E_err:   0.003959
[2025-10-07 02:33:24] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -30.426700 | E_var:     0.0695 | E_err:   0.004120
[2025-10-07 02:33:28] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -30.430802 | E_var:     0.0540 | E_err:   0.003630
[2025-10-07 02:33:31] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -30.426532 | E_var:     0.0486 | E_err:   0.003443
[2025-10-07 02:33:35] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -30.425092 | E_var:     0.0549 | E_err:   0.003660
[2025-10-07 02:33:39] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -30.423771 | E_var:     0.0577 | E_err:   0.003754
[2025-10-07 02:33:42] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -30.430261 | E_var:     0.0657 | E_err:   0.004006
[2025-10-07 02:33:46] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -30.427538 | E_var:     0.0822 | E_err:   0.004480
[2025-10-07 02:33:51] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -30.426913 | E_var:     0.0488 | E_err:   0.003453
[2025-10-07 02:33:55] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -30.426314 | E_var:     0.0797 | E_err:   0.004411
[2025-10-07 02:33:58] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -30.426912 | E_var:     0.0693 | E_err:   0.004112
[2025-10-07 02:34:02] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -30.427461 | E_var:     0.0493 | E_err:   0.003470
[2025-10-07 02:34:06] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -30.430829 | E_var:     0.0876 | E_err:   0.004625
[2025-10-07 02:34:09] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -30.423365 | E_var:     0.0741 | E_err:   0.004254
[2025-10-07 02:34:13] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -30.425631 | E_var:     0.0501 | E_err:   0.003496
[2025-10-07 02:34:17] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -30.430499 | E_var:     0.0501 | E_err:   0.003496
[2025-10-07 02:34:20] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -30.424327 | E_var:     0.0757 | E_err:   0.004300
[2025-10-07 02:34:24] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -30.425143 | E_var:     0.0530 | E_err:   0.003596
[2025-10-07 02:34:28] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -30.420161 | E_var:     0.0661 | E_err:   0.004016
[2025-10-07 02:34:31] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -30.422782 | E_var:     0.0696 | E_err:   0.004124
[2025-10-07 02:34:35] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -30.421772 | E_var:     0.0537 | E_err:   0.003619
[2025-10-07 02:34:39] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -30.420981 | E_var:     0.0593 | E_err:   0.003806
[2025-10-07 02:34:42] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -30.425352 | E_var:     0.0683 | E_err:   0.004082
[2025-10-07 02:34:46] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -30.431021 | E_var:     0.0487 | E_err:   0.003450
[2025-10-07 02:34:50] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -30.421322 | E_var:     0.0470 | E_err:   0.003388
[2025-10-07 02:34:53] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -30.431681 | E_var:     0.0505 | E_err:   0.003510
[2025-10-07 02:34:57] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -30.421854 | E_var:     0.0679 | E_err:   0.004071
[2025-10-07 02:35:01] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -30.425797 | E_var:     0.0503 | E_err:   0.003505
[2025-10-07 02:35:04] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -30.430832 | E_var:     0.0619 | E_err:   0.003888
[2025-10-07 02:35:04] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 02:35:08] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -30.433707 | E_var:     0.0583 | E_err:   0.003773
[2025-10-07 02:35:12] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -30.417285 | E_var:     0.1004 | E_err:   0.004951
[2025-10-07 02:35:15] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -30.423649 | E_var:     0.0700 | E_err:   0.004134
[2025-10-07 02:35:19] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -30.426498 | E_var:     0.0542 | E_err:   0.003639
[2025-10-07 02:35:23] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -30.424996 | E_var:     0.0729 | E_err:   0.004218
[2025-10-07 02:35:31] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -30.427485 | E_var:     0.0493 | E_err:   0.003468
[2025-10-07 02:35:34] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -30.429416 | E_var:     0.0521 | E_err:   0.003566
[2025-10-07 02:35:38] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -30.428313 | E_var:     0.0453 | E_err:   0.003327
[2025-10-07 02:35:42] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -30.428306 | E_var:     0.0487 | E_err:   0.003448
[2025-10-07 02:35:45] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -30.431483 | E_var:     0.0535 | E_err:   0.003613
[2025-10-07 02:35:49] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -30.418446 | E_var:     0.0445 | E_err:   0.003297
[2025-10-07 02:35:53] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -30.424248 | E_var:     0.0643 | E_err:   0.003961
[2025-10-07 02:35:56] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -30.433035 | E_var:     0.0546 | E_err:   0.003651
[2025-10-07 02:36:00] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -30.427961 | E_var:     0.0702 | E_err:   0.004141
[2025-10-07 02:36:04] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -30.429836 | E_var:     0.1821 | E_err:   0.006667
[2025-10-07 02:36:07] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -30.426105 | E_var:     0.0600 | E_err:   0.003828
[2025-10-07 02:36:11] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -30.425699 | E_var:     0.1034 | E_err:   0.005023
[2025-10-07 02:36:15] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -30.426582 | E_var:     0.1319 | E_err:   0.005675
[2025-10-07 02:36:18] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -30.422269 | E_var:     0.0900 | E_err:   0.004686
[2025-10-07 02:36:22] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -30.425330 | E_var:     0.0474 | E_err:   0.003400
[2025-10-07 02:36:26] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -30.428584 | E_var:     0.0592 | E_err:   0.003801
[2025-10-07 02:36:29] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -30.421869 | E_var:     0.0553 | E_err:   0.003674
[2025-10-07 02:36:33] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -30.430023 | E_var:     0.0851 | E_err:   0.004557
[2025-10-07 02:36:37] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -30.430894 | E_var:     0.0745 | E_err:   0.004264
[2025-10-07 02:36:40] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -30.431429 | E_var:     0.0652 | E_err:   0.003991
[2025-10-07 02:36:44] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -30.420489 | E_var:     0.0800 | E_err:   0.004419
[2025-10-07 02:36:47] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -30.424261 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 02:36:51] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -30.423182 | E_var:     0.0667 | E_err:   0.004036
[2025-10-07 02:36:55] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -30.419375 | E_var:     0.0695 | E_err:   0.004118
[2025-10-07 02:36:58] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -30.423131 | E_var:     0.0581 | E_err:   0.003768
[2025-10-07 02:37:02] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -30.424223 | E_var:     0.0868 | E_err:   0.004604
[2025-10-07 02:37:06] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -30.427200 | E_var:     0.0520 | E_err:   0.003562
[2025-10-07 02:37:09] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -30.428732 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 02:37:13] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -30.426359 | E_var:     0.0506 | E_err:   0.003515
[2025-10-07 02:37:17] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -30.426639 | E_var:     0.0615 | E_err:   0.003874
[2025-10-07 02:37:20] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -30.425433 | E_var:     0.0586 | E_err:   0.003783
[2025-10-07 02:37:24] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -30.422551 | E_var:     0.0579 | E_err:   0.003759
[2025-10-07 02:37:28] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -30.431479 | E_var:     0.0640 | E_err:   0.003953
[2025-10-07 02:37:31] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -30.421759 | E_var:     0.0545 | E_err:   0.003649
[2025-10-07 02:37:35] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -30.426967 | E_var:     0.0424 | E_err:   0.003218
[2025-10-07 02:37:39] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -30.431673 | E_var:     0.0684 | E_err:   0.004088
[2025-10-07 02:37:42] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -30.426015 | E_var:     0.0692 | E_err:   0.004112
[2025-10-07 02:37:46] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -30.422618 | E_var:     0.0566 | E_err:   0.003716
[2025-10-07 02:41:25] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -30.428291 | E_var:     0.0595 | E_err:   0.003810
[2025-10-07 02:41:29] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -30.427614 | E_var:     0.0541 | E_err:   0.003635
[2025-10-07 02:41:33] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -30.428988 | E_var:     0.0485 | E_err:   0.003441
[2025-10-07 02:41:36] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -30.424529 | E_var:     0.0534 | E_err:   0.003610
[2025-10-07 02:41:40] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -30.424842 | E_var:     0.0591 | E_err:   0.003798
[2025-10-07 02:41:44] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -30.428874 | E_var:     0.0631 | E_err:   0.003925
[2025-10-07 02:41:47] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -30.428578 | E_var:     0.0474 | E_err:   0.003402
[2025-10-07 02:41:51] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -30.420524 | E_var:     0.0528 | E_err:   0.003591
[2025-10-07 02:41:55] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -30.434068 | E_var:     0.0524 | E_err:   0.003576
[2025-10-07 02:41:58] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -30.421895 | E_var:     0.0845 | E_err:   0.004543
[2025-10-07 02:42:02] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -30.430417 | E_var:     0.0607 | E_err:   0.003851
[2025-10-07 02:42:06] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -30.428614 | E_var:     0.3986 | E_err:   0.009865
[2025-10-07 02:42:09] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -30.436545 | E_var:     0.0685 | E_err:   0.004090
[2025-10-07 02:42:13] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -30.430268 | E_var:     0.0519 | E_err:   0.003560
[2025-10-07 02:42:17] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -30.427817 | E_var:     0.0529 | E_err:   0.003594
[2025-10-07 02:42:20] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -30.427761 | E_var:     0.0528 | E_err:   0.003592
[2025-10-07 02:42:24] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -30.423730 | E_var:     0.0540 | E_err:   0.003630
[2025-10-07 02:42:28] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -30.419339 | E_var:     0.1057 | E_err:   0.005079
[2025-10-07 02:42:31] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -30.419858 | E_var:     0.0822 | E_err:   0.004480
[2025-10-07 02:42:35] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -30.419729 | E_var:     0.0670 | E_err:   0.004044
[2025-10-07 02:42:39] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -30.426151 | E_var:     0.0649 | E_err:   0.003980
[2025-10-07 02:42:42] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -30.422465 | E_var:     0.0585 | E_err:   0.003779
[2025-10-07 02:42:46] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -30.427179 | E_var:     0.0465 | E_err:   0.003369
[2025-10-07 02:42:50] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -30.425717 | E_var:     0.0575 | E_err:   0.003746
[2025-10-07 02:42:53] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -30.432243 | E_var:     0.0584 | E_err:   0.003776
[2025-10-07 02:42:57] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -30.429391 | E_var:     0.0668 | E_err:   0.004038
[2025-10-07 02:43:00] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -30.426801 | E_var:     0.0619 | E_err:   0.003889
[2025-10-07 02:43:04] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -30.421983 | E_var:     0.1151 | E_err:   0.005302
[2025-10-07 02:43:08] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -30.424573 | E_var:     0.0640 | E_err:   0.003954
[2025-10-07 02:43:11] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -30.426210 | E_var:     0.0665 | E_err:   0.004030
[2025-10-07 02:43:15] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -30.429576 | E_var:     0.0732 | E_err:   0.004228
[2025-10-07 02:43:19] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -30.428180 | E_var:     0.0808 | E_err:   0.004440
[2025-10-07 02:43:22] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -30.425027 | E_var:     0.0653 | E_err:   0.003993
[2025-10-07 02:43:26] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -30.424134 | E_var:     0.0707 | E_err:   0.004153
[2025-10-07 02:43:30] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -30.424988 | E_var:     0.0530 | E_err:   0.003598
[2025-10-07 02:43:33] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -30.428051 | E_var:     0.0525 | E_err:   0.003581
[2025-10-07 02:43:37] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -30.430861 | E_var:     0.0470 | E_err:   0.003386
[2025-10-07 02:43:41] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -30.425789 | E_var:     0.0414 | E_err:   0.003177
[2025-10-07 02:43:44] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -30.426979 | E_var:     0.0468 | E_err:   0.003380
[2025-10-07 02:43:48] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -30.425167 | E_var:     0.0493 | E_err:   0.003468
[2025-10-07 02:43:52] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -30.425049 | E_var:     0.0777 | E_err:   0.004355
[2025-10-07 02:43:55] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -30.425355 | E_var:     0.0621 | E_err:   0.003892
[2025-10-07 02:43:59] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -30.425626 | E_var:     0.0581 | E_err:   0.003767
[2025-10-07 02:44:03] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -30.423266 | E_var:     0.0570 | E_err:   0.003731
[2025-10-07 02:44:06] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -30.425664 | E_var:     0.0454 | E_err:   0.003329
[2025-10-07 02:44:10] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -30.426175 | E_var:     0.0670 | E_err:   0.004044
[2025-10-07 02:44:14] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -30.422155 | E_var:     0.0552 | E_err:   0.003671
[2025-10-07 02:44:17] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -30.432226 | E_var:     0.0455 | E_err:   0.003334
[2025-10-07 02:44:21] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -30.416258 | E_var:     0.0952 | E_err:   0.004821
[2025-10-07 02:44:25] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -30.428038 | E_var:     0.0519 | E_err:   0.003561
[2025-10-07 02:44:28] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -30.423474 | E_var:     0.0940 | E_err:   0.004790
[2025-10-07 02:44:32] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -30.423691 | E_var:     0.0442 | E_err:   0.003285
[2025-10-07 02:44:36] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -30.421257 | E_var:     0.0534 | E_err:   0.003610
[2025-10-07 02:44:39] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -30.430758 | E_var:     0.0522 | E_err:   0.003570
[2025-10-07 02:44:43] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -30.423031 | E_var:     0.0535 | E_err:   0.003614
[2025-10-07 02:44:47] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -30.423210 | E_var:     0.0556 | E_err:   0.003685
[2025-10-07 02:44:50] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -30.428773 | E_var:     0.0522 | E_err:   0.003569
[2025-10-07 02:44:50] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 02:44:54] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -30.428732 | E_var:     0.0608 | E_err:   0.003852
[2025-10-07 02:44:58] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -30.432447 | E_var:     0.0699 | E_err:   0.004132
[2025-10-07 02:45:01] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -30.422932 | E_var:     0.0614 | E_err:   0.003873
[2025-10-07 02:45:05] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -30.428120 | E_var:     0.0443 | E_err:   0.003289
[2025-10-07 02:45:09] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -30.430212 | E_var:     0.1204 | E_err:   0.005422
[2025-10-07 02:45:12] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -30.421608 | E_var:     0.0529 | E_err:   0.003593
[2025-10-07 02:45:16] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -30.421489 | E_var:     0.0707 | E_err:   0.004153
[2025-10-07 02:45:20] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -30.426223 | E_var:     0.0550 | E_err:   0.003664
[2025-10-07 02:45:23] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -30.422764 | E_var:     0.0573 | E_err:   0.003739
[2025-10-07 02:45:27] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -30.424371 | E_var:     0.0863 | E_err:   0.004591
[2025-10-07 02:45:31] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -30.426473 | E_var:     0.0992 | E_err:   0.004921
[2025-10-07 02:45:34] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -30.428113 | E_var:     0.0523 | E_err:   0.003573
[2025-10-07 02:45:38] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -30.418429 | E_var:     0.0966 | E_err:   0.004857
[2025-10-07 02:45:42] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -30.423656 | E_var:     0.0603 | E_err:   0.003836
[2025-10-07 02:45:45] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -30.429254 | E_var:     0.0838 | E_err:   0.004523
[2025-10-07 02:45:49] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -30.430229 | E_var:     0.0481 | E_err:   0.003428
[2025-10-07 02:45:53] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -30.424922 | E_var:     0.0701 | E_err:   0.004138
[2025-10-07 02:45:56] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -30.429809 | E_var:     0.0861 | E_err:   0.004584
[2025-10-07 02:46:00] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -30.426001 | E_var:     0.0760 | E_err:   0.004306
[2025-10-07 02:46:04] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -30.427295 | E_var:     0.0488 | E_err:   0.003451
[2025-10-07 02:46:07] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -30.420361 | E_var:     0.0506 | E_err:   0.003516
[2025-10-07 02:46:11] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -30.431512 | E_var:     0.0606 | E_err:   0.003847
[2025-10-07 02:46:15] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -30.427012 | E_var:     0.0751 | E_err:   0.004281
[2025-10-07 02:46:18] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -30.423931 | E_var:     0.0547 | E_err:   0.003655
[2025-10-07 02:46:22] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -30.430884 | E_var:     0.0506 | E_err:   0.003515
[2025-10-07 02:46:26] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -30.430355 | E_var:     0.0707 | E_err:   0.004154
[2025-10-07 02:46:29] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -30.420566 | E_var:     0.0752 | E_err:   0.004284
[2025-10-07 02:46:33] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -30.426098 | E_var:     0.2279 | E_err:   0.007459
[2025-10-07 02:46:37] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -30.420233 | E_var:     0.0761 | E_err:   0.004309
[2025-10-07 02:46:40] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -30.425096 | E_var:     0.0507 | E_err:   0.003520
[2025-10-07 02:46:44] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -30.431071 | E_var:     0.0573 | E_err:   0.003739
[2025-10-07 02:46:48] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -30.428933 | E_var:     0.0502 | E_err:   0.003502
[2025-10-07 02:46:51] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -30.429211 | E_var:     0.0683 | E_err:   0.004084
[2025-10-07 02:46:55] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -30.429263 | E_var:     0.0507 | E_err:   0.003517
[2025-10-07 02:46:59] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -30.425113 | E_var:     0.0850 | E_err:   0.004555
[2025-10-07 02:47:02] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -30.425517 | E_var:     0.0685 | E_err:   0.004091
[2025-10-07 02:47:06] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -30.420357 | E_var:     0.0976 | E_err:   0.004882
[2025-10-07 02:47:10] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -30.427524 | E_var:     0.0529 | E_err:   0.003595
[2025-10-07 02:47:13] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -30.430128 | E_var:     0.0707 | E_err:   0.004155
[2025-10-07 02:47:17] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -30.427480 | E_var:     0.0566 | E_err:   0.003717
[2025-10-07 02:47:21] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -30.431462 | E_var:     0.0581 | E_err:   0.003765
[2025-10-07 02:47:24] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -30.422263 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 02:47:28] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -30.426753 | E_var:     0.0690 | E_err:   0.004103
[2025-10-07 02:47:32] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -30.428058 | E_var:     0.0623 | E_err:   0.003900
[2025-10-07 02:47:35] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -30.429196 | E_var:     0.0587 | E_err:   0.003786
[2025-10-07 02:47:39] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -30.418303 | E_var:     0.0746 | E_err:   0.004267
[2025-10-07 02:47:43] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -30.422124 | E_var:     0.0663 | E_err:   0.004022
[2025-10-07 02:47:46] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -30.423222 | E_var:     0.0491 | E_err:   0.003462
[2025-10-07 02:47:50] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -30.426505 | E_var:     0.0848 | E_err:   0.004551
[2025-10-07 02:47:54] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -30.421796 | E_var:     0.0539 | E_err:   0.003627
[2025-10-07 02:47:57] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -30.426088 | E_var:     0.0639 | E_err:   0.003950
[2025-10-07 02:48:01] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -30.429185 | E_var:     0.0804 | E_err:   0.004432
[2025-10-07 02:48:05] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -30.424547 | E_var:     0.1017 | E_err:   0.004983
[2025-10-07 02:48:08] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -30.422790 | E_var:     0.0487 | E_err:   0.003447
[2025-10-07 02:48:12] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -30.428723 | E_var:     0.0650 | E_err:   0.003983
[2025-10-07 02:48:16] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -30.424784 | E_var:     0.0567 | E_err:   0.003721
[2025-10-07 02:48:19] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -30.432689 | E_var:     0.0606 | E_err:   0.003847
[2025-10-07 02:48:23] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -30.431365 | E_var:     0.0507 | E_err:   0.003519
[2025-10-07 02:48:27] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -30.422070 | E_var:     0.0755 | E_err:   0.004294
[2025-10-07 02:48:30] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -30.428913 | E_var:     0.0610 | E_err:   0.003858
[2025-10-07 02:48:34] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -30.423866 | E_var:     0.0594 | E_err:   0.003807
[2025-10-07 02:48:38] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -30.425714 | E_var:     0.0698 | E_err:   0.004129
[2025-10-07 02:48:41] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -30.427905 | E_var:     0.1114 | E_err:   0.005216
[2025-10-07 02:48:45] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -30.427834 | E_var:     0.1127 | E_err:   0.005246
[2025-10-07 02:48:49] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -30.424939 | E_var:     0.0606 | E_err:   0.003848
[2025-10-07 02:48:52] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -30.432308 | E_var:     0.0796 | E_err:   0.004408
[2025-10-07 02:48:56] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -30.429193 | E_var:     0.0621 | E_err:   0.003894
[2025-10-07 02:49:00] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -30.426046 | E_var:     0.0600 | E_err:   0.003828
[2025-10-07 02:49:03] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -30.418477 | E_var:     0.0542 | E_err:   0.003637
[2025-10-07 02:49:07] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -30.428633 | E_var:     0.0594 | E_err:   0.003807
[2025-10-07 02:49:11] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -30.422621 | E_var:     0.0647 | E_err:   0.003974
[2025-10-07 02:49:14] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -30.422358 | E_var:     0.0590 | E_err:   0.003795
[2025-10-07 02:49:18] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -30.429541 | E_var:     0.0761 | E_err:   0.004311
[2025-10-07 02:49:22] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -30.427332 | E_var:     0.0554 | E_err:   0.003679
[2025-10-07 02:49:25] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -30.428311 | E_var:     0.0766 | E_err:   0.004324
[2025-10-07 02:49:29] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -30.423419 | E_var:     0.0601 | E_err:   0.003830
[2025-10-07 02:49:33] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -30.424206 | E_var:     0.0474 | E_err:   0.003401
[2025-10-07 02:49:36] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -30.421487 | E_var:     0.0557 | E_err:   0.003686
[2025-10-07 02:49:40] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -30.427193 | E_var:     0.0585 | E_err:   0.003778
[2025-10-07 02:49:44] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -30.419025 | E_var:     0.0659 | E_err:   0.004011
[2025-10-07 02:49:47] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -30.427266 | E_var:     0.0614 | E_err:   0.003871
[2025-10-07 02:49:51] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -30.427010 | E_var:     0.0507 | E_err:   0.003518
[2025-10-07 02:49:55] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -30.434550 | E_var:     0.0831 | E_err:   0.004503
[2025-10-07 02:49:58] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -30.421501 | E_var:     0.0729 | E_err:   0.004219
[2025-10-07 02:50:02] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -30.429267 | E_var:     0.0565 | E_err:   0.003715
[2025-10-07 02:50:06] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -30.424401 | E_var:     0.0573 | E_err:   0.003742
[2025-10-07 02:50:09] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -30.422368 | E_var:     0.0459 | E_err:   0.003346
[2025-10-07 02:50:13] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -30.425687 | E_var:     0.0527 | E_err:   0.003589
[2025-10-07 02:50:17] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -30.420733 | E_var:     0.0568 | E_err:   0.003725
[2025-10-07 02:50:20] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -30.431860 | E_var:     0.0669 | E_err:   0.004040
[2025-10-07 02:50:24] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -30.430379 | E_var:     0.0576 | E_err:   0.003749
[2025-10-07 02:50:28] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -30.425421 | E_var:     0.0540 | E_err:   0.003631
[2025-10-07 02:50:31] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -30.421423 | E_var:     0.0569 | E_err:   0.003728
[2025-10-07 02:50:35] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -30.420525 | E_var:     0.1587 | E_err:   0.006225
[2025-10-07 02:50:39] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -30.423719 | E_var:     0.0542 | E_err:   0.003637
[2025-10-07 02:50:42] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -30.424935 | E_var:     0.0734 | E_err:   0.004233
[2025-10-07 02:50:46] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -30.416662 | E_var:     0.1491 | E_err:   0.006034
[2025-10-07 02:50:50] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -30.423425 | E_var:     0.0955 | E_err:   0.004828
[2025-10-07 02:50:53] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -30.428201 | E_var:     0.0573 | E_err:   0.003742
[2025-10-07 02:50:57] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -30.424863 | E_var:     0.0435 | E_err:   0.003259
[2025-10-07 02:50:57] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 02:51:01] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -30.429431 | E_var:     0.0527 | E_err:   0.003588
[2025-10-07 02:51:04] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -30.434146 | E_var:     0.0580 | E_err:   0.003763
[2025-10-07 02:51:08] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -30.424606 | E_var:     0.0973 | E_err:   0.004873
[2025-10-07 02:51:12] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -30.423861 | E_var:     0.0544 | E_err:   0.003643
[2025-10-07 02:51:15] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -30.425555 | E_var:     0.0580 | E_err:   0.003762
[2025-10-07 02:51:19] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -30.425689 | E_var:     0.0524 | E_err:   0.003578
[2025-10-07 02:51:23] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -30.431399 | E_var:     0.0545 | E_err:   0.003649
[2025-10-07 02:51:26] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -30.422335 | E_var:     0.0581 | E_err:   0.003767
[2025-10-07 02:51:30] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -30.424769 | E_var:     0.0531 | E_err:   0.003600
[2025-10-07 02:51:34] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -30.419417 | E_var:     0.0625 | E_err:   0.003907
[2025-10-07 02:51:37] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -30.428063 | E_var:     0.0536 | E_err:   0.003616
[2025-10-07 02:51:41] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -30.418958 | E_var:     0.0493 | E_err:   0.003468
[2025-10-07 02:51:45] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -30.431833 | E_var:     0.0556 | E_err:   0.003683
[2025-10-07 02:51:48] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -30.428471 | E_var:     0.0503 | E_err:   0.003506
[2025-10-07 02:51:52] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -30.424910 | E_var:     0.0635 | E_err:   0.003938
[2025-10-07 02:51:55] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -30.430029 | E_var:     0.0619 | E_err:   0.003886
[2025-10-07 02:51:59] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -30.428611 | E_var:     0.0553 | E_err:   0.003673
[2025-10-07 02:52:03] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -30.423276 | E_var:     0.0747 | E_err:   0.004269
[2025-10-07 02:52:06] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -30.427170 | E_var:     0.0551 | E_err:   0.003668
[2025-10-07 02:52:10] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -30.425648 | E_var:     0.0469 | E_err:   0.003384
[2025-10-07 02:52:14] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -30.419741 | E_var:     0.0641 | E_err:   0.003956
[2025-10-07 02:52:17] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -30.427736 | E_var:     0.0436 | E_err:   0.003263
[2025-10-07 02:52:21] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -30.425850 | E_var:     0.0577 | E_err:   0.003752
[2025-10-07 02:52:25] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -30.425884 | E_var:     0.0501 | E_err:   0.003498
[2025-10-07 02:52:28] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -30.423864 | E_var:     0.0623 | E_err:   0.003901
[2025-10-07 02:52:32] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -30.424455 | E_var:     0.0483 | E_err:   0.003434
[2025-10-07 02:52:36] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -30.426225 | E_var:     0.0936 | E_err:   0.004781
[2025-10-07 02:52:39] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -30.426947 | E_var:     0.0612 | E_err:   0.003866
[2025-10-07 02:52:43] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -30.424338 | E_var:     0.0561 | E_err:   0.003702
[2025-10-07 02:52:47] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -30.424088 | E_var:     0.0603 | E_err:   0.003837
[2025-10-07 02:52:50] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -30.420561 | E_var:     0.0660 | E_err:   0.004015
[2025-10-07 02:52:54] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -30.425138 | E_var:     0.0721 | E_err:   0.004196
[2025-10-07 02:52:58] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -30.423262 | E_var:     0.0739 | E_err:   0.004248
[2025-10-07 02:53:01] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -30.417560 | E_var:     0.0639 | E_err:   0.003950
[2025-10-07 02:53:05] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -30.430356 | E_var:     0.0530 | E_err:   0.003598
[2025-10-07 02:53:09] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -30.430325 | E_var:     0.0564 | E_err:   0.003711
[2025-10-07 02:53:12] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -30.424452 | E_var:     0.0617 | E_err:   0.003880
[2025-10-07 02:53:16] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -30.425593 | E_var:     0.0828 | E_err:   0.004496
[2025-10-07 02:53:20] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -30.425271 | E_var:     0.0686 | E_err:   0.004091
[2025-10-07 02:53:24] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -30.428891 | E_var:     0.0648 | E_err:   0.003978
[2025-10-07 02:53:27] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -30.428302 | E_var:     0.0611 | E_err:   0.003863
[2025-10-07 02:53:31] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -30.419505 | E_var:     0.0613 | E_err:   0.003867
[2025-10-07 02:53:35] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -30.426705 | E_var:     0.0657 | E_err:   0.004005
[2025-10-07 02:53:38] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -30.433920 | E_var:     0.0674 | E_err:   0.004055
[2025-10-07 02:53:42] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -30.423942 | E_var:     0.0551 | E_err:   0.003666
[2025-10-07 02:53:46] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -30.427943 | E_var:     0.0655 | E_err:   0.003999
[2025-10-07 02:53:49] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -30.424186 | E_var:     0.0497 | E_err:   0.003485
[2025-10-07 02:53:53] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -30.427371 | E_var:     0.0558 | E_err:   0.003692
[2025-10-07 02:53:57] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -30.425183 | E_var:     0.0608 | E_err:   0.003853
[2025-10-07 02:54:01] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -30.427369 | E_var:     0.0533 | E_err:   0.003608
[2025-10-07 02:54:04] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -30.429309 | E_var:     0.0638 | E_err:   0.003946
[2025-10-07 02:54:08] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -30.426793 | E_var:     0.0532 | E_err:   0.003603
[2025-10-07 02:54:12] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -30.429573 | E_var:     0.0563 | E_err:   0.003709
[2025-10-07 02:54:15] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -30.427501 | E_var:     0.0432 | E_err:   0.003246
[2025-10-07 02:54:19] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -30.431675 | E_var:     0.0452 | E_err:   0.003322
[2025-10-07 02:54:23] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -30.424116 | E_var:     0.0499 | E_err:   0.003490
[2025-10-07 02:54:26] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -30.426691 | E_var:     0.0765 | E_err:   0.004322
[2025-10-07 02:54:30] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -30.431909 | E_var:     0.0521 | E_err:   0.003566
[2025-10-07 02:54:34] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -30.425448 | E_var:     0.0527 | E_err:   0.003588
[2025-10-07 02:54:37] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -30.432684 | E_var:     0.0672 | E_err:   0.004051
[2025-10-07 02:54:41] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -30.429549 | E_var:     0.0537 | E_err:   0.003620
[2025-10-07 02:54:45] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -30.429365 | E_var:     0.0870 | E_err:   0.004609
[2025-10-07 02:54:48] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -30.433745 | E_var:     0.0571 | E_err:   0.003735
[2025-10-07 02:54:52] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -30.426954 | E_var:     0.0636 | E_err:   0.003941
[2025-10-07 02:54:56] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -30.424922 | E_var:     0.0511 | E_err:   0.003531
[2025-10-07 02:54:59] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -30.430567 | E_var:     0.0632 | E_err:   0.003928
[2025-10-07 02:55:03] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -30.429867 | E_var:     0.0494 | E_err:   0.003473
[2025-10-07 02:55:06] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -30.424290 | E_var:     0.0523 | E_err:   0.003573
[2025-10-07 02:55:10] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -30.430179 | E_var:     0.0480 | E_err:   0.003422
[2025-10-07 02:55:14] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -30.433087 | E_var:     0.0578 | E_err:   0.003757
[2025-10-07 02:55:18] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -30.431548 | E_var:     0.0476 | E_err:   0.003409
[2025-10-07 02:55:21] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -30.431739 | E_var:     0.0673 | E_err:   0.004055
[2025-10-07 02:55:25] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -30.433071 | E_var:     0.0642 | E_err:   0.003959
[2025-10-07 02:55:29] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -30.424924 | E_var:     0.0703 | E_err:   0.004142
[2025-10-07 02:55:32] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -30.423508 | E_var:     0.0554 | E_err:   0.003678
[2025-10-07 02:55:36] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -30.437333 | E_var:     0.3689 | E_err:   0.009490
[2025-10-07 02:55:39] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -30.426072 | E_var:     0.0581 | E_err:   0.003767
[2025-10-07 02:55:43] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -30.430863 | E_var:     0.0548 | E_err:   0.003659
[2025-10-07 02:55:47] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -30.427982 | E_var:     0.0500 | E_err:   0.003494
[2025-10-07 02:55:50] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -30.421261 | E_var:     0.0582 | E_err:   0.003771
[2025-10-07 02:55:54] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -30.422228 | E_var:     0.0468 | E_err:   0.003381
[2025-10-07 02:55:58] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -30.418384 | E_var:     0.2902 | E_err:   0.008417
[2025-10-07 02:56:01] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -30.424866 | E_var:     0.0579 | E_err:   0.003759
[2025-10-07 02:56:05] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -30.422307 | E_var:     0.0633 | E_err:   0.003931
[2025-10-07 02:56:09] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -30.426034 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 02:56:12] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -30.426717 | E_var:     0.0712 | E_err:   0.004168
[2025-10-07 02:56:16] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -30.425946 | E_var:     0.0544 | E_err:   0.003646
[2025-10-07 02:56:20] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -30.422301 | E_var:     0.0564 | E_err:   0.003710
[2025-10-07 02:56:23] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -30.416931 | E_var:     0.3521 | E_err:   0.009271
[2025-10-07 02:56:27] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -30.426630 | E_var:     0.0604 | E_err:   0.003841
[2025-10-07 02:56:31] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -30.426768 | E_var:     0.0512 | E_err:   0.003535
[2025-10-07 02:56:34] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -30.420726 | E_var:     0.0534 | E_err:   0.003609
[2025-10-07 02:56:38] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -30.429597 | E_var:     0.0611 | E_err:   0.003863
[2025-10-07 02:56:42] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -30.426695 | E_var:     0.0646 | E_err:   0.003971
[2025-10-07 02:56:45] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -30.422538 | E_var:     0.0550 | E_err:   0.003664
[2025-10-07 02:56:49] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -30.430785 | E_var:     0.1110 | E_err:   0.005207
[2025-10-07 02:56:53] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -30.427753 | E_var:     0.0698 | E_err:   0.004129
[2025-10-07 02:56:56] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -30.428554 | E_var:     0.0607 | E_err:   0.003849
[2025-10-07 02:57:00] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -30.424326 | E_var:     0.0494 | E_err:   0.003473
[2025-10-07 02:57:04] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -30.424126 | E_var:     0.0767 | E_err:   0.004328
[2025-10-07 02:57:04] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 02:57:08] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -30.422724 | E_var:     0.0594 | E_err:   0.003809
[2025-10-07 02:57:11] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -30.423287 | E_var:     0.0542 | E_err:   0.003637
[2025-10-07 02:57:15] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -30.432003 | E_var:     0.0610 | E_err:   0.003860
[2025-10-07 02:57:19] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -30.432555 | E_var:     0.1305 | E_err:   0.005645
[2025-10-07 02:57:22] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -30.420326 | E_var:     0.0648 | E_err:   0.003977
[2025-10-07 02:57:26] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -30.426292 | E_var:     0.0705 | E_err:   0.004149
[2025-10-07 02:57:30] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -30.422383 | E_var:     0.0595 | E_err:   0.003812
[2025-10-07 02:57:33] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -30.427407 | E_var:     0.0622 | E_err:   0.003896
[2025-10-07 02:57:37] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -30.419652 | E_var:     0.0598 | E_err:   0.003822
[2025-10-07 02:57:41] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -30.429291 | E_var:     0.0602 | E_err:   0.003835
[2025-10-07 02:57:44] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -30.429885 | E_var:     0.0725 | E_err:   0.004207
[2025-10-07 02:57:48] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -30.425894 | E_var:     0.0839 | E_err:   0.004525
[2025-10-07 02:57:51] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -30.424786 | E_var:     0.0944 | E_err:   0.004800
[2025-10-07 02:57:55] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -30.428066 | E_var:     0.1198 | E_err:   0.005407
[2025-10-07 02:57:59] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -30.426558 | E_var:     0.0551 | E_err:   0.003666
[2025-10-07 02:58:02] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -30.428798 | E_var:     0.0543 | E_err:   0.003642
[2025-10-07 02:58:06] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -30.421635 | E_var:     0.1631 | E_err:   0.006310
[2025-10-07 02:58:10] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -30.421050 | E_var:     0.0714 | E_err:   0.004175
[2025-10-07 02:58:13] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -30.422207 | E_var:     0.1135 | E_err:   0.005264
[2025-10-07 02:58:17] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -30.428593 | E_var:     0.0579 | E_err:   0.003758
[2025-10-07 02:58:21] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -30.432505 | E_var:     0.1002 | E_err:   0.004945
[2025-10-07 02:58:24] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -30.425286 | E_var:     0.0526 | E_err:   0.003583
[2025-10-07 02:58:28] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -30.426101 | E_var:     0.0495 | E_err:   0.003476
[2025-10-07 02:58:32] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -30.422789 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 02:58:35] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -30.424175 | E_var:     0.0512 | E_err:   0.003537
[2025-10-07 02:58:39] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -30.423702 | E_var:     0.0531 | E_err:   0.003599
[2025-10-07 02:58:43] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -30.424125 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 02:58:46] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -30.431485 | E_var:     0.0637 | E_err:   0.003942
[2025-10-07 02:58:50] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -30.429168 | E_var:     0.0507 | E_err:   0.003519
[2025-10-07 02:58:54] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -30.426230 | E_var:     0.0541 | E_err:   0.003635
[2025-10-07 02:58:57] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -30.425492 | E_var:     0.0508 | E_err:   0.003520
[2025-10-07 02:59:01] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -30.425071 | E_var:     0.1162 | E_err:   0.005326
[2025-10-07 02:59:05] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -30.421339 | E_var:     0.0470 | E_err:   0.003387
[2025-10-07 02:59:08] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -30.428341 | E_var:     0.1006 | E_err:   0.004955
[2025-10-07 02:59:12] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -30.421632 | E_var:     0.0607 | E_err:   0.003850
[2025-10-07 02:59:16] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -30.432359 | E_var:     0.0845 | E_err:   0.004542
[2025-10-07 02:59:19] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -30.418525 | E_var:     0.0655 | E_err:   0.003998
[2025-10-07 02:59:23] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -30.434163 | E_var:     0.0596 | E_err:   0.003815
[2025-10-07 02:59:27] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -30.422861 | E_var:     0.0606 | E_err:   0.003847
[2025-10-07 02:59:30] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -30.428303 | E_var:     0.0535 | E_err:   0.003614
[2025-10-07 02:59:34] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -30.431731 | E_var:     0.0530 | E_err:   0.003598
[2025-10-07 02:59:38] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -30.424071 | E_var:     0.0588 | E_err:   0.003790
[2025-10-07 02:59:41] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -30.430936 | E_var:     0.0598 | E_err:   0.003820
[2025-10-07 02:59:45] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -30.422553 | E_var:     0.0519 | E_err:   0.003559
[2025-10-07 02:59:49] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -30.431628 | E_var:     0.0656 | E_err:   0.004001
[2025-10-07 02:59:52] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -30.430249 | E_var:     0.0606 | E_err:   0.003848
[2025-10-07 02:59:56] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -30.428190 | E_var:     0.0535 | E_err:   0.003613
[2025-10-07 03:00:00] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -30.422207 | E_var:     0.0516 | E_err:   0.003548
[2025-10-07 03:00:03] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -30.424516 | E_var:     0.0490 | E_err:   0.003459
[2025-10-07 03:00:07] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -30.427765 | E_var:     0.0567 | E_err:   0.003721
[2025-10-07 03:00:11] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -30.426597 | E_var:     0.0807 | E_err:   0.004439
[2025-10-07 03:00:14] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -30.429844 | E_var:     0.0477 | E_err:   0.003412
[2025-10-07 03:00:18] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -30.424283 | E_var:     0.0593 | E_err:   0.003805
[2025-10-07 03:00:22] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -30.428484 | E_var:     0.0617 | E_err:   0.003881
[2025-10-07 03:00:25] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -30.431667 | E_var:     0.0553 | E_err:   0.003673
[2025-10-07 03:00:29] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -30.423856 | E_var:     0.0477 | E_err:   0.003413
[2025-10-07 03:00:33] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -30.427643 | E_var:     0.0653 | E_err:   0.003993
[2025-10-07 03:00:36] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -30.425496 | E_var:     0.0488 | E_err:   0.003452
[2025-10-07 03:00:40] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -30.428535 | E_var:     0.0589 | E_err:   0.003793
[2025-10-07 03:00:44] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -30.426163 | E_var:     0.0508 | E_err:   0.003522
[2025-10-07 03:00:47] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -30.429712 | E_var:     0.0439 | E_err:   0.003273
[2025-10-07 03:00:51] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -30.424845 | E_var:     0.0634 | E_err:   0.003935
[2025-10-07 03:00:55] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -30.425227 | E_var:     0.0560 | E_err:   0.003697
[2025-10-07 03:00:58] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -30.429363 | E_var:     0.0542 | E_err:   0.003636
[2025-10-07 03:01:02] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -30.419684 | E_var:     0.0420 | E_err:   0.003202
[2025-10-07 03:01:06] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -30.423409 | E_var:     0.0484 | E_err:   0.003438
[2025-10-07 03:01:09] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -30.428533 | E_var:     0.0522 | E_err:   0.003569
[2025-10-07 03:01:13] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -30.424846 | E_var:     0.0661 | E_err:   0.004017
[2025-10-07 03:01:17] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -30.423556 | E_var:     0.0561 | E_err:   0.003702
[2025-10-07 03:01:20] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -30.429877 | E_var:     0.0598 | E_err:   0.003822
[2025-10-07 03:01:24] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -30.430384 | E_var:     0.0719 | E_err:   0.004189
[2025-10-07 03:01:28] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -30.419798 | E_var:     0.0564 | E_err:   0.003710
[2025-10-07 03:01:31] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -30.423265 | E_var:     0.0545 | E_err:   0.003648
[2025-10-07 03:01:35] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -30.427172 | E_var:     0.0713 | E_err:   0.004172
[2025-10-07 03:01:39] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -30.432808 | E_var:     0.2108 | E_err:   0.007174
[2025-10-07 03:01:42] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -30.427652 | E_var:     0.0563 | E_err:   0.003708
[2025-10-07 03:01:46] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -30.426781 | E_var:     0.0466 | E_err:   0.003375
[2025-10-07 03:01:50] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -30.420166 | E_var:     0.0558 | E_err:   0.003692
[2025-10-07 03:01:53] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -30.421113 | E_var:     0.0587 | E_err:   0.003786
[2025-10-07 03:01:57] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -30.421831 | E_var:     0.0761 | E_err:   0.004310
[2025-10-07 03:02:01] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -30.433428 | E_var:     0.0640 | E_err:   0.003952
[2025-10-07 03:02:04] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -30.431351 | E_var:     0.0803 | E_err:   0.004428
[2025-10-07 03:02:08] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -30.426346 | E_var:     0.0541 | E_err:   0.003634
[2025-10-07 03:02:12] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -30.426002 | E_var:     0.0624 | E_err:   0.003903
[2025-10-07 03:02:15] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -30.431636 | E_var:     0.0537 | E_err:   0.003621
[2025-10-07 03:02:19] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -30.426996 | E_var:     0.0472 | E_err:   0.003396
[2025-10-07 03:02:23] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -30.427110 | E_var:     0.0554 | E_err:   0.003676
[2025-10-07 03:02:26] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -30.427917 | E_var:     0.0667 | E_err:   0.004035
[2025-10-07 03:02:30] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -30.430727 | E_var:     0.0980 | E_err:   0.004892
[2025-10-07 03:02:34] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -30.424170 | E_var:     0.0584 | E_err:   0.003775
[2025-10-07 03:02:37] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -30.432034 | E_var:     0.0498 | E_err:   0.003486
[2025-10-07 03:02:41] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -30.423887 | E_var:     0.0525 | E_err:   0.003579
[2025-10-07 03:02:45] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -30.424992 | E_var:     0.0541 | E_err:   0.003636
[2025-10-07 03:02:48] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -30.432701 | E_var:     0.0679 | E_err:   0.004072
[2025-10-07 03:02:52] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -30.421911 | E_var:     0.0467 | E_err:   0.003377
[2025-10-07 03:02:56] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -30.426697 | E_var:     0.0827 | E_err:   0.004493
[2025-10-07 03:02:59] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -30.432741 | E_var:     0.0662 | E_err:   0.004019
[2025-10-07 03:03:03] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -30.422684 | E_var:     0.1038 | E_err:   0.005033
[2025-10-07 03:03:07] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -30.425219 | E_var:     0.0542 | E_err:   0.003637
[2025-10-07 03:03:10] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -30.428360 | E_var:     0.0656 | E_err:   0.004002
[2025-10-07 03:03:10] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 03:03:14] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -30.422946 | E_var:     0.0597 | E_err:   0.003816
[2025-10-07 03:03:18] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -30.423658 | E_var:     0.0754 | E_err:   0.004291
[2025-10-07 03:03:21] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -30.428400 | E_var:     0.0530 | E_err:   0.003596
[2025-10-07 03:03:25] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -30.425404 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 03:03:29] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -30.427662 | E_var:     0.0508 | E_err:   0.003523
[2025-10-07 03:03:32] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -30.426017 | E_var:     0.0551 | E_err:   0.003666
[2025-10-07 03:03:36] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -30.428136 | E_var:     0.0587 | E_err:   0.003784
[2025-10-07 03:03:40] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -30.430509 | E_var:     0.0870 | E_err:   0.004610
[2025-10-07 03:03:43] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -30.425767 | E_var:     0.0480 | E_err:   0.003423
[2025-10-07 03:03:47] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -30.431021 | E_var:     0.0532 | E_err:   0.003605
[2025-10-07 03:03:51] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -30.430545 | E_var:     0.0761 | E_err:   0.004312
[2025-10-07 03:03:54] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -30.427461 | E_var:     0.0508 | E_err:   0.003523
[2025-10-07 03:03:58] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -30.420395 | E_var:     0.0471 | E_err:   0.003392
[2025-10-07 03:04:02] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -30.428539 | E_var:     0.0527 | E_err:   0.003586
[2025-10-07 03:04:05] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -30.425793 | E_var:     0.0472 | E_err:   0.003393
[2025-10-07 03:04:09] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -30.421877 | E_var:     0.0687 | E_err:   0.004095
[2025-10-07 03:04:13] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -30.428477 | E_var:     0.0531 | E_err:   0.003600
[2025-10-07 03:04:16] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -30.429531 | E_var:     0.0592 | E_err:   0.003800
[2025-10-07 03:04:20] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -30.427175 | E_var:     0.0748 | E_err:   0.004272
[2025-10-07 03:04:24] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -30.430264 | E_var:     0.0879 | E_err:   0.004634
[2025-10-07 03:04:27] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -30.424323 | E_var:     0.0722 | E_err:   0.004198
[2025-10-07 03:04:31] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -30.423731 | E_var:     0.0637 | E_err:   0.003942
[2025-10-07 03:04:35] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -30.425235 | E_var:     0.0655 | E_err:   0.003999
[2025-10-07 03:04:38] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -30.424644 | E_var:     0.0738 | E_err:   0.004245
[2025-10-07 03:04:42] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -30.431224 | E_var:     0.0579 | E_err:   0.003760
[2025-10-07 03:04:46] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -30.427457 | E_var:     0.0662 | E_err:   0.004021
[2025-10-07 03:04:49] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -30.427935 | E_var:     0.0572 | E_err:   0.003737
[2025-10-07 03:04:53] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -30.426035 | E_var:     0.0498 | E_err:   0.003486
[2025-10-07 03:04:57] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -30.425548 | E_var:     0.0591 | E_err:   0.003798
[2025-10-07 03:05:00] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -30.423016 | E_var:     0.0502 | E_err:   0.003501
[2025-10-07 03:05:04] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -30.425738 | E_var:     0.0571 | E_err:   0.003732
[2025-10-07 03:05:08] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -30.427182 | E_var:     0.0520 | E_err:   0.003564
[2025-10-07 03:05:11] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -30.422600 | E_var:     0.0749 | E_err:   0.004277
[2025-10-07 03:05:15] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -30.422668 | E_var:     0.0654 | E_err:   0.003995
[2025-10-07 03:05:19] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -30.430665 | E_var:     0.0476 | E_err:   0.003409
[2025-10-07 03:05:22] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -30.426726 | E_var:     0.0624 | E_err:   0.003902
[2025-10-07 03:05:26] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -30.421067 | E_var:     0.0470 | E_err:   0.003387
[2025-10-07 03:05:29] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -30.424850 | E_var:     0.0628 | E_err:   0.003915
[2025-10-07 03:05:33] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -30.424309 | E_var:     0.0561 | E_err:   0.003702
[2025-10-07 03:05:37] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -30.428221 | E_var:     0.0560 | E_err:   0.003699
[2025-10-07 03:05:40] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -30.425918 | E_var:     0.0902 | E_err:   0.004692
[2025-10-07 03:05:44] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -30.420047 | E_var:     0.1227 | E_err:   0.005474
[2025-10-07 03:05:48] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -30.429066 | E_var:     0.0675 | E_err:   0.004060
[2025-10-07 03:05:51] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -30.421397 | E_var:     0.0609 | E_err:   0.003855
[2025-10-07 03:05:55] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -30.432963 | E_var:     0.0568 | E_err:   0.003725
[2025-10-07 03:05:59] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -30.428266 | E_var:     0.0814 | E_err:   0.004459
[2025-10-07 03:06:02] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -30.425143 | E_var:     0.1188 | E_err:   0.005385
[2025-10-07 03:06:06] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -30.430581 | E_var:     0.0614 | E_err:   0.003871
[2025-10-07 03:06:10] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -30.423084 | E_var:     0.0936 | E_err:   0.004782
[2025-10-07 03:06:13] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -30.431038 | E_var:     0.0571 | E_err:   0.003732
[2025-10-07 03:06:13] ======================================================================================================
[2025-10-07 03:06:13] ✅ Training completed successfully
[2025-10-07 03:06:13] Total restarts: 2
[2025-10-07 03:06:15] Final Energy: -30.43103838 ± 0.00373212
[2025-10-07 03:06:15] Final Variance: 0.057052
[2025-10-07 03:06:15] ======================================================================================================
[2025-10-07 03:06:15] ======================================================================================================
[2025-10-07 03:06:15] Training completed | Runtime: 4144.8s
[2025-10-07 03:06:16] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 03:06:16] ======================================================================================================
