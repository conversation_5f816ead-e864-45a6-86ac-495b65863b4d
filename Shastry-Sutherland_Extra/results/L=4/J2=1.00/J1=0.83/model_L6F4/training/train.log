[2025-10-07 00:51:24] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.82/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 00:51:24]   - 迭代次数: final
[2025-10-07 00:51:24]   - 能量: -29.592239+0.001012j ± 0.003703, Var: 0.056178
[2025-10-07 00:51:24]   - 时间戳: 2025-10-07T00:51:13.152049+08:00
[2025-10-07 00:51:41] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 00:51:41] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 00:51:41] ======================================================================================================
[2025-10-07 00:51:41] GCNN for Shastry-Sutherland Model
[2025-10-07 00:51:41] ======================================================================================================
[2025-10-07 00:51:41] System parameters:
[2025-10-07 00:51:41]   - System size: L=4, N=64
[2025-10-07 00:51:41]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-10-07 00:51:41] ------------------------------------------------------------------------------------------------------
[2025-10-07 00:51:41] Model parameters:
[2025-10-07 00:51:41]   - Number of layers = 6
[2025-10-07 00:51:41]   - Number of features = 4
[2025-10-07 00:51:41]   - Total parameters = 20780
[2025-10-07 00:51:41] ------------------------------------------------------------------------------------------------------
[2025-10-07 00:51:41] Training parameters:
[2025-10-07 00:51:41]   - Total iterations: 1050
[2025-10-07 00:51:41]   - Annealing cycles: 3
[2025-10-07 00:51:41]   - Initial period: 150
[2025-10-07 00:51:41]   - Period multiplier: 2.0
[2025-10-07 00:51:41]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 00:51:41]   - Samples: 4096
[2025-10-07 00:51:41]   - Discarded samples: 0
[2025-10-07 00:51:41]   - Chunk size: 4096
[2025-10-07 00:51:41]   - Diagonal shift: 0.15
[2025-10-07 00:51:41]   - Gradient clipping: 1.0
[2025-10-07 00:51:41]   - Checkpoint enabled: interval=100
[2025-10-07 00:51:41]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.83/model_L6F4/training/checkpoints
[2025-10-07 00:51:41] ------------------------------------------------------------------------------------------------------
[2025-10-07 00:51:41] Device status:
[2025-10-07 00:51:41]   - Devices model: NVIDIA H200 NVL
[2025-10-07 00:51:41]   - Number of devices: 1
[2025-10-07 00:51:41]   - Sharding: True
[2025-10-07 00:51:42] ======================================================================================================
[2025-10-07 00:52:25] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -30.013397 | E_var:     0.1874 | E_err:   0.006764
[2025-10-07 00:52:51] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -30.007654 | E_var:     0.1098 | E_err:   0.005177
[2025-10-07 00:52:55] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -30.002059 | E_var:     0.0859 | E_err:   0.004580
[2025-10-07 00:52:58] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -30.008411 | E_var:     0.1167 | E_err:   0.005338
[2025-10-07 00:53:02] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -30.005284 | E_var:     0.0792 | E_err:   0.004397
[2025-10-07 00:53:06] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -30.019074 | E_var:     0.1348 | E_err:   0.005736
[2025-10-07 00:53:09] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -30.008619 | E_var:     0.0683 | E_err:   0.004082
[2025-10-07 00:53:13] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -30.012859 | E_var:     0.0756 | E_err:   0.004297
[2025-10-07 00:53:17] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -30.010725 | E_var:     0.0597 | E_err:   0.003818
[2025-10-07 00:53:20] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -30.013134 | E_var:     0.1217 | E_err:   0.005451
[2025-10-07 00:53:24] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -30.007178 | E_var:     0.0651 | E_err:   0.003987
[2025-10-07 00:53:27] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -30.009524 | E_var:     0.0649 | E_err:   0.003982
[2025-10-07 00:53:31] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -30.012267 | E_var:     0.0572 | E_err:   0.003737
[2025-10-07 00:53:35] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -30.014843 | E_var:     0.0893 | E_err:   0.004670
[2025-10-07 00:53:38] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -30.012389 | E_var:     0.0652 | E_err:   0.003991
[2025-10-07 00:53:42] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -30.011050 | E_var:     0.1051 | E_err:   0.005067
[2025-10-07 00:53:46] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -30.018185 | E_var:     0.1081 | E_err:   0.005137
[2025-10-07 00:53:49] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -30.006036 | E_var:     0.0567 | E_err:   0.003721
[2025-10-07 00:53:53] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -30.015463 | E_var:     0.0555 | E_err:   0.003680
[2025-10-07 00:53:57] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -30.012479 | E_var:     0.0603 | E_err:   0.003838
[2025-10-07 00:54:00] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -30.009394 | E_var:     0.0490 | E_err:   0.003460
[2025-10-07 00:54:04] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -30.006125 | E_var:     0.0563 | E_err:   0.003708
[2025-10-07 00:54:08] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -30.013487 | E_var:     0.0489 | E_err:   0.003454
[2025-10-07 00:54:11] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -30.008743 | E_var:     0.0728 | E_err:   0.004215
[2025-10-07 00:54:15] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -30.009728 | E_var:     0.0657 | E_err:   0.004005
[2025-10-07 00:54:19] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -30.015317 | E_var:     0.0677 | E_err:   0.004065
[2025-10-07 00:54:22] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -30.008010 | E_var:     0.0535 | E_err:   0.003613
[2025-10-07 00:54:26] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -30.012295 | E_var:     0.0994 | E_err:   0.004926
[2025-10-07 00:54:30] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -30.008807 | E_var:     0.0653 | E_err:   0.003994
[2025-10-07 00:54:33] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -30.017055 | E_var:     0.0598 | E_err:   0.003822
[2025-10-07 00:54:37] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -30.015989 | E_var:     0.0573 | E_err:   0.003739
[2025-10-07 00:54:40] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -30.010462 | E_var:     0.0676 | E_err:   0.004063
[2025-10-07 00:54:44] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -30.010000 | E_var:     0.0600 | E_err:   0.003829
[2025-10-07 00:54:48] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -30.009690 | E_var:     0.0687 | E_err:   0.004097
[2025-10-07 00:54:51] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -30.007569 | E_var:     0.0682 | E_err:   0.004081
[2025-10-07 00:54:55] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -30.007758 | E_var:     0.0877 | E_err:   0.004626
[2025-10-07 00:54:59] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -30.006254 | E_var:     0.0865 | E_err:   0.004596
[2025-10-07 00:55:02] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -30.019268 | E_var:     0.0522 | E_err:   0.003568
[2025-10-07 00:55:06] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -30.011056 | E_var:     0.0612 | E_err:   0.003866
[2025-10-07 00:55:10] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -30.008947 | E_var:     0.0572 | E_err:   0.003736
[2025-10-07 00:55:13] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -30.018561 | E_var:     0.0721 | E_err:   0.004197
[2025-10-07 00:55:17] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -30.008579 | E_var:     0.0802 | E_err:   0.004424
[2025-10-07 00:55:21] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -30.007714 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 00:55:24] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -30.016517 | E_var:     0.0719 | E_err:   0.004190
[2025-10-07 00:55:28] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -30.010480 | E_var:     0.0560 | E_err:   0.003698
[2025-10-07 00:55:32] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -30.003603 | E_var:     0.0832 | E_err:   0.004507
[2025-10-07 00:55:35] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -30.007713 | E_var:     0.0598 | E_err:   0.003820
[2025-10-07 00:55:39] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -30.015513 | E_var:     0.0795 | E_err:   0.004405
[2025-10-07 00:55:43] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -30.011004 | E_var:     0.0698 | E_err:   0.004129
[2025-10-07 00:55:46] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -30.013275 | E_var:     0.0904 | E_err:   0.004699
[2025-10-07 00:55:50] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -30.015641 | E_var:     0.0671 | E_err:   0.004046
[2025-10-07 00:55:54] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -30.020555 | E_var:     0.0642 | E_err:   0.003960
[2025-10-07 00:55:57] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -30.011335 | E_var:     0.0902 | E_err:   0.004693
[2025-10-07 00:56:01] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -30.010386 | E_var:     0.0750 | E_err:   0.004279
[2025-10-07 00:56:05] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -30.011563 | E_var:     0.0707 | E_err:   0.004154
[2025-10-07 00:56:08] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -30.017410 | E_var:     0.0449 | E_err:   0.003311
[2025-10-07 00:56:12] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -30.009816 | E_var:     0.0604 | E_err:   0.003840
[2025-10-07 00:56:16] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -30.017522 | E_var:     0.0638 | E_err:   0.003946
[2025-10-07 00:56:19] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -30.016399 | E_var:     0.0700 | E_err:   0.004135
[2025-10-07 00:56:23] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -30.009469 | E_var:     0.0670 | E_err:   0.004043
[2025-10-07 00:56:26] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -30.012189 | E_var:     0.0565 | E_err:   0.003713
[2025-10-07 00:56:30] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -30.015645 | E_var:     0.0602 | E_err:   0.003833
[2025-10-07 00:56:34] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -30.007668 | E_var:     0.0780 | E_err:   0.004365
[2025-10-07 00:56:37] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -30.009387 | E_var:     0.0682 | E_err:   0.004080
[2025-10-07 00:56:41] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -30.012966 | E_var:     0.0578 | E_err:   0.003755
[2025-10-07 00:56:45] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -30.013326 | E_var:     0.0645 | E_err:   0.003968
[2025-10-07 00:56:48] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -30.001835 | E_var:     0.0672 | E_err:   0.004049
[2025-10-07 00:56:52] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -30.011717 | E_var:     0.1014 | E_err:   0.004976
[2025-10-07 00:56:56] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -30.010582 | E_var:     0.0608 | E_err:   0.003851
[2025-10-07 00:56:59] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -30.019381 | E_var:     0.0592 | E_err:   0.003802
[2025-10-07 00:57:03] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -30.007004 | E_var:     0.0675 | E_err:   0.004060
[2025-10-07 00:57:07] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -30.014290 | E_var:     0.0730 | E_err:   0.004223
[2025-10-07 00:57:10] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -30.015080 | E_var:     0.0673 | E_err:   0.004054
[2025-10-07 00:57:14] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -30.011298 | E_var:     0.0728 | E_err:   0.004215
[2025-10-07 00:57:18] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -30.010795 | E_var:     0.0617 | E_err:   0.003881
[2025-10-07 00:57:21] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -30.009548 | E_var:     0.0543 | E_err:   0.003639
[2025-10-07 00:57:25] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -30.014153 | E_var:     0.0567 | E_err:   0.003719
[2025-10-07 00:57:29] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -30.012753 | E_var:     0.0571 | E_err:   0.003732
[2025-10-07 00:57:32] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -30.013628 | E_var:     0.0478 | E_err:   0.003416
[2025-10-07 00:57:36] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -30.009923 | E_var:     0.0695 | E_err:   0.004120
[2025-10-07 00:57:40] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -30.014888 | E_var:     0.0560 | E_err:   0.003698
[2025-10-07 00:57:43] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -30.014237 | E_var:     0.0784 | E_err:   0.004374
[2025-10-07 00:57:47] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -30.008079 | E_var:     0.0734 | E_err:   0.004233
[2025-10-07 00:57:51] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -30.012768 | E_var:     0.0587 | E_err:   0.003785
[2025-10-07 00:57:54] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -30.010299 | E_var:     0.0560 | E_err:   0.003699
[2025-10-07 00:57:58] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -30.003432 | E_var:     0.0709 | E_err:   0.004162
[2025-10-07 00:58:02] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -30.014157 | E_var:     0.0750 | E_err:   0.004279
[2025-10-07 00:58:05] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -30.005273 | E_var:     0.0686 | E_err:   0.004093
[2025-10-07 00:58:09] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -30.016405 | E_var:     0.1057 | E_err:   0.005080
[2025-10-07 00:58:13] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -30.015008 | E_var:     0.0594 | E_err:   0.003807
[2025-10-07 00:58:16] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -30.015447 | E_var:     0.0641 | E_err:   0.003955
[2025-10-07 00:58:20] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -30.011287 | E_var:     0.0586 | E_err:   0.003781
[2025-10-07 00:58:24] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -30.012153 | E_var:     0.0608 | E_err:   0.003854
[2025-10-07 00:58:27] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -30.010172 | E_var:     0.0685 | E_err:   0.004091
[2025-10-07 00:58:31] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -30.012916 | E_var:     0.0543 | E_err:   0.003640
[2025-10-07 00:58:34] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -30.013866 | E_var:     0.0572 | E_err:   0.003738
[2025-10-07 00:58:38] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -30.012901 | E_var:     0.0522 | E_err:   0.003570
[2025-10-07 00:58:42] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -30.011194 | E_var:     0.0661 | E_err:   0.004018
[2025-10-07 00:58:45] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -30.010855 | E_var:     0.0715 | E_err:   0.004177
[2025-10-07 00:58:49] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -30.012915 | E_var:     0.0719 | E_err:   0.004191
[2025-10-07 00:58:49] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 00:58:53] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -30.003557 | E_var:     0.0814 | E_err:   0.004457
[2025-10-07 00:58:56] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -30.013155 | E_var:     0.0646 | E_err:   0.003972
[2025-10-07 00:59:00] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -30.014002 | E_var:     0.1334 | E_err:   0.005708
[2025-10-07 00:59:04] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -30.011523 | E_var:     0.0641 | E_err:   0.003956
[2025-10-07 00:59:07] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -30.011483 | E_var:     0.0625 | E_err:   0.003908
[2025-10-07 00:59:11] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -30.017359 | E_var:     0.0558 | E_err:   0.003690
[2025-10-07 00:59:15] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -30.010067 | E_var:     0.0613 | E_err:   0.003868
[2025-10-07 00:59:18] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -30.009636 | E_var:     0.0737 | E_err:   0.004241
[2025-10-07 00:59:22] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -30.013336 | E_var:     0.0638 | E_err:   0.003948
[2025-10-07 00:59:26] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -30.010888 | E_var:     0.0733 | E_err:   0.004229
[2025-10-07 00:59:29] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -30.025021 | E_var:     0.0926 | E_err:   0.004755
[2025-10-07 00:59:33] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -30.011798 | E_var:     0.0591 | E_err:   0.003798
[2025-10-07 00:59:37] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -30.009265 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 00:59:40] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -30.006114 | E_var:     0.0674 | E_err:   0.004057
[2025-10-07 00:59:44] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -30.020231 | E_var:     0.0542 | E_err:   0.003636
[2025-10-07 00:59:48] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -30.016193 | E_var:     0.0588 | E_err:   0.003788
[2025-10-07 00:59:51] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -30.023753 | E_var:     0.0971 | E_err:   0.004869
[2025-10-07 00:59:55] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -30.008870 | E_var:     0.1536 | E_err:   0.006123
[2025-10-07 00:59:59] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -30.009353 | E_var:     0.0775 | E_err:   0.004349
[2025-10-07 01:00:02] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -30.012971 | E_var:     0.0739 | E_err:   0.004249
[2025-10-07 01:00:06] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -30.019848 | E_var:     0.0748 | E_err:   0.004274
[2025-10-07 01:00:10] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -30.018599 | E_var:     0.0697 | E_err:   0.004126
[2025-10-07 01:00:13] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -30.012366 | E_var:     0.0695 | E_err:   0.004118
[2025-10-07 01:00:17] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -30.004923 | E_var:     0.0664 | E_err:   0.004025
[2025-10-07 01:00:21] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -30.007340 | E_var:     0.0887 | E_err:   0.004652
[2025-10-07 01:00:24] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -30.018796 | E_var:     0.0622 | E_err:   0.003897
[2025-10-07 01:00:28] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -30.010411 | E_var:     0.0535 | E_err:   0.003613
[2025-10-07 01:00:32] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -30.013366 | E_var:     0.0606 | E_err:   0.003846
[2025-10-07 01:00:35] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -30.005596 | E_var:     0.0564 | E_err:   0.003711
[2025-10-07 01:00:39] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -30.013172 | E_var:     0.0749 | E_err:   0.004277
[2025-10-07 01:00:42] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -30.009502 | E_var:     0.0815 | E_err:   0.004460
[2025-10-07 01:00:46] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -30.011945 | E_var:     0.0661 | E_err:   0.004018
[2025-10-07 01:00:50] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -30.013885 | E_var:     0.0876 | E_err:   0.004624
[2025-10-07 01:00:53] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -30.011512 | E_var:     0.0866 | E_err:   0.004599
[2025-10-07 01:00:57] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -30.014642 | E_var:     0.0640 | E_err:   0.003953
[2025-10-07 01:01:01] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -30.011822 | E_var:     0.0540 | E_err:   0.003632
[2025-10-07 01:01:04] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -30.011255 | E_var:     0.0910 | E_err:   0.004714
[2025-10-07 01:01:08] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -30.017992 | E_var:     0.0647 | E_err:   0.003973
[2025-10-07 01:01:12] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -30.018265 | E_var:     0.0773 | E_err:   0.004344
[2025-10-07 01:01:15] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -30.009496 | E_var:     0.0951 | E_err:   0.004818
[2025-10-07 01:01:19] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -30.011454 | E_var:     0.0515 | E_err:   0.003545
[2025-10-07 01:01:23] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -30.003314 | E_var:     0.1427 | E_err:   0.005902
[2025-10-07 01:01:26] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -30.011234 | E_var:     0.0566 | E_err:   0.003717
[2025-10-07 01:01:30] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -30.012920 | E_var:     0.0901 | E_err:   0.004690
[2025-10-07 01:01:34] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -30.016473 | E_var:     0.0901 | E_err:   0.004689
[2025-10-07 01:01:37] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -30.011358 | E_var:     0.0603 | E_err:   0.003838
[2025-10-07 01:01:41] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -30.008144 | E_var:     0.0712 | E_err:   0.004169
[2025-10-07 01:01:45] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -30.012357 | E_var:     0.0635 | E_err:   0.003939
[2025-10-07 01:01:48] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -30.013398 | E_var:     0.0663 | E_err:   0.004024
[2025-10-07 01:01:52] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -30.014342 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 01:01:52] 🔄 RESTART #1 | Period: 300
[2025-10-07 01:01:56] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -30.010688 | E_var:     0.0577 | E_err:   0.003755
[2025-10-07 01:01:59] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -30.018225 | E_var:     0.0923 | E_err:   0.004747
[2025-10-07 01:02:03] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -30.011558 | E_var:     0.0638 | E_err:   0.003947
[2025-10-07 01:02:07] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -30.014238 | E_var:     0.0783 | E_err:   0.004374
[2025-10-07 01:02:10] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -30.010213 | E_var:     0.2063 | E_err:   0.007098
[2025-10-07 01:02:14] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -30.006937 | E_var:     0.0609 | E_err:   0.003854
[2025-10-07 01:02:18] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -30.013249 | E_var:     0.0592 | E_err:   0.003801
[2025-10-07 01:02:21] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -30.006454 | E_var:     0.1249 | E_err:   0.005523
[2025-10-07 01:02:25] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -30.013483 | E_var:     0.0596 | E_err:   0.003816
[2025-10-07 01:02:29] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -30.012170 | E_var:     0.0607 | E_err:   0.003851
[2025-10-07 01:02:32] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -30.010624 | E_var:     0.0722 | E_err:   0.004200
[2025-10-07 01:02:36] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -30.006737 | E_var:     0.0665 | E_err:   0.004029
[2025-10-07 01:02:39] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -30.016504 | E_var:     0.0670 | E_err:   0.004044
[2025-10-07 01:02:43] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -30.006419 | E_var:     0.0842 | E_err:   0.004535
[2025-10-07 01:02:47] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -30.017388 | E_var:     0.0652 | E_err:   0.003991
[2025-10-07 01:02:50] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -30.010381 | E_var:     0.0590 | E_err:   0.003795
[2025-10-07 01:02:54] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -30.009917 | E_var:     0.0821 | E_err:   0.004478
[2025-10-07 01:02:58] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -30.020116 | E_var:     0.0768 | E_err:   0.004331
[2025-10-07 01:03:01] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -30.006977 | E_var:     0.0683 | E_err:   0.004082
[2025-10-07 01:03:05] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -30.013954 | E_var:     0.0675 | E_err:   0.004059
[2025-10-07 01:03:09] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -30.013439 | E_var:     0.0588 | E_err:   0.003788
[2025-10-07 01:03:12] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -30.017134 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 01:03:16] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -30.008327 | E_var:     0.0552 | E_err:   0.003670
[2025-10-07 01:03:20] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -30.010628 | E_var:     0.0726 | E_err:   0.004211
[2025-10-07 01:03:23] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -30.017844 | E_var:     0.0614 | E_err:   0.003872
[2025-10-07 01:03:27] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -30.007660 | E_var:     0.0595 | E_err:   0.003810
[2025-10-07 01:03:31] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -30.013655 | E_var:     0.0572 | E_err:   0.003738
[2025-10-07 01:03:34] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -30.015314 | E_var:     0.0760 | E_err:   0.004306
[2025-10-07 01:03:38] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -30.004742 | E_var:     0.0619 | E_err:   0.003888
[2025-10-07 01:03:42] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -30.019699 | E_var:     0.0665 | E_err:   0.004031
[2025-10-07 01:03:45] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -30.011954 | E_var:     0.0832 | E_err:   0.004506
[2025-10-07 01:03:49] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -30.013423 | E_var:     0.0592 | E_err:   0.003803
[2025-10-07 01:03:53] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -30.015099 | E_var:     0.0669 | E_err:   0.004041
[2025-10-07 01:03:56] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -30.008333 | E_var:     0.0621 | E_err:   0.003894
[2025-10-07 01:04:00] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -30.012259 | E_var:     0.0857 | E_err:   0.004573
[2025-10-07 01:04:04] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -30.010617 | E_var:     0.0649 | E_err:   0.003980
[2025-10-07 01:04:07] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -30.005743 | E_var:     0.0651 | E_err:   0.003987
[2025-10-07 01:04:11] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -30.021042 | E_var:     0.0581 | E_err:   0.003765
[2025-10-07 01:04:15] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -30.013515 | E_var:     0.0620 | E_err:   0.003891
[2025-10-07 01:04:18] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -30.010131 | E_var:     0.0954 | E_err:   0.004826
[2025-10-07 01:04:22] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -30.017314 | E_var:     0.0745 | E_err:   0.004264
[2025-10-07 01:04:26] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -30.011279 | E_var:     0.0766 | E_err:   0.004324
[2025-10-07 01:04:29] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -30.017752 | E_var:     0.0770 | E_err:   0.004337
[2025-10-07 01:04:33] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -30.014370 | E_var:     0.0933 | E_err:   0.004774
[2025-10-07 01:04:37] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -30.010473 | E_var:     0.0794 | E_err:   0.004403
[2025-10-07 01:04:40] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -30.003417 | E_var:     0.2933 | E_err:   0.008461
[2025-10-07 01:04:44] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -30.012920 | E_var:     0.1022 | E_err:   0.004995
[2025-10-07 01:04:47] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -30.008018 | E_var:     0.0623 | E_err:   0.003899
[2025-10-07 01:04:51] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -30.007501 | E_var:     0.0773 | E_err:   0.004345
[2025-10-07 01:04:55] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -30.019117 | E_var:     0.1954 | E_err:   0.006907
[2025-10-07 01:04:55] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 01:04:59] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -30.013771 | E_var:     0.0694 | E_err:   0.004117
[2025-10-07 01:05:02] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -30.008800 | E_var:     0.0728 | E_err:   0.004215
[2025-10-07 01:05:06] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -30.011000 | E_var:     0.0529 | E_err:   0.003594
[2025-10-07 01:05:09] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -30.014902 | E_var:     0.0661 | E_err:   0.004017
[2025-10-07 01:05:13] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -30.017038 | E_var:     0.0627 | E_err:   0.003914
[2025-10-07 01:05:17] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -30.006475 | E_var:     0.0645 | E_err:   0.003970
[2025-10-07 01:05:20] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -30.005232 | E_var:     0.0725 | E_err:   0.004208
[2025-10-07 01:05:24] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -30.014867 | E_var:     0.0859 | E_err:   0.004581
[2025-10-07 01:05:28] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -30.013329 | E_var:     0.0986 | E_err:   0.004907
[2025-10-07 01:05:31] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -30.017722 | E_var:     0.0550 | E_err:   0.003666
[2025-10-07 01:05:35] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -30.013568 | E_var:     0.0524 | E_err:   0.003577
[2025-10-07 01:05:39] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -30.014400 | E_var:     0.0567 | E_err:   0.003722
[2025-10-07 01:05:42] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -30.011023 | E_var:     0.1001 | E_err:   0.004942
[2025-10-07 01:05:46] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -30.014455 | E_var:     0.0588 | E_err:   0.003789
[2025-10-07 01:05:50] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -30.005945 | E_var:     0.0777 | E_err:   0.004356
[2025-10-07 01:05:53] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -30.015957 | E_var:     0.0852 | E_err:   0.004560
[2025-10-07 01:05:57] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -30.007490 | E_var:     0.0563 | E_err:   0.003708
[2025-10-07 01:06:01] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -30.011137 | E_var:     0.0617 | E_err:   0.003882
[2025-10-07 01:06:04] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -30.010907 | E_var:     0.0634 | E_err:   0.003933
[2025-10-07 01:06:08] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -30.018731 | E_var:     0.0914 | E_err:   0.004725
[2025-10-07 01:06:12] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -30.011488 | E_var:     0.0745 | E_err:   0.004265
[2025-10-07 01:06:15] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -30.005738 | E_var:     0.0612 | E_err:   0.003867
[2025-10-07 01:06:19] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -30.014446 | E_var:     0.0640 | E_err:   0.003953
[2025-10-07 01:06:23] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -30.006157 | E_var:     0.0716 | E_err:   0.004180
[2025-10-07 01:06:26] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -30.017770 | E_var:     0.0663 | E_err:   0.004022
[2025-10-07 01:06:30] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -30.014653 | E_var:     0.0490 | E_err:   0.003460
[2025-10-07 01:06:33] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -30.014158 | E_var:     0.0763 | E_err:   0.004317
[2025-10-07 01:06:37] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -30.009378 | E_var:     0.0769 | E_err:   0.004332
[2025-10-07 01:06:41] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -30.015893 | E_var:     0.0858 | E_err:   0.004576
[2025-10-07 01:06:44] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -30.015114 | E_var:     0.1001 | E_err:   0.004943
[2025-10-07 01:06:48] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -30.010989 | E_var:     0.0618 | E_err:   0.003884
[2025-10-07 01:06:52] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -30.014566 | E_var:     0.0630 | E_err:   0.003923
[2025-10-07 01:06:55] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -30.013773 | E_var:     0.0579 | E_err:   0.003761
[2025-10-07 01:06:59] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -30.020277 | E_var:     0.0866 | E_err:   0.004599
[2025-10-07 01:07:03] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -30.013637 | E_var:     0.0836 | E_err:   0.004516
[2025-10-07 01:07:06] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -30.018606 | E_var:     0.0582 | E_err:   0.003771
[2025-10-07 01:07:10] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -30.010740 | E_var:     0.0554 | E_err:   0.003676
[2025-10-07 01:07:14] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -30.013456 | E_var:     0.0786 | E_err:   0.004380
[2025-10-07 01:07:17] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -30.008954 | E_var:     0.0611 | E_err:   0.003862
[2025-10-07 01:07:21] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -30.010321 | E_var:     0.0677 | E_err:   0.004067
[2025-10-07 01:07:25] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -30.015827 | E_var:     0.0498 | E_err:   0.003488
[2025-10-07 01:07:28] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -30.002476 | E_var:     0.0757 | E_err:   0.004298
[2025-10-07 01:07:32] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -30.010203 | E_var:     0.0703 | E_err:   0.004143
[2025-10-07 01:07:36] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -30.015334 | E_var:     0.0732 | E_err:   0.004227
[2025-10-07 01:07:39] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -30.011384 | E_var:     0.0534 | E_err:   0.003612
[2025-10-07 01:07:43] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -30.007582 | E_var:     0.1039 | E_err:   0.005036
[2025-10-07 01:07:47] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -30.019961 | E_var:     0.0584 | E_err:   0.003776
[2025-10-07 01:07:50] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -30.017141 | E_var:     0.0724 | E_err:   0.004204
[2025-10-07 01:07:54] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -30.011520 | E_var:     0.0693 | E_err:   0.004113
[2025-10-07 01:07:57] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -30.019542 | E_var:     0.0604 | E_err:   0.003841
[2025-10-07 01:08:01] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -30.008186 | E_var:     0.2633 | E_err:   0.008018
[2025-10-07 01:08:05] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -30.006675 | E_var:     0.0802 | E_err:   0.004424
[2025-10-07 01:08:08] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -30.012657 | E_var:     0.0552 | E_err:   0.003669
[2025-10-07 01:08:12] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -30.010309 | E_var:     0.0729 | E_err:   0.004218
[2025-10-07 01:08:16] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -30.016582 | E_var:     0.0632 | E_err:   0.003928
[2025-10-07 01:08:19] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -30.010543 | E_var:     0.0678 | E_err:   0.004068
[2025-10-07 01:08:23] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -30.014068 | E_var:     0.0720 | E_err:   0.004193
[2025-10-07 01:08:27] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -30.014047 | E_var:     0.0663 | E_err:   0.004023
[2025-10-07 01:08:30] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -30.010936 | E_var:     0.0620 | E_err:   0.003891
[2025-10-07 01:08:34] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -30.014946 | E_var:     0.0616 | E_err:   0.003879
[2025-10-07 01:08:38] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -30.018177 | E_var:     0.0836 | E_err:   0.004518
[2025-10-07 01:08:41] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -30.010883 | E_var:     0.0608 | E_err:   0.003853
[2025-10-07 01:08:45] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -30.013867 | E_var:     0.0645 | E_err:   0.003968
[2025-10-07 01:08:49] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -30.011666 | E_var:     0.0610 | E_err:   0.003860
[2025-10-07 01:08:52] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -30.006023 | E_var:     0.1016 | E_err:   0.004980
[2025-10-07 01:08:56] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -30.015508 | E_var:     0.0510 | E_err:   0.003527
[2025-10-07 01:08:59] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -30.012158 | E_var:     0.0718 | E_err:   0.004187
[2025-10-07 01:09:03] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -30.012200 | E_var:     0.1357 | E_err:   0.005756
[2025-10-07 01:09:07] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -30.007463 | E_var:     0.0590 | E_err:   0.003796
[2025-10-07 01:09:10] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -30.013117 | E_var:     0.0620 | E_err:   0.003892
[2025-10-07 01:09:14] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -30.011176 | E_var:     0.0557 | E_err:   0.003688
[2025-10-07 01:09:18] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -30.010288 | E_var:     0.0721 | E_err:   0.004194
[2025-10-07 01:09:21] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -30.014968 | E_var:     0.0608 | E_err:   0.003852
[2025-10-07 01:09:25] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -30.013085 | E_var:     0.0685 | E_err:   0.004090
[2025-10-07 01:09:29] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -30.015076 | E_var:     0.0901 | E_err:   0.004690
[2025-10-07 01:09:33] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -30.007459 | E_var:     0.0632 | E_err:   0.003929
[2025-10-07 01:09:36] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -30.009744 | E_var:     0.1801 | E_err:   0.006630
[2025-10-07 01:09:40] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -30.016189 | E_var:     0.0802 | E_err:   0.004426
[2025-10-07 01:09:44] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -30.005686 | E_var:     0.0718 | E_err:   0.004187
[2025-10-07 01:09:47] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -30.016014 | E_var:     0.0634 | E_err:   0.003934
[2025-10-07 01:09:51] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -30.012805 | E_var:     0.1091 | E_err:   0.005160
[2025-10-07 01:09:55] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -30.018846 | E_var:     0.0851 | E_err:   0.004559
[2025-10-07 01:09:58] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -30.009767 | E_var:     0.0581 | E_err:   0.003766
[2025-10-07 01:10:02] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -30.008511 | E_var:     0.0595 | E_err:   0.003812
[2025-10-07 01:10:06] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -30.005787 | E_var:     0.1097 | E_err:   0.005176
[2025-10-07 01:10:09] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -30.010119 | E_var:     0.0912 | E_err:   0.004718
[2025-10-07 01:10:13] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -30.012029 | E_var:     0.0642 | E_err:   0.003960
[2025-10-07 01:10:17] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -30.011414 | E_var:     0.0553 | E_err:   0.003676
[2025-10-07 01:10:20] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -30.007293 | E_var:     0.0662 | E_err:   0.004019
[2025-10-07 01:10:24] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -30.011425 | E_var:     0.0523 | E_err:   0.003572
[2025-10-07 01:10:27] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -30.016780 | E_var:     0.0550 | E_err:   0.003665
[2025-10-07 01:10:31] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -30.011910 | E_var:     0.0695 | E_err:   0.004118
[2025-10-07 01:10:35] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -30.008747 | E_var:     0.0806 | E_err:   0.004436
[2025-10-07 01:10:38] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -30.021186 | E_var:     0.0664 | E_err:   0.004027
[2025-10-07 01:10:42] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -30.010219 | E_var:     0.0668 | E_err:   0.004039
[2025-10-07 01:10:46] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -30.015995 | E_var:     0.0524 | E_err:   0.003577
[2025-10-07 01:10:49] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -30.010370 | E_var:     0.0745 | E_err:   0.004264
[2025-10-07 01:10:53] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -30.007347 | E_var:     0.0653 | E_err:   0.003992
[2025-10-07 01:10:57] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -30.014330 | E_var:     0.0700 | E_err:   0.004133
[2025-10-07 01:11:00] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -30.013598 | E_var:     0.1830 | E_err:   0.006685
[2025-10-07 01:11:00] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 01:11:04] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -30.012530 | E_var:     0.0586 | E_err:   0.003781
[2025-10-07 01:11:08] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -30.007980 | E_var:     0.0649 | E_err:   0.003982
[2025-10-07 01:11:11] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -30.010495 | E_var:     0.0646 | E_err:   0.003970
[2025-10-07 01:11:15] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -30.013645 | E_var:     0.0556 | E_err:   0.003685
[2025-10-07 01:11:19] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -30.007592 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 01:11:22] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -30.015044 | E_var:     0.0779 | E_err:   0.004361
[2025-10-07 01:11:26] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -30.012936 | E_var:     0.0736 | E_err:   0.004239
[2025-10-07 01:11:30] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -30.007136 | E_var:     0.0675 | E_err:   0.004059
[2025-10-07 01:11:33] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -30.003502 | E_var:     0.0638 | E_err:   0.003945
[2025-10-07 01:11:37] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -30.005207 | E_var:     0.1175 | E_err:   0.005355
[2025-10-07 01:11:41] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -30.009602 | E_var:     0.0589 | E_err:   0.003794
[2025-10-07 01:11:44] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -30.010753 | E_var:     0.0809 | E_err:   0.004444
[2025-10-07 01:11:48] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -30.011586 | E_var:     0.0708 | E_err:   0.004157
[2025-10-07 01:11:52] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -30.010844 | E_var:     0.0502 | E_err:   0.003500
[2025-10-07 01:11:55] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -30.016572 | E_var:     0.0645 | E_err:   0.003967
[2025-10-07 01:11:59] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -30.016465 | E_var:     0.0732 | E_err:   0.004227
[2025-10-07 01:12:03] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -30.014601 | E_var:     0.0627 | E_err:   0.003911
[2025-10-07 01:12:06] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -30.014396 | E_var:     0.0741 | E_err:   0.004254
[2025-10-07 01:12:10] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -30.008448 | E_var:     0.0700 | E_err:   0.004135
[2025-10-07 01:12:14] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -30.009674 | E_var:     0.0539 | E_err:   0.003629
[2025-10-07 01:12:17] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -30.005151 | E_var:     0.0602 | E_err:   0.003835
[2025-10-07 01:12:21] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -30.016879 | E_var:     0.0661 | E_err:   0.004019
[2025-10-07 01:12:24] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -30.011869 | E_var:     0.0806 | E_err:   0.004435
[2025-10-07 01:12:28] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -30.012696 | E_var:     0.0796 | E_err:   0.004409
[2025-10-07 01:12:32] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -30.013676 | E_var:     0.0802 | E_err:   0.004424
[2025-10-07 01:12:35] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -30.015727 | E_var:     0.0526 | E_err:   0.003585
[2025-10-07 01:12:39] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -30.013329 | E_var:     0.0682 | E_err:   0.004081
[2025-10-07 01:12:43] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -30.015072 | E_var:     0.0649 | E_err:   0.003982
[2025-10-07 01:12:46] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -30.015570 | E_var:     0.0784 | E_err:   0.004375
[2025-10-07 01:12:50] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -30.018285 | E_var:     0.0617 | E_err:   0.003882
[2025-10-07 01:12:54] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -30.009478 | E_var:     0.0722 | E_err:   0.004200
[2025-10-07 01:12:57] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -30.015090 | E_var:     0.0759 | E_err:   0.004304
[2025-10-07 01:13:01] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -30.008568 | E_var:     0.0572 | E_err:   0.003735
[2025-10-07 01:13:05] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -30.016349 | E_var:     0.0613 | E_err:   0.003869
[2025-10-07 01:13:08] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -30.013038 | E_var:     0.0909 | E_err:   0.004711
[2025-10-07 01:13:12] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -30.012248 | E_var:     0.0686 | E_err:   0.004094
[2025-10-07 01:13:16] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -30.015195 | E_var:     0.0675 | E_err:   0.004060
[2025-10-07 01:13:19] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -30.005298 | E_var:     0.0632 | E_err:   0.003929
[2025-10-07 01:13:23] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -30.011947 | E_var:     0.0690 | E_err:   0.004105
[2025-10-07 01:13:27] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -30.015548 | E_var:     0.0634 | E_err:   0.003934
[2025-10-07 01:13:30] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -30.007485 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 01:13:34] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -30.016479 | E_var:     0.0557 | E_err:   0.003686
[2025-10-07 01:13:38] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -30.015797 | E_var:     0.0574 | E_err:   0.003744
[2025-10-07 01:13:41] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -30.010294 | E_var:     0.0771 | E_err:   0.004338
[2025-10-07 01:13:45] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -30.012794 | E_var:     0.0541 | E_err:   0.003633
[2025-10-07 01:13:49] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -30.015676 | E_var:     0.0522 | E_err:   0.003571
[2025-10-07 01:13:52] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -30.002767 | E_var:     0.0692 | E_err:   0.004109
[2025-10-07 01:13:56] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -30.013920 | E_var:     0.0664 | E_err:   0.004026
[2025-10-07 01:13:59] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -30.014213 | E_var:     0.0598 | E_err:   0.003820
[2025-10-07 01:14:03] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -30.007384 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 01:14:07] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -30.009019 | E_var:     0.0612 | E_err:   0.003865
[2025-10-07 01:14:10] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -30.004298 | E_var:     0.0647 | E_err:   0.003975
[2025-10-07 01:14:14] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -30.011257 | E_var:     0.0648 | E_err:   0.003978
[2025-10-07 01:14:18] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -30.013925 | E_var:     0.0724 | E_err:   0.004204
[2025-10-07 01:14:21] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -30.016545 | E_var:     0.0636 | E_err:   0.003940
[2025-10-07 01:14:25] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -30.015449 | E_var:     0.0549 | E_err:   0.003661
[2025-10-07 01:14:29] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -30.012779 | E_var:     0.0515 | E_err:   0.003547
[2025-10-07 01:14:32] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -30.012412 | E_var:     0.0717 | E_err:   0.004185
[2025-10-07 01:14:36] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -30.014022 | E_var:     0.0562 | E_err:   0.003706
[2025-10-07 01:14:40] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -30.014687 | E_var:     0.0664 | E_err:   0.004026
[2025-10-07 01:14:43] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -30.010198 | E_var:     0.0646 | E_err:   0.003970
[2025-10-07 01:14:47] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -30.012180 | E_var:     0.0721 | E_err:   0.004196
[2025-10-07 01:14:51] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -30.015637 | E_var:     0.0634 | E_err:   0.003934
[2025-10-07 01:14:54] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -30.019615 | E_var:     0.0716 | E_err:   0.004180
[2025-10-07 01:14:58] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -30.016086 | E_var:     0.0673 | E_err:   0.004054
[2025-10-07 01:15:02] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -30.007208 | E_var:     0.0627 | E_err:   0.003912
[2025-10-07 01:15:05] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -30.006263 | E_var:     0.0554 | E_err:   0.003677
[2025-10-07 01:15:09] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -30.012399 | E_var:     0.0571 | E_err:   0.003733
[2025-10-07 01:15:13] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -30.014926 | E_var:     0.0670 | E_err:   0.004045
[2025-10-07 01:15:16] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -30.009748 | E_var:     0.0890 | E_err:   0.004661
[2025-10-07 01:15:20] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -30.015062 | E_var:     0.0770 | E_err:   0.004335
[2025-10-07 01:15:23] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -30.014170 | E_var:     0.0637 | E_err:   0.003942
[2025-10-07 01:15:27] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -30.004584 | E_var:     0.0736 | E_err:   0.004240
[2025-10-07 01:15:31] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -30.016659 | E_var:     0.0709 | E_err:   0.004160
[2025-10-07 01:15:34] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -30.018535 | E_var:     0.0565 | E_err:   0.003714
[2025-10-07 01:15:38] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -30.010079 | E_var:     0.0672 | E_err:   0.004050
[2025-10-07 01:15:42] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -30.014637 | E_var:     0.0585 | E_err:   0.003778
[2025-10-07 01:15:45] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -30.015650 | E_var:     0.0668 | E_err:   0.004039
[2025-10-07 01:15:49] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -30.013300 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 01:15:53] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -30.010593 | E_var:     0.0769 | E_err:   0.004334
[2025-10-07 01:15:56] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -30.011808 | E_var:     0.0567 | E_err:   0.003722
[2025-10-07 01:16:00] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -30.012351 | E_var:     0.0579 | E_err:   0.003760
[2025-10-07 01:16:04] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -30.020829 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 01:16:07] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -30.013215 | E_var:     0.0629 | E_err:   0.003917
[2025-10-07 01:16:11] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -30.012108 | E_var:     0.0578 | E_err:   0.003758
[2025-10-07 01:16:15] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -30.009660 | E_var:     0.0637 | E_err:   0.003945
[2025-10-07 01:16:18] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -30.009424 | E_var:     0.0642 | E_err:   0.003960
[2025-10-07 01:16:22] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -30.013038 | E_var:     0.0587 | E_err:   0.003784
[2025-10-07 01:16:26] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -30.016354 | E_var:     0.0523 | E_err:   0.003575
[2025-10-07 01:16:29] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -30.007684 | E_var:     0.0640 | E_err:   0.003952
[2025-10-07 01:16:33] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -30.007035 | E_var:     0.0598 | E_err:   0.003822
[2025-10-07 01:16:37] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -30.005557 | E_var:     0.1066 | E_err:   0.005102
[2025-10-07 01:16:40] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -30.014766 | E_var:     0.0714 | E_err:   0.004176
[2025-10-07 01:16:44] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -30.005689 | E_var:     0.0657 | E_err:   0.004004
[2025-10-07 01:16:47] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -30.017918 | E_var:     0.0820 | E_err:   0.004474
[2025-10-07 01:16:51] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -30.006611 | E_var:     0.0556 | E_err:   0.003686
[2025-10-07 01:16:55] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -30.016154 | E_var:     0.0549 | E_err:   0.003661
[2025-10-07 01:16:58] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -30.008619 | E_var:     0.0498 | E_err:   0.003486
[2025-10-07 01:17:02] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -30.015554 | E_var:     0.0632 | E_err:   0.003929
[2025-10-07 01:17:06] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -30.016384 | E_var:     0.0533 | E_err:   0.003606
[2025-10-07 01:17:06] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 01:17:09] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -30.012819 | E_var:     0.0514 | E_err:   0.003543
[2025-10-07 01:17:13] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -30.008561 | E_var:     0.1286 | E_err:   0.005603
[2025-10-07 01:17:17] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -30.011101 | E_var:     0.0605 | E_err:   0.003842
[2025-10-07 01:17:20] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -30.014732 | E_var:     0.0747 | E_err:   0.004272
[2025-10-07 01:17:24] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -30.012791 | E_var:     0.0957 | E_err:   0.004833
[2025-10-07 01:17:28] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -30.012666 | E_var:     0.0659 | E_err:   0.004012
[2025-10-07 01:17:31] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -30.009599 | E_var:     0.0908 | E_err:   0.004707
[2025-10-07 01:17:35] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -30.017298 | E_var:     0.0752 | E_err:   0.004285
[2025-10-07 01:17:39] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -30.007779 | E_var:     0.0681 | E_err:   0.004077
[2025-10-07 01:17:42] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -30.010918 | E_var:     0.0693 | E_err:   0.004113
[2025-10-07 01:17:46] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -30.014076 | E_var:     0.0566 | E_err:   0.003716
[2025-10-07 01:17:50] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -30.018358 | E_var:     0.0633 | E_err:   0.003932
[2025-10-07 01:17:53] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -30.022344 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 01:17:57] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -30.008041 | E_var:     0.0697 | E_err:   0.004126
[2025-10-07 01:18:01] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -30.012474 | E_var:     0.0640 | E_err:   0.003953
[2025-10-07 01:18:04] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -30.012678 | E_var:     0.0636 | E_err:   0.003940
[2025-10-07 01:18:08] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -30.009577 | E_var:     0.0650 | E_err:   0.003984
[2025-10-07 01:18:12] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -30.017626 | E_var:     0.1399 | E_err:   0.005845
[2025-10-07 01:18:15] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -30.020400 | E_var:     0.2170 | E_err:   0.007278
[2025-10-07 01:18:19] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -30.015578 | E_var:     0.0688 | E_err:   0.004099
[2025-10-07 01:18:23] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -30.014789 | E_var:     0.0716 | E_err:   0.004181
[2025-10-07 01:18:26] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -30.013963 | E_var:     0.0583 | E_err:   0.003773
[2025-10-07 01:18:30] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -30.011977 | E_var:     0.0592 | E_err:   0.003803
[2025-10-07 01:18:34] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -30.007797 | E_var:     0.0557 | E_err:   0.003688
[2025-10-07 01:18:37] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -30.015117 | E_var:     0.0464 | E_err:   0.003366
[2025-10-07 01:18:41] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -30.007264 | E_var:     0.0671 | E_err:   0.004048
[2025-10-07 01:18:45] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -30.010564 | E_var:     0.0661 | E_err:   0.004018
[2025-10-07 01:18:48] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -30.015518 | E_var:     0.0747 | E_err:   0.004271
[2025-10-07 01:18:52] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -30.014028 | E_var:     0.0677 | E_err:   0.004065
[2025-10-07 01:18:55] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -30.012161 | E_var:     0.1227 | E_err:   0.005474
[2025-10-07 01:18:59] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -30.016820 | E_var:     0.0616 | E_err:   0.003877
[2025-10-07 01:19:03] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -30.003108 | E_var:     0.1135 | E_err:   0.005263
[2025-10-07 01:19:06] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -30.017876 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 01:19:10] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -30.010570 | E_var:     0.0655 | E_err:   0.003999
[2025-10-07 01:19:14] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -30.011362 | E_var:     0.0571 | E_err:   0.003735
[2025-10-07 01:19:17] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -30.009750 | E_var:     0.0562 | E_err:   0.003704
[2025-10-07 01:19:21] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -30.014777 | E_var:     0.0529 | E_err:   0.003593
[2025-10-07 01:19:25] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -30.003741 | E_var:     0.0650 | E_err:   0.003984
[2025-10-07 01:19:28] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -30.008492 | E_var:     0.0608 | E_err:   0.003854
[2025-10-07 01:19:32] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -30.013707 | E_var:     0.0637 | E_err:   0.003943
[2025-10-07 01:19:36] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -30.011665 | E_var:     0.0773 | E_err:   0.004345
[2025-10-07 01:19:39] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -30.012257 | E_var:     0.0486 | E_err:   0.003444
[2025-10-07 01:19:43] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -30.007896 | E_var:     0.0589 | E_err:   0.003791
[2025-10-07 01:19:47] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -30.011682 | E_var:     0.0542 | E_err:   0.003638
[2025-10-07 01:19:50] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -30.015723 | E_var:     0.0688 | E_err:   0.004099
[2025-10-07 01:19:54] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -30.017454 | E_var:     0.0554 | E_err:   0.003678
[2025-10-07 01:19:58] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -30.008480 | E_var:     0.0750 | E_err:   0.004280
[2025-10-07 01:20:01] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -30.006007 | E_var:     0.0670 | E_err:   0.004045
[2025-10-07 01:20:05] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -30.006759 | E_var:     0.0793 | E_err:   0.004401
[2025-10-07 01:20:08] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -30.011919 | E_var:     0.0751 | E_err:   0.004282
[2025-10-07 01:20:08] 🔄 RESTART #2 | Period: 600
[2025-10-07 01:20:12] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -30.012230 | E_var:     0.0551 | E_err:   0.003669
[2025-10-07 01:20:16] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -30.012776 | E_var:     0.0594 | E_err:   0.003808
[2025-10-07 01:20:19] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -30.015240 | E_var:     0.0565 | E_err:   0.003714
[2025-10-07 01:20:23] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -30.016049 | E_var:     0.0672 | E_err:   0.004049
[2025-10-07 01:20:27] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -30.008988 | E_var:     0.0487 | E_err:   0.003448
[2025-10-07 01:20:30] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -30.015976 | E_var:     0.0560 | E_err:   0.003697
[2025-10-07 01:20:34] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -30.011888 | E_var:     0.0683 | E_err:   0.004084
[2025-10-07 01:20:38] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -30.016400 | E_var:     0.0757 | E_err:   0.004299
[2025-10-07 01:20:41] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -30.010676 | E_var:     0.0522 | E_err:   0.003571
[2025-10-07 01:20:45] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -30.017203 | E_var:     0.0670 | E_err:   0.004044
[2025-10-07 01:20:49] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -30.014671 | E_var:     0.0779 | E_err:   0.004360
[2025-10-07 01:20:52] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -30.010121 | E_var:     0.0602 | E_err:   0.003833
[2025-10-07 01:20:56] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -30.018503 | E_var:     0.0817 | E_err:   0.004465
[2025-10-07 01:21:00] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -30.014338 | E_var:     0.0582 | E_err:   0.003768
[2025-10-07 01:21:03] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -30.010962 | E_var:     0.0637 | E_err:   0.003942
[2025-10-07 01:21:07] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -30.014467 | E_var:     0.0638 | E_err:   0.003946
[2025-10-07 01:21:11] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -30.014640 | E_var:     0.0514 | E_err:   0.003541
[2025-10-07 01:21:14] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -30.020499 | E_var:     0.0737 | E_err:   0.004242
[2025-10-07 01:21:18] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -30.008998 | E_var:     0.1020 | E_err:   0.004990
[2025-10-07 01:21:21] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -30.017875 | E_var:     0.0599 | E_err:   0.003824
[2025-10-07 01:21:25] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -30.008987 | E_var:     0.0553 | E_err:   0.003673
[2025-10-07 01:21:29] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -30.005521 | E_var:     0.0652 | E_err:   0.003990
[2025-10-07 01:21:32] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -30.009897 | E_var:     0.1051 | E_err:   0.005064
[2025-10-07 01:21:36] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -30.016751 | E_var:     0.0881 | E_err:   0.004638
[2025-10-07 01:21:40] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -30.013245 | E_var:     0.0678 | E_err:   0.004067
[2025-10-07 01:21:43] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -30.014262 | E_var:     0.0547 | E_err:   0.003655
[2025-10-07 01:21:47] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -30.007962 | E_var:     0.0760 | E_err:   0.004308
[2025-10-07 01:21:51] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -30.013353 | E_var:     0.0719 | E_err:   0.004190
[2025-10-07 01:21:54] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -30.012088 | E_var:     0.1143 | E_err:   0.005281
[2025-10-07 01:21:58] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -30.010922 | E_var:     0.0731 | E_err:   0.004226
[2025-10-07 01:22:02] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -30.012517 | E_var:     0.0555 | E_err:   0.003680
[2025-10-07 01:22:05] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -30.003736 | E_var:     0.0691 | E_err:   0.004108
[2025-10-07 01:22:09] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -30.019182 | E_var:     0.1042 | E_err:   0.005043
[2025-10-07 01:22:13] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -30.012628 | E_var:     0.0586 | E_err:   0.003782
[2025-10-07 01:22:16] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -30.013381 | E_var:     0.0687 | E_err:   0.004096
[2025-10-07 01:22:20] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -30.012777 | E_var:     0.0585 | E_err:   0.003778
[2025-10-07 01:22:24] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -30.009461 | E_var:     0.0575 | E_err:   0.003746
[2025-10-07 01:22:27] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -30.010312 | E_var:     0.0581 | E_err:   0.003766
[2025-10-07 01:22:31] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -30.013252 | E_var:     0.0682 | E_err:   0.004080
[2025-10-07 01:22:35] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -30.014005 | E_var:     0.0719 | E_err:   0.004191
[2025-10-07 01:22:38] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -30.014199 | E_var:     0.0552 | E_err:   0.003672
[2025-10-07 01:22:42] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -30.004061 | E_var:     0.0646 | E_err:   0.003972
[2025-10-07 01:22:45] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -30.016627 | E_var:     0.1376 | E_err:   0.005796
[2025-10-07 01:22:49] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -30.015422 | E_var:     0.0834 | E_err:   0.004513
[2025-10-07 01:22:53] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -30.009986 | E_var:     0.0594 | E_err:   0.003807
[2025-10-07 01:22:56] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -30.014531 | E_var:     0.0635 | E_err:   0.003937
[2025-10-07 01:23:00] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -30.012514 | E_var:     0.0538 | E_err:   0.003623
[2025-10-07 01:23:04] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -30.012270 | E_var:     0.0618 | E_err:   0.003884
[2025-10-07 01:23:07] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -30.010088 | E_var:     0.0641 | E_err:   0.003956
[2025-10-07 01:23:11] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -30.009066 | E_var:     0.0509 | E_err:   0.003526
[2025-10-07 01:23:11] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 01:23:15] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -30.012111 | E_var:     0.0550 | E_err:   0.003664
[2025-10-07 01:23:18] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -30.008046 | E_var:     0.0764 | E_err:   0.004319
[2025-10-07 01:23:22] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -30.010619 | E_var:     0.0849 | E_err:   0.004552
[2025-10-07 01:23:26] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -30.003142 | E_var:     0.0852 | E_err:   0.004562
[2025-10-07 01:23:29] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -30.015008 | E_var:     0.0765 | E_err:   0.004321
[2025-10-07 01:23:33] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -30.014073 | E_var:     0.0756 | E_err:   0.004297
[2025-10-07 01:23:37] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -30.008079 | E_var:     0.0749 | E_err:   0.004277
[2025-10-07 01:23:40] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -30.008960 | E_var:     0.0640 | E_err:   0.003954
[2025-10-07 01:23:44] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -30.012968 | E_var:     0.0749 | E_err:   0.004277
[2025-10-07 01:23:48] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -30.010507 | E_var:     0.0816 | E_err:   0.004463
[2025-10-07 01:23:51] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -30.017074 | E_var:     0.0603 | E_err:   0.003836
[2025-10-07 01:23:55] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -30.016412 | E_var:     0.0683 | E_err:   0.004084
[2025-10-07 01:23:59] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -30.010968 | E_var:     0.0732 | E_err:   0.004228
[2025-10-07 01:24:02] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -30.010469 | E_var:     0.0761 | E_err:   0.004309
[2025-10-07 01:24:06] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -30.015489 | E_var:     0.0614 | E_err:   0.003871
[2025-10-07 01:24:10] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -30.011494 | E_var:     0.0558 | E_err:   0.003691
[2025-10-07 01:24:13] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -30.015775 | E_var:     0.0600 | E_err:   0.003826
[2025-10-07 01:24:17] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -30.000807 | E_var:     0.0683 | E_err:   0.004085
[2025-10-07 01:24:20] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -30.016567 | E_var:     0.0540 | E_err:   0.003631
[2025-10-07 01:24:24] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -30.016235 | E_var:     0.0812 | E_err:   0.004452
[2025-10-07 01:24:28] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -30.010995 | E_var:     0.0670 | E_err:   0.004045
[2025-10-07 01:24:31] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -30.012288 | E_var:     0.0534 | E_err:   0.003612
[2025-10-07 01:24:35] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -30.009279 | E_var:     0.0541 | E_err:   0.003636
[2025-10-07 01:24:39] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -30.011199 | E_var:     0.0805 | E_err:   0.004433
[2025-10-07 01:24:42] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -30.013525 | E_var:     0.0544 | E_err:   0.003645
[2025-10-07 01:24:46] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -30.012741 | E_var:     0.0746 | E_err:   0.004267
[2025-10-07 01:24:50] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -30.016592 | E_var:     0.0599 | E_err:   0.003825
[2025-10-07 01:24:53] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -30.011767 | E_var:     0.0646 | E_err:   0.003972
[2025-10-07 01:24:57] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -30.013358 | E_var:     0.0628 | E_err:   0.003917
[2025-10-07 01:25:01] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -30.011781 | E_var:     0.0677 | E_err:   0.004064
[2025-10-07 01:25:04] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -30.012125 | E_var:     0.0556 | E_err:   0.003684
[2025-10-07 01:25:08] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -30.012306 | E_var:     0.0544 | E_err:   0.003645
[2025-10-07 01:25:12] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -30.021173 | E_var:     0.0705 | E_err:   0.004150
[2025-10-07 01:25:15] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -30.019684 | E_var:     0.0805 | E_err:   0.004434
[2025-10-07 01:25:19] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -30.017327 | E_var:     0.0602 | E_err:   0.003834
[2025-10-07 01:25:23] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -30.015879 | E_var:     0.0999 | E_err:   0.004938
[2025-10-07 01:25:26] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -30.013018 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 01:25:30] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -30.016605 | E_var:     0.1009 | E_err:   0.004963
[2025-10-07 01:25:33] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -30.015207 | E_var:     0.0704 | E_err:   0.004146
[2025-10-07 01:25:37] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -30.009131 | E_var:     0.0648 | E_err:   0.003978
[2025-10-07 01:25:41] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -30.010086 | E_var:     0.0775 | E_err:   0.004350
[2025-10-07 01:25:44] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -30.012434 | E_var:     0.0681 | E_err:   0.004076
[2025-10-07 01:25:48] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -30.013343 | E_var:     0.0548 | E_err:   0.003659
[2025-10-07 01:25:52] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -30.008859 | E_var:     0.0985 | E_err:   0.004903
[2025-10-07 01:25:55] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -30.006164 | E_var:     0.0694 | E_err:   0.004118
[2025-10-07 01:25:59] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -30.012412 | E_var:     0.0657 | E_err:   0.004004
[2025-10-07 01:26:03] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -30.010212 | E_var:     0.0639 | E_err:   0.003950
[2025-10-07 01:26:06] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -30.016770 | E_var:     0.0599 | E_err:   0.003825
[2025-10-07 01:26:10] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -30.013638 | E_var:     0.0721 | E_err:   0.004196
[2025-10-07 01:26:14] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -30.010218 | E_var:     0.0528 | E_err:   0.003591
[2025-10-07 01:26:17] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -30.022045 | E_var:     0.0710 | E_err:   0.004164
[2025-10-07 01:26:21] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -30.014757 | E_var:     0.0591 | E_err:   0.003799
[2025-10-07 01:26:25] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -30.010174 | E_var:     0.0484 | E_err:   0.003438
[2025-10-07 01:26:28] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -30.015826 | E_var:     0.0605 | E_err:   0.003845
[2025-10-07 01:26:32] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -30.017232 | E_var:     0.0927 | E_err:   0.004758
[2025-10-07 01:26:36] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -30.013741 | E_var:     0.0733 | E_err:   0.004230
[2025-10-07 01:26:39] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -30.008805 | E_var:     0.0591 | E_err:   0.003798
[2025-10-07 01:26:43] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -30.009710 | E_var:     0.0834 | E_err:   0.004512
[2025-10-07 01:26:46] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -30.016608 | E_var:     0.0824 | E_err:   0.004485
[2025-10-07 01:26:50] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -30.013450 | E_var:     0.0546 | E_err:   0.003650
[2025-10-07 01:26:54] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -30.006549 | E_var:     0.0740 | E_err:   0.004250
[2025-10-07 01:26:57] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -30.023373 | E_var:     0.0829 | E_err:   0.004498
[2025-10-07 01:27:01] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -30.016272 | E_var:     0.0621 | E_err:   0.003893
[2025-10-07 01:27:05] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -30.006094 | E_var:     0.0702 | E_err:   0.004141
[2025-10-07 01:27:08] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -30.010997 | E_var:     0.0604 | E_err:   0.003840
[2025-10-07 01:27:12] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -30.003339 | E_var:     0.0671 | E_err:   0.004048
[2025-10-07 01:27:16] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -30.011680 | E_var:     0.0516 | E_err:   0.003550
[2025-10-07 01:27:19] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -30.004797 | E_var:     0.0622 | E_err:   0.003898
[2025-10-07 01:27:23] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -30.012292 | E_var:     0.0723 | E_err:   0.004202
[2025-10-07 01:27:27] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -30.016458 | E_var:     0.0581 | E_err:   0.003765
[2025-10-07 01:27:30] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -30.011346 | E_var:     0.0606 | E_err:   0.003848
[2025-10-07 01:27:34] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -30.018528 | E_var:     0.0791 | E_err:   0.004395
[2025-10-07 01:27:38] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -30.012447 | E_var:     0.0657 | E_err:   0.004005
[2025-10-07 01:27:41] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -30.011473 | E_var:     0.0731 | E_err:   0.004224
[2025-10-07 01:27:45] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -30.003398 | E_var:     0.0548 | E_err:   0.003658
[2025-10-07 01:27:49] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -30.010479 | E_var:     0.0653 | E_err:   0.003993
[2025-10-07 01:27:52] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -30.013713 | E_var:     0.0723 | E_err:   0.004201
[2025-10-07 01:27:56] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -30.005319 | E_var:     0.0634 | E_err:   0.003935
[2025-10-07 01:28:00] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -30.015025 | E_var:     0.0744 | E_err:   0.004261
[2025-10-07 01:28:03] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -30.015216 | E_var:     0.0550 | E_err:   0.003666
[2025-10-07 01:28:07] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -30.007390 | E_var:     0.0587 | E_err:   0.003785
[2025-10-07 01:28:10] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -30.009101 | E_var:     0.0635 | E_err:   0.003939
[2025-10-07 01:28:14] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -30.018640 | E_var:     0.0575 | E_err:   0.003748
[2025-10-07 01:28:18] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -30.013960 | E_var:     0.0604 | E_err:   0.003839
[2025-10-07 01:28:21] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -30.010605 | E_var:     0.0585 | E_err:   0.003778
[2025-10-07 01:28:25] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -30.016111 | E_var:     0.0657 | E_err:   0.004004
[2025-10-07 01:28:29] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -30.012142 | E_var:     0.0748 | E_err:   0.004275
[2025-10-07 01:28:32] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -30.013901 | E_var:     0.0672 | E_err:   0.004051
[2025-10-07 01:28:36] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -30.010271 | E_var:     0.0622 | E_err:   0.003898
[2025-10-07 01:28:40] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -30.013131 | E_var:     0.0923 | E_err:   0.004748
[2025-10-07 01:28:43] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -30.014824 | E_var:     0.0592 | E_err:   0.003801
[2025-10-07 01:28:47] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -30.012363 | E_var:     0.1042 | E_err:   0.005044
[2025-10-07 01:28:51] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -30.002788 | E_var:     0.0652 | E_err:   0.003990
[2025-10-07 01:28:54] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -30.009884 | E_var:     0.0571 | E_err:   0.003734
[2025-10-07 01:28:58] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -30.013349 | E_var:     0.0629 | E_err:   0.003920
[2025-10-07 01:29:02] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -30.013443 | E_var:     0.0711 | E_err:   0.004167
[2025-10-07 01:29:05] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -30.005887 | E_var:     0.0674 | E_err:   0.004055
[2025-10-07 01:29:09] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -30.019996 | E_var:     0.0650 | E_err:   0.003984
[2025-10-07 01:29:13] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -30.012777 | E_var:     0.0634 | E_err:   0.003935
[2025-10-07 01:29:16] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -30.013749 | E_var:     0.0614 | E_err:   0.003870
[2025-10-07 01:29:16] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 01:29:20] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -30.015097 | E_var:     0.0637 | E_err:   0.003942
[2025-10-07 01:29:24] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -30.013580 | E_var:     0.0809 | E_err:   0.004444
[2025-10-07 01:29:27] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -30.012672 | E_var:     0.0742 | E_err:   0.004255
[2025-10-07 01:29:31] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -30.017130 | E_var:     0.0700 | E_err:   0.004134
[2025-10-07 01:29:35] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -30.009727 | E_var:     0.0709 | E_err:   0.004161
[2025-10-07 01:29:38] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -30.018531 | E_var:     0.0754 | E_err:   0.004290
[2025-10-07 01:29:42] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -30.012506 | E_var:     0.0533 | E_err:   0.003608
[2025-10-07 01:29:45] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -30.011477 | E_var:     0.0848 | E_err:   0.004550
[2025-10-07 01:29:49] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -30.009813 | E_var:     0.0526 | E_err:   0.003585
[2025-10-07 01:29:53] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -30.012251 | E_var:     0.0444 | E_err:   0.003293
[2025-10-07 01:29:56] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -30.013596 | E_var:     0.0627 | E_err:   0.003913
[2025-10-07 01:30:00] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -30.005035 | E_var:     0.1439 | E_err:   0.005927
[2025-10-07 01:30:04] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -30.008720 | E_var:     0.0825 | E_err:   0.004487
[2025-10-07 01:30:07] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -30.010604 | E_var:     0.0644 | E_err:   0.003965
[2025-10-07 01:30:11] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -30.008787 | E_var:     0.0576 | E_err:   0.003749
[2025-10-07 01:30:15] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -30.017549 | E_var:     0.0531 | E_err:   0.003600
[2025-10-07 01:30:18] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -30.013233 | E_var:     0.0754 | E_err:   0.004291
[2025-10-07 01:30:22] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -30.005954 | E_var:     0.0503 | E_err:   0.003505
[2025-10-07 01:30:26] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -30.008945 | E_var:     0.0560 | E_err:   0.003697
[2025-10-07 01:30:29] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -30.012356 | E_var:     0.0670 | E_err:   0.004044
[2025-10-07 01:30:33] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -30.009199 | E_var:     0.0761 | E_err:   0.004309
[2025-10-07 01:30:37] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -30.010210 | E_var:     0.0686 | E_err:   0.004093
[2025-10-07 01:30:40] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -30.005415 | E_var:     0.0771 | E_err:   0.004338
[2025-10-07 01:30:44] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -30.018449 | E_var:     0.0646 | E_err:   0.003971
[2025-10-07 01:30:48] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -30.016544 | E_var:     0.0611 | E_err:   0.003861
[2025-10-07 01:30:51] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -30.013662 | E_var:     0.0514 | E_err:   0.003544
[2025-10-07 01:30:55] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -30.012282 | E_var:     0.0585 | E_err:   0.003779
[2025-10-07 01:30:59] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -30.015085 | E_var:     0.0643 | E_err:   0.003963
[2025-10-07 01:31:02] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -30.016297 | E_var:     0.0750 | E_err:   0.004278
[2025-10-07 01:31:06] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -30.018981 | E_var:     0.0660 | E_err:   0.004014
[2025-10-07 01:31:10] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -30.014889 | E_var:     0.0596 | E_err:   0.003815
[2025-10-07 01:31:13] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -30.013479 | E_var:     0.0591 | E_err:   0.003800
[2025-10-07 01:31:17] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -30.005197 | E_var:     0.0561 | E_err:   0.003699
[2025-10-07 01:31:21] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -30.024953 | E_var:     0.0564 | E_err:   0.003709
[2025-10-07 01:31:24] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -30.017813 | E_var:     0.0555 | E_err:   0.003683
[2025-10-07 01:31:28] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -30.013491 | E_var:     0.0719 | E_err:   0.004189
[2025-10-07 01:31:31] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -30.010150 | E_var:     0.0508 | E_err:   0.003523
[2025-10-07 01:31:35] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -30.016199 | E_var:     0.0536 | E_err:   0.003616
[2025-10-07 01:31:39] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -30.008498 | E_var:     0.0618 | E_err:   0.003884
[2025-10-07 01:31:42] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -30.010146 | E_var:     0.0727 | E_err:   0.004214
[2025-10-07 01:31:46] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -30.014817 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 01:31:50] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -30.006977 | E_var:     0.0688 | E_err:   0.004098
[2025-10-07 01:31:53] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -30.012553 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 01:31:57] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -30.008944 | E_var:     0.0832 | E_err:   0.004507
[2025-10-07 01:32:01] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -30.003637 | E_var:     0.0521 | E_err:   0.003568
[2025-10-07 01:32:04] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -30.013757 | E_var:     0.0678 | E_err:   0.004067
[2025-10-07 01:32:08] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -30.007037 | E_var:     0.0691 | E_err:   0.004107
[2025-10-07 01:32:12] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -30.010206 | E_var:     0.0763 | E_err:   0.004317
[2025-10-07 01:32:15] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -30.002917 | E_var:     0.0542 | E_err:   0.003636
[2025-10-07 01:32:19] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -30.011927 | E_var:     0.0792 | E_err:   0.004398
[2025-10-07 01:32:23] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -30.014721 | E_var:     0.0591 | E_err:   0.003800
[2025-10-07 01:32:26] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -30.017176 | E_var:     0.0526 | E_err:   0.003583
[2025-10-07 01:32:30] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -30.016005 | E_var:     0.0742 | E_err:   0.004255
[2025-10-07 01:32:34] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -30.014172 | E_var:     0.0639 | E_err:   0.003949
[2025-10-07 01:32:37] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -30.015575 | E_var:     0.1058 | E_err:   0.005083
[2025-10-07 01:32:41] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -30.018975 | E_var:     0.0536 | E_err:   0.003616
[2025-10-07 01:32:45] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -30.010368 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 01:32:48] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -30.019862 | E_var:     0.0822 | E_err:   0.004479
[2025-10-07 01:32:52] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -30.010482 | E_var:     0.0537 | E_err:   0.003622
[2025-10-07 01:32:55] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -30.012754 | E_var:     0.0555 | E_err:   0.003681
[2025-10-07 01:32:59] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -30.011938 | E_var:     0.0527 | E_err:   0.003586
[2025-10-07 01:33:03] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -30.012749 | E_var:     0.0654 | E_err:   0.003996
[2025-10-07 01:33:06] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -30.011422 | E_var:     0.0541 | E_err:   0.003635
[2025-10-07 01:33:10] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -30.023258 | E_var:     0.0855 | E_err:   0.004568
[2025-10-07 01:33:14] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -30.010699 | E_var:     0.0640 | E_err:   0.003954
[2025-10-07 01:33:17] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -30.018608 | E_var:     0.0748 | E_err:   0.004273
[2025-10-07 01:33:21] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -30.007394 | E_var:     0.0593 | E_err:   0.003804
[2025-10-07 01:33:25] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -30.008844 | E_var:     0.0671 | E_err:   0.004048
[2025-10-07 01:33:28] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -30.012411 | E_var:     0.0791 | E_err:   0.004395
[2025-10-07 01:33:32] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -30.014023 | E_var:     0.0591 | E_err:   0.003797
[2025-10-07 01:33:36] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -30.013014 | E_var:     0.0545 | E_err:   0.003647
[2025-10-07 01:33:39] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -30.015834 | E_var:     0.0579 | E_err:   0.003760
[2025-10-07 01:33:43] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -30.008870 | E_var:     0.0658 | E_err:   0.004007
[2025-10-07 01:33:47] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -30.015001 | E_var:     0.0713 | E_err:   0.004171
[2025-10-07 01:33:50] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -30.007449 | E_var:     0.0666 | E_err:   0.004031
[2025-10-07 01:33:54] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -30.011028 | E_var:     0.0582 | E_err:   0.003770
[2025-10-07 01:33:58] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -30.015037 | E_var:     0.0531 | E_err:   0.003601
[2025-10-07 01:34:01] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -30.011619 | E_var:     0.0457 | E_err:   0.003342
[2025-10-07 01:34:05] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -30.007898 | E_var:     0.0467 | E_err:   0.003378
[2025-10-07 01:34:09] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -30.012775 | E_var:     0.0743 | E_err:   0.004258
[2025-10-07 01:34:12] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -30.013836 | E_var:     0.0599 | E_err:   0.003826
[2025-10-07 01:34:16] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -30.014379 | E_var:     0.0542 | E_err:   0.003636
[2025-10-07 01:34:19] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -30.011241 | E_var:     0.0624 | E_err:   0.003902
[2025-10-07 01:34:23] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -30.001430 | E_var:     0.1472 | E_err:   0.005994
[2025-10-07 01:34:27] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -30.014763 | E_var:     0.0640 | E_err:   0.003951
[2025-10-07 01:34:30] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -30.011618 | E_var:     0.0761 | E_err:   0.004312
[2025-10-07 01:34:34] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -30.019962 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 01:34:38] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -30.009700 | E_var:     0.0564 | E_err:   0.003711
[2025-10-07 01:34:41] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -30.006548 | E_var:     0.0742 | E_err:   0.004255
[2025-10-07 01:34:45] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -30.016721 | E_var:     0.0562 | E_err:   0.003704
[2025-10-07 01:34:49] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -30.007504 | E_var:     0.0762 | E_err:   0.004314
[2025-10-07 01:34:52] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -30.019390 | E_var:     0.0573 | E_err:   0.003739
[2025-10-07 01:34:56] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -30.022447 | E_var:     0.0800 | E_err:   0.004418
[2025-10-07 01:35:00] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -30.010768 | E_var:     0.1328 | E_err:   0.005694
[2025-10-07 01:35:03] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -30.018528 | E_var:     0.0540 | E_err:   0.003631
[2025-10-07 01:35:07] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -30.017249 | E_var:     0.0514 | E_err:   0.003542
[2025-10-07 01:35:11] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -30.014857 | E_var:     0.0572 | E_err:   0.003736
[2025-10-07 01:35:14] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -30.004589 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 01:35:18] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -30.019811 | E_var:     0.0692 | E_err:   0.004110
[2025-10-07 01:35:22] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -30.019733 | E_var:     0.1757 | E_err:   0.006549
[2025-10-07 01:35:22] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 01:35:25] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -30.014473 | E_var:     0.0583 | E_err:   0.003774
[2025-10-07 01:35:29] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -30.016995 | E_var:     0.0676 | E_err:   0.004063
[2025-10-07 01:35:32] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -30.008598 | E_var:     0.0701 | E_err:   0.004136
[2025-10-07 01:35:36] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -30.014592 | E_var:     0.0576 | E_err:   0.003749
[2025-10-07 01:35:40] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -30.015187 | E_var:     0.0601 | E_err:   0.003830
[2025-10-07 01:35:43] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -30.017986 | E_var:     0.0563 | E_err:   0.003707
[2025-10-07 01:35:47] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -30.013493 | E_var:     0.0649 | E_err:   0.003980
[2025-10-07 01:35:51] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -30.008835 | E_var:     0.0781 | E_err:   0.004368
[2025-10-07 01:35:54] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -30.007774 | E_var:     0.0701 | E_err:   0.004138
[2025-10-07 01:35:58] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -30.011022 | E_var:     0.0823 | E_err:   0.004483
[2025-10-07 01:36:02] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -30.010573 | E_var:     0.0537 | E_err:   0.003619
[2025-10-07 01:36:05] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -30.014348 | E_var:     0.0787 | E_err:   0.004383
[2025-10-07 01:36:09] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -30.006508 | E_var:     0.0718 | E_err:   0.004187
[2025-10-07 01:36:13] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -30.007142 | E_var:     0.0732 | E_err:   0.004229
[2025-10-07 01:36:16] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -30.017940 | E_var:     0.0702 | E_err:   0.004141
[2025-10-07 01:36:20] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -30.012515 | E_var:     0.0641 | E_err:   0.003957
[2025-10-07 01:36:24] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -30.009233 | E_var:     0.0743 | E_err:   0.004260
[2025-10-07 01:36:27] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -30.011566 | E_var:     0.0511 | E_err:   0.003534
[2025-10-07 01:36:31] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -30.014429 | E_var:     0.0591 | E_err:   0.003797
[2025-10-07 01:36:35] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -30.013285 | E_var:     0.0690 | E_err:   0.004104
[2025-10-07 01:36:38] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -30.013039 | E_var:     0.0563 | E_err:   0.003709
[2025-10-07 01:36:42] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -30.006075 | E_var:     0.0761 | E_err:   0.004311
[2025-10-07 01:36:46] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -30.011376 | E_var:     0.0492 | E_err:   0.003466
[2025-10-07 01:36:49] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -30.017737 | E_var:     0.0642 | E_err:   0.003958
[2025-10-07 01:36:53] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -30.013858 | E_var:     0.0544 | E_err:   0.003644
[2025-10-07 01:36:56] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -30.004556 | E_var:     0.0611 | E_err:   0.003861
[2025-10-07 01:37:00] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -30.016269 | E_var:     0.0670 | E_err:   0.004045
[2025-10-07 01:37:04] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -30.011328 | E_var:     0.0884 | E_err:   0.004645
[2025-10-07 01:37:07] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -30.017568 | E_var:     0.0856 | E_err:   0.004571
[2025-10-07 01:37:11] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -30.016446 | E_var:     0.0584 | E_err:   0.003775
[2025-10-07 01:37:15] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -30.026114 | E_var:     0.0609 | E_err:   0.003857
[2025-10-07 01:37:18] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -30.009538 | E_var:     0.0490 | E_err:   0.003459
[2025-10-07 01:37:22] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -30.007992 | E_var:     0.0977 | E_err:   0.004884
[2025-10-07 01:37:26] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -30.006728 | E_var:     0.0556 | E_err:   0.003685
[2025-10-07 01:37:29] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -30.020766 | E_var:     0.1615 | E_err:   0.006280
[2025-10-07 01:37:33] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -30.008556 | E_var:     0.0554 | E_err:   0.003679
[2025-10-07 01:37:37] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -30.020547 | E_var:     0.0612 | E_err:   0.003864
[2025-10-07 01:37:40] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -30.013602 | E_var:     0.0547 | E_err:   0.003653
[2025-10-07 01:37:44] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -30.010205 | E_var:     0.0502 | E_err:   0.003502
[2025-10-07 01:37:48] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -30.010065 | E_var:     0.0517 | E_err:   0.003552
[2025-10-07 01:37:51] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -30.012945 | E_var:     0.0497 | E_err:   0.003485
[2025-10-07 01:37:55] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -30.012158 | E_var:     0.0597 | E_err:   0.003819
[2025-10-07 01:37:59] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -30.013066 | E_var:     0.0525 | E_err:   0.003580
[2025-10-07 01:38:02] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -30.011622 | E_var:     0.0584 | E_err:   0.003775
[2025-10-07 01:38:06] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -30.012059 | E_var:     0.0671 | E_err:   0.004046
[2025-10-07 01:38:10] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -30.018420 | E_var:     0.0567 | E_err:   0.003722
[2025-10-07 01:38:13] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -30.019064 | E_var:     0.0629 | E_err:   0.003920
[2025-10-07 01:38:17] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -30.015661 | E_var:     0.0586 | E_err:   0.003782
[2025-10-07 01:38:20] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -30.012955 | E_var:     0.0648 | E_err:   0.003978
[2025-10-07 01:38:24] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -30.006349 | E_var:     0.0763 | E_err:   0.004316
[2025-10-07 01:38:28] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -30.012537 | E_var:     0.0640 | E_err:   0.003953
[2025-10-07 01:38:31] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -30.016192 | E_var:     0.0849 | E_err:   0.004553
[2025-10-07 01:38:35] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -30.018885 | E_var:     0.0635 | E_err:   0.003939
[2025-10-07 01:38:39] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -30.013690 | E_var:     0.0631 | E_err:   0.003924
[2025-10-07 01:38:42] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -30.011863 | E_var:     0.0544 | E_err:   0.003644
[2025-10-07 01:38:46] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -30.010026 | E_var:     0.0553 | E_err:   0.003674
[2025-10-07 01:38:50] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -30.013022 | E_var:     0.0603 | E_err:   0.003838
[2025-10-07 01:38:53] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -30.013998 | E_var:     0.0632 | E_err:   0.003928
[2025-10-07 01:38:57] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -30.006562 | E_var:     0.0519 | E_err:   0.003559
[2025-10-07 01:39:01] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -30.011966 | E_var:     0.0634 | E_err:   0.003933
[2025-10-07 01:39:04] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -30.015551 | E_var:     0.0854 | E_err:   0.004565
[2025-10-07 01:39:08] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -30.019113 | E_var:     0.0713 | E_err:   0.004172
[2025-10-07 01:39:12] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -30.007734 | E_var:     0.0728 | E_err:   0.004216
[2025-10-07 01:39:15] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -30.010130 | E_var:     0.0715 | E_err:   0.004178
[2025-10-07 01:39:19] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -30.011208 | E_var:     0.0588 | E_err:   0.003789
[2025-10-07 01:39:23] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -30.015353 | E_var:     0.1172 | E_err:   0.005348
[2025-10-07 01:39:26] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -30.011887 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 01:39:30] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -30.013237 | E_var:     0.0470 | E_err:   0.003387
[2025-10-07 01:39:34] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -30.017542 | E_var:     0.0599 | E_err:   0.003826
[2025-10-07 01:39:37] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -30.014523 | E_var:     0.0521 | E_err:   0.003567
[2025-10-07 01:39:41] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -30.018973 | E_var:     0.0533 | E_err:   0.003607
[2025-10-07 01:39:44] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -30.006902 | E_var:     0.0665 | E_err:   0.004029
[2025-10-07 01:39:48] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -30.012055 | E_var:     0.0594 | E_err:   0.003808
[2025-10-07 01:39:52] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -30.020519 | E_var:     0.0731 | E_err:   0.004224
[2025-10-07 01:39:55] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -30.007666 | E_var:     0.0779 | E_err:   0.004361
[2025-10-07 01:39:59] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -30.015090 | E_var:     0.0566 | E_err:   0.003717
[2025-10-07 01:40:03] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -30.013818 | E_var:     0.0687 | E_err:   0.004096
[2025-10-07 01:40:06] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -30.013274 | E_var:     0.0610 | E_err:   0.003860
[2025-10-07 01:40:10] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -30.019795 | E_var:     0.0815 | E_err:   0.004462
[2025-10-07 01:40:14] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -30.011355 | E_var:     0.0603 | E_err:   0.003837
[2025-10-07 01:40:17] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -30.012745 | E_var:     0.0474 | E_err:   0.003401
[2025-10-07 01:40:21] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -30.004852 | E_var:     0.0796 | E_err:   0.004409
[2025-10-07 01:40:25] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -30.020142 | E_var:     0.1029 | E_err:   0.005013
[2025-10-07 01:40:28] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -30.012518 | E_var:     0.0679 | E_err:   0.004071
[2025-10-07 01:40:32] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -30.011870 | E_var:     0.0520 | E_err:   0.003562
[2025-10-07 01:40:36] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -30.010323 | E_var:     0.0720 | E_err:   0.004192
[2025-10-07 01:40:39] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -30.018089 | E_var:     0.0569 | E_err:   0.003726
[2025-10-07 01:40:43] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -30.011993 | E_var:     0.0695 | E_err:   0.004119
[2025-10-07 01:40:47] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -30.015813 | E_var:     0.0659 | E_err:   0.004011
[2025-10-07 01:40:50] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -30.018818 | E_var:     0.0635 | E_err:   0.003937
[2025-10-07 01:40:54] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -30.012242 | E_var:     0.0998 | E_err:   0.004936
[2025-10-07 01:40:57] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -30.021093 | E_var:     0.8878 | E_err:   0.014722
[2025-10-07 01:41:01] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -30.015541 | E_var:     0.0654 | E_err:   0.003997
[2025-10-07 01:41:05] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -30.008977 | E_var:     0.0567 | E_err:   0.003722
[2025-10-07 01:41:08] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -30.014096 | E_var:     0.0519 | E_err:   0.003561
[2025-10-07 01:41:12] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -30.012779 | E_var:     0.0668 | E_err:   0.004039
[2025-10-07 01:41:16] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -30.010316 | E_var:     0.0582 | E_err:   0.003768
[2025-10-07 01:41:19] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -30.008286 | E_var:     0.0874 | E_err:   0.004620
[2025-10-07 01:41:23] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -30.010496 | E_var:     0.0979 | E_err:   0.004889
[2025-10-07 01:41:27] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -30.010018 | E_var:     0.0728 | E_err:   0.004216
[2025-10-07 01:41:27] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 01:41:30] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -30.014938 | E_var:     0.0790 | E_err:   0.004392
[2025-10-07 01:41:34] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -30.012787 | E_var:     0.0651 | E_err:   0.003986
[2025-10-07 01:41:38] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -30.009612 | E_var:     0.0582 | E_err:   0.003768
[2025-10-07 01:41:41] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -30.017661 | E_var:     0.0552 | E_err:   0.003672
[2025-10-07 01:41:45] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -30.012949 | E_var:     0.0510 | E_err:   0.003529
[2025-10-07 01:41:49] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -30.016148 | E_var:     0.0615 | E_err:   0.003873
[2025-10-07 01:41:52] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -30.012597 | E_var:     0.0529 | E_err:   0.003592
[2025-10-07 01:41:56] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -30.013913 | E_var:     0.0826 | E_err:   0.004489
[2025-10-07 01:42:00] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -30.017918 | E_var:     0.0706 | E_err:   0.004151
[2025-10-07 01:42:03] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -30.004335 | E_var:     0.0719 | E_err:   0.004189
[2025-10-07 01:42:07] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -30.010124 | E_var:     0.0697 | E_err:   0.004126
[2025-10-07 01:42:11] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -30.013066 | E_var:     0.0531 | E_err:   0.003599
[2025-10-07 01:42:14] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -30.014721 | E_var:     0.0755 | E_err:   0.004292
[2025-10-07 01:42:18] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -30.016228 | E_var:     0.0595 | E_err:   0.003812
[2025-10-07 01:42:22] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -30.020785 | E_var:     0.1127 | E_err:   0.005245
[2025-10-07 01:42:25] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -30.016771 | E_var:     0.0712 | E_err:   0.004169
[2025-10-07 01:42:29] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -30.006158 | E_var:     0.0736 | E_err:   0.004240
[2025-10-07 01:42:32] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -30.017252 | E_var:     0.0615 | E_err:   0.003874
[2025-10-07 01:42:36] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -30.007192 | E_var:     0.0893 | E_err:   0.004669
[2025-10-07 01:42:40] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -30.006864 | E_var:     0.0820 | E_err:   0.004473
[2025-10-07 01:42:43] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -30.012513 | E_var:     0.0682 | E_err:   0.004079
[2025-10-07 01:42:47] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -30.024060 | E_var:     0.0694 | E_err:   0.004117
[2025-10-07 01:42:51] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -30.021568 | E_var:     0.0971 | E_err:   0.004868
[2025-10-07 01:42:54] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -30.017271 | E_var:     0.0591 | E_err:   0.003799
[2025-10-07 01:42:58] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -30.008235 | E_var:     0.0733 | E_err:   0.004232
[2025-10-07 01:43:02] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -30.016879 | E_var:     0.0529 | E_err:   0.003594
[2025-10-07 01:43:05] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -30.011733 | E_var:     0.0589 | E_err:   0.003791
[2025-10-07 01:43:09] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -30.015182 | E_var:     0.0626 | E_err:   0.003910
[2025-10-07 01:43:13] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -30.014912 | E_var:     0.0558 | E_err:   0.003691
[2025-10-07 01:43:16] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -30.005381 | E_var:     0.0814 | E_err:   0.004458
[2025-10-07 01:43:20] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -30.014988 | E_var:     0.0636 | E_err:   0.003939
[2025-10-07 01:43:24] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -30.015684 | E_var:     0.1061 | E_err:   0.005089
[2025-10-07 01:43:27] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -30.013803 | E_var:     0.1010 | E_err:   0.004966
[2025-10-07 01:43:31] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -30.015804 | E_var:     0.0632 | E_err:   0.003928
[2025-10-07 01:43:35] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -30.016297 | E_var:     0.1014 | E_err:   0.004977
[2025-10-07 01:43:38] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -30.011893 | E_var:     0.0569 | E_err:   0.003726
[2025-10-07 01:43:42] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -30.012434 | E_var:     0.0567 | E_err:   0.003722
[2025-10-07 01:43:46] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -30.011712 | E_var:     0.0790 | E_err:   0.004392
[2025-10-07 01:43:49] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -30.014419 | E_var:     0.0868 | E_err:   0.004604
[2025-10-07 01:43:53] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -30.007612 | E_var:     0.0939 | E_err:   0.004788
[2025-10-07 01:43:57] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -30.013385 | E_var:     0.0574 | E_err:   0.003743
[2025-10-07 01:44:00] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -30.011418 | E_var:     0.0503 | E_err:   0.003506
[2025-10-07 01:44:04] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -30.016933 | E_var:     0.0725 | E_err:   0.004207
[2025-10-07 01:44:07] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -30.013486 | E_var:     0.0868 | E_err:   0.004603
[2025-10-07 01:44:11] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -30.009266 | E_var:     0.0640 | E_err:   0.003952
[2025-10-07 01:44:15] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -30.009925 | E_var:     0.0530 | E_err:   0.003596
[2025-10-07 01:44:18] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -30.015291 | E_var:     0.0565 | E_err:   0.003713
[2025-10-07 01:44:22] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -30.003933 | E_var:     0.0961 | E_err:   0.004843
[2025-10-07 01:44:26] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -30.011625 | E_var:     0.0507 | E_err:   0.003518
[2025-10-07 01:44:29] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -30.015237 | E_var:     0.0516 | E_err:   0.003548
[2025-10-07 01:44:33] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -30.009103 | E_var:     0.0560 | E_err:   0.003699
[2025-10-07 01:44:37] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -30.012281 | E_var:     0.0459 | E_err:   0.003349
[2025-10-07 01:44:40] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -30.009104 | E_var:     0.0551 | E_err:   0.003668
[2025-10-07 01:44:44] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -30.017622 | E_var:     0.0740 | E_err:   0.004249
[2025-10-07 01:44:48] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -30.010398 | E_var:     0.0711 | E_err:   0.004165
[2025-10-07 01:44:51] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -30.015491 | E_var:     0.0581 | E_err:   0.003765
[2025-10-07 01:44:55] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -30.012485 | E_var:     0.0469 | E_err:   0.003384
[2025-10-07 01:44:59] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -30.018560 | E_var:     0.0721 | E_err:   0.004197
[2025-10-07 01:45:03] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -30.013278 | E_var:     0.0747 | E_err:   0.004270
[2025-10-07 01:45:06] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -30.012721 | E_var:     0.0560 | E_err:   0.003696
[2025-10-07 01:45:10] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -30.013237 | E_var:     0.0626 | E_err:   0.003910
[2025-10-07 01:45:13] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -30.006524 | E_var:     0.0664 | E_err:   0.004026
[2025-10-07 01:45:17] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -30.012264 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 01:45:21] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -30.018742 | E_var:     0.0556 | E_err:   0.003683
[2025-10-07 01:45:24] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -30.004294 | E_var:     0.0753 | E_err:   0.004288
[2025-10-07 01:45:28] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -30.007964 | E_var:     0.0800 | E_err:   0.004421
[2025-10-07 01:45:32] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -30.019204 | E_var:     0.0646 | E_err:   0.003971
[2025-10-07 01:45:35] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -30.006429 | E_var:     0.0666 | E_err:   0.004033
[2025-10-07 01:45:39] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -30.011825 | E_var:     0.0531 | E_err:   0.003602
[2025-10-07 01:45:43] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -30.005661 | E_var:     0.0776 | E_err:   0.004351
[2025-10-07 01:45:46] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -30.016016 | E_var:     0.0593 | E_err:   0.003805
[2025-10-07 01:45:50] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -30.019817 | E_var:     0.0556 | E_err:   0.003684
[2025-10-07 01:45:54] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -30.011627 | E_var:     0.0510 | E_err:   0.003530
[2025-10-07 01:45:57] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -30.004164 | E_var:     0.0662 | E_err:   0.004020
[2025-10-07 01:46:01] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -30.014572 | E_var:     0.0683 | E_err:   0.004084
[2025-10-07 01:46:05] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -30.013195 | E_var:     0.0567 | E_err:   0.003722
[2025-10-07 01:46:08] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -30.013240 | E_var:     0.0667 | E_err:   0.004034
[2025-10-07 01:46:12] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -30.016440 | E_var:     0.0639 | E_err:   0.003949
[2025-10-07 01:46:16] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -30.014069 | E_var:     0.0555 | E_err:   0.003682
[2025-10-07 01:46:19] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -30.019888 | E_var:     0.0646 | E_err:   0.003970
[2025-10-07 01:46:23] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -30.013032 | E_var:     0.0686 | E_err:   0.004092
[2025-10-07 01:46:27] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -30.019069 | E_var:     0.0558 | E_err:   0.003691
[2025-10-07 01:46:30] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -30.009915 | E_var:     0.0582 | E_err:   0.003768
[2025-10-07 01:46:34] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -30.010553 | E_var:     0.0586 | E_err:   0.003781
[2025-10-07 01:46:37] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -30.010685 | E_var:     0.0626 | E_err:   0.003911
[2025-10-07 01:46:41] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -30.018208 | E_var:     0.0891 | E_err:   0.004665
[2025-10-07 01:46:45] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -30.021888 | E_var:     0.0600 | E_err:   0.003826
[2025-10-07 01:46:48] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -30.011037 | E_var:     0.0870 | E_err:   0.004610
[2025-10-07 01:46:52] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -30.011336 | E_var:     0.0607 | E_err:   0.003849
[2025-10-07 01:46:56] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -30.021716 | E_var:     0.0704 | E_err:   0.004145
[2025-10-07 01:46:59] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -30.009477 | E_var:     0.0491 | E_err:   0.003461
[2025-10-07 01:47:03] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -30.022015 | E_var:     0.0623 | E_err:   0.003901
[2025-10-07 01:47:07] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -30.007624 | E_var:     0.0646 | E_err:   0.003970
[2025-10-07 01:47:10] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -30.005772 | E_var:     0.1001 | E_err:   0.004943
[2025-10-07 01:47:14] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -30.003856 | E_var:     0.1590 | E_err:   0.006230
[2025-10-07 01:47:18] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -30.012672 | E_var:     0.0570 | E_err:   0.003731
[2025-10-07 01:47:21] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -30.015350 | E_var:     0.0544 | E_err:   0.003644
[2025-10-07 01:47:25] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -30.002715 | E_var:     0.0715 | E_err:   0.004177
[2025-10-07 01:47:29] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -30.013011 | E_var:     0.0414 | E_err:   0.003178
[2025-10-07 01:47:32] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -30.020705 | E_var:     0.0593 | E_err:   0.003806
[2025-10-07 01:47:32] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 01:47:36] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -30.016432 | E_var:     0.0507 | E_err:   0.003519
[2025-10-07 01:47:40] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -30.015429 | E_var:     0.0703 | E_err:   0.004142
[2025-10-07 01:47:43] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -30.011055 | E_var:     0.1113 | E_err:   0.005213
[2025-10-07 01:47:47] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -30.019922 | E_var:     0.0552 | E_err:   0.003671
[2025-10-07 01:47:50] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -30.009916 | E_var:     0.0513 | E_err:   0.003539
[2025-10-07 01:47:54] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -30.011397 | E_var:     0.0522 | E_err:   0.003570
[2025-10-07 01:47:58] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -30.015967 | E_var:     0.0708 | E_err:   0.004158
[2025-10-07 01:48:01] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -30.006728 | E_var:     0.0579 | E_err:   0.003760
[2025-10-07 01:48:05] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -30.018629 | E_var:     0.0524 | E_err:   0.003576
[2025-10-07 01:48:09] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -30.004577 | E_var:     0.1324 | E_err:   0.005686
[2025-10-07 01:48:12] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -30.012539 | E_var:     0.1703 | E_err:   0.006449
[2025-10-07 01:48:16] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -30.009976 | E_var:     0.0538 | E_err:   0.003624
[2025-10-07 01:48:20] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -30.012371 | E_var:     0.0817 | E_err:   0.004465
[2025-10-07 01:48:23] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -30.006054 | E_var:     0.0620 | E_err:   0.003891
[2025-10-07 01:48:27] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -30.010313 | E_var:     0.0685 | E_err:   0.004090
[2025-10-07 01:48:31] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -30.010823 | E_var:     0.0545 | E_err:   0.003646
[2025-10-07 01:48:34] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -30.011775 | E_var:     0.0623 | E_err:   0.003899
[2025-10-07 01:48:38] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -30.013283 | E_var:     0.0940 | E_err:   0.004790
[2025-10-07 01:48:42] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -30.011651 | E_var:     0.0609 | E_err:   0.003856
[2025-10-07 01:48:45] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -30.017119 | E_var:     0.0636 | E_err:   0.003942
[2025-10-07 01:48:49] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -30.016777 | E_var:     0.0466 | E_err:   0.003372
[2025-10-07 01:48:53] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -30.012256 | E_var:     0.0589 | E_err:   0.003792
[2025-10-07 01:48:56] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -30.009023 | E_var:     0.0637 | E_err:   0.003944
[2025-10-07 01:49:00] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -30.013296 | E_var:     0.0578 | E_err:   0.003755
[2025-10-07 01:49:04] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -30.007344 | E_var:     0.0748 | E_err:   0.004274
[2025-10-07 01:49:07] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -30.003543 | E_var:     0.0887 | E_err:   0.004653
[2025-10-07 01:49:11] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -30.015221 | E_var:     0.0869 | E_err:   0.004607
[2025-10-07 01:49:14] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -30.013518 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 01:49:18] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -30.016703 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 01:49:22] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -30.005919 | E_var:     0.0530 | E_err:   0.003596
[2025-10-07 01:49:25] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -30.017643 | E_var:     0.0853 | E_err:   0.004565
[2025-10-07 01:49:29] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -30.016544 | E_var:     0.0784 | E_err:   0.004374
[2025-10-07 01:49:33] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -30.016802 | E_var:     0.0695 | E_err:   0.004119
[2025-10-07 01:49:36] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -30.012029 | E_var:     0.1154 | E_err:   0.005308
[2025-10-07 01:49:40] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -30.014840 | E_var:     0.0595 | E_err:   0.003810
[2025-10-07 01:49:44] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -30.016956 | E_var:     0.0536 | E_err:   0.003619
[2025-10-07 01:49:47] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -30.011677 | E_var:     0.0636 | E_err:   0.003940
[2025-10-07 01:49:51] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -30.007867 | E_var:     0.0549 | E_err:   0.003661
[2025-10-07 01:49:55] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -30.007747 | E_var:     0.0870 | E_err:   0.004608
[2025-10-07 01:49:58] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -30.020121 | E_var:     0.0707 | E_err:   0.004156
[2025-10-07 01:50:02] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -30.013972 | E_var:     0.0552 | E_err:   0.003672
[2025-10-07 01:50:06] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -30.013405 | E_var:     0.0624 | E_err:   0.003904
[2025-10-07 01:50:09] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -30.017976 | E_var:     0.0746 | E_err:   0.004268
[2025-10-07 01:50:13] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -30.009260 | E_var:     0.0676 | E_err:   0.004061
[2025-10-07 01:50:17] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -30.016449 | E_var:     0.0541 | E_err:   0.003636
[2025-10-07 01:50:20] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -30.009118 | E_var:     0.0623 | E_err:   0.003899
[2025-10-07 01:50:24] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -30.013025 | E_var:     0.0823 | E_err:   0.004483
[2025-10-07 01:50:27] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -30.015667 | E_var:     0.1946 | E_err:   0.006892
[2025-10-07 01:50:31] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -30.015245 | E_var:     0.0669 | E_err:   0.004041
[2025-10-07 01:50:35] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -30.017052 | E_var:     0.0645 | E_err:   0.003967
[2025-10-07 01:50:38] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -30.015256 | E_var:     0.0836 | E_err:   0.004518
[2025-10-07 01:50:42] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -30.012732 | E_var:     0.0661 | E_err:   0.004019
[2025-10-07 01:50:46] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -30.008082 | E_var:     0.0537 | E_err:   0.003620
[2025-10-07 01:50:49] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -30.007137 | E_var:     0.0765 | E_err:   0.004322
[2025-10-07 01:50:53] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -30.008063 | E_var:     0.0821 | E_err:   0.004478
[2025-10-07 01:50:57] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -30.015679 | E_var:     0.1104 | E_err:   0.005193
[2025-10-07 01:51:00] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -30.013564 | E_var:     0.0741 | E_err:   0.004253
[2025-10-07 01:51:04] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -30.015248 | E_var:     0.0540 | E_err:   0.003630
[2025-10-07 01:51:08] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -30.008393 | E_var:     0.1050 | E_err:   0.005063
[2025-10-07 01:51:11] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -30.017495 | E_var:     0.0539 | E_err:   0.003627
[2025-10-07 01:51:15] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -30.013438 | E_var:     0.0791 | E_err:   0.004396
[2025-10-07 01:51:19] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -30.008764 | E_var:     0.0581 | E_err:   0.003766
[2025-10-07 01:51:22] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -30.012487 | E_var:     0.0618 | E_err:   0.003885
[2025-10-07 01:51:26] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -30.007453 | E_var:     0.0593 | E_err:   0.003806
[2025-10-07 01:51:30] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -30.008536 | E_var:     0.1051 | E_err:   0.005066
[2025-10-07 01:51:33] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -30.012953 | E_var:     0.0731 | E_err:   0.004225
[2025-10-07 01:51:37] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -30.017277 | E_var:     0.0622 | E_err:   0.003896
[2025-10-07 01:51:41] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -30.009334 | E_var:     0.0624 | E_err:   0.003903
[2025-10-07 01:51:44] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -30.018440 | E_var:     0.0443 | E_err:   0.003287
[2025-10-07 01:51:48] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -30.012491 | E_var:     0.0655 | E_err:   0.003998
[2025-10-07 01:51:51] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -30.010892 | E_var:     0.0600 | E_err:   0.003828
[2025-10-07 01:51:55] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -30.014804 | E_var:     0.0959 | E_err:   0.004839
[2025-10-07 01:51:59] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -30.007089 | E_var:     0.0608 | E_err:   0.003852
[2025-10-07 01:52:02] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -30.010576 | E_var:     0.0657 | E_err:   0.004005
[2025-10-07 01:52:06] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -30.005522 | E_var:     0.0615 | E_err:   0.003874
[2025-10-07 01:52:10] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -30.011649 | E_var:     0.0670 | E_err:   0.004044
[2025-10-07 01:52:13] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -30.011957 | E_var:     0.0643 | E_err:   0.003963
[2025-10-07 01:52:17] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -30.013222 | E_var:     0.0632 | E_err:   0.003928
[2025-10-07 01:52:21] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -30.010349 | E_var:     0.0720 | E_err:   0.004193
[2025-10-07 01:52:24] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -30.008824 | E_var:     0.0638 | E_err:   0.003947
[2025-10-07 01:52:28] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -30.012797 | E_var:     0.0691 | E_err:   0.004106
[2025-10-07 01:52:32] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -30.021459 | E_var:     0.0534 | E_err:   0.003609
[2025-10-07 01:52:35] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -30.016336 | E_var:     0.0776 | E_err:   0.004351
[2025-10-07 01:52:39] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -30.010685 | E_var:     0.0697 | E_err:   0.004125
[2025-10-07 01:52:43] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -30.003998 | E_var:     0.0708 | E_err:   0.004158
[2025-10-07 01:52:46] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -30.012800 | E_var:     0.0644 | E_err:   0.003965
[2025-10-07 01:52:50] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -30.015852 | E_var:     0.0884 | E_err:   0.004646
[2025-10-07 01:52:54] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -30.013310 | E_var:     0.0624 | E_err:   0.003903
[2025-10-07 01:52:57] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -30.015341 | E_var:     0.0669 | E_err:   0.004041
[2025-10-07 01:53:01] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -30.006949 | E_var:     0.0862 | E_err:   0.004588
[2025-10-07 01:53:04] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -30.015195 | E_var:     0.0610 | E_err:   0.003858
[2025-10-07 01:53:08] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -30.015108 | E_var:     0.0670 | E_err:   0.004044
[2025-10-07 01:53:12] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -30.015727 | E_var:     0.0844 | E_err:   0.004538
[2025-10-07 01:53:15] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -30.011076 | E_var:     0.0630 | E_err:   0.003921
[2025-10-07 01:53:19] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -30.015840 | E_var:     0.0684 | E_err:   0.004087
[2025-10-07 01:53:23] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -30.013662 | E_var:     0.0804 | E_err:   0.004430
[2025-10-07 01:53:26] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -30.011264 | E_var:     0.0564 | E_err:   0.003711
[2025-10-07 01:53:30] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -30.012920 | E_var:     0.0973 | E_err:   0.004875
[2025-10-07 01:53:34] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -30.011537 | E_var:     0.0538 | E_err:   0.003626
[2025-10-07 01:53:37] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -30.016199 | E_var:     0.0581 | E_err:   0.003766
[2025-10-07 01:53:37] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 01:53:41] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -30.018080 | E_var:     0.0541 | E_err:   0.003636
[2025-10-07 01:53:45] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -30.008330 | E_var:     0.0540 | E_err:   0.003631
[2025-10-07 01:53:48] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -30.006479 | E_var:     0.0573 | E_err:   0.003739
[2025-10-07 01:53:52] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -30.011477 | E_var:     0.0652 | E_err:   0.003991
[2025-10-07 01:53:56] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -30.007150 | E_var:     0.0530 | E_err:   0.003599
[2025-10-07 01:53:59] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -30.004970 | E_var:     0.0718 | E_err:   0.004187
[2025-10-07 01:54:03] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -30.014838 | E_var:     0.0585 | E_err:   0.003778
[2025-10-07 01:54:07] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -30.019665 | E_var:     0.1396 | E_err:   0.005837
[2025-10-07 01:54:10] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -30.017979 | E_var:     0.0628 | E_err:   0.003916
[2025-10-07 01:54:14] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -30.019560 | E_var:     0.0595 | E_err:   0.003813
[2025-10-07 01:54:18] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -30.006911 | E_var:     0.1131 | E_err:   0.005256
[2025-10-07 01:54:21] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -30.012946 | E_var:     0.0547 | E_err:   0.003654
[2025-10-07 01:54:25] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -30.010328 | E_var:     0.0514 | E_err:   0.003542
[2025-10-07 01:54:28] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -30.014792 | E_var:     0.0766 | E_err:   0.004323
[2025-10-07 01:54:32] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -30.002041 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 01:54:36] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -30.005039 | E_var:     0.0568 | E_err:   0.003725
[2025-10-07 01:54:39] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -30.014183 | E_var:     0.0698 | E_err:   0.004129
[2025-10-07 01:54:43] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -30.008173 | E_var:     0.0749 | E_err:   0.004275
[2025-10-07 01:54:47] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -30.018321 | E_var:     0.0529 | E_err:   0.003593
[2025-10-07 01:54:50] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -30.015627 | E_var:     0.0885 | E_err:   0.004647
[2025-10-07 01:54:54] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -30.011683 | E_var:     0.0583 | E_err:   0.003771
[2025-10-07 01:54:58] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -30.012011 | E_var:     0.0567 | E_err:   0.003721
[2025-10-07 01:55:01] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -30.012734 | E_var:     0.0773 | E_err:   0.004343
[2025-10-07 01:55:05] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -30.016098 | E_var:     0.0535 | E_err:   0.003615
[2025-10-07 01:55:09] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -30.013487 | E_var:     0.0815 | E_err:   0.004461
[2025-10-07 01:55:12] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -30.014968 | E_var:     0.0520 | E_err:   0.003564
[2025-10-07 01:55:16] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -30.017538 | E_var:     0.0497 | E_err:   0.003484
[2025-10-07 01:55:20] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -30.012568 | E_var:     0.0573 | E_err:   0.003740
[2025-10-07 01:55:23] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -30.010142 | E_var:     0.0551 | E_err:   0.003667
[2025-10-07 01:55:27] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -30.019270 | E_var:     0.0638 | E_err:   0.003947
[2025-10-07 01:55:31] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -30.016228 | E_var:     0.0571 | E_err:   0.003733
[2025-10-07 01:55:34] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -30.016413 | E_var:     0.0519 | E_err:   0.003560
[2025-10-07 01:55:38] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -30.012373 | E_var:     0.0608 | E_err:   0.003852
[2025-10-07 01:55:42] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -30.016966 | E_var:     0.0676 | E_err:   0.004061
[2025-10-07 01:55:45] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -30.013046 | E_var:     0.1240 | E_err:   0.005503
[2025-10-07 01:55:49] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -30.015407 | E_var:     0.0467 | E_err:   0.003377
[2025-10-07 01:55:52] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -30.014210 | E_var:     0.0650 | E_err:   0.003984
[2025-10-07 01:55:56] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -30.019260 | E_var:     0.0714 | E_err:   0.004174
[2025-10-07 01:56:00] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -30.016405 | E_var:     0.0653 | E_err:   0.003992
[2025-10-07 01:56:03] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -30.013073 | E_var:     0.0662 | E_err:   0.004022
[2025-10-07 01:56:07] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -30.015716 | E_var:     0.0764 | E_err:   0.004319
[2025-10-07 01:56:11] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -30.014997 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 01:56:14] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -30.016342 | E_var:     0.0750 | E_err:   0.004280
[2025-10-07 01:56:18] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -30.018465 | E_var:     0.0558 | E_err:   0.003690
[2025-10-07 01:56:22] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -30.017795 | E_var:     0.0872 | E_err:   0.004615
[2025-10-07 01:56:25] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -30.005488 | E_var:     0.0619 | E_err:   0.003889
[2025-10-07 01:56:29] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -30.010763 | E_var:     0.0558 | E_err:   0.003691
[2025-10-07 01:56:33] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -30.010716 | E_var:     0.0665 | E_err:   0.004028
[2025-10-07 01:56:36] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -30.006052 | E_var:     0.1073 | E_err:   0.005118
[2025-10-07 01:56:40] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -30.014236 | E_var:     0.0642 | E_err:   0.003959
[2025-10-07 01:56:40] ======================================================================================================
[2025-10-07 01:56:40] ✅ Training completed successfully
[2025-10-07 01:56:40] Total restarts: 2
[2025-10-07 01:56:41] Final Energy: -30.01423610 ± 0.00395862
[2025-10-07 01:56:41] Final Variance: 0.064187
[2025-10-07 01:56:41] ======================================================================================================
[2025-10-07 01:56:41] ======================================================================================================
[2025-10-07 01:56:41] Training completed | Runtime: 3900.1s
[2025-10-07 01:56:42] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 01:56:42] ======================================================================================================
