[2025-10-06 03:54:40] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.81/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 03:54:40]   - 迭代次数: final
[2025-10-06 03:54:40]   - 能量: -29.186881+0.000153j ± 0.004940, Var: 0.099958
[2025-10-06 03:54:40]   - 时间戳: 2025-10-06T03:54:29.367007+08:00
[2025-10-06 03:54:55] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 03:54:55] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 03:54:55] ======================================================================================================
[2025-10-06 03:54:55] GCNN for Shastry-Sutherland Model
[2025-10-06 03:54:55] ======================================================================================================
[2025-10-06 03:54:55] System parameters:
[2025-10-06 03:54:55]   - System size: L=4, N=64
[2025-10-06 03:54:55]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-10-06 03:54:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 03:54:55] Model parameters:
[2025-10-06 03:54:55]   - Number of layers = 4
[2025-10-06 03:54:55]   - Number of features = 4
[2025-10-06 03:54:55]   - Total parameters = 12572
[2025-10-06 03:54:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 03:54:55] Training parameters:
[2025-10-06 03:54:55]   - Total iterations: 1050
[2025-10-06 03:54:55]   - Annealing cycles: 3
[2025-10-06 03:54:55]   - Initial period: 150
[2025-10-06 03:54:55]   - Period multiplier: 2.0
[2025-10-06 03:54:55]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 03:54:55]   - Samples: 4096
[2025-10-06 03:54:55]   - Discarded samples: 0
[2025-10-06 03:54:55]   - Chunk size: 4096
[2025-10-06 03:54:55]   - Diagonal shift: 0.15
[2025-10-06 03:54:55]   - Gradient clipping: 1.0
[2025-10-06 03:54:55]   - Checkpoint enabled: interval=100
[2025-10-06 03:54:55]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.82/model_L4F4/training/checkpoints
[2025-10-06 03:54:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 03:54:55] Device status:
[2025-10-06 03:54:55]   - Devices model: NVIDIA H200 NVL
[2025-10-06 03:54:55]   - Number of devices: 1
[2025-10-06 03:54:55]   - Sharding: True
[2025-10-06 03:54:56] ======================================================================================================
[2025-10-06 03:55:29] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -29.606568 | E_var:     0.3262 | E_err:   0.008925
[2025-10-06 03:55:48] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -29.590801 | E_var:     0.1628 | E_err:   0.006304
[2025-10-06 03:55:51] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -29.584119 | E_var:     0.1564 | E_err:   0.006179
[2025-10-06 03:55:53] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -29.595874 | E_var:     0.0977 | E_err:   0.004885
[2025-10-06 03:55:55] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -29.593274 | E_var:     0.0929 | E_err:   0.004763
[2025-10-06 03:55:58] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -29.596980 | E_var:     0.0826 | E_err:   0.004490
[2025-10-06 03:56:00] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -29.597811 | E_var:     0.0848 | E_err:   0.004550
[2025-10-06 03:56:03] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -29.598200 | E_var:     0.0937 | E_err:   0.004783
[2025-10-06 03:56:05] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -29.592353 | E_var:     0.0821 | E_err:   0.004478
[2025-10-06 03:56:07] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -29.590446 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 03:56:10] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -29.588389 | E_var:     0.1019 | E_err:   0.004987
[2025-10-06 03:56:12] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -29.588703 | E_var:     0.1125 | E_err:   0.005242
[2025-10-06 03:56:15] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -29.590127 | E_var:     0.1031 | E_err:   0.005016
[2025-10-06 03:56:17] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -29.600733 | E_var:     0.0706 | E_err:   0.004151
[2025-10-06 03:56:20] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -29.593488 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 03:56:22] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -29.596473 | E_var:     0.0723 | E_err:   0.004201
[2025-10-06 03:56:24] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -29.592735 | E_var:     0.0817 | E_err:   0.004465
[2025-10-06 03:56:27] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -29.593718 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 03:56:29] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -29.594762 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 03:56:32] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -29.591074 | E_var:     0.1296 | E_err:   0.005626
[2025-10-06 03:56:34] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -29.601091 | E_var:     0.1190 | E_err:   0.005391
[2025-10-06 03:56:36] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -29.600728 | E_var:     0.0917 | E_err:   0.004733
[2025-10-06 03:56:39] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -29.593152 | E_var:     0.1134 | E_err:   0.005263
[2025-10-06 03:56:41] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -29.597400 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 03:56:44] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -29.596450 | E_var:     0.0911 | E_err:   0.004716
[2025-10-06 03:56:46] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -29.596235 | E_var:     0.0731 | E_err:   0.004224
[2025-10-06 03:56:48] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -29.599797 | E_var:     0.0775 | E_err:   0.004350
[2025-10-06 03:56:51] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -29.595108 | E_var:     0.0882 | E_err:   0.004642
[2025-10-06 03:56:53] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -29.592749 | E_var:     0.0682 | E_err:   0.004080
[2025-10-06 03:56:56] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -29.596640 | E_var:     0.1282 | E_err:   0.005594
[2025-10-06 03:56:58] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -29.591353 | E_var:     0.1084 | E_err:   0.005144
[2025-10-06 03:57:01] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -29.598296 | E_var:     0.1204 | E_err:   0.005421
[2025-10-06 03:57:03] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -29.595015 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 03:57:05] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -29.587376 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 03:57:08] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -29.600009 | E_var:     0.0789 | E_err:   0.004388
[2025-10-06 03:57:10] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -29.592187 | E_var:     0.0801 | E_err:   0.004422
[2025-10-06 03:57:13] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -29.591232 | E_var:     0.1149 | E_err:   0.005295
[2025-10-06 03:57:15] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -29.590807 | E_var:     0.1445 | E_err:   0.005940
[2025-10-06 03:57:17] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -29.595324 | E_var:     0.0833 | E_err:   0.004508
[2025-10-06 03:57:20] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -29.596788 | E_var:     0.0707 | E_err:   0.004156
[2025-10-06 03:57:22] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -29.594224 | E_var:     0.0804 | E_err:   0.004430
[2025-10-06 03:57:25] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -29.589801 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 03:57:27] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -29.593684 | E_var:     0.0879 | E_err:   0.004631
[2025-10-06 03:57:29] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -29.598037 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 03:57:32] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -29.592088 | E_var:     0.0818 | E_err:   0.004469
[2025-10-06 03:57:34] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -29.601556 | E_var:     0.0972 | E_err:   0.004872
[2025-10-06 03:57:37] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -29.596004 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 03:57:39] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -29.599801 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 03:57:42] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -29.592720 | E_var:     0.0821 | E_err:   0.004478
[2025-10-06 03:57:44] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -29.586694 | E_var:     0.0775 | E_err:   0.004350
[2025-10-06 03:57:46] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -29.593565 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 03:57:49] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -29.608984 | E_var:     0.2280 | E_err:   0.007461
[2025-10-06 03:57:51] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -29.596778 | E_var:     0.0750 | E_err:   0.004280
[2025-10-06 03:57:54] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -29.590491 | E_var:     0.0881 | E_err:   0.004639
[2025-10-06 03:57:56] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -29.588849 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 03:57:58] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -29.587152 | E_var:     0.0853 | E_err:   0.004562
[2025-10-06 03:58:01] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -29.592127 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 03:58:03] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -29.601544 | E_var:     0.1133 | E_err:   0.005258
[2025-10-06 03:58:06] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -29.596913 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 03:58:08] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -29.594383 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 03:58:11] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -29.595773 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 03:58:13] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -29.600209 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 03:58:15] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -29.591982 | E_var:     0.1101 | E_err:   0.005183
[2025-10-06 03:58:18] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -29.587775 | E_var:     0.0771 | E_err:   0.004337
[2025-10-06 03:58:20] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -29.592767 | E_var:     0.1106 | E_err:   0.005195
[2025-10-06 03:58:23] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -29.592681 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 03:58:25] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -29.592411 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 03:58:27] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -29.597746 | E_var:     0.1200 | E_err:   0.005412
[2025-10-06 03:58:30] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -29.594880 | E_var:     0.1012 | E_err:   0.004971
[2025-10-06 03:58:32] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -29.590316 | E_var:     0.0880 | E_err:   0.004634
[2025-10-06 03:58:35] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -29.590593 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 03:58:37] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -29.597603 | E_var:     0.0920 | E_err:   0.004738
[2025-10-06 03:58:39] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -29.595335 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 03:58:42] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -29.589737 | E_var:     0.1030 | E_err:   0.005015
[2025-10-06 03:58:44] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -29.592580 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 03:58:47] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -29.590194 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 03:58:49] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -29.598472 | E_var:     0.0944 | E_err:   0.004802
[2025-10-06 03:58:52] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -29.591129 | E_var:     0.1137 | E_err:   0.005269
[2025-10-06 03:58:54] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -29.593858 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 03:58:56] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -29.604496 | E_var:     0.0588 | E_err:   0.003788
[2025-10-06 03:58:59] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -29.591579 | E_var:     0.1239 | E_err:   0.005499
[2025-10-06 03:59:01] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -29.594860 | E_var:     0.1287 | E_err:   0.005606
[2025-10-06 03:59:04] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -29.589764 | E_var:     0.0822 | E_err:   0.004481
[2025-10-06 03:59:06] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -29.590152 | E_var:     0.0982 | E_err:   0.004895
[2025-10-06 03:59:08] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -29.586616 | E_var:     0.0788 | E_err:   0.004385
[2025-10-06 03:59:11] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -29.598279 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 03:59:13] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -29.590721 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 03:59:16] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -29.596798 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 03:59:18] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -29.595950 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 03:59:21] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -29.599786 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 03:59:23] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -29.604554 | E_var:     0.1229 | E_err:   0.005477
[2025-10-06 03:59:25] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -29.598920 | E_var:     0.0744 | E_err:   0.004263
[2025-10-06 03:59:28] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -29.591585 | E_var:     0.0820 | E_err:   0.004475
[2025-10-06 03:59:30] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -29.595348 | E_var:     0.0735 | E_err:   0.004236
[2025-10-06 03:59:33] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -29.596471 | E_var:     0.0780 | E_err:   0.004364
[2025-10-06 03:59:35] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -29.591559 | E_var:     0.0862 | E_err:   0.004586
[2025-10-06 03:59:37] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -29.590409 | E_var:     0.0768 | E_err:   0.004329
[2025-10-06 03:59:40] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -29.594567 | E_var:     0.1086 | E_err:   0.005148
[2025-10-06 03:59:42] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -29.590479 | E_var:     0.2558 | E_err:   0.007903
[2025-10-06 03:59:45] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -29.588661 | E_var:     0.1072 | E_err:   0.005116
[2025-10-06 03:59:45] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 03:59:47] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -29.601810 | E_var:     0.3226 | E_err:   0.008875
[2025-10-06 03:59:50] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -29.594883 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 03:59:52] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -29.593641 | E_var:     0.0843 | E_err:   0.004536
[2025-10-06 03:59:54] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -29.594905 | E_var:     0.0702 | E_err:   0.004140
[2025-10-06 03:59:57] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -29.595593 | E_var:     0.0757 | E_err:   0.004298
[2025-10-06 03:59:59] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -29.591780 | E_var:     0.0859 | E_err:   0.004579
[2025-10-06 04:00:02] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -29.598300 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 04:00:04] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -29.598406 | E_var:     0.0900 | E_err:   0.004687
[2025-10-06 04:00:06] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -29.594046 | E_var:     0.1011 | E_err:   0.004967
[2025-10-06 04:00:09] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -29.593077 | E_var:     0.1297 | E_err:   0.005626
[2025-10-06 04:00:11] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -29.590238 | E_var:     0.0809 | E_err:   0.004445
[2025-10-06 04:00:14] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -29.599486 | E_var:     0.0679 | E_err:   0.004071
[2025-10-06 04:00:16] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -29.591280 | E_var:     0.0654 | E_err:   0.003997
[2025-10-06 04:00:18] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -29.595528 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 04:00:21] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -29.588545 | E_var:     0.1243 | E_err:   0.005509
[2025-10-06 04:00:23] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -29.599989 | E_var:     0.1054 | E_err:   0.005072
[2025-10-06 04:00:26] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -29.590924 | E_var:     0.1433 | E_err:   0.005914
[2025-10-06 04:00:28] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -29.594215 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 04:00:31] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -29.601334 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 04:00:33] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -29.595687 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 04:00:35] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -29.590551 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 04:00:38] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -29.602040 | E_var:     0.0905 | E_err:   0.004700
[2025-10-06 04:00:40] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -29.584105 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 04:00:43] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -29.596048 | E_var:     0.0886 | E_err:   0.004650
[2025-10-06 04:00:45] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -29.583956 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 04:00:47] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -29.599496 | E_var:     0.0861 | E_err:   0.004585
[2025-10-06 04:00:50] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -29.598616 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 04:00:52] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -29.593656 | E_var:     0.0840 | E_err:   0.004529
[2025-10-06 04:00:55] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -29.582112 | E_var:     0.1197 | E_err:   0.005407
[2025-10-06 04:00:57] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -29.594020 | E_var:     0.0905 | E_err:   0.004701
[2025-10-06 04:01:00] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -29.592079 | E_var:     0.0762 | E_err:   0.004313
[2025-10-06 04:01:02] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -29.592757 | E_var:     0.0980 | E_err:   0.004891
[2025-10-06 04:01:04] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -29.597107 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 04:01:07] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -29.584284 | E_var:     0.0955 | E_err:   0.004830
[2025-10-06 04:01:09] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -29.590717 | E_var:     0.1360 | E_err:   0.005763
[2025-10-06 04:01:12] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -29.589610 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 04:01:14] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -29.589975 | E_var:     0.1005 | E_err:   0.004952
[2025-10-06 04:01:16] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -29.593262 | E_var:     0.0862 | E_err:   0.004588
[2025-10-06 04:01:19] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -29.592991 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 04:01:21] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -29.591800 | E_var:     0.1347 | E_err:   0.005734
[2025-10-06 04:01:24] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -29.596233 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 04:01:26] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -29.594373 | E_var:     0.1034 | E_err:   0.005023
[2025-10-06 04:01:28] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -29.591691 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 04:01:31] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -29.596947 | E_var:     0.1411 | E_err:   0.005869
[2025-10-06 04:01:33] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -29.598497 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 04:01:36] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -29.593644 | E_var:     0.0883 | E_err:   0.004644
[2025-10-06 04:01:38] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -29.597975 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 04:01:41] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -29.595131 | E_var:     0.1003 | E_err:   0.004949
[2025-10-06 04:01:43] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -29.596329 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 04:01:45] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -29.595583 | E_var:     0.0684 | E_err:   0.004087
[2025-10-06 04:01:45] 🔄 RESTART #1 | Period: 300
[2025-10-06 04:01:48] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -29.603572 | E_var:     0.0800 | E_err:   0.004418
[2025-10-06 04:01:50] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -29.596005 | E_var:     0.0873 | E_err:   0.004616
[2025-10-06 04:01:53] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -29.595174 | E_var:     0.0836 | E_err:   0.004519
[2025-10-06 04:01:55] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -29.593438 | E_var:     0.0913 | E_err:   0.004721
[2025-10-06 04:01:57] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -29.595567 | E_var:     0.1099 | E_err:   0.005179
[2025-10-06 04:02:00] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -29.594835 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 04:02:02] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -29.599854 | E_var:     0.1004 | E_err:   0.004951
[2025-10-06 04:02:05] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -29.597735 | E_var:     0.1259 | E_err:   0.005544
[2025-10-06 04:02:07] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -29.588470 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 04:02:10] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -29.592832 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 04:02:12] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -29.592433 | E_var:     0.0711 | E_err:   0.004165
[2025-10-06 04:02:14] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -29.594668 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 04:02:17] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -29.599567 | E_var:     0.1592 | E_err:   0.006234
[2025-10-06 04:02:19] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -29.599417 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 04:02:22] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -29.595748 | E_var:     0.0731 | E_err:   0.004224
[2025-10-06 04:02:24] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -29.598918 | E_var:     0.1132 | E_err:   0.005258
[2025-10-06 04:02:26] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -29.596143 | E_var:     0.1210 | E_err:   0.005434
[2025-10-06 04:02:29] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -29.590748 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 04:02:31] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -29.591985 | E_var:     0.1270 | E_err:   0.005569
[2025-10-06 04:02:34] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -29.600057 | E_var:     0.0822 | E_err:   0.004479
[2025-10-06 04:02:36] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -29.596559 | E_var:     0.1034 | E_err:   0.005024
[2025-10-06 04:02:38] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -29.588733 | E_var:     0.0955 | E_err:   0.004829
[2025-10-06 04:02:41] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -29.601243 | E_var:     0.0739 | E_err:   0.004247
[2025-10-06 04:02:43] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -29.587153 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 04:02:46] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -29.598047 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 04:02:48] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -29.599374 | E_var:     0.1294 | E_err:   0.005621
[2025-10-06 04:02:51] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -29.585869 | E_var:     0.1084 | E_err:   0.005143
[2025-10-06 04:02:53] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -29.594450 | E_var:     0.0940 | E_err:   0.004789
[2025-10-06 04:02:55] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -29.589038 | E_var:     0.1017 | E_err:   0.004984
[2025-10-06 04:02:58] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -29.596344 | E_var:     0.1010 | E_err:   0.004966
[2025-10-06 04:03:00] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -29.603343 | E_var:     0.0892 | E_err:   0.004666
[2025-10-06 04:03:03] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -29.591955 | E_var:     0.0949 | E_err:   0.004815
[2025-10-06 04:03:05] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -29.604542 | E_var:     0.1049 | E_err:   0.005060
[2025-10-06 04:03:07] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -29.601558 | E_var:     0.0970 | E_err:   0.004865
[2025-10-06 04:03:10] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -29.594124 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 04:03:12] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -29.588612 | E_var:     0.0759 | E_err:   0.004304
[2025-10-06 04:03:15] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -29.597050 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 04:03:17] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -29.600239 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 04:03:20] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -29.597092 | E_var:     0.0847 | E_err:   0.004546
[2025-10-06 04:03:22] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -29.593867 | E_var:     0.0897 | E_err:   0.004678
[2025-10-06 04:03:24] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -29.595392 | E_var:     0.1164 | E_err:   0.005331
[2025-10-06 04:03:27] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -29.593801 | E_var:     0.0714 | E_err:   0.004175
[2025-10-06 04:03:29] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -29.601981 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 04:03:32] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -29.597670 | E_var:     0.0900 | E_err:   0.004686
[2025-10-06 04:03:34] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -29.595513 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 04:03:36] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -29.595362 | E_var:     0.0927 | E_err:   0.004757
[2025-10-06 04:03:39] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -29.592043 | E_var:     0.1162 | E_err:   0.005327
[2025-10-06 04:03:41] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -29.596348 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 04:03:44] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -29.606129 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 04:03:46] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -29.601061 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 04:03:46] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 04:03:49] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -29.597692 | E_var:     0.1324 | E_err:   0.005685
[2025-10-06 04:03:51] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -29.593281 | E_var:     0.0974 | E_err:   0.004875
[2025-10-06 04:03:53] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -29.604434 | E_var:     0.1858 | E_err:   0.006736
[2025-10-06 04:03:56] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -29.599274 | E_var:     0.0906 | E_err:   0.004703
[2025-10-06 04:03:58] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -29.596903 | E_var:     0.1423 | E_err:   0.005894
[2025-10-06 04:04:01] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -29.602539 | E_var:     0.0956 | E_err:   0.004832
[2025-10-06 04:04:03] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -29.601732 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 04:04:06] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -29.587751 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 04:04:08] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -29.594466 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 04:04:10] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -29.586825 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 04:04:13] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -29.596454 | E_var:     0.0726 | E_err:   0.004211
[2025-10-06 04:04:15] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -29.599348 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 04:04:18] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -29.587790 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 04:04:20] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -29.590979 | E_var:     0.1775 | E_err:   0.006583
[2025-10-06 04:04:22] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -29.600806 | E_var:     0.0918 | E_err:   0.004735
[2025-10-06 04:04:25] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -29.594058 | E_var:     0.0797 | E_err:   0.004412
[2025-10-06 04:04:27] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -29.594688 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 04:04:30] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -29.601642 | E_var:     0.0841 | E_err:   0.004530
[2025-10-06 04:04:32] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -29.593811 | E_var:     0.0845 | E_err:   0.004541
[2025-10-06 04:04:34] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -29.597002 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 04:04:37] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -29.591301 | E_var:     0.0793 | E_err:   0.004399
[2025-10-06 04:04:39] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -29.597038 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 04:04:42] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -29.594552 | E_var:     0.1216 | E_err:   0.005450
[2025-10-06 04:04:44] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -29.593746 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 04:04:47] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -29.591066 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 04:04:49] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -29.600623 | E_var:     0.0800 | E_err:   0.004419
[2025-10-06 04:04:51] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -29.594880 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 04:04:54] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -29.593724 | E_var:     0.0797 | E_err:   0.004410
[2025-10-06 04:04:56] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -29.598416 | E_var:     0.0832 | E_err:   0.004508
[2025-10-06 04:04:59] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -29.582469 | E_var:     0.1103 | E_err:   0.005188
[2025-10-06 04:05:01] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -29.591301 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 04:05:03] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -29.595443 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 04:05:06] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -29.594712 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 04:05:08] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -29.591710 | E_var:     0.1829 | E_err:   0.006682
[2025-10-06 04:05:11] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -29.594831 | E_var:     0.0728 | E_err:   0.004217
[2025-10-06 04:05:13] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -29.593989 | E_var:     0.1075 | E_err:   0.005124
[2025-10-06 04:05:16] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -29.598254 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 04:05:18] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -29.593676 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 04:05:20] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -29.594186 | E_var:     0.0840 | E_err:   0.004530
[2025-10-06 04:05:23] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -29.597060 | E_var:     0.0784 | E_err:   0.004376
[2025-10-06 04:05:25] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -29.595087 | E_var:     0.0837 | E_err:   0.004520
[2025-10-06 04:05:28] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -29.600561 | E_var:     0.0944 | E_err:   0.004802
[2025-10-06 04:05:30] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -29.587000 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 04:05:32] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -29.596897 | E_var:     0.0793 | E_err:   0.004401
[2025-10-06 04:05:35] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -29.588148 | E_var:     0.0777 | E_err:   0.004357
[2025-10-06 04:05:37] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -29.593436 | E_var:     0.0790 | E_err:   0.004390
[2025-10-06 04:05:40] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -29.591627 | E_var:     0.0876 | E_err:   0.004626
[2025-10-06 04:05:42] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -29.589454 | E_var:     0.0831 | E_err:   0.004504
[2025-10-06 04:05:44] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -29.592960 | E_var:     0.0794 | E_err:   0.004403
[2025-10-06 04:05:47] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -29.598205 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 04:05:49] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -29.589951 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 04:05:52] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -29.587984 | E_var:     0.1068 | E_err:   0.005107
[2025-10-06 04:05:54] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -29.588224 | E_var:     0.5097 | E_err:   0.011155
[2025-10-06 04:05:57] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -29.600005 | E_var:     0.1214 | E_err:   0.005445
[2025-10-06 04:05:59] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -29.596585 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 04:06:01] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -29.592500 | E_var:     0.1052 | E_err:   0.005067
[2025-10-06 04:06:04] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -29.588836 | E_var:     0.0710 | E_err:   0.004163
[2025-10-06 04:06:06] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -29.591926 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 04:06:09] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -29.595261 | E_var:     0.0954 | E_err:   0.004825
[2025-10-06 04:06:11] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -29.592516 | E_var:     0.1024 | E_err:   0.005000
[2025-10-06 04:06:13] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -29.591267 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 04:06:16] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -29.595376 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 04:06:18] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -29.597873 | E_var:     0.0926 | E_err:   0.004755
[2025-10-06 04:06:21] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -29.588134 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 04:06:23] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -29.590271 | E_var:     0.0762 | E_err:   0.004313
[2025-10-06 04:06:26] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -29.591730 | E_var:     0.0894 | E_err:   0.004673
[2025-10-06 04:06:28] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -29.591657 | E_var:     0.0775 | E_err:   0.004349
[2025-10-06 04:06:30] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -29.596040 | E_var:     0.0678 | E_err:   0.004068
[2025-10-06 04:06:33] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -29.599902 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 04:06:35] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -29.597614 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 04:06:38] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -29.589333 | E_var:     0.0692 | E_err:   0.004109
[2025-10-06 04:06:40] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -29.592548 | E_var:     0.0814 | E_err:   0.004459
[2025-10-06 04:06:42] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -29.595811 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 04:06:45] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -29.587710 | E_var:     0.1145 | E_err:   0.005286
[2025-10-06 04:06:47] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -29.593344 | E_var:     0.0781 | E_err:   0.004365
[2025-10-06 04:06:50] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -29.594740 | E_var:     0.1004 | E_err:   0.004951
[2025-10-06 04:06:52] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -29.594199 | E_var:     0.0918 | E_err:   0.004735
[2025-10-06 04:06:55] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -29.593549 | E_var:     0.0993 | E_err:   0.004924
[2025-10-06 04:06:57] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -29.597876 | E_var:     0.0839 | E_err:   0.004527
[2025-10-06 04:06:59] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -29.592055 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 04:07:02] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -29.598799 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 04:07:04] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -29.594336 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 04:07:07] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -29.593519 | E_var:     0.0662 | E_err:   0.004020
[2025-10-06 04:07:09] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -29.599041 | E_var:     0.0971 | E_err:   0.004868
[2025-10-06 04:07:11] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -29.592807 | E_var:     0.0808 | E_err:   0.004442
[2025-10-06 04:07:14] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -29.598215 | E_var:     0.1000 | E_err:   0.004942
[2025-10-06 04:07:16] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -29.584314 | E_var:     0.0804 | E_err:   0.004429
[2025-10-06 04:07:19] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -29.592845 | E_var:     0.0869 | E_err:   0.004607
[2025-10-06 04:07:21] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -29.589690 | E_var:     0.1908 | E_err:   0.006825
[2025-10-06 04:07:24] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -29.592352 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 04:07:26] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -29.595433 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 04:07:28] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -29.603411 | E_var:     0.0747 | E_err:   0.004271
[2025-10-06 04:07:31] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -29.600919 | E_var:     0.1388 | E_err:   0.005821
[2025-10-06 04:07:33] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -29.597690 | E_var:     0.1116 | E_err:   0.005219
[2025-10-06 04:07:36] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -29.595926 | E_var:     0.1340 | E_err:   0.005720
[2025-10-06 04:07:38] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -29.585223 | E_var:     0.1001 | E_err:   0.004943
[2025-10-06 04:07:41] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -29.600522 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 04:07:43] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -29.603842 | E_var:     0.0777 | E_err:   0.004357
[2025-10-06 04:07:45] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -29.595322 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 04:07:48] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -29.589744 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 04:07:48] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 04:07:50] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -29.592676 | E_var:     0.0859 | E_err:   0.004580
[2025-10-06 04:07:53] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -29.598577 | E_var:     0.1008 | E_err:   0.004962
[2025-10-06 04:07:55] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -29.594066 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 04:07:57] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -29.595046 | E_var:     0.0743 | E_err:   0.004259
[2025-10-06 04:08:00] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -29.592883 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 04:08:02] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -29.598410 | E_var:     0.0894 | E_err:   0.004671
[2025-10-06 04:08:05] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -29.587597 | E_var:     0.0997 | E_err:   0.004933
[2025-10-06 04:08:07] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -29.595526 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 04:08:09] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -29.594106 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 04:08:12] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -29.604234 | E_var:     0.0795 | E_err:   0.004407
[2025-10-06 04:08:14] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -29.597940 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 04:08:17] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -29.596899 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 04:08:19] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -29.598981 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 04:08:22] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -29.599022 | E_var:     0.0930 | E_err:   0.004765
[2025-10-06 04:08:24] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -29.594943 | E_var:     0.1036 | E_err:   0.005030
[2025-10-06 04:08:26] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -29.600077 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 04:08:29] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -29.590417 | E_var:     0.1258 | E_err:   0.005541
[2025-10-06 04:08:31] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -29.599500 | E_var:     0.0796 | E_err:   0.004409
[2025-10-06 04:08:34] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -29.591083 | E_var:     0.0729 | E_err:   0.004217
[2025-10-06 04:08:36] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -29.597393 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 04:08:38] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -29.591707 | E_var:     0.0803 | E_err:   0.004427
[2025-10-06 04:08:41] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -29.597594 | E_var:     0.0747 | E_err:   0.004270
[2025-10-06 04:08:43] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -29.599934 | E_var:     0.0620 | E_err:   0.003890
[2025-10-06 04:08:46] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -29.588803 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 04:08:48] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -29.599623 | E_var:     0.2990 | E_err:   0.008544
[2025-10-06 04:08:51] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -29.597316 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 04:08:53] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -29.593293 | E_var:     0.0705 | E_err:   0.004149
[2025-10-06 04:08:55] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -29.591134 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 04:08:58] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -29.599249 | E_var:     0.0714 | E_err:   0.004176
[2025-10-06 04:09:00] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -29.594977 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 04:09:03] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -29.591679 | E_var:     0.0880 | E_err:   0.004634
[2025-10-06 04:09:05] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -29.599551 | E_var:     0.0718 | E_err:   0.004188
[2025-10-06 04:09:07] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -29.595672 | E_var:     0.0969 | E_err:   0.004865
[2025-10-06 04:09:10] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -29.605052 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 04:09:12] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -29.599766 | E_var:     0.1306 | E_err:   0.005647
[2025-10-06 04:09:15] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -29.597957 | E_var:     0.0863 | E_err:   0.004589
[2025-10-06 04:09:17] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -29.588219 | E_var:     0.1005 | E_err:   0.004955
[2025-10-06 04:09:20] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -29.596450 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 04:09:22] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -29.598063 | E_var:     0.0863 | E_err:   0.004591
[2025-10-06 04:09:24] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -29.590218 | E_var:     0.1060 | E_err:   0.005088
[2025-10-06 04:09:27] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -29.594771 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 04:09:29] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -29.596802 | E_var:     0.0687 | E_err:   0.004095
[2025-10-06 04:09:32] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -29.598704 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 04:09:34] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -29.592826 | E_var:     0.1031 | E_err:   0.005018
[2025-10-06 04:09:36] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -29.593282 | E_var:     0.1714 | E_err:   0.006470
[2025-10-06 04:09:39] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -29.591007 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 04:09:41] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -29.597037 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 04:09:44] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -29.595008 | E_var:     0.0789 | E_err:   0.004390
[2025-10-06 04:09:46] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -29.593979 | E_var:     0.0894 | E_err:   0.004672
[2025-10-06 04:09:48] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -29.593115 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 04:09:51] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -29.603502 | E_var:     0.0786 | E_err:   0.004382
[2025-10-06 04:09:53] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -29.600522 | E_var:     0.0805 | E_err:   0.004434
[2025-10-06 04:09:56] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -29.598610 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 04:09:58] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -29.607968 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 04:10:01] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -29.594413 | E_var:     0.0815 | E_err:   0.004459
[2025-10-06 04:10:03] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -29.592157 | E_var:     0.0815 | E_err:   0.004459
[2025-10-06 04:10:05] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -29.596245 | E_var:     0.0836 | E_err:   0.004519
[2025-10-06 04:10:08] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -29.596876 | E_var:     0.1106 | E_err:   0.005197
[2025-10-06 04:10:10] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -29.597073 | E_var:     0.0804 | E_err:   0.004430
[2025-10-06 04:10:13] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -29.589121 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 04:10:15] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -29.600239 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 04:10:18] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -29.593350 | E_var:     0.1979 | E_err:   0.006951
[2025-10-06 04:10:20] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -29.597176 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 04:10:22] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -29.597276 | E_var:     0.1177 | E_err:   0.005361
[2025-10-06 04:10:25] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -29.592088 | E_var:     0.1060 | E_err:   0.005088
[2025-10-06 04:10:27] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -29.601275 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 04:10:30] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -29.589209 | E_var:     0.0800 | E_err:   0.004418
[2025-10-06 04:10:32] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -29.593587 | E_var:     0.2459 | E_err:   0.007748
[2025-10-06 04:10:34] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -29.596265 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 04:10:37] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -29.602554 | E_var:     0.0767 | E_err:   0.004327
[2025-10-06 04:10:39] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -29.591362 | E_var:     0.1002 | E_err:   0.004946
[2025-10-06 04:10:42] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -29.592268 | E_var:     0.0938 | E_err:   0.004784
[2025-10-06 04:10:44] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -29.591858 | E_var:     0.0697 | E_err:   0.004125
[2025-10-06 04:10:47] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -29.596126 | E_var:     0.0669 | E_err:   0.004041
[2025-10-06 04:10:49] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -29.592395 | E_var:     0.1636 | E_err:   0.006320
[2025-10-06 04:10:51] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -29.593344 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 04:10:54] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -29.587637 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 04:10:56] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -29.596824 | E_var:     0.0792 | E_err:   0.004399
[2025-10-06 04:10:59] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -29.589881 | E_var:     0.0999 | E_err:   0.004939
[2025-10-06 04:11:01] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -29.593214 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 04:11:03] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -29.601362 | E_var:     0.1083 | E_err:   0.005142
[2025-10-06 04:11:06] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -29.597336 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 04:11:08] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -29.594838 | E_var:     0.1130 | E_err:   0.005253
[2025-10-06 04:11:11] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -29.594565 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 04:11:13] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -29.598432 | E_var:     0.0705 | E_err:   0.004150
[2025-10-06 04:11:16] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -29.600350 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 04:11:18] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -29.603741 | E_var:     0.0756 | E_err:   0.004297
[2025-10-06 04:11:20] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -29.592033 | E_var:     0.0973 | E_err:   0.004874
[2025-10-06 04:11:23] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -29.598898 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 04:11:25] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -29.594692 | E_var:     0.0794 | E_err:   0.004403
[2025-10-06 04:11:28] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -29.593667 | E_var:     0.1038 | E_err:   0.005033
[2025-10-06 04:11:30] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -29.589198 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 04:11:32] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -29.589794 | E_var:     0.1506 | E_err:   0.006064
[2025-10-06 04:11:35] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -29.600510 | E_var:     0.1453 | E_err:   0.005957
[2025-10-06 04:11:37] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -29.597693 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 04:11:40] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -29.602773 | E_var:     0.1091 | E_err:   0.005162
[2025-10-06 04:11:42] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -29.594012 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 04:11:45] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -29.590797 | E_var:     0.1096 | E_err:   0.005174
[2025-10-06 04:11:47] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -29.590933 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 04:11:49] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -29.592334 | E_var:     0.1946 | E_err:   0.006893
[2025-10-06 04:11:49] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 04:11:52] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -29.601641 | E_var:     0.1531 | E_err:   0.006115
[2025-10-06 04:11:54] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -29.591626 | E_var:     0.0776 | E_err:   0.004352
[2025-10-06 04:11:57] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -29.589366 | E_var:     0.1384 | E_err:   0.005813
[2025-10-06 04:11:59] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -29.596136 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 04:12:01] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -29.597248 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 04:12:04] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -29.591214 | E_var:     0.0926 | E_err:   0.004756
[2025-10-06 04:12:06] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -29.596468 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 04:12:09] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -29.587246 | E_var:     0.0825 | E_err:   0.004487
[2025-10-06 04:12:11] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -29.601467 | E_var:     0.0838 | E_err:   0.004522
[2025-10-06 04:12:14] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -29.598154 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 04:12:16] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -29.589618 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 04:12:18] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -29.596009 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 04:12:21] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -29.597363 | E_var:     0.1119 | E_err:   0.005226
[2025-10-06 04:12:23] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -29.599548 | E_var:     0.0846 | E_err:   0.004546
[2025-10-06 04:12:26] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -29.597144 | E_var:     0.0870 | E_err:   0.004608
[2025-10-06 04:12:28] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -29.597734 | E_var:     0.0836 | E_err:   0.004517
[2025-10-06 04:12:31] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -29.598968 | E_var:     0.1106 | E_err:   0.005197
[2025-10-06 04:12:33] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -29.588536 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 04:12:35] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -29.597926 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 04:12:38] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -29.592723 | E_var:     0.0910 | E_err:   0.004714
[2025-10-06 04:12:40] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -29.596017 | E_var:     0.0744 | E_err:   0.004261
[2025-10-06 04:12:43] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -29.590140 | E_var:     0.1030 | E_err:   0.005013
[2025-10-06 04:12:45] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -29.593055 | E_var:     0.0951 | E_err:   0.004817
[2025-10-06 04:12:47] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -29.590391 | E_var:     0.1146 | E_err:   0.005290
[2025-10-06 04:12:50] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -29.589067 | E_var:     0.0770 | E_err:   0.004334
[2025-10-06 04:12:52] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -29.598003 | E_var:     0.0871 | E_err:   0.004612
[2025-10-06 04:12:55] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -29.589752 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 04:12:57] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -29.593747 | E_var:     0.1106 | E_err:   0.005197
[2025-10-06 04:12:59] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -29.590947 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 04:13:02] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -29.595625 | E_var:     0.0684 | E_err:   0.004088
[2025-10-06 04:13:04] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -29.598886 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 04:13:07] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -29.594407 | E_var:     0.0717 | E_err:   0.004184
[2025-10-06 04:13:09] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -29.587092 | E_var:     0.0892 | E_err:   0.004666
[2025-10-06 04:13:12] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -29.591323 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 04:13:14] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -29.602455 | E_var:     0.0903 | E_err:   0.004695
[2025-10-06 04:13:16] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -29.587099 | E_var:     0.1234 | E_err:   0.005489
[2025-10-06 04:13:19] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -29.601613 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 04:13:21] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -29.595621 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 04:13:24] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -29.589481 | E_var:     0.1260 | E_err:   0.005547
[2025-10-06 04:13:26] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -29.592268 | E_var:     0.0989 | E_err:   0.004913
[2025-10-06 04:13:29] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -29.588886 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 04:13:31] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -29.594426 | E_var:     0.1158 | E_err:   0.005316
[2025-10-06 04:13:33] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -29.600855 | E_var:     0.0978 | E_err:   0.004887
[2025-10-06 04:13:36] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -29.596316 | E_var:     0.1011 | E_err:   0.004968
[2025-10-06 04:13:38] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -29.599388 | E_var:     0.0955 | E_err:   0.004828
[2025-10-06 04:13:41] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -29.593208 | E_var:     0.0798 | E_err:   0.004415
[2025-10-06 04:13:43] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -29.590837 | E_var:     0.0778 | E_err:   0.004359
[2025-10-06 04:13:45] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -29.594785 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 04:13:48] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -29.599286 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 04:13:50] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -29.593162 | E_var:     0.0782 | E_err:   0.004371
[2025-10-06 04:13:50] 🔄 RESTART #2 | Period: 600
[2025-10-06 04:13:53] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -29.599242 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 04:13:55] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -29.590462 | E_var:     0.0807 | E_err:   0.004439
[2025-10-06 04:13:58] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -29.596853 | E_var:     0.1380 | E_err:   0.005804
[2025-10-06 04:14:00] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -29.599771 | E_var:     0.0789 | E_err:   0.004389
[2025-10-06 04:14:02] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -29.587751 | E_var:     0.0808 | E_err:   0.004440
[2025-10-06 04:14:05] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -29.596301 | E_var:     0.1385 | E_err:   0.005815
[2025-10-06 04:14:07] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -29.597520 | E_var:     0.0918 | E_err:   0.004735
[2025-10-06 04:14:10] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -29.599281 | E_var:     0.0900 | E_err:   0.004688
[2025-10-06 04:14:12] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -29.605504 | E_var:     0.1007 | E_err:   0.004959
[2025-10-06 04:14:14] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -29.585806 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 04:14:17] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -29.598998 | E_var:     0.0851 | E_err:   0.004557
[2025-10-06 04:14:19] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -29.596322 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 04:14:22] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -29.585845 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 04:14:24] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -29.603913 | E_var:     0.0766 | E_err:   0.004323
[2025-10-06 04:14:26] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -29.600916 | E_var:     0.0820 | E_err:   0.004474
[2025-10-06 04:14:29] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -29.595726 | E_var:     0.0712 | E_err:   0.004168
[2025-10-06 04:14:31] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -29.599316 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 04:14:34] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -29.592853 | E_var:     0.1114 | E_err:   0.005216
[2025-10-06 04:14:36] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -29.596008 | E_var:     0.1108 | E_err:   0.005202
[2025-10-06 04:14:39] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -29.602443 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 04:14:41] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -29.594908 | E_var:     0.0844 | E_err:   0.004539
[2025-10-06 04:14:43] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -29.597462 | E_var:     0.0978 | E_err:   0.004885
[2025-10-06 04:14:46] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -29.596061 | E_var:     0.0722 | E_err:   0.004198
[2025-10-06 04:14:48] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -29.591127 | E_var:     0.0713 | E_err:   0.004172
[2025-10-06 04:14:51] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -29.595690 | E_var:     0.0927 | E_err:   0.004756
[2025-10-06 04:14:53] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -29.600446 | E_var:     0.0925 | E_err:   0.004753
[2025-10-06 04:14:55] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -29.593153 | E_var:     0.0738 | E_err:   0.004245
[2025-10-06 04:14:58] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -29.593268 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 04:15:00] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -29.594071 | E_var:     0.0840 | E_err:   0.004528
[2025-10-06 04:15:03] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -29.598056 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 04:15:05] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -29.591510 | E_var:     0.0898 | E_err:   0.004681
[2025-10-06 04:15:07] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -29.602322 | E_var:     0.1212 | E_err:   0.005440
[2025-10-06 04:15:10] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -29.595987 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 04:15:12] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -29.594177 | E_var:     0.0858 | E_err:   0.004577
[2025-10-06 04:15:15] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -29.596176 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 04:15:17] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -29.583485 | E_var:     0.6080 | E_err:   0.012183
[2025-10-06 04:15:19] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -29.602003 | E_var:     0.0759 | E_err:   0.004306
[2025-10-06 04:15:22] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -29.586391 | E_var:     0.0838 | E_err:   0.004523
[2025-10-06 04:15:24] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -29.581951 | E_var:     0.1269 | E_err:   0.005567
[2025-10-06 04:15:27] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -29.598439 | E_var:     0.0727 | E_err:   0.004214
[2025-10-06 04:15:29] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -29.591573 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 04:15:32] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -29.593228 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 04:15:34] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -29.597757 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 04:15:36] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -29.595478 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 04:15:39] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -29.588821 | E_var:     0.0784 | E_err:   0.004374
[2025-10-06 04:15:41] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -29.591686 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 04:15:44] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -29.603362 | E_var:     0.0723 | E_err:   0.004201
[2025-10-06 04:15:46] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -29.594356 | E_var:     0.0849 | E_err:   0.004551
[2025-10-06 04:15:48] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -29.597252 | E_var:     0.1298 | E_err:   0.005629
[2025-10-06 04:15:51] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -29.599928 | E_var:     0.1196 | E_err:   0.005405
[2025-10-06 04:15:51] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 04:15:53] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -29.585539 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 04:15:56] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -29.595535 | E_var:     0.0818 | E_err:   0.004468
[2025-10-06 04:15:58] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -29.600464 | E_var:     0.1085 | E_err:   0.005147
[2025-10-06 04:16:01] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -29.595723 | E_var:     0.0966 | E_err:   0.004858
[2025-10-06 04:16:03] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -29.594736 | E_var:     0.0826 | E_err:   0.004492
[2025-10-06 04:16:05] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -29.597069 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 04:16:08] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -29.596515 | E_var:     0.0791 | E_err:   0.004395
[2025-10-06 04:16:10] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -29.598999 | E_var:     0.1230 | E_err:   0.005479
[2025-10-06 04:16:13] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -29.598280 | E_var:     0.0942 | E_err:   0.004794
[2025-10-06 04:16:15] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -29.602313 | E_var:     0.1057 | E_err:   0.005081
[2025-10-06 04:16:17] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -29.598596 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 04:16:20] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -29.591331 | E_var:     0.1475 | E_err:   0.006001
[2025-10-06 04:16:22] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -29.595105 | E_var:     0.0836 | E_err:   0.004518
[2025-10-06 04:16:25] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -29.597776 | E_var:     0.1117 | E_err:   0.005222
[2025-10-06 04:16:27] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -29.599043 | E_var:     0.0882 | E_err:   0.004639
[2025-10-06 04:16:30] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -29.596336 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 04:16:32] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -29.598313 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 04:16:34] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -29.596176 | E_var:     0.0920 | E_err:   0.004738
[2025-10-06 04:16:37] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -29.592267 | E_var:     0.0821 | E_err:   0.004478
[2025-10-06 04:16:39] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -29.591701 | E_var:     0.1070 | E_err:   0.005111
[2025-10-06 04:16:42] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -29.597495 | E_var:     0.0745 | E_err:   0.004265
[2025-10-06 04:16:44] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -29.597295 | E_var:     0.1006 | E_err:   0.004955
[2025-10-06 04:16:46] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -29.590682 | E_var:     0.1591 | E_err:   0.006232
[2025-10-06 04:16:49] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -29.589214 | E_var:     0.1313 | E_err:   0.005662
[2025-10-06 04:16:51] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -29.590212 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 04:16:54] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -29.592791 | E_var:     0.0755 | E_err:   0.004293
[2025-10-06 04:16:56] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -29.591743 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 04:16:59] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -29.594735 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 04:17:01] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -29.585281 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 04:17:03] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -29.590707 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 04:17:06] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -29.592913 | E_var:     0.0776 | E_err:   0.004354
[2025-10-06 04:17:08] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -29.585597 | E_var:     0.1146 | E_err:   0.005290
[2025-10-06 04:17:11] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -29.596657 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 04:17:13] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -29.586308 | E_var:     0.1304 | E_err:   0.005641
[2025-10-06 04:17:15] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -29.601355 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 04:17:18] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -29.592184 | E_var:     0.0756 | E_err:   0.004298
[2025-10-06 04:17:20] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -29.601774 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 04:17:23] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -29.597343 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 04:17:25] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -29.589276 | E_var:     0.1339 | E_err:   0.005718
[2025-10-06 04:17:28] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -29.597323 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 04:17:30] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -29.598623 | E_var:     0.0979 | E_err:   0.004890
[2025-10-06 04:17:32] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -29.598666 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 04:17:35] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -29.586808 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 04:17:37] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -29.588478 | E_var:     0.1041 | E_err:   0.005040
[2025-10-06 04:17:40] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -29.595463 | E_var:     0.0818 | E_err:   0.004469
[2025-10-06 04:17:42] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -29.590512 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 04:17:44] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -29.591190 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 04:17:47] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -29.597006 | E_var:     0.1067 | E_err:   0.005105
[2025-10-06 04:17:49] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -29.591665 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 04:17:52] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -29.591682 | E_var:     0.0777 | E_err:   0.004355
[2025-10-06 04:17:54] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -29.592777 | E_var:     0.1169 | E_err:   0.005342
[2025-10-06 04:17:56] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -29.592662 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 04:17:59] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -29.593520 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 04:18:01] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -29.594941 | E_var:     0.0783 | E_err:   0.004373
[2025-10-06 04:18:04] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -29.594572 | E_var:     0.1030 | E_err:   0.005015
[2025-10-06 04:18:06] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -29.591546 | E_var:     0.0736 | E_err:   0.004238
[2025-10-06 04:18:09] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -29.597155 | E_var:     0.0970 | E_err:   0.004865
[2025-10-06 04:18:11] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -29.585765 | E_var:     0.1220 | E_err:   0.005458
[2025-10-06 04:18:13] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -29.593478 | E_var:     0.0762 | E_err:   0.004312
[2025-10-06 04:18:16] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -29.598846 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 04:18:18] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -29.589121 | E_var:     0.0812 | E_err:   0.004453
[2025-10-06 04:18:21] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -29.588234 | E_var:     0.1066 | E_err:   0.005102
[2025-10-06 04:18:23] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -29.604574 | E_var:     0.0904 | E_err:   0.004697
[2025-10-06 04:18:25] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -29.589732 | E_var:     0.1030 | E_err:   0.005015
[2025-10-06 04:18:28] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -29.591773 | E_var:     0.0752 | E_err:   0.004286
[2025-10-06 04:18:30] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -29.592207 | E_var:     0.0652 | E_err:   0.003990
[2025-10-06 04:18:33] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -29.590509 | E_var:     0.1129 | E_err:   0.005250
[2025-10-06 04:18:35] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -29.590510 | E_var:     0.0818 | E_err:   0.004470
[2025-10-06 04:18:37] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -29.593618 | E_var:     0.0751 | E_err:   0.004282
[2025-10-06 04:18:40] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -29.594764 | E_var:     0.1314 | E_err:   0.005665
[2025-10-06 04:18:42] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -29.594662 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 04:18:45] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -29.590634 | E_var:     0.0954 | E_err:   0.004825
[2025-10-06 04:18:47] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -29.592611 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 04:18:50] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -29.603122 | E_var:     0.0878 | E_err:   0.004630
[2025-10-06 04:18:52] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -29.596109 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 04:18:54] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -29.595628 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 04:18:57] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -29.593435 | E_var:     0.1010 | E_err:   0.004967
[2025-10-06 04:18:59] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -29.595877 | E_var:     0.0959 | E_err:   0.004840
[2025-10-06 04:19:02] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -29.597631 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 04:19:04] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -29.592417 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 04:19:06] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -29.600826 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 04:19:09] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -29.596356 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 04:19:11] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -29.595829 | E_var:     0.1126 | E_err:   0.005243
[2025-10-06 04:19:14] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -29.594091 | E_var:     0.1019 | E_err:   0.004989
[2025-10-06 04:19:16] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -29.589515 | E_var:     0.0826 | E_err:   0.004492
[2025-10-06 04:19:18] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -29.598122 | E_var:     0.1219 | E_err:   0.005456
[2025-10-06 04:19:21] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -29.587184 | E_var:     0.0983 | E_err:   0.004898
[2025-10-06 04:19:23] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -29.603295 | E_var:     0.1090 | E_err:   0.005159
[2025-10-06 04:19:26] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -29.597277 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 04:19:28] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -29.593511 | E_var:     0.0895 | E_err:   0.004673
[2025-10-06 04:19:31] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -29.593914 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 04:19:33] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -29.589906 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 04:19:35] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -29.594577 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 04:19:38] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -29.584700 | E_var:     0.0695 | E_err:   0.004118
[2025-10-06 04:19:40] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -29.596034 | E_var:     0.0973 | E_err:   0.004873
[2025-10-06 04:19:43] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -29.589818 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 04:19:45] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -29.587133 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 04:19:48] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -29.595109 | E_var:     0.0777 | E_err:   0.004354
[2025-10-06 04:19:50] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -29.599553 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 04:19:52] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -29.596298 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 04:19:52] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 04:19:55] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -29.604127 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 04:19:57] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -29.588280 | E_var:     0.0921 | E_err:   0.004742
[2025-10-06 04:20:00] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -29.596318 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 04:20:02] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -29.587170 | E_var:     0.0763 | E_err:   0.004316
[2025-10-06 04:20:05] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -29.594292 | E_var:     0.0915 | E_err:   0.004728
[2025-10-06 04:20:07] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -29.589747 | E_var:     0.2032 | E_err:   0.007044
[2025-10-06 04:20:09] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -29.587791 | E_var:     0.0657 | E_err:   0.004004
[2025-10-06 04:20:12] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -29.598499 | E_var:     0.0939 | E_err:   0.004789
[2025-10-06 04:20:14] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -29.599604 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 04:20:17] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -29.591705 | E_var:     0.0920 | E_err:   0.004738
[2025-10-06 04:20:19] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -29.601134 | E_var:     0.1047 | E_err:   0.005057
[2025-10-06 04:20:21] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -29.594483 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 04:20:24] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -29.596189 | E_var:     0.0816 | E_err:   0.004465
[2025-10-06 04:20:26] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -29.594600 | E_var:     0.0802 | E_err:   0.004424
[2025-10-06 04:20:29] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -29.583519 | E_var:     0.0922 | E_err:   0.004745
[2025-10-06 04:20:31] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -29.600113 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 04:20:34] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -29.588405 | E_var:     0.0724 | E_err:   0.004204
[2025-10-06 04:20:36] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -29.598408 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 04:20:38] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -29.598184 | E_var:     0.0886 | E_err:   0.004652
[2025-10-06 04:20:41] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -29.593550 | E_var:     0.1020 | E_err:   0.004991
[2025-10-06 04:20:43] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -29.582501 | E_var:     0.2336 | E_err:   0.007552
[2025-10-06 04:20:46] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -29.593579 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 04:20:48] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -29.595661 | E_var:     0.0875 | E_err:   0.004621
[2025-10-06 04:20:50] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -29.595814 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 04:20:53] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -29.602246 | E_var:     0.1219 | E_err:   0.005455
[2025-10-06 04:20:55] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -29.591331 | E_var:     0.0801 | E_err:   0.004421
[2025-10-06 04:20:58] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -29.594937 | E_var:     0.0780 | E_err:   0.004364
[2025-10-06 04:21:00] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -29.599140 | E_var:     0.0734 | E_err:   0.004234
[2025-10-06 04:21:03] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -29.600563 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 04:21:05] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -29.593464 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 04:21:07] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -29.597850 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 04:21:10] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -29.589862 | E_var:     0.0952 | E_err:   0.004822
[2025-10-06 04:21:12] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -29.599523 | E_var:     0.0938 | E_err:   0.004786
[2025-10-06 04:21:15] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -29.583108 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 04:21:17] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -29.609294 | E_var:     0.1063 | E_err:   0.005094
[2025-10-06 04:21:20] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -29.591847 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 04:21:22] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -29.591235 | E_var:     0.0969 | E_err:   0.004863
[2025-10-06 04:21:24] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -29.600959 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 04:21:27] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -29.580847 | E_var:     0.1132 | E_err:   0.005256
[2025-10-06 04:21:29] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -29.594371 | E_var:     0.0934 | E_err:   0.004774
[2025-10-06 04:21:32] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -29.592001 | E_var:     0.0815 | E_err:   0.004460
[2025-10-06 04:21:34] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -29.590619 | E_var:     0.0876 | E_err:   0.004626
[2025-10-06 04:21:36] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -29.600338 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 04:21:39] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -29.601048 | E_var:     0.0874 | E_err:   0.004620
[2025-10-06 04:21:41] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -29.593092 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 04:21:44] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -29.595615 | E_var:     0.0964 | E_err:   0.004850
[2025-10-06 04:21:46] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -29.587892 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 04:21:48] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -29.594544 | E_var:     0.0734 | E_err:   0.004232
[2025-10-06 04:21:51] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -29.591871 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 04:21:53] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -29.592189 | E_var:     0.1228 | E_err:   0.005475
[2025-10-06 04:21:56] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -29.605303 | E_var:     0.0895 | E_err:   0.004674
[2025-10-06 04:21:58] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -29.595131 | E_var:     0.1026 | E_err:   0.005004
[2025-10-06 04:22:01] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -29.598206 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 04:22:03] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -29.600312 | E_var:     0.1004 | E_err:   0.004952
[2025-10-06 04:22:05] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -29.591182 | E_var:     0.0867 | E_err:   0.004600
[2025-10-06 04:22:08] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -29.592617 | E_var:     0.1098 | E_err:   0.005177
[2025-10-06 04:22:10] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -29.598739 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 04:22:13] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -29.593783 | E_var:     0.0767 | E_err:   0.004326
[2025-10-06 04:22:15] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -29.598448 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 04:22:17] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -29.597772 | E_var:     0.1996 | E_err:   0.006981
[2025-10-06 04:22:20] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -29.594603 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 04:22:22] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -29.590221 | E_var:     0.2163 | E_err:   0.007267
[2025-10-06 04:22:25] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -29.593644 | E_var:     0.1046 | E_err:   0.005054
[2025-10-06 04:22:27] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -29.598288 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 04:22:30] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -29.590588 | E_var:     0.0763 | E_err:   0.004315
[2025-10-06 04:22:32] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -29.591850 | E_var:     0.1045 | E_err:   0.005051
[2025-10-06 04:22:34] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -29.605203 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 04:22:37] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -29.595861 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 04:22:39] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -29.583140 | E_var:     0.0858 | E_err:   0.004577
[2025-10-06 04:22:42] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -29.594855 | E_var:     0.0895 | E_err:   0.004676
[2025-10-06 04:22:44] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -29.598101 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 04:22:47] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -29.594321 | E_var:     0.0862 | E_err:   0.004589
[2025-10-06 04:22:49] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -29.591329 | E_var:     0.0826 | E_err:   0.004492
[2025-10-06 04:22:51] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -29.595699 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 04:22:54] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -29.601514 | E_var:     0.0718 | E_err:   0.004186
[2025-10-06 04:22:56] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -29.586943 | E_var:     0.1062 | E_err:   0.005092
[2025-10-06 04:22:59] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -29.590942 | E_var:     0.0817 | E_err:   0.004465
[2025-10-06 04:23:01] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -29.595830 | E_var:     0.0943 | E_err:   0.004797
[2025-10-06 04:23:03] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -29.596123 | E_var:     0.1024 | E_err:   0.004999
[2025-10-06 04:23:06] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -29.593674 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 04:23:08] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -29.591638 | E_var:     0.1053 | E_err:   0.005069
[2025-10-06 04:23:11] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -29.599896 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 04:23:13] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -29.600815 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 04:23:15] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -29.601218 | E_var:     0.0733 | E_err:   0.004230
[2025-10-06 04:23:18] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -29.595070 | E_var:     0.0752 | E_err:   0.004286
[2025-10-06 04:23:20] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -29.590896 | E_var:     0.0801 | E_err:   0.004423
[2025-10-06 04:23:23] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -29.597097 | E_var:     0.1071 | E_err:   0.005114
[2025-10-06 04:23:25] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -29.600466 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 04:23:27] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -29.590402 | E_var:     0.0892 | E_err:   0.004665
[2025-10-06 04:23:30] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -29.599353 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 04:23:32] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -29.596508 | E_var:     0.0780 | E_err:   0.004364
[2025-10-06 04:23:35] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -29.589808 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 04:23:37] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -29.599258 | E_var:     0.0744 | E_err:   0.004263
[2025-10-06 04:23:40] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -29.600398 | E_var:     0.0941 | E_err:   0.004794
[2025-10-06 04:23:42] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -29.596320 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 04:23:44] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -29.590594 | E_var:     0.0787 | E_err:   0.004384
[2025-10-06 04:23:47] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -29.596509 | E_var:     0.0958 | E_err:   0.004835
[2025-10-06 04:23:49] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -29.595385 | E_var:     0.1080 | E_err:   0.005135
[2025-10-06 04:23:52] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -29.594224 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 04:23:54] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -29.590698 | E_var:     0.1349 | E_err:   0.005739
[2025-10-06 04:23:54] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 04:23:57] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -29.591900 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 04:23:59] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -29.595694 | E_var:     0.0703 | E_err:   0.004143
[2025-10-06 04:24:01] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -29.593457 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 04:24:04] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -29.596339 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 04:24:06] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -29.595266 | E_var:     0.0690 | E_err:   0.004104
[2025-10-06 04:24:09] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -29.608430 | E_var:     0.1288 | E_err:   0.005607
[2025-10-06 04:24:11] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -29.596839 | E_var:     0.1321 | E_err:   0.005680
[2025-10-06 04:24:13] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -29.598355 | E_var:     0.0890 | E_err:   0.004663
[2025-10-06 04:24:16] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -29.596913 | E_var:     0.1100 | E_err:   0.005181
[2025-10-06 04:24:18] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -29.598156 | E_var:     0.1213 | E_err:   0.005442
[2025-10-06 04:24:21] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -29.596849 | E_var:     0.1331 | E_err:   0.005700
[2025-10-06 04:24:23] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -29.594958 | E_var:     0.0906 | E_err:   0.004703
[2025-10-06 04:24:26] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -29.590479 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 04:24:28] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -29.600150 | E_var:     0.0825 | E_err:   0.004487
[2025-10-06 04:24:30] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -29.587327 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 04:24:33] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -29.590516 | E_var:     0.0773 | E_err:   0.004343
[2025-10-06 04:24:35] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -29.604039 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 04:24:38] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -29.595179 | E_var:     0.0791 | E_err:   0.004395
[2025-10-06 04:24:40] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -29.593716 | E_var:     0.1650 | E_err:   0.006346
[2025-10-06 04:24:42] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -29.600239 | E_var:     0.0956 | E_err:   0.004830
[2025-10-06 04:24:45] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -29.595027 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 04:24:47] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -29.600228 | E_var:     0.0859 | E_err:   0.004581
[2025-10-06 04:24:50] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -29.589599 | E_var:     0.1000 | E_err:   0.004940
[2025-10-06 04:24:52] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -29.598462 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 04:24:54] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -29.596179 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 04:24:57] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -29.593493 | E_var:     0.1082 | E_err:   0.005141
[2025-10-06 04:24:59] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -29.603356 | E_var:     0.0980 | E_err:   0.004890
[2025-10-06 04:25:02] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -29.595837 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 04:25:04] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -29.587649 | E_var:     0.0917 | E_err:   0.004731
[2025-10-06 04:25:07] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -29.592411 | E_var:     0.1090 | E_err:   0.005160
[2025-10-06 04:25:09] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -29.594261 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 04:25:11] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -29.594982 | E_var:     0.0677 | E_err:   0.004065
[2025-10-06 04:25:14] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -29.594393 | E_var:     0.1098 | E_err:   0.005178
[2025-10-06 04:25:16] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -29.594478 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 04:25:19] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -29.595150 | E_var:     0.0823 | E_err:   0.004481
[2025-10-06 04:25:21] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -29.588638 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 04:25:23] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -29.596069 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 04:25:26] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -29.597897 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 04:25:28] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -29.602271 | E_var:     0.0804 | E_err:   0.004432
[2025-10-06 04:25:31] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -29.593606 | E_var:     0.0763 | E_err:   0.004315
[2025-10-06 04:25:33] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -29.594570 | E_var:     0.0761 | E_err:   0.004310
[2025-10-06 04:25:35] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -29.597688 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 04:25:38] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -29.584214 | E_var:     0.1062 | E_err:   0.005091
[2025-10-06 04:25:40] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -29.584841 | E_var:     0.1123 | E_err:   0.005235
[2025-10-06 04:25:43] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -29.602752 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 04:25:45] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -29.594886 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 04:25:48] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -29.591287 | E_var:     0.0855 | E_err:   0.004569
[2025-10-06 04:25:50] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -29.600171 | E_var:     0.0758 | E_err:   0.004300
[2025-10-06 04:25:52] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -29.596018 | E_var:     0.0686 | E_err:   0.004093
[2025-10-06 04:25:55] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -29.595309 | E_var:     0.0826 | E_err:   0.004490
[2025-10-06 04:25:57] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -29.592502 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 04:26:00] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -29.600465 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 04:26:02] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -29.600346 | E_var:     0.0883 | E_err:   0.004642
[2025-10-06 04:26:04] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -29.596424 | E_var:     0.0740 | E_err:   0.004250
[2025-10-06 04:26:07] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -29.599601 | E_var:     0.0827 | E_err:   0.004493
[2025-10-06 04:26:09] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -29.596437 | E_var:     0.0923 | E_err:   0.004746
[2025-10-06 04:26:12] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -29.591809 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 04:26:14] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -29.582945 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 04:26:16] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -29.598386 | E_var:     0.0971 | E_err:   0.004868
[2025-10-06 04:26:19] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -29.591639 | E_var:     0.2110 | E_err:   0.007178
[2025-10-06 04:26:21] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -29.595441 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 04:26:24] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -29.598269 | E_var:     0.0971 | E_err:   0.004870
[2025-10-06 04:26:26] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -29.590635 | E_var:     0.0818 | E_err:   0.004470
[2025-10-06 04:26:29] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -29.583256 | E_var:     0.1207 | E_err:   0.005429
[2025-10-06 04:26:31] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -29.593951 | E_var:     0.0764 | E_err:   0.004317
[2025-10-06 04:26:33] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -29.606405 | E_var:     0.0999 | E_err:   0.004939
[2025-10-06 04:26:36] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -29.600026 | E_var:     0.0909 | E_err:   0.004710
[2025-10-06 04:26:38] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -29.585342 | E_var:     0.1130 | E_err:   0.005253
[2025-10-06 04:26:41] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -29.602329 | E_var:     0.0968 | E_err:   0.004861
[2025-10-06 04:26:43] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -29.589303 | E_var:     0.0812 | E_err:   0.004452
[2025-10-06 04:26:45] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -29.597934 | E_var:     0.0816 | E_err:   0.004462
[2025-10-06 04:26:48] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -29.601079 | E_var:     0.1014 | E_err:   0.004977
[2025-10-06 04:26:50] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -29.596984 | E_var:     0.0891 | E_err:   0.004665
[2025-10-06 04:26:53] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -29.598835 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 04:26:55] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -29.594015 | E_var:     0.0800 | E_err:   0.004419
[2025-10-06 04:26:58] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -29.602241 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 04:27:00] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -29.607274 | E_var:     0.1118 | E_err:   0.005224
[2025-10-06 04:27:02] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -29.595130 | E_var:     0.0850 | E_err:   0.004554
[2025-10-06 04:27:05] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -29.598504 | E_var:     0.0794 | E_err:   0.004404
[2025-10-06 04:27:07] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -29.588709 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 04:27:10] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -29.600403 | E_var:     0.0765 | E_err:   0.004322
[2025-10-06 04:27:12] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -29.584622 | E_var:     0.0777 | E_err:   0.004354
[2025-10-06 04:27:14] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -29.594447 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 04:27:17] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -29.590253 | E_var:     0.0735 | E_err:   0.004237
[2025-10-06 04:27:19] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -29.597873 | E_var:     0.0758 | E_err:   0.004301
[2025-10-06 04:27:22] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -29.594186 | E_var:     0.1002 | E_err:   0.004945
[2025-10-06 04:27:24] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -29.601350 | E_var:     0.0720 | E_err:   0.004192
[2025-10-06 04:27:27] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -29.605828 | E_var:     0.1155 | E_err:   0.005309
[2025-10-06 04:27:29] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -29.589622 | E_var:     0.0814 | E_err:   0.004459
[2025-10-06 04:27:31] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -29.596698 | E_var:     0.1138 | E_err:   0.005272
[2025-10-06 04:27:34] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -29.591735 | E_var:     0.0909 | E_err:   0.004712
[2025-10-06 04:27:36] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -29.591829 | E_var:     0.0960 | E_err:   0.004841
[2025-10-06 04:27:39] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -29.606673 | E_var:     0.0897 | E_err:   0.004681
[2025-10-06 04:27:41] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -29.585614 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 04:27:43] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -29.590343 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 04:27:46] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -29.590970 | E_var:     0.0791 | E_err:   0.004395
[2025-10-06 04:27:48] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -29.586558 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 04:27:51] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -29.596839 | E_var:     0.0815 | E_err:   0.004461
[2025-10-06 04:27:53] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -29.588218 | E_var:     0.0687 | E_err:   0.004095
[2025-10-06 04:27:55] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -29.597187 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 04:27:56] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 04:27:58] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -29.597644 | E_var:     0.1117 | E_err:   0.005221
[2025-10-06 04:28:00] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -29.597463 | E_var:     0.0760 | E_err:   0.004309
[2025-10-06 04:28:03] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -29.588510 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 04:28:05] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -29.594374 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 04:28:08] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -29.598205 | E_var:     0.0798 | E_err:   0.004414
[2025-10-06 04:28:10] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -29.589961 | E_var:     0.0694 | E_err:   0.004117
[2025-10-06 04:28:12] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -29.581040 | E_var:     0.0871 | E_err:   0.004612
[2025-10-06 04:28:15] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -29.598908 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 04:28:17] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -29.593638 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 04:28:20] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -29.601513 | E_var:     0.1037 | E_err:   0.005032
[2025-10-06 04:28:22] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -29.592860 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 04:28:24] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -29.597208 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 04:28:27] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -29.597710 | E_var:     0.0934 | E_err:   0.004774
[2025-10-06 04:28:29] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -29.593132 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 04:28:32] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -29.585919 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 04:28:34] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -29.593482 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 04:28:36] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -29.589695 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 04:28:39] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -29.588237 | E_var:     0.1064 | E_err:   0.005096
[2025-10-06 04:28:41] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -29.594891 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 04:28:44] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -29.590685 | E_var:     0.1066 | E_err:   0.005101
[2025-10-06 04:28:46] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -29.599257 | E_var:     0.0914 | E_err:   0.004725
[2025-10-06 04:28:48] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -29.596656 | E_var:     0.1660 | E_err:   0.006366
[2025-10-06 04:28:51] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -29.596881 | E_var:     0.0900 | E_err:   0.004688
[2025-10-06 04:28:53] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -29.597026 | E_var:     0.1012 | E_err:   0.004972
[2025-10-06 04:28:56] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -29.590898 | E_var:     0.0801 | E_err:   0.004421
[2025-10-06 04:28:58] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -29.591722 | E_var:     0.1732 | E_err:   0.006503
[2025-10-06 04:29:01] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -29.602398 | E_var:     0.0727 | E_err:   0.004213
[2025-10-06 04:29:03] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -29.595660 | E_var:     0.0729 | E_err:   0.004220
[2025-10-06 04:29:05] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -29.595075 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 04:29:08] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -29.596279 | E_var:     0.0997 | E_err:   0.004935
[2025-10-06 04:29:10] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -29.591693 | E_var:     0.0847 | E_err:   0.004548
[2025-10-06 04:29:13] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -29.586600 | E_var:     0.0938 | E_err:   0.004786
[2025-10-06 04:29:15] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -29.597616 | E_var:     0.0890 | E_err:   0.004661
[2025-10-06 04:29:18] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -29.597136 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 04:29:20] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -29.599495 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 04:29:22] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -29.602465 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 04:29:25] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -29.591359 | E_var:     0.1019 | E_err:   0.004989
[2025-10-06 04:29:27] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -29.600472 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 04:29:30] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -29.588623 | E_var:     0.1083 | E_err:   0.005143
[2025-10-06 04:29:32] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -29.587709 | E_var:     0.1919 | E_err:   0.006846
[2025-10-06 04:29:34] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -29.604561 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 04:29:37] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -29.590531 | E_var:     0.0807 | E_err:   0.004439
[2025-10-06 04:29:39] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -29.587003 | E_var:     0.1305 | E_err:   0.005645
[2025-10-06 04:29:42] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -29.590789 | E_var:     0.0981 | E_err:   0.004894
[2025-10-06 04:29:44] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -29.593430 | E_var:     0.0820 | E_err:   0.004473
[2025-10-06 04:29:47] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -29.594598 | E_var:     0.0683 | E_err:   0.004084
[2025-10-06 04:29:49] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -29.599358 | E_var:     0.0962 | E_err:   0.004845
[2025-10-06 04:29:51] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -29.593868 | E_var:     0.0818 | E_err:   0.004470
[2025-10-06 04:29:54] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -29.597853 | E_var:     0.0672 | E_err:   0.004049
[2025-10-06 04:29:56] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -29.595731 | E_var:     0.0896 | E_err:   0.004678
[2025-10-06 04:29:59] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -29.597848 | E_var:     0.0747 | E_err:   0.004272
[2025-10-06 04:30:01] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -29.598004 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 04:30:03] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -29.592622 | E_var:     0.1332 | E_err:   0.005703
[2025-10-06 04:30:06] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -29.588734 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 04:30:08] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -29.590806 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 04:30:11] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -29.601215 | E_var:     0.0929 | E_err:   0.004761
[2025-10-06 04:30:13] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -29.594360 | E_var:     0.1079 | E_err:   0.005133
[2025-10-06 04:30:15] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -29.588630 | E_var:     0.0741 | E_err:   0.004253
[2025-10-06 04:30:18] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -29.596782 | E_var:     0.0820 | E_err:   0.004473
[2025-10-06 04:30:20] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -29.597096 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 04:30:23] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -29.588661 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 04:30:25] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -29.592042 | E_var:     0.0758 | E_err:   0.004302
[2025-10-06 04:30:27] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -29.602898 | E_var:     0.0832 | E_err:   0.004508
[2025-10-06 04:30:30] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -29.592963 | E_var:     0.1133 | E_err:   0.005260
[2025-10-06 04:30:32] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -29.586656 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 04:30:35] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -29.597405 | E_var:     0.1028 | E_err:   0.005010
[2025-10-06 04:30:37] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -29.587778 | E_var:     0.1407 | E_err:   0.005862
[2025-10-06 04:30:40] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -29.597732 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 04:30:42] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -29.595347 | E_var:     0.0734 | E_err:   0.004232
[2025-10-06 04:30:44] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -29.599295 | E_var:     0.0801 | E_err:   0.004422
[2025-10-06 04:30:47] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -29.598589 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 04:30:49] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -29.594745 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 04:30:52] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -29.588540 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 04:30:54] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -29.600746 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 04:30:56] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -29.594114 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 04:30:59] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -29.592546 | E_var:     0.0861 | E_err:   0.004585
[2025-10-06 04:31:01] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -29.597055 | E_var:     0.1102 | E_err:   0.005188
[2025-10-06 04:31:04] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -29.599592 | E_var:     0.0652 | E_err:   0.003990
[2025-10-06 04:31:06] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -29.600819 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 04:31:09] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -29.593640 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 04:31:11] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -29.607016 | E_var:     0.1307 | E_err:   0.005648
[2025-10-06 04:31:13] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -29.598879 | E_var:     0.0753 | E_err:   0.004288
[2025-10-06 04:31:16] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -29.601370 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 04:31:18] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -29.591452 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 04:31:21] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -29.602802 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 04:31:23] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -29.600320 | E_var:     0.1153 | E_err:   0.005307
[2025-10-06 04:31:25] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -29.594286 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 04:31:28] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -29.605295 | E_var:     0.1419 | E_err:   0.005887
[2025-10-06 04:31:30] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -29.604752 | E_var:     0.2143 | E_err:   0.007234
[2025-10-06 04:31:33] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -29.602010 | E_var:     0.0866 | E_err:   0.004599
[2025-10-06 04:31:35] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -29.596511 | E_var:     0.0655 | E_err:   0.003998
[2025-10-06 04:31:38] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -29.591581 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 04:31:40] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -29.590922 | E_var:     0.0675 | E_err:   0.004060
[2025-10-06 04:31:42] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -29.587963 | E_var:     0.0792 | E_err:   0.004399
[2025-10-06 04:31:45] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -29.599015 | E_var:     0.1218 | E_err:   0.005452
[2025-10-06 04:31:47] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -29.600885 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 04:31:50] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -29.596246 | E_var:     0.0789 | E_err:   0.004388
[2025-10-06 04:31:52] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -29.593532 | E_var:     0.1194 | E_err:   0.005398
[2025-10-06 04:31:54] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -29.591874 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 04:31:57] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -29.599803 | E_var:     0.0955 | E_err:   0.004828
[2025-10-06 04:31:57] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 04:31:59] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -29.599523 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 04:32:02] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -29.597197 | E_var:     0.0760 | E_err:   0.004307
[2025-10-06 04:32:04] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -29.599985 | E_var:     0.0792 | E_err:   0.004398
[2025-10-06 04:32:06] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -29.591323 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 04:32:09] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -29.592756 | E_var:     0.0986 | E_err:   0.004906
[2025-10-06 04:32:11] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -29.596465 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 04:32:14] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -29.601379 | E_var:     0.1242 | E_err:   0.005506
[2025-10-06 04:32:16] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -29.587419 | E_var:     0.1012 | E_err:   0.004969
[2025-10-06 04:32:19] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -29.590222 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 04:32:21] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -29.602991 | E_var:     0.0941 | E_err:   0.004794
[2025-10-06 04:32:23] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -29.595260 | E_var:     0.0747 | E_err:   0.004270
[2025-10-06 04:32:26] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -29.591651 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 04:32:28] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -29.599546 | E_var:     0.0950 | E_err:   0.004817
[2025-10-06 04:32:31] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -29.595055 | E_var:     0.1249 | E_err:   0.005523
[2025-10-06 04:32:33] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -29.600196 | E_var:     0.0880 | E_err:   0.004636
[2025-10-06 04:32:35] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -29.603416 | E_var:     0.0735 | E_err:   0.004235
[2025-10-06 04:32:38] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -29.596931 | E_var:     0.0830 | E_err:   0.004502
[2025-10-06 04:32:40] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -29.594943 | E_var:     0.0706 | E_err:   0.004152
[2025-10-06 04:32:43] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -29.589750 | E_var:     0.0831 | E_err:   0.004506
[2025-10-06 04:32:45] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -29.595312 | E_var:     0.1021 | E_err:   0.004994
[2025-10-06 04:32:47] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -29.593819 | E_var:     0.0775 | E_err:   0.004351
[2025-10-06 04:32:50] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -29.592579 | E_var:     0.1071 | E_err:   0.005112
[2025-10-06 04:32:52] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -29.597292 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 04:32:55] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -29.596962 | E_var:     0.0959 | E_err:   0.004838
[2025-10-06 04:32:57] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -29.590166 | E_var:     0.1093 | E_err:   0.005167
[2025-10-06 04:33:00] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -29.586322 | E_var:     0.1203 | E_err:   0.005421
[2025-10-06 04:33:02] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -29.598488 | E_var:     0.0969 | E_err:   0.004863
[2025-10-06 04:33:04] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -29.590109 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 04:33:07] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -29.593688 | E_var:     0.0826 | E_err:   0.004492
[2025-10-06 04:33:09] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -29.601097 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 04:33:12] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -29.583709 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 04:33:14] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -29.591053 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 04:33:16] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -29.606550 | E_var:     0.0843 | E_err:   0.004536
[2025-10-06 04:33:19] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -29.590013 | E_var:     0.0876 | E_err:   0.004626
[2025-10-06 04:33:21] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -29.594820 | E_var:     0.1089 | E_err:   0.005155
[2025-10-06 04:33:24] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -29.590344 | E_var:     0.0801 | E_err:   0.004424
[2025-10-06 04:33:26] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -29.597000 | E_var:     0.0718 | E_err:   0.004187
[2025-10-06 04:33:29] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -29.595101 | E_var:     0.0587 | E_err:   0.003786
[2025-10-06 04:33:31] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -29.595746 | E_var:     0.0801 | E_err:   0.004422
[2025-10-06 04:33:33] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -29.594763 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 04:33:36] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -29.598492 | E_var:     0.0822 | E_err:   0.004479
[2025-10-06 04:33:38] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -29.597246 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 04:33:41] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -29.594665 | E_var:     0.0858 | E_err:   0.004577
[2025-10-06 04:33:43] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -29.598229 | E_var:     0.0693 | E_err:   0.004114
[2025-10-06 04:33:45] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -29.596110 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 04:33:48] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -29.586990 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 04:33:50] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -29.590329 | E_var:     0.0871 | E_err:   0.004612
[2025-10-06 04:33:53] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -29.590359 | E_var:     0.0767 | E_err:   0.004326
[2025-10-06 04:33:55] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -29.589638 | E_var:     0.1057 | E_err:   0.005081
[2025-10-06 04:33:57] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -29.589575 | E_var:     0.0890 | E_err:   0.004663
[2025-10-06 04:34:00] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -29.592231 | E_var:     0.1117 | E_err:   0.005223
[2025-10-06 04:34:02] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -29.595715 | E_var:     0.0735 | E_err:   0.004237
[2025-10-06 04:34:05] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -29.584338 | E_var:     0.1135 | E_err:   0.005264
[2025-10-06 04:34:07] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -29.592306 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 04:34:10] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -29.586462 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 04:34:12] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -29.594685 | E_var:     0.0762 | E_err:   0.004314
[2025-10-06 04:34:14] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -29.590287 | E_var:     0.1082 | E_err:   0.005141
[2025-10-06 04:34:17] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -29.596435 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 04:34:19] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -29.596749 | E_var:     0.1002 | E_err:   0.004945
[2025-10-06 04:34:22] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -29.600008 | E_var:     0.0657 | E_err:   0.004005
[2025-10-06 04:34:24] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -29.588354 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 04:34:26] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -29.594631 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 04:34:29] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -29.588523 | E_var:     0.0696 | E_err:   0.004123
[2025-10-06 04:34:31] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -29.594306 | E_var:     0.0905 | E_err:   0.004701
[2025-10-06 04:34:34] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -29.602606 | E_var:     0.0978 | E_err:   0.004886
[2025-10-06 04:34:36] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -29.597486 | E_var:     0.0796 | E_err:   0.004409
[2025-10-06 04:34:39] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -29.592097 | E_var:     0.0989 | E_err:   0.004913
[2025-10-06 04:34:41] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -29.589090 | E_var:     0.1115 | E_err:   0.005216
[2025-10-06 04:34:43] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -29.595777 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 04:34:46] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -29.592218 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 04:34:48] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -29.589745 | E_var:     0.0768 | E_err:   0.004331
[2025-10-06 04:34:51] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -29.597911 | E_var:     0.0726 | E_err:   0.004211
[2025-10-06 04:34:53] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -29.602493 | E_var:     0.0871 | E_err:   0.004611
[2025-10-06 04:34:55] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -29.599076 | E_var:     0.0724 | E_err:   0.004204
[2025-10-06 04:34:58] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -29.587610 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 04:35:00] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -29.588573 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 04:35:03] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -29.599346 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 04:35:05] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -29.591705 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 04:35:07] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -29.600939 | E_var:     0.1524 | E_err:   0.006100
[2025-10-06 04:35:10] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -29.589842 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 04:35:12] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -29.591403 | E_var:     0.1201 | E_err:   0.005415
[2025-10-06 04:35:15] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -29.590277 | E_var:     0.1165 | E_err:   0.005334
[2025-10-06 04:35:17] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -29.596144 | E_var:     0.0975 | E_err:   0.004879
[2025-10-06 04:35:20] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -29.596475 | E_var:     0.0891 | E_err:   0.004664
[2025-10-06 04:35:22] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -29.596471 | E_var:     0.0666 | E_err:   0.004033
[2025-10-06 04:35:24] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -29.603653 | E_var:     0.0919 | E_err:   0.004738
[2025-10-06 04:35:27] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -29.599569 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 04:35:29] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -29.601425 | E_var:     0.1474 | E_err:   0.005999
[2025-10-06 04:35:32] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -29.602155 | E_var:     0.0836 | E_err:   0.004518
[2025-10-06 04:35:34] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -29.591670 | E_var:     0.0752 | E_err:   0.004285
[2025-10-06 04:35:36] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -29.598658 | E_var:     0.0930 | E_err:   0.004766
[2025-10-06 04:35:39] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -29.597303 | E_var:     0.0855 | E_err:   0.004570
[2025-10-06 04:35:41] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -29.595798 | E_var:     0.0734 | E_err:   0.004234
[2025-10-06 04:35:44] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -29.595950 | E_var:     0.0963 | E_err:   0.004849
[2025-10-06 04:35:46] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -29.599661 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 04:35:48] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -29.591718 | E_var:     0.1155 | E_err:   0.005309
[2025-10-06 04:35:51] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -29.597500 | E_var:     0.0643 | E_err:   0.003962
[2025-10-06 04:35:53] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -29.594766 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 04:35:56] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -29.590649 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 04:35:58] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -29.591494 | E_var:     0.1251 | E_err:   0.005527
[2025-10-06 04:35:58] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 04:36:01] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -29.595178 | E_var:     0.1393 | E_err:   0.005831
[2025-10-06 04:36:03] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -29.596261 | E_var:     0.1668 | E_err:   0.006381
[2025-10-06 04:36:05] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -29.597738 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 04:36:08] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -29.597746 | E_var:     0.0793 | E_err:   0.004401
[2025-10-06 04:36:10] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -29.594401 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 04:36:13] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -29.598855 | E_var:     0.1086 | E_err:   0.005148
[2025-10-06 04:36:15] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -29.598610 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 04:36:17] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -29.591189 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 04:36:20] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -29.590125 | E_var:     0.0758 | E_err:   0.004301
[2025-10-06 04:36:22] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -29.596339 | E_var:     0.0765 | E_err:   0.004322
[2025-10-06 04:36:25] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -29.595485 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 04:36:27] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -29.598023 | E_var:     0.0839 | E_err:   0.004527
[2025-10-06 04:36:30] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -29.591186 | E_var:     0.0669 | E_err:   0.004041
[2025-10-06 04:36:32] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -29.586594 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 04:36:34] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -29.593487 | E_var:     0.0902 | E_err:   0.004694
[2025-10-06 04:36:37] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -29.587843 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 04:36:39] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -29.596699 | E_var:     0.0729 | E_err:   0.004220
[2025-10-06 04:36:42] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -29.599653 | E_var:     0.0748 | E_err:   0.004273
[2025-10-06 04:36:44] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -29.589533 | E_var:     0.0760 | E_err:   0.004308
[2025-10-06 04:36:46] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -29.601924 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 04:36:49] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -29.599898 | E_var:     0.0790 | E_err:   0.004392
[2025-10-06 04:36:51] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -29.605371 | E_var:     0.0926 | E_err:   0.004755
[2025-10-06 04:36:54] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -29.591203 | E_var:     0.0797 | E_err:   0.004412
[2025-10-06 04:36:56] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -29.595810 | E_var:     0.0708 | E_err:   0.004158
[2025-10-06 04:36:58] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -29.590817 | E_var:     0.1032 | E_err:   0.005021
[2025-10-06 04:37:01] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -29.593297 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 04:37:03] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -29.596657 | E_var:     0.0956 | E_err:   0.004831
[2025-10-06 04:37:06] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -29.592621 | E_var:     0.1434 | E_err:   0.005918
[2025-10-06 04:37:08] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -29.596652 | E_var:     0.1251 | E_err:   0.005527
[2025-10-06 04:37:11] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -29.592611 | E_var:     0.0834 | E_err:   0.004511
[2025-10-06 04:37:13] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -29.592173 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 04:37:15] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -29.598570 | E_var:     0.0743 | E_err:   0.004258
[2025-10-06 04:37:18] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -29.590636 | E_var:     0.0716 | E_err:   0.004182
[2025-10-06 04:37:20] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -29.598525 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 04:37:23] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -29.592406 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 04:37:25] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -29.585995 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 04:37:27] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -29.589860 | E_var:     0.0801 | E_err:   0.004423
[2025-10-06 04:37:30] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -29.598429 | E_var:     0.0806 | E_err:   0.004437
[2025-10-06 04:37:32] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -29.592617 | E_var:     0.0821 | E_err:   0.004478
[2025-10-06 04:37:35] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -29.591584 | E_var:     0.1103 | E_err:   0.005188
[2025-10-06 04:37:37] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -29.598023 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 04:37:39] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -29.600040 | E_var:     0.0784 | E_err:   0.004376
[2025-10-06 04:37:42] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -29.599725 | E_var:     0.0800 | E_err:   0.004419
[2025-10-06 04:37:44] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -29.587548 | E_var:     0.0918 | E_err:   0.004735
[2025-10-06 04:37:47] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -29.590507 | E_var:     0.0843 | E_err:   0.004537
[2025-10-06 04:37:49] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -29.595982 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 04:37:52] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -29.596903 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 04:37:54] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -29.592278 | E_var:     0.0670 | E_err:   0.004046
[2025-10-06 04:37:56] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -29.602190 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 04:37:59] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -29.599770 | E_var:     0.0914 | E_err:   0.004723
[2025-10-06 04:37:59] ======================================================================================================
[2025-10-06 04:37:59] ✅ Training completed successfully
[2025-10-06 04:37:59] Total restarts: 2
[2025-10-06 04:38:00] Final Energy: -29.59976981 ± 0.00472324
[2025-10-06 04:38:00] Final Variance: 0.091378
[2025-10-06 04:38:00] ======================================================================================================
[2025-10-06 04:38:00] ======================================================================================================
[2025-10-06 04:38:00] Training completed | Runtime: 2584.7s
[2025-10-06 04:38:00] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 04:38:00] ======================================================================================================
