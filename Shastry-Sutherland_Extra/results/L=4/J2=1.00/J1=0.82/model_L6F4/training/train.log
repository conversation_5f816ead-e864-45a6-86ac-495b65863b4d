[2025-10-06 23:45:15] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.81/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 23:45:15]   - 迭代次数: final
[2025-10-06 23:45:15]   - 能量: -29.188714+0.000284j ± 0.004578, Var: 0.085844
[2025-10-06 23:45:15]   - 时间戳: 2025-10-06T23:45:03.650054+08:00
[2025-10-06 23:45:33] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 23:45:33] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 23:45:33] ======================================================================================================
[2025-10-06 23:45:33] GCNN for Shastry-Sutherland Model
[2025-10-06 23:45:33] ======================================================================================================
[2025-10-06 23:45:33] System parameters:
[2025-10-06 23:45:33]   - System size: L=4, N=64
[2025-10-06 23:45:33]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-10-06 23:45:33] ------------------------------------------------------------------------------------------------------
[2025-10-06 23:45:33] Model parameters:
[2025-10-06 23:45:33]   - Number of layers = 6
[2025-10-06 23:45:33]   - Number of features = 4
[2025-10-06 23:45:33]   - Total parameters = 20780
[2025-10-06 23:45:33] ------------------------------------------------------------------------------------------------------
[2025-10-06 23:45:33] Training parameters:
[2025-10-06 23:45:33]   - Total iterations: 1050
[2025-10-06 23:45:33]   - Annealing cycles: 3
[2025-10-06 23:45:33]   - Initial period: 150
[2025-10-06 23:45:33]   - Period multiplier: 2.0
[2025-10-06 23:45:33]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 23:45:33]   - Samples: 4096
[2025-10-06 23:45:33]   - Discarded samples: 0
[2025-10-06 23:45:33]   - Chunk size: 4096
[2025-10-06 23:45:33]   - Diagonal shift: 0.15
[2025-10-06 23:45:33]   - Gradient clipping: 1.0
[2025-10-06 23:45:33]   - Checkpoint enabled: interval=100
[2025-10-06 23:45:33]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.82/model_L6F4/training/checkpoints
[2025-10-06 23:45:33] ------------------------------------------------------------------------------------------------------
[2025-10-06 23:45:33] Device status:
[2025-10-06 23:45:33]   - Devices model: NVIDIA H200 NVL
[2025-10-06 23:45:33]   - Number of devices: 1
[2025-10-06 23:45:33]   - Sharding: True
[2025-10-06 23:45:34] ======================================================================================================
[2025-10-06 23:46:17] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -29.591889 | E_var:     0.2340 | E_err:   0.007559
[2025-10-06 23:46:43] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -29.601499 | E_var:     0.1467 | E_err:   0.005986
[2025-10-06 23:46:47] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -29.597696 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 23:46:50] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -29.592846 | E_var:     0.0822 | E_err:   0.004479
[2025-10-06 23:46:54] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -29.599542 | E_var:     0.0783 | E_err:   0.004373
[2025-10-06 23:46:58] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -29.599014 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 23:47:01] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -29.604480 | E_var:     0.0802 | E_err:   0.004424
[2025-10-06 23:47:05] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -29.598044 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 23:47:09] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -29.597133 | E_var:     0.0999 | E_err:   0.004939
[2025-10-06 23:47:12] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -29.602996 | E_var:     0.0804 | E_err:   0.004432
[2025-10-06 23:47:16] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -29.593713 | E_var:     0.0826 | E_err:   0.004492
[2025-10-06 23:47:20] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -29.597880 | E_var:     0.0816 | E_err:   0.004462
[2025-10-06 23:47:23] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -29.597047 | E_var:     0.0946 | E_err:   0.004807
[2025-10-06 23:47:27] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -29.601799 | E_var:     0.0660 | E_err:   0.004014
[2025-10-06 23:47:31] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -29.606865 | E_var:     0.0586 | E_err:   0.003784
[2025-10-06 23:47:34] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -29.595145 | E_var:     0.0851 | E_err:   0.004557
[2025-10-06 23:47:38] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -29.597369 | E_var:     0.0732 | E_err:   0.004226
[2025-10-06 23:47:42] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -29.600956 | E_var:     0.0652 | E_err:   0.003991
[2025-10-06 23:47:46] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -29.595215 | E_var:     0.0844 | E_err:   0.004539
[2025-10-06 23:47:49] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -29.593695 | E_var:     0.1240 | E_err:   0.005503
[2025-10-06 23:47:53] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -29.599002 | E_var:     0.0937 | E_err:   0.004783
[2025-10-06 23:47:57] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -29.601042 | E_var:     0.1324 | E_err:   0.005685
[2025-10-06 23:48:00] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -29.602169 | E_var:     0.0562 | E_err:   0.003705
[2025-10-06 23:48:04] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -29.601795 | E_var:     0.0835 | E_err:   0.004515
[2025-10-06 23:48:08] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -29.596782 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 23:48:11] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -29.595587 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 23:48:15] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -29.613472 | E_var:     0.1044 | E_err:   0.005049
[2025-10-06 23:48:19] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -29.601140 | E_var:     0.0554 | E_err:   0.003678
[2025-10-06 23:48:22] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -29.608694 | E_var:     0.4885 | E_err:   0.010921
[2025-10-06 23:48:26] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -29.593481 | E_var:     0.0579 | E_err:   0.003761
[2025-10-06 23:48:30] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -29.602688 | E_var:     0.1175 | E_err:   0.005357
[2025-10-06 23:48:33] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -29.601885 | E_var:     0.0605 | E_err:   0.003845
[2025-10-06 23:48:37] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -29.598796 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 23:48:41] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -29.595606 | E_var:     0.0721 | E_err:   0.004196
[2025-10-06 23:48:44] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -29.597717 | E_var:     0.0617 | E_err:   0.003881
[2025-10-06 23:48:48] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -29.599197 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 23:48:52] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -29.598544 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 23:48:55] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -29.601559 | E_var:     0.0617 | E_err:   0.003881
[2025-10-06 23:48:59] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -29.600598 | E_var:     0.0706 | E_err:   0.004152
[2025-10-06 23:49:03] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -29.596694 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 23:49:06] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -29.602760 | E_var:     0.0623 | E_err:   0.003900
[2025-10-06 23:49:10] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -29.596380 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 23:49:14] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -29.599385 | E_var:     0.0703 | E_err:   0.004143
[2025-10-06 23:49:18] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -29.594467 | E_var:     0.0653 | E_err:   0.003992
[2025-10-06 23:49:21] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -29.602468 | E_var:     0.0905 | E_err:   0.004699
[2025-10-06 23:49:25] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -29.604178 | E_var:     0.0660 | E_err:   0.004015
[2025-10-06 23:49:29] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -29.601081 | E_var:     0.1018 | E_err:   0.004985
[2025-10-06 23:49:32] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -29.600414 | E_var:     0.0560 | E_err:   0.003697
[2025-10-06 23:49:36] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -29.594347 | E_var:     0.0771 | E_err:   0.004337
[2025-10-06 23:49:40] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -29.595984 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 23:49:43] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -29.601975 | E_var:     0.0643 | E_err:   0.003962
[2025-10-06 23:49:47] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -29.604255 | E_var:     0.0614 | E_err:   0.003871
[2025-10-06 23:49:51] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -29.595345 | E_var:     0.0582 | E_err:   0.003770
[2025-10-06 23:49:54] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -29.596204 | E_var:     0.0999 | E_err:   0.004940
[2025-10-06 23:49:58] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -29.594498 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 23:50:02] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -29.593055 | E_var:     0.0718 | E_err:   0.004187
[2025-10-06 23:50:05] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -29.601500 | E_var:     0.0701 | E_err:   0.004138
[2025-10-06 23:50:09] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -29.607381 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 23:50:13] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -29.595288 | E_var:     0.0666 | E_err:   0.004034
[2025-10-06 23:50:17] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -29.598708 | E_var:     0.0692 | E_err:   0.004112
[2025-10-06 23:50:20] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -29.601493 | E_var:     0.0559 | E_err:   0.003693
[2025-10-06 23:50:24] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -29.607755 | E_var:     0.0658 | E_err:   0.004009
[2025-10-06 23:50:28] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -29.603323 | E_var:     0.0669 | E_err:   0.004042
[2025-10-06 23:50:31] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -29.598839 | E_var:     0.0659 | E_err:   0.004011
[2025-10-06 23:50:35] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -29.597388 | E_var:     0.0653 | E_err:   0.003993
[2025-10-06 23:50:39] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -29.598445 | E_var:     0.0654 | E_err:   0.003997
[2025-10-06 23:50:42] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -29.599218 | E_var:     0.0641 | E_err:   0.003955
[2025-10-06 23:50:46] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -29.604761 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 23:50:50] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -29.597150 | E_var:     0.0595 | E_err:   0.003812
[2025-10-06 23:50:53] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -29.591010 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 23:50:57] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -29.602967 | E_var:     0.0662 | E_err:   0.004021
[2025-10-06 23:51:01] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -29.602617 | E_var:     0.0751 | E_err:   0.004283
[2025-10-06 23:51:04] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -29.603076 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 23:51:08] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -29.594231 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 23:51:12] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -29.609932 | E_var:     0.0647 | E_err:   0.003973
[2025-10-06 23:51:15] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -29.599463 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 23:51:19] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -29.597997 | E_var:     0.0631 | E_err:   0.003924
[2025-10-06 23:51:23] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -29.595891 | E_var:     0.1217 | E_err:   0.005451
[2025-10-06 23:51:26] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -29.601650 | E_var:     0.0548 | E_err:   0.003658
[2025-10-06 23:51:30] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -29.598947 | E_var:     0.0665 | E_err:   0.004029
[2025-10-06 23:51:34] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -29.593124 | E_var:     0.0621 | E_err:   0.003893
[2025-10-06 23:51:37] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -29.595043 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 23:51:41] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -29.606373 | E_var:     0.0627 | E_err:   0.003912
[2025-10-06 23:51:45] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -29.602015 | E_var:     0.0648 | E_err:   0.003976
[2025-10-06 23:51:48] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -29.598291 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 23:51:52] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -29.597029 | E_var:     0.0632 | E_err:   0.003927
[2025-10-06 23:51:56] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -29.593150 | E_var:     0.1770 | E_err:   0.006574
[2025-10-06 23:51:59] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -29.595216 | E_var:     0.0723 | E_err:   0.004200
[2025-10-06 23:52:03] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -29.597801 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 23:52:07] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -29.603377 | E_var:     0.0832 | E_err:   0.004506
[2025-10-06 23:52:10] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -29.598110 | E_var:     0.0710 | E_err:   0.004163
[2025-10-06 23:52:14] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -29.603396 | E_var:     0.0638 | E_err:   0.003947
[2025-10-06 23:52:18] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -29.599960 | E_var:     0.0641 | E_err:   0.003956
[2025-10-06 23:52:22] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -29.600749 | E_var:     0.0611 | E_err:   0.003862
[2025-10-06 23:52:25] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -29.601646 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 23:52:29] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -29.600824 | E_var:     0.0648 | E_err:   0.003979
[2025-10-06 23:52:33] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -29.596335 | E_var:     0.0957 | E_err:   0.004834
[2025-10-06 23:52:36] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -29.595646 | E_var:     0.0631 | E_err:   0.003925
[2025-10-06 23:52:40] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -29.599405 | E_var:     0.0958 | E_err:   0.004835
[2025-10-06 23:52:44] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -29.596209 | E_var:     0.0596 | E_err:   0.003814
[2025-10-06 23:52:44] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 23:52:47] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -29.594988 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 23:52:51] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -29.594119 | E_var:     0.0611 | E_err:   0.003862
[2025-10-06 23:52:55] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -29.597659 | E_var:     0.0959 | E_err:   0.004839
[2025-10-06 23:52:58] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -29.601084 | E_var:     0.0620 | E_err:   0.003892
[2025-10-06 23:53:02] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -29.594707 | E_var:     0.0622 | E_err:   0.003896
[2025-10-06 23:53:06] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -29.597837 | E_var:     0.0805 | E_err:   0.004433
[2025-10-06 23:53:09] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -29.595626 | E_var:     0.0731 | E_err:   0.004225
[2025-10-06 23:53:13] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -29.598672 | E_var:     0.0659 | E_err:   0.004011
[2025-10-06 23:53:17] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -29.601500 | E_var:     0.0710 | E_err:   0.004162
[2025-10-06 23:53:20] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -29.598377 | E_var:     0.0964 | E_err:   0.004852
[2025-10-06 23:53:24] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -29.600369 | E_var:     0.0734 | E_err:   0.004232
[2025-10-06 23:53:28] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -29.594147 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 23:53:31] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -29.603748 | E_var:     0.0725 | E_err:   0.004209
[2025-10-06 23:53:35] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -29.601050 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 23:53:39] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -29.603658 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 23:53:42] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -29.599064 | E_var:     0.0772 | E_err:   0.004340
[2025-10-06 23:53:46] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -29.595446 | E_var:     0.0760 | E_err:   0.004306
[2025-10-06 23:53:50] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -29.591630 | E_var:     0.0636 | E_err:   0.003939
[2025-10-06 23:53:55] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -29.605568 | E_var:     0.1417 | E_err:   0.005881
[2025-10-06 23:53:59] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -29.596510 | E_var:     0.0761 | E_err:   0.004310
[2025-10-06 23:54:03] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -29.601741 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 23:54:06] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -29.603259 | E_var:     0.0653 | E_err:   0.003993
[2025-10-06 23:54:10] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -29.594820 | E_var:     0.0865 | E_err:   0.004594
[2025-10-06 23:54:14] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -29.603008 | E_var:     0.0682 | E_err:   0.004081
[2025-10-06 23:54:17] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -29.601281 | E_var:     0.0686 | E_err:   0.004092
[2025-10-06 23:54:21] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -29.592841 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 23:54:25] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -29.599416 | E_var:     0.0586 | E_err:   0.003783
[2025-10-06 23:54:28] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -29.598299 | E_var:     0.0756 | E_err:   0.004298
[2025-10-06 23:54:32] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -29.592665 | E_var:     0.0667 | E_err:   0.004034
[2025-10-06 23:54:36] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -29.599112 | E_var:     0.0883 | E_err:   0.004642
[2025-10-06 23:54:39] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -29.601275 | E_var:     0.1085 | E_err:   0.005146
[2025-10-06 23:54:43] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -29.592896 | E_var:     0.0709 | E_err:   0.004161
[2025-10-06 23:54:47] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -29.604690 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 23:54:50] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -29.601591 | E_var:     0.0945 | E_err:   0.004804
[2025-10-06 23:54:54] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -29.592030 | E_var:     0.1153 | E_err:   0.005306
[2025-10-06 23:54:58] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -29.596299 | E_var:     0.0660 | E_err:   0.004013
[2025-10-06 23:55:01] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -29.599169 | E_var:     0.0675 | E_err:   0.004060
[2025-10-06 23:55:05] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -29.589791 | E_var:     0.0597 | E_err:   0.003817
[2025-10-06 23:55:09] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -29.599888 | E_var:     0.0680 | E_err:   0.004074
[2025-10-06 23:55:12] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -29.594495 | E_var:     0.0677 | E_err:   0.004066
[2025-10-06 23:55:16] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -29.610511 | E_var:     0.1455 | E_err:   0.005960
[2025-10-06 23:55:20] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -29.602826 | E_var:     0.0614 | E_err:   0.003872
[2025-10-06 23:55:23] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -29.594743 | E_var:     0.0725 | E_err:   0.004208
[2025-10-06 23:55:27] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -29.610099 | E_var:     0.3347 | E_err:   0.009039
[2025-10-06 23:55:31] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -29.606550 | E_var:     0.0615 | E_err:   0.003875
[2025-10-06 23:55:34] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -29.596742 | E_var:     0.2170 | E_err:   0.007278
[2025-10-06 23:55:38] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -29.593582 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 23:55:42] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -29.594477 | E_var:     0.0667 | E_err:   0.004034
[2025-10-06 23:55:45] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -29.598794 | E_var:     0.0624 | E_err:   0.003903
[2025-10-06 23:55:49] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -29.607009 | E_var:     0.1054 | E_err:   0.005072
[2025-10-06 23:55:49] 🔄 RESTART #1 | Period: 300
[2025-10-06 23:55:53] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -29.602010 | E_var:     0.0594 | E_err:   0.003808
[2025-10-06 23:55:56] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -29.596902 | E_var:     0.1734 | E_err:   0.006507
[2025-10-06 23:56:00] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -29.601633 | E_var:     0.0658 | E_err:   0.004008
[2025-10-06 23:56:04] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -29.599655 | E_var:     0.2000 | E_err:   0.006987
[2025-10-06 23:56:07] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -29.601095 | E_var:     0.0777 | E_err:   0.004355
[2025-10-06 23:56:11] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -29.603153 | E_var:     0.0616 | E_err:   0.003878
[2025-10-06 23:56:15] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -29.598943 | E_var:     0.0633 | E_err:   0.003930
[2025-10-06 23:56:19] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -29.597602 | E_var:     0.0787 | E_err:   0.004384
[2025-10-06 23:56:23] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -29.598332 | E_var:     0.0636 | E_err:   0.003940
[2025-10-06 23:56:27] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -29.588289 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 23:56:30] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -29.593330 | E_var:     0.0715 | E_err:   0.004177
[2025-10-06 23:56:34] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -29.592701 | E_var:     0.0942 | E_err:   0.004795
[2025-10-06 23:56:38] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -29.588171 | E_var:     0.0638 | E_err:   0.003947
[2025-10-06 23:56:41] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -29.601151 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 23:56:45] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -29.605776 | E_var:     0.0959 | E_err:   0.004838
[2025-10-06 23:56:49] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -29.601323 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 23:56:52] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -29.597158 | E_var:     0.0606 | E_err:   0.003847
[2025-10-06 23:56:56] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -29.588330 | E_var:     0.0760 | E_err:   0.004308
[2025-10-06 23:57:00] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -29.601065 | E_var:     0.1166 | E_err:   0.005336
[2025-10-06 23:57:03] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -29.597886 | E_var:     0.0667 | E_err:   0.004037
[2025-10-06 23:57:07] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -29.601197 | E_var:     0.0716 | E_err:   0.004182
[2025-10-06 23:57:11] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -29.603991 | E_var:     0.0751 | E_err:   0.004281
[2025-10-06 23:57:14] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -29.597191 | E_var:     0.0619 | E_err:   0.003889
[2025-10-06 23:57:18] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -29.602555 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 23:57:22] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -29.602836 | E_var:     0.0886 | E_err:   0.004651
[2025-10-06 23:57:25] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -29.596173 | E_var:     0.1048 | E_err:   0.005058
[2025-10-06 23:57:29] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -29.604917 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 23:57:33] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -29.601611 | E_var:     0.0643 | E_err:   0.003963
[2025-10-06 23:57:36] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -29.593775 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 23:57:40] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -29.597050 | E_var:     0.0842 | E_err:   0.004534
[2025-10-06 23:57:44] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -29.607903 | E_var:     0.0952 | E_err:   0.004822
[2025-10-06 23:57:48] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -29.602333 | E_var:     0.0652 | E_err:   0.003989
[2025-10-06 23:57:51] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -29.593197 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 23:57:55] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -29.597917 | E_var:     0.1220 | E_err:   0.005457
[2025-10-06 23:57:59] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -29.591006 | E_var:     0.0787 | E_err:   0.004383
[2025-10-06 23:58:02] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -29.602732 | E_var:     0.1066 | E_err:   0.005100
[2025-10-06 23:58:06] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -29.594015 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 23:58:10] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -29.599406 | E_var:     0.0820 | E_err:   0.004474
[2025-10-06 23:58:13] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -29.600247 | E_var:     0.0669 | E_err:   0.004043
[2025-10-06 23:58:17] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -29.599170 | E_var:     0.0711 | E_err:   0.004165
[2025-10-06 23:58:21] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -29.603843 | E_var:     0.0667 | E_err:   0.004036
[2025-10-06 23:58:24] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -29.597153 | E_var:     0.0846 | E_err:   0.004546
[2025-10-06 23:58:28] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -29.598742 | E_var:     0.0704 | E_err:   0.004147
[2025-10-06 23:58:31] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -29.597346 | E_var:     0.0733 | E_err:   0.004229
[2025-10-06 23:58:35] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -29.596040 | E_var:     0.0610 | E_err:   0.003858
[2025-10-06 23:58:39] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -29.596608 | E_var:     0.0656 | E_err:   0.004002
[2025-10-06 23:58:42] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -29.590183 | E_var:     0.2255 | E_err:   0.007419
[2025-10-06 23:58:46] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -29.593485 | E_var:     0.1803 | E_err:   0.006635
[2025-10-06 23:58:50] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -29.596112 | E_var:     0.0703 | E_err:   0.004143
[2025-10-06 23:58:53] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -29.596290 | E_var:     0.0649 | E_err:   0.003980
[2025-10-06 23:58:54] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 23:58:57] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -29.601830 | E_var:     0.0802 | E_err:   0.004424
[2025-10-06 23:59:01] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -29.594703 | E_var:     0.0693 | E_err:   0.004114
[2025-10-06 23:59:05] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -29.600331 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 23:59:08] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -29.598988 | E_var:     0.0854 | E_err:   0.004565
[2025-10-06 23:59:12] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -29.602488 | E_var:     0.0578 | E_err:   0.003758
[2025-10-06 23:59:16] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -29.590905 | E_var:     0.1839 | E_err:   0.006700
[2025-10-06 23:59:19] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -29.597764 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 23:59:23] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -29.601700 | E_var:     0.0654 | E_err:   0.003997
[2025-10-06 23:59:27] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -29.602141 | E_var:     0.0564 | E_err:   0.003711
[2025-10-06 23:59:30] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -29.595065 | E_var:     0.1215 | E_err:   0.005445
[2025-10-06 23:59:34] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -29.606266 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 23:59:38] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -29.605337 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 23:59:41] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -29.594897 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 23:59:45] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -29.601603 | E_var:     0.0721 | E_err:   0.004195
[2025-10-06 23:59:49] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -29.597238 | E_var:     0.0658 | E_err:   0.004009
[2025-10-06 23:59:53] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -29.599102 | E_var:     0.0679 | E_err:   0.004072
[2025-10-06 23:59:56] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -29.596832 | E_var:     0.0791 | E_err:   0.004395
[2025-10-07 00:00:00] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -29.593046 | E_var:     0.0563 | E_err:   0.003708
[2025-10-07 00:00:04] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -29.598411 | E_var:     0.1107 | E_err:   0.005198
[2025-10-07 00:00:07] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -29.597619 | E_var:     0.0621 | E_err:   0.003894
[2025-10-07 00:00:11] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -29.595775 | E_var:     0.0693 | E_err:   0.004113
[2025-10-07 00:00:15] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -29.588552 | E_var:     0.0703 | E_err:   0.004142
[2025-10-07 00:00:18] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -29.605673 | E_var:     0.0609 | E_err:   0.003855
[2025-10-07 00:00:22] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -29.598402 | E_var:     0.0778 | E_err:   0.004357
[2025-10-07 00:00:26] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -29.604512 | E_var:     0.1471 | E_err:   0.005993
[2025-10-07 00:00:29] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -29.596273 | E_var:     0.0877 | E_err:   0.004628
[2025-10-07 00:00:33] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -29.603161 | E_var:     0.0660 | E_err:   0.004013
[2025-10-07 00:00:37] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -29.604522 | E_var:     0.0883 | E_err:   0.004644
[2025-10-07 00:00:40] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -29.604075 | E_var:     0.0770 | E_err:   0.004336
[2025-10-07 00:00:44] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -29.599803 | E_var:     0.0791 | E_err:   0.004395
[2025-10-07 00:00:48] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -29.594805 | E_var:     0.0671 | E_err:   0.004047
[2025-10-07 00:00:51] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -29.596265 | E_var:     0.0689 | E_err:   0.004102
[2025-10-07 00:00:55] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -29.598156 | E_var:     0.0726 | E_err:   0.004209
[2025-10-07 00:00:59] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -29.598990 | E_var:     0.0784 | E_err:   0.004374
[2025-10-07 00:01:02] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -29.595052 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 00:01:06] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -29.598046 | E_var:     0.0638 | E_err:   0.003946
[2025-10-07 00:01:10] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -29.606470 | E_var:     0.0615 | E_err:   0.003876
[2025-10-07 00:01:13] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -29.605379 | E_var:     0.0730 | E_err:   0.004221
[2025-10-07 00:01:17] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -29.602740 | E_var:     0.0860 | E_err:   0.004582
[2025-10-07 00:01:21] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -29.600693 | E_var:     0.0819 | E_err:   0.004472
[2025-10-07 00:01:24] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -29.602039 | E_var:     0.2391 | E_err:   0.007641
[2025-10-07 00:01:28] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -29.600108 | E_var:     0.0569 | E_err:   0.003727
[2025-10-07 00:01:32] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -29.589671 | E_var:     0.0998 | E_err:   0.004937
[2025-10-07 00:01:35] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -29.601716 | E_var:     0.1103 | E_err:   0.005188
[2025-10-07 00:01:39] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -29.594523 | E_var:     0.0658 | E_err:   0.004008
[2025-10-07 00:01:43] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -29.602375 | E_var:     0.0630 | E_err:   0.003921
[2025-10-07 00:01:46] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -29.599758 | E_var:     0.0790 | E_err:   0.004391
[2025-10-07 00:01:50] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -29.602776 | E_var:     0.0762 | E_err:   0.004314
[2025-10-07 00:01:54] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -29.597962 | E_var:     0.0719 | E_err:   0.004190
[2025-10-07 00:01:57] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -29.605270 | E_var:     0.1000 | E_err:   0.004942
[2025-10-07 00:02:01] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -29.600256 | E_var:     0.0522 | E_err:   0.003571
[2025-10-07 00:02:05] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -29.599306 | E_var:     0.0676 | E_err:   0.004062
[2025-10-07 00:02:08] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -29.598692 | E_var:     0.1022 | E_err:   0.004996
[2025-10-07 00:02:12] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -29.605899 | E_var:     0.0738 | E_err:   0.004244
[2025-10-07 00:02:16] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -29.601854 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 00:02:20] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -29.595512 | E_var:     0.0752 | E_err:   0.004285
[2025-10-07 00:02:23] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -29.597609 | E_var:     0.1060 | E_err:   0.005087
[2025-10-07 00:02:28] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -29.590023 | E_var:     0.2459 | E_err:   0.007748
[2025-10-07 00:02:32] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -29.603404 | E_var:     0.0960 | E_err:   0.004842
[2025-10-07 00:02:35] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -29.593383 | E_var:     0.1287 | E_err:   0.005606
[2025-10-07 00:02:39] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -29.598222 | E_var:     0.0570 | E_err:   0.003732
[2025-10-07 00:02:43] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -29.607398 | E_var:     0.0610 | E_err:   0.003858
[2025-10-07 00:02:46] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -29.600078 | E_var:     0.1307 | E_err:   0.005648
[2025-10-07 00:02:50] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -29.603941 | E_var:     0.0628 | E_err:   0.003915
[2025-10-07 00:02:54] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -29.598110 | E_var:     0.1338 | E_err:   0.005716
[2025-10-07 00:02:57] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -29.595874 | E_var:     0.0607 | E_err:   0.003850
[2025-10-07 00:03:01] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -29.599136 | E_var:     0.0776 | E_err:   0.004354
[2025-10-07 00:03:05] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -29.599572 | E_var:     0.0688 | E_err:   0.004099
[2025-10-07 00:03:08] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -29.604429 | E_var:     0.0880 | E_err:   0.004636
[2025-10-07 00:03:12] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -29.599861 | E_var:     0.0614 | E_err:   0.003871
[2025-10-07 00:03:16] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -29.603082 | E_var:     0.0762 | E_err:   0.004312
[2025-10-07 00:03:19] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -29.595319 | E_var:     0.0662 | E_err:   0.004019
[2025-10-07 00:03:23] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -29.594024 | E_var:     0.0875 | E_err:   0.004623
[2025-10-07 00:03:27] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -29.598688 | E_var:     0.0705 | E_err:   0.004149
[2025-10-07 00:03:30] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -29.602635 | E_var:     0.0633 | E_err:   0.003932
[2025-10-07 00:03:34] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -29.605632 | E_var:     0.0672 | E_err:   0.004049
[2025-10-07 00:03:38] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -29.597093 | E_var:     0.0802 | E_err:   0.004426
[2025-10-07 00:03:41] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -29.601032 | E_var:     0.0715 | E_err:   0.004179
[2025-10-07 00:03:45] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -29.608655 | E_var:     0.0806 | E_err:   0.004435
[2025-10-07 00:03:49] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -29.597028 | E_var:     0.0593 | E_err:   0.003804
[2025-10-07 00:03:52] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -29.597128 | E_var:     0.0890 | E_err:   0.004661
[2025-10-07 00:03:56] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -29.600598 | E_var:     0.0777 | E_err:   0.004355
[2025-10-07 00:04:00] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -29.591856 | E_var:     0.0665 | E_err:   0.004028
[2025-10-07 00:04:03] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -29.592683 | E_var:     0.0767 | E_err:   0.004329
[2025-10-07 00:04:07] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -29.600778 | E_var:     0.0624 | E_err:   0.003904
[2025-10-07 00:04:11] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -29.590136 | E_var:     0.0763 | E_err:   0.004317
[2025-10-07 00:04:14] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -29.602547 | E_var:     0.0516 | E_err:   0.003548
[2025-10-07 00:04:18] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -29.599056 | E_var:     0.1015 | E_err:   0.004977
[2025-10-07 00:04:22] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -29.601250 | E_var:     0.0605 | E_err:   0.003844
[2025-10-07 00:04:25] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -29.604233 | E_var:     0.0932 | E_err:   0.004770
[2025-10-07 00:04:29] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -29.604919 | E_var:     0.0716 | E_err:   0.004181
[2025-10-07 00:04:33] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -29.592692 | E_var:     0.0543 | E_err:   0.003643
[2025-10-07 00:04:36] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -29.599224 | E_var:     0.0693 | E_err:   0.004113
[2025-10-07 00:04:40] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -29.607768 | E_var:     0.0901 | E_err:   0.004691
[2025-10-07 00:04:44] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -29.605982 | E_var:     0.0495 | E_err:   0.003478
[2025-10-07 00:04:47] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -29.598798 | E_var:     0.0868 | E_err:   0.004603
[2025-10-07 00:04:51] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -29.603549 | E_var:     0.1162 | E_err:   0.005327
[2025-10-07 00:04:55] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -29.598333 | E_var:     0.0717 | E_err:   0.004183
[2025-10-07 00:04:58] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -29.598459 | E_var:     0.0908 | E_err:   0.004708
[2025-10-07 00:05:02] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -29.595686 | E_var:     0.0810 | E_err:   0.004446
[2025-10-07 00:05:02] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 00:05:06] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -29.594467 | E_var:     0.0647 | E_err:   0.003974
[2025-10-07 00:05:09] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -29.599534 | E_var:     0.0699 | E_err:   0.004131
[2025-10-07 00:05:13] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -29.596330 | E_var:     0.0940 | E_err:   0.004789
[2025-10-07 00:05:17] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -29.593190 | E_var:     0.0765 | E_err:   0.004321
[2025-10-07 00:05:20] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -29.594440 | E_var:     0.0687 | E_err:   0.004097
[2025-10-07 00:05:24] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -29.601648 | E_var:     0.0612 | E_err:   0.003866
[2025-10-07 00:05:28] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -29.607007 | E_var:     0.1196 | E_err:   0.005404
[2025-10-07 00:05:31] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -29.600381 | E_var:     0.0650 | E_err:   0.003983
[2025-10-07 00:05:35] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -29.596676 | E_var:     0.1316 | E_err:   0.005668
[2025-10-07 00:05:39] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -29.596851 | E_var:     0.0850 | E_err:   0.004556
[2025-10-07 00:05:42] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -29.597583 | E_var:     0.0854 | E_err:   0.004567
[2025-10-07 00:05:46] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -29.600876 | E_var:     0.0727 | E_err:   0.004212
[2025-10-07 00:05:50] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -29.605891 | E_var:     0.0655 | E_err:   0.004000
[2025-10-07 00:05:53] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -29.604982 | E_var:     0.0735 | E_err:   0.004235
[2025-10-07 00:05:57] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -29.601108 | E_var:     0.0889 | E_err:   0.004659
[2025-10-07 00:06:01] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -29.596036 | E_var:     0.0681 | E_err:   0.004079
[2025-10-07 00:06:04] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -29.592339 | E_var:     0.0645 | E_err:   0.003969
[2025-10-07 00:06:08] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -29.605761 | E_var:     0.0733 | E_err:   0.004230
[2025-10-07 00:06:12] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -29.603752 | E_var:     0.0816 | E_err:   0.004463
[2025-10-07 00:06:15] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -29.596408 | E_var:     0.0680 | E_err:   0.004076
[2025-10-07 00:06:19] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -29.596914 | E_var:     0.1081 | E_err:   0.005137
[2025-10-07 00:06:23] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -29.601461 | E_var:     0.0769 | E_err:   0.004333
[2025-10-07 00:06:26] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -29.601854 | E_var:     0.0725 | E_err:   0.004207
[2025-10-07 00:06:30] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -29.597664 | E_var:     0.0632 | E_err:   0.003928
[2025-10-07 00:06:34] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -29.597112 | E_var:     0.0835 | E_err:   0.004514
[2025-10-07 00:06:38] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -29.596989 | E_var:     0.0768 | E_err:   0.004330
[2025-10-07 00:06:42] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -29.596517 | E_var:     0.0550 | E_err:   0.003663
[2025-10-07 00:06:45] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -29.599001 | E_var:     0.0814 | E_err:   0.004459
[2025-10-07 00:06:49] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -29.604331 | E_var:     0.0719 | E_err:   0.004189
[2025-10-07 00:06:53] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -29.600782 | E_var:     0.0696 | E_err:   0.004123
[2025-10-07 00:06:56] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -29.601228 | E_var:     0.0728 | E_err:   0.004216
[2025-10-07 00:07:00] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -29.598981 | E_var:     0.0724 | E_err:   0.004203
[2025-10-07 00:07:04] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -29.596154 | E_var:     0.0637 | E_err:   0.003943
[2025-10-07 00:07:07] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -29.598181 | E_var:     0.0641 | E_err:   0.003956
[2025-10-07 00:07:11] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -29.602806 | E_var:     0.0757 | E_err:   0.004300
[2025-10-07 00:07:15] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -29.602787 | E_var:     0.0966 | E_err:   0.004857
[2025-10-07 00:07:18] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -29.598442 | E_var:     0.0850 | E_err:   0.004555
[2025-10-07 00:07:22] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -29.595966 | E_var:     0.0832 | E_err:   0.004506
[2025-10-07 00:07:26] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -29.613774 | E_var:     0.0596 | E_err:   0.003814
[2025-10-07 00:07:29] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -29.597986 | E_var:     0.0617 | E_err:   0.003882
[2025-10-07 00:07:33] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -29.610491 | E_var:     0.1092 | E_err:   0.005164
[2025-10-07 00:07:37] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -29.603179 | E_var:     0.0804 | E_err:   0.004429
[2025-10-07 00:07:40] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -29.611912 | E_var:     0.1265 | E_err:   0.005556
[2025-10-07 00:07:44] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -29.598465 | E_var:     0.0744 | E_err:   0.004262
[2025-10-07 00:07:48] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -29.601216 | E_var:     0.0575 | E_err:   0.003747
[2025-10-07 00:07:51] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -29.601024 | E_var:     0.1415 | E_err:   0.005878
[2025-10-07 00:07:55] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -29.592055 | E_var:     0.0604 | E_err:   0.003840
[2025-10-07 00:07:59] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -29.600291 | E_var:     0.0924 | E_err:   0.004749
[2025-10-07 00:08:02] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -29.596263 | E_var:     0.0990 | E_err:   0.004916
[2025-10-07 00:08:06] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -29.609127 | E_var:     0.0637 | E_err:   0.003945
[2025-10-07 00:08:10] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -29.598528 | E_var:     0.0603 | E_err:   0.003838
[2025-10-07 00:08:13] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -29.598505 | E_var:     0.0622 | E_err:   0.003898
[2025-10-07 00:08:17] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -29.602976 | E_var:     0.0695 | E_err:   0.004119
[2025-10-07 00:08:21] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -29.610997 | E_var:     0.1020 | E_err:   0.004991
[2025-10-07 00:08:24] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -29.600583 | E_var:     0.0681 | E_err:   0.004078
[2025-10-07 00:08:28] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -29.601005 | E_var:     0.0729 | E_err:   0.004218
[2025-10-07 00:08:32] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -29.607978 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 00:08:35] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -29.590585 | E_var:     0.0580 | E_err:   0.003763
[2025-10-07 00:08:39] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -29.603393 | E_var:     0.0601 | E_err:   0.003832
[2025-10-07 00:08:43] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -29.607180 | E_var:     0.0636 | E_err:   0.003940
[2025-10-07 00:08:46] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -29.598132 | E_var:     0.0671 | E_err:   0.004047
[2025-10-07 00:08:50] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -29.606062 | E_var:     0.0501 | E_err:   0.003496
[2025-10-07 00:08:54] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -29.596704 | E_var:     0.0595 | E_err:   0.003812
[2025-10-07 00:08:57] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -29.599736 | E_var:     0.0587 | E_err:   0.003786
[2025-10-07 00:09:01] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -29.597177 | E_var:     0.0898 | E_err:   0.004681
[2025-10-07 00:09:05] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -29.596117 | E_var:     0.0838 | E_err:   0.004523
[2025-10-07 00:09:08] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -29.602659 | E_var:     0.0705 | E_err:   0.004150
[2025-10-07 00:09:12] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -29.604401 | E_var:     0.0747 | E_err:   0.004271
[2025-10-07 00:09:16] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -29.598346 | E_var:     0.0710 | E_err:   0.004164
[2025-10-07 00:09:19] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -29.590742 | E_var:     0.0727 | E_err:   0.004213
[2025-10-07 00:09:23] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -29.595699 | E_var:     0.0791 | E_err:   0.004395
[2025-10-07 00:09:27] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -29.594147 | E_var:     0.0811 | E_err:   0.004449
[2025-10-07 00:09:31] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -29.596464 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 00:09:34] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -29.606676 | E_var:     0.0621 | E_err:   0.003895
[2025-10-07 00:09:38] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -29.596873 | E_var:     0.0590 | E_err:   0.003797
[2025-10-07 00:09:42] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -29.598867 | E_var:     0.0734 | E_err:   0.004232
[2025-10-07 00:09:45] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -29.600507 | E_var:     0.0636 | E_err:   0.003941
[2025-10-07 00:09:49] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -29.599507 | E_var:     0.0839 | E_err:   0.004525
[2025-10-07 00:09:53] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -29.591968 | E_var:     0.0712 | E_err:   0.004169
[2025-10-07 00:09:56] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -29.592000 | E_var:     0.0837 | E_err:   0.004520
[2025-10-07 00:10:00] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -29.607737 | E_var:     0.0634 | E_err:   0.003935
[2025-10-07 00:10:04] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -29.607956 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 00:10:07] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -29.601905 | E_var:     0.0723 | E_err:   0.004202
[2025-10-07 00:10:11] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -29.596504 | E_var:     0.0671 | E_err:   0.004046
[2025-10-07 00:10:15] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -29.595304 | E_var:     0.0880 | E_err:   0.004636
[2025-10-07 00:10:18] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -29.600743 | E_var:     0.0625 | E_err:   0.003905
[2025-10-07 00:10:22] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -29.593490 | E_var:     0.0635 | E_err:   0.003937
[2025-10-07 00:10:26] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -29.598566 | E_var:     0.0805 | E_err:   0.004434
[2025-10-07 00:10:30] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -29.596545 | E_var:     0.0753 | E_err:   0.004287
[2025-10-07 00:10:34] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -29.597824 | E_var:     0.0752 | E_err:   0.004284
[2025-10-07 00:10:37] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -29.598120 | E_var:     0.0742 | E_err:   0.004255
[2025-10-07 00:10:41] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -29.593905 | E_var:     0.0726 | E_err:   0.004210
[2025-10-07 00:10:45] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -29.603309 | E_var:     0.1472 | E_err:   0.005995
[2025-10-07 00:10:48] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -29.602245 | E_var:     0.1123 | E_err:   0.005236
[2025-10-07 00:10:52] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -29.598727 | E_var:     0.0693 | E_err:   0.004114
[2025-10-07 00:10:56] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -29.596566 | E_var:     0.0680 | E_err:   0.004074
[2025-10-07 00:10:59] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -29.594045 | E_var:     0.0888 | E_err:   0.004655
[2025-10-07 00:11:03] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -29.596864 | E_var:     0.0660 | E_err:   0.004013
[2025-10-07 00:11:07] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -29.596080 | E_var:     0.0692 | E_err:   0.004110
[2025-10-07 00:11:10] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -29.595769 | E_var:     0.0717 | E_err:   0.004184
[2025-10-07 00:11:10] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 00:11:14] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -29.605442 | E_var:     0.0757 | E_err:   0.004298
[2025-10-07 00:11:18] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -29.596467 | E_var:     0.0812 | E_err:   0.004453
[2025-10-07 00:11:21] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -29.599175 | E_var:     0.0853 | E_err:   0.004564
[2025-10-07 00:11:25] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -29.601198 | E_var:     0.0708 | E_err:   0.004156
[2025-10-07 00:11:29] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -29.599758 | E_var:     0.0687 | E_err:   0.004095
[2025-10-07 00:11:32] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -29.600457 | E_var:     0.1004 | E_err:   0.004952
[2025-10-07 00:11:36] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -29.600818 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 00:11:40] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -29.606265 | E_var:     0.0891 | E_err:   0.004664
[2025-10-07 00:11:43] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -29.597891 | E_var:     0.0717 | E_err:   0.004184
[2025-10-07 00:11:47] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -29.600572 | E_var:     0.0617 | E_err:   0.003882
[2025-10-07 00:11:51] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -29.604340 | E_var:     0.0715 | E_err:   0.004179
[2025-10-07 00:11:54] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -29.598644 | E_var:     0.0905 | E_err:   0.004700
[2025-10-07 00:11:58] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -29.595718 | E_var:     0.0847 | E_err:   0.004546
[2025-10-07 00:12:02] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -29.600945 | E_var:     0.0567 | E_err:   0.003720
[2025-10-07 00:12:05] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -29.590543 | E_var:     0.0680 | E_err:   0.004075
[2025-10-07 00:12:09] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -29.595722 | E_var:     0.0799 | E_err:   0.004417
[2025-10-07 00:12:13] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -29.595618 | E_var:     0.0743 | E_err:   0.004259
[2025-10-07 00:12:16] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -29.600988 | E_var:     0.0794 | E_err:   0.004402
[2025-10-07 00:12:20] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -29.592552 | E_var:     0.0736 | E_err:   0.004240
[2025-10-07 00:12:24] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -29.598989 | E_var:     0.0536 | E_err:   0.003618
[2025-10-07 00:12:28] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -29.599485 | E_var:     0.0777 | E_err:   0.004356
[2025-10-07 00:12:31] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -29.599376 | E_var:     0.0741 | E_err:   0.004254
[2025-10-07 00:12:35] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -29.600835 | E_var:     0.0785 | E_err:   0.004377
[2025-10-07 00:12:39] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -29.597645 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 00:12:42] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -29.601514 | E_var:     0.0703 | E_err:   0.004144
[2025-10-07 00:12:46] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -29.593609 | E_var:     0.0857 | E_err:   0.004575
[2025-10-07 00:12:50] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -29.598587 | E_var:     0.0604 | E_err:   0.003841
[2025-10-07 00:12:53] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -29.600624 | E_var:     0.0745 | E_err:   0.004266
[2025-10-07 00:12:57] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -29.600561 | E_var:     0.0702 | E_err:   0.004140
[2025-10-07 00:13:01] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -29.596541 | E_var:     0.0542 | E_err:   0.003637
[2025-10-07 00:13:04] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -29.595340 | E_var:     0.1015 | E_err:   0.004977
[2025-10-07 00:13:08] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -29.598221 | E_var:     0.0727 | E_err:   0.004213
[2025-10-07 00:13:12] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -29.600234 | E_var:     0.0670 | E_err:   0.004046
[2025-10-07 00:13:16] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -29.598037 | E_var:     0.0660 | E_err:   0.004013
[2025-10-07 00:13:20] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -29.595299 | E_var:     0.0700 | E_err:   0.004133
[2025-10-07 00:13:23] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -29.600479 | E_var:     0.0955 | E_err:   0.004828
[2025-10-07 00:13:27] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -29.599848 | E_var:     0.0930 | E_err:   0.004764
[2025-10-07 00:13:31] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -29.601022 | E_var:     0.1182 | E_err:   0.005372
[2025-10-07 00:13:34] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -29.596707 | E_var:     0.0798 | E_err:   0.004415
[2025-10-07 00:13:38] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -29.600016 | E_var:     0.0770 | E_err:   0.004335
[2025-10-07 00:13:42] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -29.602834 | E_var:     0.0824 | E_err:   0.004487
[2025-10-07 00:13:45] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -29.595895 | E_var:     0.0788 | E_err:   0.004387
[2025-10-07 00:13:49] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -29.596381 | E_var:     0.0755 | E_err:   0.004293
[2025-10-07 00:13:53] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -29.599321 | E_var:     0.0791 | E_err:   0.004395
[2025-10-07 00:13:56] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -29.596490 | E_var:     0.0723 | E_err:   0.004201
[2025-10-07 00:14:00] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -29.596820 | E_var:     0.0832 | E_err:   0.004507
[2025-10-07 00:14:04] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -29.602988 | E_var:     0.0520 | E_err:   0.003563
[2025-10-07 00:14:07] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -29.604515 | E_var:     0.0600 | E_err:   0.003828
[2025-10-07 00:14:11] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -29.598886 | E_var:     0.0679 | E_err:   0.004071
[2025-10-07 00:14:15] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -29.599263 | E_var:     0.0584 | E_err:   0.003775
[2025-10-07 00:14:15] 🔄 RESTART #2 | Period: 600
[2025-10-07 00:14:18] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -29.600580 | E_var:     0.0684 | E_err:   0.004087
[2025-10-07 00:14:22] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -29.593652 | E_var:     0.0616 | E_err:   0.003879
[2025-10-07 00:14:26] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -29.600527 | E_var:     0.0929 | E_err:   0.004763
[2025-10-07 00:14:29] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -29.609009 | E_var:     0.0657 | E_err:   0.004004
[2025-10-07 00:14:33] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -29.594268 | E_var:     0.0846 | E_err:   0.004545
[2025-10-07 00:14:37] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -29.597583 | E_var:     0.0558 | E_err:   0.003690
[2025-10-07 00:14:40] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -29.599154 | E_var:     0.0770 | E_err:   0.004336
[2025-10-07 00:14:44] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -29.600986 | E_var:     0.0687 | E_err:   0.004096
[2025-10-07 00:14:48] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -29.596699 | E_var:     0.0704 | E_err:   0.004146
[2025-10-07 00:14:51] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -29.600941 | E_var:     0.0841 | E_err:   0.004530
[2025-10-07 00:14:55] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -29.591605 | E_var:     0.0659 | E_err:   0.004010
[2025-10-07 00:14:59] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -29.597245 | E_var:     0.0750 | E_err:   0.004280
[2025-10-07 00:15:02] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -29.595783 | E_var:     0.0882 | E_err:   0.004640
[2025-10-07 00:15:06] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -29.606815 | E_var:     0.0685 | E_err:   0.004090
[2025-10-07 00:15:10] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -29.597584 | E_var:     0.0668 | E_err:   0.004040
[2025-10-07 00:15:13] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -29.598057 | E_var:     0.0591 | E_err:   0.003798
[2025-10-07 00:15:17] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -29.599685 | E_var:     0.0745 | E_err:   0.004266
[2025-10-07 00:15:21] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -29.592123 | E_var:     0.0739 | E_err:   0.004248
[2025-10-07 00:15:24] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -29.605422 | E_var:     0.0597 | E_err:   0.003818
[2025-10-07 00:15:28] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -29.598312 | E_var:     0.0653 | E_err:   0.003993
[2025-10-07 00:15:32] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -29.602234 | E_var:     0.0739 | E_err:   0.004246
[2025-10-07 00:15:35] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -29.595691 | E_var:     0.0594 | E_err:   0.003808
[2025-10-07 00:15:39] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -29.594895 | E_var:     0.0801 | E_err:   0.004422
[2025-10-07 00:15:43] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -29.597848 | E_var:     0.0615 | E_err:   0.003874
[2025-10-07 00:15:46] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -29.594082 | E_var:     0.0790 | E_err:   0.004392
[2025-10-07 00:15:50] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -29.602423 | E_var:     0.0946 | E_err:   0.004807
[2025-10-07 00:15:54] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -29.597746 | E_var:     0.0668 | E_err:   0.004038
[2025-10-07 00:15:58] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -29.599246 | E_var:     0.0675 | E_err:   0.004061
[2025-10-07 00:16:02] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -29.595947 | E_var:     0.0816 | E_err:   0.004463
[2025-10-07 00:16:05] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -29.604500 | E_var:     0.0614 | E_err:   0.003872
[2025-10-07 00:16:09] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -29.600047 | E_var:     0.0834 | E_err:   0.004513
[2025-10-07 00:16:13] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -29.600323 | E_var:     0.0867 | E_err:   0.004601
[2025-10-07 00:16:16] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -29.593175 | E_var:     0.0872 | E_err:   0.004615
[2025-10-07 00:16:20] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -29.600477 | E_var:     0.1128 | E_err:   0.005247
[2025-10-07 00:16:24] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -29.600686 | E_var:     0.0669 | E_err:   0.004042
[2025-10-07 00:16:27] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -29.601504 | E_var:     0.0583 | E_err:   0.003772
[2025-10-07 00:16:31] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -29.613061 | E_var:     0.0772 | E_err:   0.004342
[2025-10-07 00:16:35] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -29.601880 | E_var:     0.0588 | E_err:   0.003788
[2025-10-07 00:16:38] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -29.598516 | E_var:     0.0596 | E_err:   0.003816
[2025-10-07 00:16:42] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -29.597832 | E_var:     0.0702 | E_err:   0.004140
[2025-10-07 00:16:46] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -29.599008 | E_var:     0.0738 | E_err:   0.004245
[2025-10-07 00:16:49] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -29.604265 | E_var:     0.0914 | E_err:   0.004723
[2025-10-07 00:16:53] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -29.602724 | E_var:     0.0615 | E_err:   0.003875
[2025-10-07 00:16:57] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -29.601625 | E_var:     0.1252 | E_err:   0.005528
[2025-10-07 00:17:00] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -29.596608 | E_var:     0.0771 | E_err:   0.004339
[2025-10-07 00:17:04] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -29.607381 | E_var:     0.1267 | E_err:   0.005561
[2025-10-07 00:17:08] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -29.596789 | E_var:     0.0661 | E_err:   0.004017
[2025-10-07 00:17:11] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -29.604225 | E_var:     0.0915 | E_err:   0.004725
[2025-10-07 00:17:15] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -29.606950 | E_var:     0.0875 | E_err:   0.004622
[2025-10-07 00:17:19] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -29.595671 | E_var:     0.0611 | E_err:   0.003862
[2025-10-07 00:17:19] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 00:17:23] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -29.592498 | E_var:     0.0846 | E_err:   0.004543
[2025-10-07 00:17:26] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -29.605287 | E_var:     0.0751 | E_err:   0.004283
[2025-10-07 00:17:30] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -29.601394 | E_var:     0.0611 | E_err:   0.003863
[2025-10-07 00:17:34] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -29.606639 | E_var:     0.0625 | E_err:   0.003908
[2025-10-07 00:17:37] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -29.600150 | E_var:     0.0598 | E_err:   0.003819
[2025-10-07 00:17:41] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -29.598878 | E_var:     0.0701 | E_err:   0.004136
[2025-10-07 00:17:45] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -29.602577 | E_var:     0.0708 | E_err:   0.004156
[2025-10-07 00:17:48] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -29.587688 | E_var:     0.0684 | E_err:   0.004088
[2025-10-07 00:17:52] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -29.595390 | E_var:     0.1413 | E_err:   0.005873
[2025-10-07 00:17:56] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -29.601570 | E_var:     0.0644 | E_err:   0.003964
[2025-10-07 00:17:59] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -29.599206 | E_var:     0.0681 | E_err:   0.004076
[2025-10-07 00:18:05] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -29.597682 | E_var:     0.0806 | E_err:   0.004437
[2025-10-07 00:18:08] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -29.597715 | E_var:     0.0585 | E_err:   0.003780
[2025-10-07 00:18:12] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -29.600863 | E_var:     0.0580 | E_err:   0.003764
[2025-10-07 00:18:16] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -29.604083 | E_var:     0.0762 | E_err:   0.004313
[2025-10-07 00:18:19] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -29.599045 | E_var:     0.0668 | E_err:   0.004038
[2025-10-07 00:18:23] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -29.598625 | E_var:     0.0803 | E_err:   0.004429
[2025-10-07 00:18:27] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -29.604627 | E_var:     0.0896 | E_err:   0.004676
[2025-10-07 00:18:30] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -29.599762 | E_var:     0.0697 | E_err:   0.004126
[2025-10-07 00:18:34] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -29.597661 | E_var:     0.0588 | E_err:   0.003790
[2025-10-07 00:18:38] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -29.601386 | E_var:     0.0583 | E_err:   0.003774
[2025-10-07 00:18:41] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -29.600735 | E_var:     0.0781 | E_err:   0.004367
[2025-10-07 00:18:45] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -29.602325 | E_var:     0.0682 | E_err:   0.004081
[2025-10-07 00:18:49] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -29.594145 | E_var:     0.0901 | E_err:   0.004689
[2025-10-07 00:18:52] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -29.598875 | E_var:     0.0501 | E_err:   0.003499
[2025-10-07 00:18:56] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -29.601392 | E_var:     0.0602 | E_err:   0.003833
[2025-10-07 00:19:00] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -29.593984 | E_var:     0.0745 | E_err:   0.004264
[2025-10-07 00:19:03] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -29.600204 | E_var:     0.0728 | E_err:   0.004215
[2025-10-07 00:19:07] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -29.600942 | E_var:     0.1261 | E_err:   0.005549
[2025-10-07 00:19:11] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -29.599206 | E_var:     0.0573 | E_err:   0.003739
[2025-10-07 00:19:14] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -29.600714 | E_var:     0.0658 | E_err:   0.004009
[2025-10-07 00:19:18] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -29.593581 | E_var:     0.0894 | E_err:   0.004673
[2025-10-07 00:19:22] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -29.599769 | E_var:     0.0733 | E_err:   0.004231
[2025-10-07 00:19:25] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -29.595556 | E_var:     0.0667 | E_err:   0.004035
[2025-10-07 00:19:29] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -29.600304 | E_var:     0.0609 | E_err:   0.003856
[2025-10-07 00:19:33] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -29.597122 | E_var:     0.0778 | E_err:   0.004357
[2025-10-07 00:19:36] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -29.590032 | E_var:     0.0762 | E_err:   0.004312
[2025-10-07 00:19:40] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -29.592860 | E_var:     0.1425 | E_err:   0.005899
[2025-10-07 00:19:44] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -29.595519 | E_var:     0.0539 | E_err:   0.003629
[2025-10-07 00:19:47] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -29.595465 | E_var:     0.0659 | E_err:   0.004012
[2025-10-07 00:19:51] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -29.609280 | E_var:     0.0893 | E_err:   0.004669
[2025-10-07 00:19:55] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -29.600346 | E_var:     0.0778 | E_err:   0.004357
[2025-10-07 00:19:58] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -29.594787 | E_var:     0.0652 | E_err:   0.003990
[2025-10-07 00:20:02] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -29.598664 | E_var:     0.0822 | E_err:   0.004479
[2025-10-07 00:20:06] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -29.597775 | E_var:     0.0660 | E_err:   0.004013
[2025-10-07 00:20:09] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -29.602220 | E_var:     0.0578 | E_err:   0.003757
[2025-10-07 00:20:13] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -29.603570 | E_var:     0.0685 | E_err:   0.004090
[2025-10-07 00:20:17] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -29.597593 | E_var:     0.1086 | E_err:   0.005149
[2025-10-07 00:20:21] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -29.605431 | E_var:     0.0817 | E_err:   0.004467
[2025-10-07 00:20:25] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -29.599091 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 00:20:29] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -29.606153 | E_var:     0.0657 | E_err:   0.004004
[2025-10-07 00:20:33] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -29.605841 | E_var:     0.0658 | E_err:   0.004007
[2025-10-07 00:20:36] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -29.600911 | E_var:     0.0577 | E_err:   0.003754
[2025-10-07 00:20:40] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -29.610911 | E_var:     0.1887 | E_err:   0.006788
[2025-10-07 00:20:44] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -29.600110 | E_var:     0.0523 | E_err:   0.003572
[2025-10-07 00:20:47] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -29.595766 | E_var:     0.0675 | E_err:   0.004060
[2025-10-07 00:20:51] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -29.596280 | E_var:     0.1783 | E_err:   0.006598
[2025-10-07 00:20:55] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -29.597756 | E_var:     0.0815 | E_err:   0.004459
[2025-10-07 00:20:58] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -29.603491 | E_var:     0.0642 | E_err:   0.003958
[2025-10-07 00:21:02] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -29.604237 | E_var:     0.0550 | E_err:   0.003664
[2025-10-07 00:21:06] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -29.601964 | E_var:     0.0788 | E_err:   0.004385
[2025-10-07 00:21:09] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -29.599305 | E_var:     0.0628 | E_err:   0.003915
[2025-10-07 00:21:13] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -29.598867 | E_var:     0.0624 | E_err:   0.003904
[2025-10-07 00:21:17] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -29.600143 | E_var:     0.0501 | E_err:   0.003496
[2025-10-07 00:21:20] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -29.595848 | E_var:     0.0709 | E_err:   0.004160
[2025-10-07 00:21:24] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -29.600700 | E_var:     0.0957 | E_err:   0.004835
[2025-10-07 00:21:28] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -29.598110 | E_var:     0.0630 | E_err:   0.003922
[2025-10-07 00:21:31] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -29.599257 | E_var:     0.0678 | E_err:   0.004067
[2025-10-07 00:21:35] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -29.599503 | E_var:     0.0658 | E_err:   0.004007
[2025-10-07 00:21:39] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -29.592577 | E_var:     0.0887 | E_err:   0.004653
[2025-10-07 00:21:42] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -29.606491 | E_var:     0.0627 | E_err:   0.003912
[2025-10-07 00:21:46] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -29.599379 | E_var:     0.0743 | E_err:   0.004258
[2025-10-07 00:21:50] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -29.603078 | E_var:     0.0719 | E_err:   0.004190
[2025-10-07 00:21:53] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -29.602404 | E_var:     0.0667 | E_err:   0.004034
[2025-10-07 00:21:57] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -29.599293 | E_var:     0.0611 | E_err:   0.003864
[2025-10-07 00:22:01] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -29.598544 | E_var:     0.0697 | E_err:   0.004125
[2025-10-07 00:22:04] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -29.596724 | E_var:     0.1117 | E_err:   0.005223
[2025-10-07 00:22:08] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -29.595066 | E_var:     0.0682 | E_err:   0.004079
[2025-10-07 00:22:12] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -29.601658 | E_var:     0.0642 | E_err:   0.003960
[2025-10-07 00:22:15] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -29.604059 | E_var:     0.0598 | E_err:   0.003819
[2025-10-07 00:22:19] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -29.608806 | E_var:     0.0835 | E_err:   0.004516
[2025-10-07 00:22:23] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -29.599948 | E_var:     0.0652 | E_err:   0.003991
[2025-10-07 00:22:26] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -29.610261 | E_var:     0.0720 | E_err:   0.004194
[2025-10-07 00:22:30] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -29.593105 | E_var:     0.0740 | E_err:   0.004251
[2025-10-07 00:22:34] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -29.601419 | E_var:     0.0750 | E_err:   0.004279
[2025-10-07 00:22:37] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -29.603209 | E_var:     0.0908 | E_err:   0.004707
[2025-10-07 00:22:41] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -29.597570 | E_var:     0.0758 | E_err:   0.004301
[2025-10-07 00:22:45] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -29.605686 | E_var:     0.0731 | E_err:   0.004224
[2025-10-07 00:22:48] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -29.603874 | E_var:     0.0593 | E_err:   0.003805
[2025-10-07 00:22:52] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -29.600417 | E_var:     0.0756 | E_err:   0.004297
[2025-10-07 00:22:56] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -29.600967 | E_var:     0.0788 | E_err:   0.004386
[2025-10-07 00:22:59] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -29.593897 | E_var:     0.1176 | E_err:   0.005357
[2025-10-07 00:23:03] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -29.596919 | E_var:     0.2105 | E_err:   0.007168
[2025-10-07 00:23:07] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -29.601384 | E_var:     0.0620 | E_err:   0.003892
[2025-10-07 00:23:10] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -29.593196 | E_var:     0.0955 | E_err:   0.004829
[2025-10-07 00:23:14] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -29.590814 | E_var:     0.1206 | E_err:   0.005427
[2025-10-07 00:23:18] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -29.598183 | E_var:     0.1721 | E_err:   0.006482
[2025-10-07 00:23:21] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -29.595195 | E_var:     0.0871 | E_err:   0.004611
[2025-10-07 00:23:25] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -29.598095 | E_var:     0.0695 | E_err:   0.004118
[2025-10-07 00:23:29] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -29.600495 | E_var:     0.0816 | E_err:   0.004464
[2025-10-07 00:23:29] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 00:23:32] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -29.597732 | E_var:     0.0676 | E_err:   0.004062
[2025-10-07 00:23:36] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -29.605595 | E_var:     0.0792 | E_err:   0.004396
[2025-10-07 00:23:40] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -29.599349 | E_var:     0.0774 | E_err:   0.004348
[2025-10-07 00:23:43] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -29.593649 | E_var:     0.0890 | E_err:   0.004661
[2025-10-07 00:23:48] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -29.600249 | E_var:     0.0776 | E_err:   0.004352
[2025-10-07 00:23:52] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -29.603865 | E_var:     0.0621 | E_err:   0.003895
[2025-10-07 00:23:56] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -29.598408 | E_var:     0.0697 | E_err:   0.004125
[2025-10-07 00:23:59] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -29.595569 | E_var:     0.0927 | E_err:   0.004757
[2025-10-07 00:24:03] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -29.593626 | E_var:     0.0607 | E_err:   0.003849
[2025-10-07 00:24:07] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -29.592910 | E_var:     0.1449 | E_err:   0.005949
[2025-10-07 00:24:10] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -29.597387 | E_var:     0.0624 | E_err:   0.003903
[2025-10-07 00:24:14] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -29.590472 | E_var:     0.0904 | E_err:   0.004698
[2025-10-07 00:24:18] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -29.605532 | E_var:     0.1213 | E_err:   0.005443
[2025-10-07 00:24:21] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -29.607710 | E_var:     0.1096 | E_err:   0.005174
[2025-10-07 00:24:25] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -29.608969 | E_var:     0.0823 | E_err:   0.004482
[2025-10-07 00:24:29] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -29.605864 | E_var:     0.0621 | E_err:   0.003894
[2025-10-07 00:24:32] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -29.598331 | E_var:     0.0846 | E_err:   0.004544
[2025-10-07 00:24:36] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -29.595691 | E_var:     0.0667 | E_err:   0.004036
[2025-10-07 00:24:40] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -29.598676 | E_var:     0.0611 | E_err:   0.003861
[2025-10-07 00:24:43] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -29.596596 | E_var:     0.0681 | E_err:   0.004076
[2025-10-07 00:24:47] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -29.603148 | E_var:     0.0710 | E_err:   0.004163
[2025-10-07 00:24:51] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -29.601441 | E_var:     0.0792 | E_err:   0.004397
[2025-10-07 00:24:54] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -29.600414 | E_var:     0.0927 | E_err:   0.004758
[2025-10-07 00:24:58] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -29.596793 | E_var:     0.0795 | E_err:   0.004404
[2025-10-07 00:25:02] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -29.599719 | E_var:     0.0771 | E_err:   0.004338
[2025-10-07 00:25:05] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -29.597450 | E_var:     0.0916 | E_err:   0.004730
[2025-10-07 00:25:09] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -29.596132 | E_var:     0.0738 | E_err:   0.004245
[2025-10-07 00:25:13] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -29.598389 | E_var:     0.0625 | E_err:   0.003908
[2025-10-07 00:25:16] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -29.592764 | E_var:     0.0804 | E_err:   0.004430
[2025-10-07 00:25:20] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -29.600876 | E_var:     0.0778 | E_err:   0.004358
[2025-10-07 00:25:24] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -29.593237 | E_var:     0.0639 | E_err:   0.003948
[2025-10-07 00:25:28] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -29.602235 | E_var:     0.0784 | E_err:   0.004375
[2025-10-07 00:25:31] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -29.605259 | E_var:     0.1555 | E_err:   0.006161
[2025-10-07 00:25:35] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -29.599151 | E_var:     0.0772 | E_err:   0.004343
[2025-10-07 00:25:39] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -29.601979 | E_var:     0.0699 | E_err:   0.004130
[2025-10-07 00:25:42] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -29.602952 | E_var:     0.0652 | E_err:   0.003991
[2025-10-07 00:25:46] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -29.596251 | E_var:     0.0689 | E_err:   0.004101
[2025-10-07 00:25:50] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -29.595423 | E_var:     0.0700 | E_err:   0.004135
[2025-10-07 00:25:53] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -29.604494 | E_var:     0.0682 | E_err:   0.004080
[2025-10-07 00:25:57] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -29.598726 | E_var:     0.0671 | E_err:   0.004049
[2025-10-07 00:26:01] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -29.596811 | E_var:     0.0726 | E_err:   0.004210
[2025-10-07 00:26:04] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -29.593078 | E_var:     0.0618 | E_err:   0.003886
[2025-10-07 00:26:08] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -29.601688 | E_var:     0.1429 | E_err:   0.005906
[2025-10-07 00:26:12] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -29.604371 | E_var:     0.0622 | E_err:   0.003897
[2025-10-07 00:26:15] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -29.602885 | E_var:     0.0645 | E_err:   0.003969
[2025-10-07 00:26:19] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -29.595261 | E_var:     0.0556 | E_err:   0.003683
[2025-10-07 00:26:23] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -29.596735 | E_var:     0.0622 | E_err:   0.003897
[2025-10-07 00:26:26] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -29.602931 | E_var:     0.0774 | E_err:   0.004346
[2025-10-07 00:26:30] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -29.605182 | E_var:     0.0971 | E_err:   0.004868
[2025-10-07 00:26:34] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -29.599508 | E_var:     0.0605 | E_err:   0.003844
[2025-10-07 00:26:38] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -29.596806 | E_var:     0.0932 | E_err:   0.004769
[2025-10-07 00:26:41] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -29.599496 | E_var:     0.2029 | E_err:   0.007037
[2025-10-07 00:26:45] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -29.597882 | E_var:     0.0977 | E_err:   0.004884
[2025-10-07 00:26:49] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -29.595447 | E_var:     0.0740 | E_err:   0.004250
[2025-10-07 00:26:52] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -29.603710 | E_var:     0.0768 | E_err:   0.004329
[2025-10-07 00:26:56] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -29.601331 | E_var:     0.0656 | E_err:   0.004001
[2025-10-07 00:27:00] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -29.594295 | E_var:     0.0795 | E_err:   0.004405
[2025-10-07 00:27:03] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -29.593294 | E_var:     0.0672 | E_err:   0.004050
[2025-10-07 00:27:07] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -29.607544 | E_var:     0.0781 | E_err:   0.004367
[2025-10-07 00:27:11] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -29.604083 | E_var:     0.0804 | E_err:   0.004430
[2025-10-07 00:27:14] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -29.598939 | E_var:     0.0530 | E_err:   0.003598
[2025-10-07 00:27:18] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -29.599213 | E_var:     0.0657 | E_err:   0.004005
[2025-10-07 00:27:22] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -29.595817 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 00:27:25] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -29.599855 | E_var:     0.0732 | E_err:   0.004228
[2025-10-07 00:27:29] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -29.593412 | E_var:     0.0671 | E_err:   0.004049
[2025-10-07 00:27:33] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -29.600271 | E_var:     0.1063 | E_err:   0.005095
[2025-10-07 00:27:36] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -29.598217 | E_var:     0.0665 | E_err:   0.004029
[2025-10-07 00:27:40] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -29.597028 | E_var:     0.1283 | E_err:   0.005597
[2025-10-07 00:27:44] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -29.601663 | E_var:     0.1021 | E_err:   0.004993
[2025-10-07 00:27:47] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -29.605990 | E_var:     0.0659 | E_err:   0.004011
[2025-10-07 00:27:51] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -29.596325 | E_var:     0.0753 | E_err:   0.004289
[2025-10-07 00:27:55] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -29.600225 | E_var:     0.0697 | E_err:   0.004126
[2025-10-07 00:27:58] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -29.596447 | E_var:     0.0797 | E_err:   0.004411
[2025-10-07 00:28:02] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -29.602669 | E_var:     0.0759 | E_err:   0.004305
[2025-10-07 00:28:06] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -29.598881 | E_var:     0.0672 | E_err:   0.004051
[2025-10-07 00:28:09] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -29.590773 | E_var:     0.0644 | E_err:   0.003965
[2025-10-07 00:28:13] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -29.604653 | E_var:     0.0739 | E_err:   0.004247
[2025-10-07 00:28:17] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -29.597761 | E_var:     0.0721 | E_err:   0.004194
[2025-10-07 00:28:20] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -29.593728 | E_var:     0.0876 | E_err:   0.004625
[2025-10-07 00:28:24] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -29.607836 | E_var:     0.0660 | E_err:   0.004016
[2025-10-07 00:28:28] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -29.601743 | E_var:     0.0634 | E_err:   0.003934
[2025-10-07 00:28:32] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -29.597785 | E_var:     0.0702 | E_err:   0.004139
[2025-10-07 00:28:35] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -29.600465 | E_var:     0.0688 | E_err:   0.004098
[2025-10-07 00:28:39] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -29.603762 | E_var:     0.0648 | E_err:   0.003978
[2025-10-07 00:28:43] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -29.596575 | E_var:     0.0603 | E_err:   0.003837
[2025-10-07 00:28:46] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -29.598103 | E_var:     0.0761 | E_err:   0.004312
[2025-10-07 00:28:50] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -29.602302 | E_var:     0.0587 | E_err:   0.003785
[2025-10-07 00:28:54] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -29.606345 | E_var:     0.0599 | E_err:   0.003825
[2025-10-07 00:28:57] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -29.602261 | E_var:     0.0700 | E_err:   0.004135
[2025-10-07 00:29:01] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -29.595113 | E_var:     0.0786 | E_err:   0.004380
[2025-10-07 00:29:05] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -29.600612 | E_var:     0.0685 | E_err:   0.004088
[2025-10-07 00:29:10] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -29.602461 | E_var:     0.0550 | E_err:   0.003665
[2025-10-07 00:29:13] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -29.604581 | E_var:     0.0618 | E_err:   0.003884
[2025-10-07 00:29:17] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -29.600097 | E_var:     0.0657 | E_err:   0.004005
[2025-10-07 00:29:21] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -29.601637 | E_var:     0.0650 | E_err:   0.003984
[2025-10-07 00:29:24] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -29.606116 | E_var:     0.0634 | E_err:   0.003934
[2025-10-07 00:29:28] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -29.591332 | E_var:     0.0656 | E_err:   0.004003
[2025-10-07 00:29:32] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -29.607129 | E_var:     0.0679 | E_err:   0.004071
[2025-10-07 00:29:35] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -29.604198 | E_var:     0.0623 | E_err:   0.003899
[2025-10-07 00:29:39] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -29.601645 | E_var:     0.0714 | E_err:   0.004174
[2025-10-07 00:29:39] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 00:29:43] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -29.604944 | E_var:     0.1646 | E_err:   0.006339
[2025-10-07 00:29:46] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -29.596995 | E_var:     0.0678 | E_err:   0.004069
[2025-10-07 00:29:50] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -29.604270 | E_var:     0.0769 | E_err:   0.004332
[2025-10-07 00:29:54] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -29.611239 | E_var:     0.0854 | E_err:   0.004567
[2025-10-07 00:29:57] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -29.597626 | E_var:     0.0740 | E_err:   0.004252
[2025-10-07 00:30:01] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -29.600187 | E_var:     0.0604 | E_err:   0.003840
[2025-10-07 00:30:05] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -29.600713 | E_var:     0.0558 | E_err:   0.003692
[2025-10-07 00:30:08] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -29.597449 | E_var:     0.0584 | E_err:   0.003777
[2025-10-07 00:30:12] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -29.603033 | E_var:     0.0657 | E_err:   0.004004
[2025-10-07 00:30:16] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -29.602735 | E_var:     0.0756 | E_err:   0.004297
[2025-10-07 00:30:19] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -29.599166 | E_var:     0.0656 | E_err:   0.004002
[2025-10-07 00:30:23] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -29.595980 | E_var:     0.0732 | E_err:   0.004229
[2025-10-07 00:30:27] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -29.606907 | E_var:     0.0575 | E_err:   0.003747
[2025-10-07 00:30:30] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -29.599202 | E_var:     0.0566 | E_err:   0.003718
[2025-10-07 00:30:34] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -29.597306 | E_var:     0.1124 | E_err:   0.005238
[2025-10-07 00:30:38] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -29.604262 | E_var:     0.0709 | E_err:   0.004160
[2025-10-07 00:30:41] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -29.598579 | E_var:     0.0544 | E_err:   0.003643
[2025-10-07 00:30:45] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -29.601592 | E_var:     0.0738 | E_err:   0.004244
[2025-10-07 00:30:49] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -29.599795 | E_var:     0.0699 | E_err:   0.004131
[2025-10-07 00:30:52] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -29.588776 | E_var:     0.0950 | E_err:   0.004816
[2025-10-07 00:30:56] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -29.597280 | E_var:     0.1276 | E_err:   0.005580
[2025-10-07 00:31:00] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -29.596092 | E_var:     0.0814 | E_err:   0.004458
[2025-10-07 00:31:03] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -29.601983 | E_var:     0.0734 | E_err:   0.004234
[2025-10-07 00:31:07] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -29.601915 | E_var:     0.0837 | E_err:   0.004521
[2025-10-07 00:31:11] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -29.606488 | E_var:     0.0592 | E_err:   0.003803
[2025-10-07 00:31:14] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -29.598149 | E_var:     0.0750 | E_err:   0.004280
[2025-10-07 00:31:18] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -29.601383 | E_var:     0.0713 | E_err:   0.004173
[2025-10-07 00:31:22] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -29.604012 | E_var:     0.0984 | E_err:   0.004903
[2025-10-07 00:31:27] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -29.604667 | E_var:     0.0641 | E_err:   0.003955
[2025-10-07 00:31:31] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -29.597812 | E_var:     0.0608 | E_err:   0.003852
[2025-10-07 00:31:35] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -29.598663 | E_var:     0.0611 | E_err:   0.003863
[2025-10-07 00:31:38] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -29.591634 | E_var:     0.0757 | E_err:   0.004298
[2025-10-07 00:31:42] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -29.594692 | E_var:     0.0801 | E_err:   0.004423
[2025-10-07 00:31:46] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -29.600735 | E_var:     0.0838 | E_err:   0.004522
[2025-10-07 00:31:49] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -29.591939 | E_var:     0.0836 | E_err:   0.004519
[2025-10-07 00:31:53] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -29.596427 | E_var:     0.0620 | E_err:   0.003890
[2025-10-07 00:31:57] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -29.601602 | E_var:     0.0574 | E_err:   0.003743
[2025-10-07 00:32:00] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -29.598472 | E_var:     0.0886 | E_err:   0.004652
[2025-10-07 00:32:04] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -29.594369 | E_var:     0.0857 | E_err:   0.004575
[2025-10-07 00:32:08] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -29.600327 | E_var:     0.0676 | E_err:   0.004063
[2025-10-07 00:32:11] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -29.603376 | E_var:     0.0573 | E_err:   0.003741
[2025-10-07 00:32:15] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -29.594062 | E_var:     0.1486 | E_err:   0.006024
[2025-10-07 00:32:19] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -29.598950 | E_var:     0.0630 | E_err:   0.003923
[2025-10-07 00:32:22] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -29.609739 | E_var:     0.0643 | E_err:   0.003962
[2025-10-07 00:32:26] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -29.598709 | E_var:     0.0695 | E_err:   0.004119
[2025-10-07 00:32:31] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -29.597714 | E_var:     0.2263 | E_err:   0.007433
[2025-10-07 00:32:34] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -29.599922 | E_var:     0.0583 | E_err:   0.003773
[2025-10-07 00:32:38] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -29.602951 | E_var:     0.0837 | E_err:   0.004522
[2025-10-07 00:32:42] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -29.603862 | E_var:     0.0683 | E_err:   0.004085
[2025-10-07 00:32:45] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -29.597179 | E_var:     0.0755 | E_err:   0.004293
[2025-10-07 00:32:49] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -29.605419 | E_var:     0.0680 | E_err:   0.004075
[2025-10-07 00:32:54] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -29.604327 | E_var:     0.0704 | E_err:   0.004145
[2025-10-07 00:32:58] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -29.602617 | E_var:     0.1322 | E_err:   0.005681
[2025-10-07 00:33:01] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -29.609012 | E_var:     0.1156 | E_err:   0.005311
[2025-10-07 00:33:05] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -29.600734 | E_var:     0.0737 | E_err:   0.004242
[2025-10-07 00:33:09] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -29.602704 | E_var:     0.0569 | E_err:   0.003728
[2025-10-07 00:33:12] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -29.593975 | E_var:     0.0675 | E_err:   0.004060
[2025-10-07 00:33:16] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -29.596272 | E_var:     0.0720 | E_err:   0.004194
[2025-10-07 00:33:20] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -29.595859 | E_var:     0.0625 | E_err:   0.003907
[2025-10-07 00:33:23] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -29.603740 | E_var:     0.0622 | E_err:   0.003897
[2025-10-07 00:33:27] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -29.593169 | E_var:     0.0648 | E_err:   0.003977
[2025-10-07 00:33:31] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -29.598578 | E_var:     0.0547 | E_err:   0.003654
[2025-10-07 00:33:34] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -29.600963 | E_var:     0.0673 | E_err:   0.004052
[2025-10-07 00:33:38] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -29.603083 | E_var:     0.0706 | E_err:   0.004152
[2025-10-07 00:33:42] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -29.604522 | E_var:     0.0602 | E_err:   0.003833
[2025-10-07 00:33:45] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -29.600419 | E_var:     0.0636 | E_err:   0.003940
[2025-10-07 00:33:49] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -29.600846 | E_var:     0.0679 | E_err:   0.004071
[2025-10-07 00:33:53] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -29.601123 | E_var:     0.0686 | E_err:   0.004092
[2025-10-07 00:33:56] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -29.595686 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 00:34:00] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -29.602645 | E_var:     0.0626 | E_err:   0.003910
[2025-10-07 00:34:04] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -29.603009 | E_var:     0.1002 | E_err:   0.004947
[2025-10-07 00:34:07] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -29.597496 | E_var:     0.0616 | E_err:   0.003878
[2025-10-07 00:34:11] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -29.603774 | E_var:     0.0717 | E_err:   0.004184
[2025-10-07 00:34:15] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -29.606915 | E_var:     0.0619 | E_err:   0.003889
[2025-10-07 00:34:18] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -29.602193 | E_var:     0.0675 | E_err:   0.004060
[2025-10-07 00:34:22] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -29.599627 | E_var:     0.0654 | E_err:   0.003995
[2025-10-07 00:34:26] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -29.604840 | E_var:     0.0887 | E_err:   0.004655
[2025-10-07 00:34:29] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -29.597081 | E_var:     0.0650 | E_err:   0.003983
[2025-10-07 00:34:33] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -29.599624 | E_var:     0.0744 | E_err:   0.004262
[2025-10-07 00:34:37] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -29.596385 | E_var:     0.0618 | E_err:   0.003885
[2025-10-07 00:34:40] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -29.605279 | E_var:     0.0671 | E_err:   0.004046
[2025-10-07 00:34:44] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -29.594796 | E_var:     0.0610 | E_err:   0.003858
[2025-10-07 00:34:48] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -29.601066 | E_var:     0.0926 | E_err:   0.004754
[2025-10-07 00:34:51] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -29.601364 | E_var:     0.0522 | E_err:   0.003570
[2025-10-07 00:34:55] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -29.606286 | E_var:     0.0614 | E_err:   0.003873
[2025-10-07 00:34:59] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -29.596125 | E_var:     0.0648 | E_err:   0.003978
[2025-10-07 00:35:02] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -29.598944 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 00:35:06] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -29.596547 | E_var:     0.0563 | E_err:   0.003707
[2025-10-07 00:35:10] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -29.599311 | E_var:     0.0742 | E_err:   0.004255
[2025-10-07 00:35:13] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -29.599120 | E_var:     0.0773 | E_err:   0.004344
[2025-10-07 00:35:17] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -29.601071 | E_var:     0.0638 | E_err:   0.003946
[2025-10-07 00:35:21] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -29.591085 | E_var:     0.0901 | E_err:   0.004690
[2025-10-07 00:35:24] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -29.593388 | E_var:     0.0525 | E_err:   0.003581
[2025-10-07 00:35:28] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -29.602428 | E_var:     0.1216 | E_err:   0.005448
[2025-10-07 00:35:32] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -29.601602 | E_var:     0.0581 | E_err:   0.003766
[2025-10-07 00:35:36] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -29.603941 | E_var:     0.0999 | E_err:   0.004939
[2025-10-07 00:35:40] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -29.596601 | E_var:     0.0626 | E_err:   0.003909
[2025-10-07 00:35:44] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -29.590852 | E_var:     0.0973 | E_err:   0.004874
[2025-10-07 00:35:47] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -29.604696 | E_var:     0.0526 | E_err:   0.003584
[2025-10-07 00:35:51] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -29.607104 | E_var:     0.0718 | E_err:   0.004188
[2025-10-07 00:35:51] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 00:35:55] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -29.592778 | E_var:     0.0624 | E_err:   0.003904
[2025-10-07 00:35:58] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -29.602738 | E_var:     0.0842 | E_err:   0.004534
[2025-10-07 00:36:02] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -29.596515 | E_var:     0.1570 | E_err:   0.006192
[2025-10-07 00:36:06] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -29.595742 | E_var:     0.0565 | E_err:   0.003713
[2025-10-07 00:36:09] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -29.597663 | E_var:     0.0878 | E_err:   0.004629
[2025-10-07 00:36:13] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -29.597109 | E_var:     0.0748 | E_err:   0.004274
[2025-10-07 00:36:17] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -29.595560 | E_var:     0.0612 | E_err:   0.003865
[2025-10-07 00:36:20] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -29.599611 | E_var:     0.0716 | E_err:   0.004182
[2025-10-07 00:36:24] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -29.596855 | E_var:     0.0656 | E_err:   0.004002
[2025-10-07 00:36:28] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -29.593271 | E_var:     0.0923 | E_err:   0.004746
[2025-10-07 00:36:31] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -29.610586 | E_var:     0.0849 | E_err:   0.004553
[2025-10-07 00:36:35] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -29.601869 | E_var:     0.0864 | E_err:   0.004593
[2025-10-07 00:36:39] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -29.597018 | E_var:     0.0839 | E_err:   0.004527
[2025-10-07 00:36:42] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -29.602882 | E_var:     0.0769 | E_err:   0.004333
[2025-10-07 00:36:46] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -29.603014 | E_var:     0.0705 | E_err:   0.004148
[2025-10-07 00:36:50] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -29.600905 | E_var:     0.0673 | E_err:   0.004055
[2025-10-07 00:36:53] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -29.599148 | E_var:     0.0621 | E_err:   0.003895
[2025-10-07 00:36:57] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -29.606513 | E_var:     0.0737 | E_err:   0.004243
[2025-10-07 00:37:01] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -29.594543 | E_var:     0.1042 | E_err:   0.005044
[2025-10-07 00:37:04] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -29.591174 | E_var:     0.0644 | E_err:   0.003965
[2025-10-07 00:37:08] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -29.606454 | E_var:     0.0556 | E_err:   0.003685
[2025-10-07 00:37:12] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -29.600263 | E_var:     0.0664 | E_err:   0.004026
[2025-10-07 00:37:15] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -29.596454 | E_var:     0.0843 | E_err:   0.004537
[2025-10-07 00:37:19] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -29.602327 | E_var:     0.0607 | E_err:   0.003850
[2025-10-07 00:37:23] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -29.604269 | E_var:     0.1220 | E_err:   0.005457
[2025-10-07 00:37:26] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -29.603673 | E_var:     0.0616 | E_err:   0.003877
[2025-10-07 00:37:30] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -29.599933 | E_var:     0.0794 | E_err:   0.004402
[2025-10-07 00:37:34] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -29.600425 | E_var:     0.0631 | E_err:   0.003926
[2025-10-07 00:37:37] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -29.600577 | E_var:     0.0597 | E_err:   0.003816
[2025-10-07 00:37:41] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -29.601310 | E_var:     0.0736 | E_err:   0.004238
[2025-10-07 00:37:45] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -29.600929 | E_var:     0.0695 | E_err:   0.004119
[2025-10-07 00:37:48] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -29.601693 | E_var:     0.0676 | E_err:   0.004064
[2025-10-07 00:37:52] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -29.596424 | E_var:     0.0701 | E_err:   0.004136
[2025-10-07 00:37:56] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -29.596897 | E_var:     0.0731 | E_err:   0.004225
[2025-10-07 00:38:00] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -29.605713 | E_var:     0.0630 | E_err:   0.003922
[2025-10-07 00:38:04] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -29.600036 | E_var:     0.0829 | E_err:   0.004499
[2025-10-07 00:38:08] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -29.600448 | E_var:     0.0548 | E_err:   0.003658
[2025-10-07 00:38:11] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -29.599354 | E_var:     0.0572 | E_err:   0.003737
[2025-10-07 00:38:15] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -29.596322 | E_var:     0.0614 | E_err:   0.003872
[2025-10-07 00:38:19] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -29.600447 | E_var:     0.0673 | E_err:   0.004053
[2025-10-07 00:38:22] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -29.598121 | E_var:     0.0557 | E_err:   0.003688
[2025-10-07 00:38:26] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -29.602918 | E_var:     0.0545 | E_err:   0.003648
[2025-10-07 00:38:30] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -29.597227 | E_var:     0.0696 | E_err:   0.004121
[2025-10-07 00:38:33] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -29.598059 | E_var:     0.0593 | E_err:   0.003805
[2025-10-07 00:38:37] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -29.600533 | E_var:     0.0729 | E_err:   0.004219
[2025-10-07 00:38:41] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -29.604206 | E_var:     0.1440 | E_err:   0.005930
[2025-10-07 00:38:44] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -29.602241 | E_var:     0.0920 | E_err:   0.004740
[2025-10-07 00:38:48] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -29.608575 | E_var:     0.0992 | E_err:   0.004920
[2025-10-07 00:38:52] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -29.592735 | E_var:     0.0708 | E_err:   0.004158
[2025-10-07 00:38:55] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -29.595652 | E_var:     0.0601 | E_err:   0.003830
[2025-10-07 00:38:59] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -29.601480 | E_var:     0.0703 | E_err:   0.004142
[2025-10-07 00:39:03] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -29.603397 | E_var:     0.1016 | E_err:   0.004980
[2025-10-07 00:39:06] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -29.600871 | E_var:     0.0666 | E_err:   0.004031
[2025-10-07 00:39:10] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -29.595582 | E_var:     0.1233 | E_err:   0.005487
[2025-10-07 00:39:14] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -29.595611 | E_var:     0.0691 | E_err:   0.004108
[2025-10-07 00:39:17] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -29.601040 | E_var:     0.0603 | E_err:   0.003838
[2025-10-07 00:39:21] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -29.594309 | E_var:     0.0675 | E_err:   0.004059
[2025-10-07 00:39:25] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -29.598521 | E_var:     0.0677 | E_err:   0.004066
[2025-10-07 00:39:28] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -29.602524 | E_var:     0.0692 | E_err:   0.004111
[2025-10-07 00:39:32] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -29.598560 | E_var:     0.0619 | E_err:   0.003888
[2025-10-07 00:39:36] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -29.595189 | E_var:     0.0681 | E_err:   0.004077
[2025-10-07 00:39:40] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -29.597093 | E_var:     0.0882 | E_err:   0.004639
[2025-10-07 00:39:43] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -29.594989 | E_var:     0.0792 | E_err:   0.004396
[2025-10-07 00:39:47] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -29.604058 | E_var:     0.0668 | E_err:   0.004038
[2025-10-07 00:39:51] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -29.602018 | E_var:     0.0777 | E_err:   0.004357
[2025-10-07 00:39:54] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -29.602471 | E_var:     0.1649 | E_err:   0.006345
[2025-10-07 00:39:58] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -29.604065 | E_var:     0.0557 | E_err:   0.003688
[2025-10-07 00:40:02] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -29.604942 | E_var:     0.0686 | E_err:   0.004093
[2025-10-07 00:40:05] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -29.605296 | E_var:     0.0686 | E_err:   0.004094
[2025-10-07 00:40:09] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -29.599667 | E_var:     0.0755 | E_err:   0.004294
[2025-10-07 00:40:13] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -29.598844 | E_var:     0.0729 | E_err:   0.004218
[2025-10-07 00:40:16] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -29.599643 | E_var:     0.0814 | E_err:   0.004457
[2025-10-07 00:40:20] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -29.597541 | E_var:     0.0555 | E_err:   0.003681
[2025-10-07 00:40:24] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -29.606758 | E_var:     0.0594 | E_err:   0.003808
[2025-10-07 00:40:27] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -29.595145 | E_var:     0.0771 | E_err:   0.004339
[2025-10-07 00:40:31] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -29.602908 | E_var:     0.0583 | E_err:   0.003774
[2025-10-07 00:40:35] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -29.603005 | E_var:     0.0752 | E_err:   0.004284
[2025-10-07 00:40:38] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -29.597235 | E_var:     0.0815 | E_err:   0.004459
[2025-10-07 00:40:42] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -29.600855 | E_var:     0.0892 | E_err:   0.004668
[2025-10-07 00:40:46] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -29.603365 | E_var:     0.0606 | E_err:   0.003847
[2025-10-07 00:40:50] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -29.597053 | E_var:     0.0663 | E_err:   0.004025
[2025-10-07 00:40:54] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -29.601447 | E_var:     0.0801 | E_err:   0.004423
[2025-10-07 00:40:57] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -29.604816 | E_var:     0.0624 | E_err:   0.003904
[2025-10-07 00:41:01] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -29.589800 | E_var:     0.1008 | E_err:   0.004961
[2025-10-07 00:41:05] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -29.599803 | E_var:     0.0828 | E_err:   0.004495
[2025-10-07 00:41:08] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -29.598706 | E_var:     0.0684 | E_err:   0.004086
[2025-10-07 00:41:12] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -29.600558 | E_var:     0.0576 | E_err:   0.003749
[2025-10-07 00:41:16] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -29.598154 | E_var:     0.0768 | E_err:   0.004329
[2025-10-07 00:41:19] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -29.595498 | E_var:     0.0839 | E_err:   0.004527
[2025-10-07 00:41:23] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -29.600237 | E_var:     0.0578 | E_err:   0.003756
[2025-10-07 00:41:27] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -29.593458 | E_var:     0.0744 | E_err:   0.004263
[2025-10-07 00:41:30] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -29.597772 | E_var:     0.0802 | E_err:   0.004426
[2025-10-07 00:41:34] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -29.596839 | E_var:     0.2792 | E_err:   0.008256
[2025-10-07 00:41:38] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -29.600137 | E_var:     0.1025 | E_err:   0.005003
[2025-10-07 00:41:41] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -29.597225 | E_var:     0.0630 | E_err:   0.003921
[2025-10-07 00:41:45] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -29.595833 | E_var:     0.0674 | E_err:   0.004056
[2025-10-07 00:41:49] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -29.609915 | E_var:     0.0834 | E_err:   0.004511
[2025-10-07 00:41:52] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -29.598207 | E_var:     0.1007 | E_err:   0.004958
[2025-10-07 00:41:56] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -29.596966 | E_var:     0.0605 | E_err:   0.003843
[2025-10-07 00:42:00] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -29.598241 | E_var:     0.0815 | E_err:   0.004461
[2025-10-07 00:42:00] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 00:42:03] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -29.602034 | E_var:     0.0633 | E_err:   0.003930
[2025-10-07 00:42:07] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -29.605090 | E_var:     0.0753 | E_err:   0.004287
[2025-10-07 00:42:11] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -29.600462 | E_var:     0.0637 | E_err:   0.003944
[2025-10-07 00:42:15] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -29.601416 | E_var:     0.1812 | E_err:   0.006652
[2025-10-07 00:42:18] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -29.601654 | E_var:     0.0649 | E_err:   0.003980
[2025-10-07 00:42:22] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -29.601066 | E_var:     0.0648 | E_err:   0.003977
[2025-10-07 00:42:26] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -29.598676 | E_var:     0.0613 | E_err:   0.003868
[2025-10-07 00:42:29] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -29.608595 | E_var:     0.0791 | E_err:   0.004395
[2025-10-07 00:42:33] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -29.598338 | E_var:     0.0582 | E_err:   0.003770
[2025-10-07 00:42:37] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -29.601845 | E_var:     0.0731 | E_err:   0.004224
[2025-10-07 00:42:40] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -29.596801 | E_var:     0.0738 | E_err:   0.004245
[2025-10-07 00:42:44] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -29.597176 | E_var:     0.0751 | E_err:   0.004281
[2025-10-07 00:42:48] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -29.600104 | E_var:     0.1188 | E_err:   0.005386
[2025-10-07 00:42:51] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -29.603454 | E_var:     0.0823 | E_err:   0.004482
[2025-10-07 00:42:55] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -29.600894 | E_var:     0.0652 | E_err:   0.003990
[2025-10-07 00:42:59] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -29.600298 | E_var:     0.0659 | E_err:   0.004010
[2025-10-07 00:43:02] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -29.601017 | E_var:     0.0630 | E_err:   0.003921
[2025-10-07 00:43:06] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -29.599247 | E_var:     0.0795 | E_err:   0.004404
[2025-10-07 00:43:10] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -29.595218 | E_var:     0.0770 | E_err:   0.004336
[2025-10-07 00:43:13] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -29.607326 | E_var:     0.1121 | E_err:   0.005232
[2025-10-07 00:43:17] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -29.604888 | E_var:     0.0776 | E_err:   0.004353
[2025-10-07 00:43:21] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -29.601898 | E_var:     0.0563 | E_err:   0.003709
[2025-10-07 00:43:24] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -29.602427 | E_var:     0.0561 | E_err:   0.003700
[2025-10-07 00:43:28] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -29.597484 | E_var:     0.0609 | E_err:   0.003855
[2025-10-07 00:43:32] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -29.601827 | E_var:     0.0542 | E_err:   0.003637
[2025-10-07 00:43:35] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -29.593088 | E_var:     0.0633 | E_err:   0.003932
[2025-10-07 00:43:39] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -29.595142 | E_var:     0.0684 | E_err:   0.004087
[2025-10-07 00:43:43] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -29.605602 | E_var:     0.1290 | E_err:   0.005612
[2025-10-07 00:43:46] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -29.596567 | E_var:     0.0671 | E_err:   0.004047
[2025-10-07 00:43:50] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -29.600014 | E_var:     0.0599 | E_err:   0.003824
[2025-10-07 00:43:54] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -29.597975 | E_var:     0.0778 | E_err:   0.004358
[2025-10-07 00:43:57] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -29.603582 | E_var:     0.0660 | E_err:   0.004015
[2025-10-07 00:44:01] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -29.602178 | E_var:     0.1357 | E_err:   0.005756
[2025-10-07 00:44:05] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -29.602981 | E_var:     0.0757 | E_err:   0.004300
[2025-10-07 00:44:08] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -29.596173 | E_var:     0.0604 | E_err:   0.003839
[2025-10-07 00:44:12] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -29.596663 | E_var:     0.0571 | E_err:   0.003733
[2025-10-07 00:44:16] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -29.598619 | E_var:     0.0596 | E_err:   0.003814
[2025-10-07 00:44:19] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -29.596661 | E_var:     0.1045 | E_err:   0.005050
[2025-10-07 00:44:23] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -29.598444 | E_var:     0.0626 | E_err:   0.003909
[2025-10-07 00:44:27] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -29.601031 | E_var:     0.0633 | E_err:   0.003931
[2025-10-07 00:44:30] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -29.596247 | E_var:     0.0810 | E_err:   0.004447
[2025-10-07 00:44:34] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -29.599349 | E_var:     0.0850 | E_err:   0.004555
[2025-10-07 00:44:38] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -29.599642 | E_var:     0.0678 | E_err:   0.004068
[2025-10-07 00:44:41] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -29.600482 | E_var:     0.0549 | E_err:   0.003662
[2025-10-07 00:44:45] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -29.607493 | E_var:     0.0811 | E_err:   0.004449
[2025-10-07 00:44:49] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -29.607414 | E_var:     0.0609 | E_err:   0.003855
[2025-10-07 00:44:52] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -29.594212 | E_var:     0.0727 | E_err:   0.004213
[2025-10-07 00:44:56] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -29.600297 | E_var:     0.0845 | E_err:   0.004542
[2025-10-07 00:45:00] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -29.598843 | E_var:     0.0632 | E_err:   0.003927
[2025-10-07 00:45:03] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -29.604147 | E_var:     0.0649 | E_err:   0.003980
[2025-10-07 00:45:07] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -29.603031 | E_var:     0.0801 | E_err:   0.004421
[2025-10-07 00:45:11] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -29.596226 | E_var:     0.1013 | E_err:   0.004972
[2025-10-07 00:45:14] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -29.594306 | E_var:     0.1004 | E_err:   0.004950
[2025-10-07 00:45:18] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -29.596523 | E_var:     0.0630 | E_err:   0.003921
[2025-10-07 00:45:22] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -29.602164 | E_var:     0.0740 | E_err:   0.004249
[2025-10-07 00:45:25] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -29.596492 | E_var:     0.0549 | E_err:   0.003661
[2025-10-07 00:45:29] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -29.602694 | E_var:     0.0585 | E_err:   0.003780
[2025-10-07 00:45:33] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -29.601041 | E_var:     0.0657 | E_err:   0.004004
[2025-10-07 00:45:36] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -29.596090 | E_var:     0.0601 | E_err:   0.003830
[2025-10-07 00:45:40] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -29.596220 | E_var:     0.0741 | E_err:   0.004254
[2025-10-07 00:45:44] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -29.597964 | E_var:     0.0726 | E_err:   0.004211
[2025-10-07 00:45:47] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -29.603752 | E_var:     0.0557 | E_err:   0.003689
[2025-10-07 00:45:51] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -29.599662 | E_var:     0.0863 | E_err:   0.004591
[2025-10-07 00:45:55] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -29.599466 | E_var:     0.1363 | E_err:   0.005769
[2025-10-07 00:45:58] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -29.602495 | E_var:     0.0620 | E_err:   0.003890
[2025-10-07 00:46:02] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -29.601508 | E_var:     0.0613 | E_err:   0.003868
[2025-10-07 00:46:06] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -29.599171 | E_var:     0.0870 | E_err:   0.004609
[2025-10-07 00:46:09] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -29.603160 | E_var:     0.0587 | E_err:   0.003785
[2025-10-07 00:46:13] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -29.599602 | E_var:     0.0555 | E_err:   0.003681
[2025-10-07 00:46:17] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -29.598201 | E_var:     0.0506 | E_err:   0.003516
[2025-10-07 00:46:20] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -29.608385 | E_var:     0.0634 | E_err:   0.003935
[2025-10-07 00:46:24] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -29.594061 | E_var:     0.0532 | E_err:   0.003604
[2025-10-07 00:46:28] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -29.598513 | E_var:     0.0699 | E_err:   0.004130
[2025-10-07 00:46:31] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -29.592932 | E_var:     0.0622 | E_err:   0.003897
[2025-10-07 00:46:35] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -29.597776 | E_var:     0.1135 | E_err:   0.005264
[2025-10-07 00:46:39] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -29.601287 | E_var:     0.0535 | E_err:   0.003615
[2025-10-07 00:46:42] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -29.601550 | E_var:     0.0704 | E_err:   0.004145
[2025-10-07 00:46:46] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -29.604214 | E_var:     0.0506 | E_err:   0.003515
[2025-10-07 00:46:50] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -29.602935 | E_var:     0.0703 | E_err:   0.004142
[2025-10-07 00:46:53] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -29.596547 | E_var:     0.0608 | E_err:   0.003854
[2025-10-07 00:46:57] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -29.602742 | E_var:     0.0610 | E_err:   0.003859
[2025-10-07 00:47:01] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -29.597874 | E_var:     0.0671 | E_err:   0.004049
[2025-10-07 00:47:04] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -29.604286 | E_var:     0.0914 | E_err:   0.004725
[2025-10-07 00:47:08] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -29.602915 | E_var:     0.0723 | E_err:   0.004202
[2025-10-07 00:47:12] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -29.601829 | E_var:     0.0966 | E_err:   0.004857
[2025-10-07 00:47:15] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -29.602678 | E_var:     0.0751 | E_err:   0.004281
[2025-10-07 00:47:19] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -29.605404 | E_var:     0.0707 | E_err:   0.004156
[2025-10-07 00:47:23] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -29.595216 | E_var:     0.0671 | E_err:   0.004047
[2025-10-07 00:47:26] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -29.602304 | E_var:     0.0755 | E_err:   0.004292
[2025-10-07 00:47:30] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -29.602698 | E_var:     0.0612 | E_err:   0.003865
[2025-10-07 00:47:34] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -29.603708 | E_var:     0.0721 | E_err:   0.004196
[2025-10-07 00:47:37] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -29.607425 | E_var:     0.0990 | E_err:   0.004915
[2025-10-07 00:47:41] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -29.597492 | E_var:     0.0784 | E_err:   0.004376
[2025-10-07 00:47:45] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -29.602397 | E_var:     0.0698 | E_err:   0.004128
[2025-10-07 00:47:48] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -29.602563 | E_var:     0.0642 | E_err:   0.003958
[2025-10-07 00:47:52] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -29.594449 | E_var:     0.0621 | E_err:   0.003893
[2025-10-07 00:47:56] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -29.603789 | E_var:     0.1105 | E_err:   0.005194
[2025-10-07 00:47:59] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -29.601486 | E_var:     0.0783 | E_err:   0.004373
[2025-10-07 00:48:03] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -29.601332 | E_var:     0.0586 | E_err:   0.003783
[2025-10-07 00:48:07] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -29.602126 | E_var:     0.0885 | E_err:   0.004649
[2025-10-07 00:48:07] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 00:48:10] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -29.599307 | E_var:     0.0870 | E_err:   0.004608
[2025-10-07 00:48:14] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -29.605753 | E_var:     0.0662 | E_err:   0.004021
[2025-10-07 00:48:18] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -29.602449 | E_var:     0.0781 | E_err:   0.004366
[2025-10-07 00:48:21] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -29.605576 | E_var:     0.0640 | E_err:   0.003953
[2025-10-07 00:48:25] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -29.606446 | E_var:     0.0801 | E_err:   0.004422
[2025-10-07 00:48:29] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -29.601056 | E_var:     0.0796 | E_err:   0.004410
[2025-10-07 00:48:32] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -29.605840 | E_var:     0.0785 | E_err:   0.004378
[2025-10-07 00:48:36] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -29.600383 | E_var:     0.0558 | E_err:   0.003692
[2025-10-07 00:48:40] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -29.600387 | E_var:     0.0665 | E_err:   0.004029
[2025-10-07 00:48:43] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -29.599652 | E_var:     0.0683 | E_err:   0.004085
[2025-10-07 00:48:47] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -29.593197 | E_var:     0.0602 | E_err:   0.003832
[2025-10-07 00:48:51] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -29.597302 | E_var:     0.0664 | E_err:   0.004026
[2025-10-07 00:48:54] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -29.599049 | E_var:     0.0517 | E_err:   0.003552
[2025-10-07 00:48:58] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -29.606017 | E_var:     0.0639 | E_err:   0.003950
[2025-10-07 00:49:02] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -29.604259 | E_var:     0.0648 | E_err:   0.003978
[2025-10-07 00:49:05] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -29.597490 | E_var:     0.0607 | E_err:   0.003850
[2025-10-07 00:49:09] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -29.606970 | E_var:     0.0652 | E_err:   0.003989
[2025-10-07 00:49:13] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -29.603476 | E_var:     0.0571 | E_err:   0.003733
[2025-10-07 00:49:17] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -29.596398 | E_var:     0.0504 | E_err:   0.003508
[2025-10-07 00:49:20] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -29.600770 | E_var:     0.0675 | E_err:   0.004059
[2025-10-07 00:49:24] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -29.603044 | E_var:     0.0653 | E_err:   0.003994
[2025-10-07 00:49:28] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -29.600448 | E_var:     0.0747 | E_err:   0.004269
[2025-10-07 00:49:31] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -29.603548 | E_var:     0.0635 | E_err:   0.003937
[2025-10-07 00:49:35] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -29.597252 | E_var:     0.0683 | E_err:   0.004084
[2025-10-07 00:49:39] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -29.597618 | E_var:     0.0853 | E_err:   0.004564
[2025-10-07 00:49:42] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -29.606118 | E_var:     0.0708 | E_err:   0.004158
[2025-10-07 00:49:46] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -29.597986 | E_var:     0.0625 | E_err:   0.003906
[2025-10-07 00:49:50] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -29.585315 | E_var:     0.3822 | E_err:   0.009660
[2025-10-07 00:49:53] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -29.590845 | E_var:     0.2292 | E_err:   0.007481
[2025-10-07 00:49:57] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -29.595111 | E_var:     0.0887 | E_err:   0.004653
[2025-10-07 00:50:01] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -29.599023 | E_var:     0.0672 | E_err:   0.004050
[2025-10-07 00:50:04] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -29.602663 | E_var:     0.0617 | E_err:   0.003882
[2025-10-07 00:50:08] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -29.602146 | E_var:     0.0622 | E_err:   0.003898
[2025-10-07 00:50:12] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -29.591175 | E_var:     0.0732 | E_err:   0.004227
[2025-10-07 00:50:15] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -29.596216 | E_var:     0.0743 | E_err:   0.004260
[2025-10-07 00:50:19] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -29.593464 | E_var:     0.0765 | E_err:   0.004322
[2025-10-07 00:50:23] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -29.599217 | E_var:     0.0877 | E_err:   0.004628
[2025-10-07 00:50:26] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -29.607201 | E_var:     0.0736 | E_err:   0.004240
[2025-10-07 00:50:30] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -29.599329 | E_var:     0.0683 | E_err:   0.004083
[2025-10-07 00:50:34] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -29.596037 | E_var:     0.0754 | E_err:   0.004290
[2025-10-07 00:50:37] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -29.594216 | E_var:     0.0705 | E_err:   0.004148
[2025-10-07 00:50:41] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -29.596330 | E_var:     0.0720 | E_err:   0.004193
[2025-10-07 00:50:45] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -29.598522 | E_var:     0.0640 | E_err:   0.003952
[2025-10-07 00:50:48] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -29.601189 | E_var:     0.0581 | E_err:   0.003766
[2025-10-07 00:50:52] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -29.590771 | E_var:     0.0638 | E_err:   0.003946
[2025-10-07 00:50:56] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -29.593805 | E_var:     0.1051 | E_err:   0.005064
[2025-10-07 00:50:59] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -29.599966 | E_var:     0.0645 | E_err:   0.003970
[2025-10-07 00:51:03] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -29.594392 | E_var:     0.0600 | E_err:   0.003826
[2025-10-07 00:51:07] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -29.600476 | E_var:     0.0657 | E_err:   0.004006
[2025-10-07 00:51:10] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -29.592239 | E_var:     0.0562 | E_err:   0.003703
[2025-10-07 00:51:10] ======================================================================================================
[2025-10-07 00:51:10] ✅ Training completed successfully
[2025-10-07 00:51:10] Total restarts: 2
[2025-10-07 00:51:11] Final Energy: -29.59223878 ± 0.00370341
[2025-10-07 00:51:11] Final Variance: 0.056178
[2025-10-07 00:51:11] ======================================================================================================
[2025-10-07 00:51:11] ======================================================================================================
[2025-10-07 00:51:11] Training completed | Runtime: 3938.6s
[2025-10-07 00:51:13] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 00:51:13] ======================================================================================================
