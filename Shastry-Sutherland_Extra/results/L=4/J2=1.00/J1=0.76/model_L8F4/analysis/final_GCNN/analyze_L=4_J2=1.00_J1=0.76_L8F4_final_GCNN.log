[2025-10-06 18:19:38] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.76/model_L8F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 18:20:00] ✓ 从checkpoint加载参数: final
[2025-10-06 18:20:00]   - 能量: -27.148452+0.000163j ± 0.005697
[2025-10-06 18:20:00] ================================================================================
[2025-10-06 18:20:00] 加载量子态: L=4, J2=1.00, J1=0.76, checkpoint=final_GCNN
[2025-10-06 18:20:00] 使用采样数目: 1048576
[2025-10-06 18:20:00] 设置样本数为: 1048576
[2025-10-06 18:20:00] 开始生成共享样本集...
[2025-10-06 18:22:48] 样本生成完成,耗时: 168.052 秒
[2025-10-06 18:22:48] ================================================================================
[2025-10-06 18:22:48] 开始计算自旋结构因子...
[2025-10-06 18:22:48] 初始化操作符缓存...
[2025-10-06 18:22:48] 预构建所有自旋相关操作符...
[2025-10-06 18:22:48] 开始计算自旋相关函数...
[2025-10-06 18:23:01] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 12.456s
[2025-10-06 18:23:16] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 15.249s
[2025-10-06 18:23:24] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.275s
[2025-10-06 18:23:32] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.250s
[2025-10-06 18:23:41] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.275s
[2025-10-06 18:23:49] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.250s
[2025-10-06 18:23:57] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.273s
[2025-10-06 18:24:06] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.273s
[2025-10-06 18:24:14] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.274s
[2025-10-06 18:24:22] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.250s
[2025-10-06 18:24:30] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.275s
[2025-10-06 18:24:39] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.275s
[2025-10-06 18:24:47] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.276s
[2025-10-06 18:24:55] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.251s
[2025-10-06 18:25:03] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.261s
[2025-10-06 18:25:12] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.265s
[2025-10-06 18:25:20] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.285s
[2025-10-06 18:25:28] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.257s
[2025-10-06 18:25:36] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.276s
[2025-10-06 18:25:45] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.259s
[2025-10-06 18:25:53] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.276s
[2025-10-06 18:26:01] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.264s
[2025-10-06 18:26:10] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.278s
[2025-10-06 18:26:18] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.286s
[2025-10-06 18:26:26] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.290s
[2025-10-06 18:26:34] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.258s
[2025-10-06 18:26:43] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.279s
[2025-10-06 18:26:51] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.275s
[2025-10-06 18:26:59] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.264s
[2025-10-06 18:27:08] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.282s
[2025-10-06 18:27:16] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.275s
[2025-10-06 18:27:24] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.277s
[2025-10-06 18:27:32] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.265s
[2025-10-06 18:27:41] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.283s
[2025-10-06 18:27:49] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.258s
[2025-10-06 18:27:57] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.282s
[2025-10-06 18:28:05] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.266s
[2025-10-06 18:28:14] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.282s
[2025-10-06 18:28:22] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.264s
[2025-10-06 18:28:30] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.266s
[2025-10-06 18:28:39] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.264s
[2025-10-06 18:28:47] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.275s
[2025-10-06 18:28:55] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.258s
[2025-10-06 18:29:03] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.273s
[2025-10-06 18:29:12] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.280s
[2025-10-06 18:29:20] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.262s
[2025-10-06 18:29:28] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.264s
[2025-10-06 18:29:36] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.275s
[2025-10-06 18:29:45] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.281s
[2025-10-06 18:29:53] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.275s
[2025-10-06 18:30:01] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.280s
[2025-10-06 18:30:10] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.277s
[2025-10-06 18:30:18] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.261s
[2025-10-06 18:30:26] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.280s
[2025-10-06 18:30:34] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.259s
[2025-10-06 18:30:43] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.259s
[2025-10-06 18:30:51] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.269s
[2025-10-06 18:30:59] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.287s
[2025-10-06 18:31:08] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.266s
[2025-10-06 18:31:16] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.267s
[2025-10-06 18:31:24] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.281s
[2025-10-06 18:31:32] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.258s
[2025-10-06 18:31:41] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.260s
[2025-10-06 18:31:49] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.257s
[2025-10-06 18:31:49] 自旋相关函数计算完成,总耗时 540.69 秒
[2025-10-06 18:31:49] 计算傅里叶变换...
[2025-10-06 18:31:55] 自旋结构因子计算完成
[2025-10-06 18:31:56] 自旋相关函数平均误差: 0.000897
