[2025-10-06 23:25:24] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/checkpoint_iter_002000.pkl
[2025-10-06 23:25:40] ✓ 从checkpoint加载参数: 2000
[2025-10-06 23:25:40]   - 能量: -27.114722-0.000119j ± 0.005882
[2025-10-06 23:25:40] ================================================================================
[2025-10-06 23:25:40] 加载量子态: L=4, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_002000
[2025-10-06 23:25:40] 使用采样数目: 1048576
[2025-10-06 23:25:40] 设置样本数为: 1048576
[2025-10-06 23:25:40] 开始生成共享样本集...
[2025-10-06 23:27:02] 样本生成完成,耗时: 82.080 秒
[2025-10-06 23:27:02] ================================================================================
[2025-10-06 23:27:02] 开始计算自旋结构因子...
[2025-10-06 23:27:02] 初始化操作符缓存...
[2025-10-06 23:27:02] 预构建所有自旋相关操作符...
[2025-10-06 23:27:02] 开始计算自旋相关函数...
[2025-10-06 23:27:09] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.113s
[2025-10-06 23:27:18] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.504s
[2025-10-06 23:27:22] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.243s
[2025-10-06 23:27:26] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.266s
[2025-10-06 23:27:31] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.280s
[2025-10-06 23:27:35] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.261s
[2025-10-06 23:27:39] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.249s
[2025-10-06 23:27:44] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.284s
[2025-10-06 23:27:48] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.311s
[2025-10-06 23:27:53] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.715s
[2025-10-06 23:27:58] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.102s
[2025-10-06 23:28:03] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.172s
[2025-10-06 23:28:08] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.166s
[2025-10-06 23:28:13] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.243s
[2025-10-06 23:28:18] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 5.101s
[2025-10-06 23:28:24] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 5.270s
[2025-10-06 23:28:29] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 5.106s
[2025-10-06 23:28:34] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.240s
[2025-10-06 23:28:39] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 5.132s
[2025-10-06 23:28:44] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 5.222s
[2025-10-06 23:28:49] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 5.145s
[2025-10-06 23:28:55] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 5.200s
[2025-10-06 23:29:00] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 5.145s
[2025-10-06 23:29:05] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 5.189s
[2025-10-06 23:29:10] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 5.176s
[2025-10-06 23:29:15] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.160s
[2025-10-06 23:29:21] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.191s
[2025-10-06 23:29:26] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.142s
[2025-10-06 23:29:31] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.198s
[2025-10-06 23:29:36] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.155s
[2025-10-06 23:29:41] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.244s
[2025-10-06 23:29:46] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.121s
[2025-10-06 23:29:52] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.241s
[2025-10-06 23:29:57] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.098s
[2025-10-06 23:30:02] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 5.241s
[2025-10-06 23:30:07] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 5.117s
[2025-10-06 23:30:12] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 5.262s
[2025-10-06 23:30:17] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 5.083s
[2025-10-06 23:30:23] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 5.244s
[2025-10-06 23:30:28] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 5.084s
[2025-10-06 23:30:33] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 5.219s
[2025-10-06 23:30:38] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 5.129s
[2025-10-06 23:30:43] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 5.228s
[2025-10-06 23:30:49] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 5.146s
[2025-10-06 23:30:54] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 5.213s
[2025-10-06 23:30:59] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 5.170s
[2025-10-06 23:31:04] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 5.172s
[2025-10-06 23:31:09] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 5.200s
[2025-10-06 23:31:14] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 5.136s
[2025-10-06 23:31:20] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 5.218s
[2025-10-06 23:31:25] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 5.106s
[2025-10-06 23:31:30] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 5.218s
[2025-10-06 23:31:35] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 5.112s
[2025-10-06 23:31:40] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 5.218s
[2025-10-06 23:31:45] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 5.063s
[2025-10-06 23:31:50] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.267s
[2025-10-06 23:31:54] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.241s
[2025-10-06 23:31:58] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.241s
[2025-10-06 23:32:02] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.241s
[2025-10-06 23:32:07] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.279s
[2025-10-06 23:32:11] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.256s
[2025-10-06 23:32:16] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 5.161s
[2025-10-06 23:32:21] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.974s
[2025-10-06 23:32:26] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 5.204s
[2025-10-06 23:32:26] 自旋相关函数计算完成,总耗时 324.03 秒
[2025-10-06 23:32:26] 计算傅里叶变换...
[2025-10-06 23:32:32] 自旋结构因子计算完成
[2025-10-06 23:32:33] 自旋相关函数平均误差: 0.000555
