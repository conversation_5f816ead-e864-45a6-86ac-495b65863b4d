[2025-10-06 23:09:15] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/checkpoint_iter_001600.pkl
[2025-10-06 23:09:31] ✓ 从checkpoint加载参数: 1600
[2025-10-06 23:09:31]   - 能量: -27.114448-0.002771j ± 0.007511
[2025-10-06 23:09:31] ================================================================================
[2025-10-06 23:09:31] 加载量子态: L=4, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_001600
[2025-10-06 23:09:31] 使用采样数目: 1048576
[2025-10-06 23:09:31] 设置样本数为: 1048576
[2025-10-06 23:09:31] 开始生成共享样本集...
[2025-10-06 23:11:57] 样本生成完成,耗时: 146.270 秒
[2025-10-06 23:11:57] ================================================================================
[2025-10-06 23:11:57] 开始计算自旋结构因子...
[2025-10-06 23:11:57] 初始化操作符缓存...
[2025-10-06 23:11:57] 预构建所有自旋相关操作符...
[2025-10-06 23:11:57] 开始计算自旋相关函数...
[2025-10-06 23:12:07] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 9.693s
[2025-10-06 23:12:19] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 12.219s
[2025-10-06 23:12:26] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 6.258s
[2025-10-06 23:12:32] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 5.982s
[2025-10-06 23:12:40] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.301s
[2025-10-06 23:12:48] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.275s
[2025-10-06 23:12:56] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.237s
[2025-10-06 23:13:05] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.267s
[2025-10-06 23:13:13] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.235s
[2025-10-06 23:13:21] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.298s
[2025-10-06 23:13:27] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 6.158s
[2025-10-06 23:13:33] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.918s
[2025-10-06 23:13:41] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.009s
[2025-10-06 23:13:49] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.088s
[2025-10-06 23:13:57] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.087s
[2025-10-06 23:14:05] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 7.989s
[2025-10-06 23:14:14] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.080s
[2025-10-06 23:14:22] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.124s
[2025-10-06 23:14:29] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 6.914s
[2025-10-06 23:14:34] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 5.765s
[2025-10-06 23:14:43] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.309s
[2025-10-06 23:14:51] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.322s
[2025-10-06 23:14:59] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.326s
[2025-10-06 23:15:08] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.375s
[2025-10-06 23:15:16] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.364s
[2025-10-06 23:15:24] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.359s
[2025-10-06 23:15:31] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 6.487s
[2025-10-06 23:15:37] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.683s
[2025-10-06 23:15:45] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.281s
[2025-10-06 23:15:53] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.237s
[2025-10-06 23:16:01] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.243s
[2025-10-06 23:16:10] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.244s
[2025-10-06 23:16:18] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.244s
[2025-10-06 23:16:25] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 6.761s
[2025-10-06 23:16:29] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.226s
[2025-10-06 23:16:33] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.228s
[2025-10-06 23:16:37] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.207s
[2025-10-06 23:16:42] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.229s
[2025-10-06 23:16:46] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.206s
[2025-10-06 23:16:50] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.239s
[2025-10-06 23:16:54] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.216s
[2025-10-06 23:16:58] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.224s
[2025-10-06 23:17:03] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.206s
[2025-10-06 23:17:07] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.242s
[2025-10-06 23:17:11] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.209s
[2025-10-06 23:17:15] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.208s
[2025-10-06 23:17:20] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.237s
[2025-10-06 23:17:24] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.205s
[2025-10-06 23:17:28] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.238s
[2025-10-06 23:17:32] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.247s
[2025-10-06 23:17:36] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.210s
[2025-10-06 23:17:41] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.239s
[2025-10-06 23:17:45] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.222s
[2025-10-06 23:17:49] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.233s
[2025-10-06 23:17:53] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.205s
[2025-10-06 23:17:58] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.250s
[2025-10-06 23:18:02] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.206s
[2025-10-06 23:18:06] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.205s
[2025-10-06 23:18:10] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.205s
[2025-10-06 23:18:15] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.241s
[2025-10-06 23:18:19] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.213s
[2025-10-06 23:18:23] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.205s
[2025-10-06 23:18:27] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.246s
[2025-10-06 23:18:31] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.218s
[2025-10-06 23:18:31] 自旋相关函数计算完成,总耗时 394.02 秒
[2025-10-06 23:18:32] 计算傅里叶变换...
[2025-10-06 23:18:38] 自旋结构因子计算完成
[2025-10-06 23:18:38] 自旋相关函数平均误差: 0.000558
