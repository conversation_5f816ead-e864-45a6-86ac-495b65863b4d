[2025-10-06 23:32:44] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/checkpoint_iter_002200.pkl
[2025-10-06 23:33:00] ✓ 从checkpoint加载参数: 2200
[2025-10-06 23:33:00]   - 能量: -27.127407+0.001422j ± 0.006063
[2025-10-06 23:33:00] ================================================================================
[2025-10-06 23:33:00] 加载量子态: L=4, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_002200
[2025-10-06 23:33:00] 使用采样数目: 1048576
[2025-10-06 23:33:00] 设置样本数为: 1048576
[2025-10-06 23:33:00] 开始生成共享样本集...
[2025-10-06 23:34:39] 样本生成完成,耗时: 99.149 秒
[2025-10-06 23:34:39] ================================================================================
[2025-10-06 23:34:39] 开始计算自旋结构因子...
[2025-10-06 23:34:39] 初始化操作符缓存...
[2025-10-06 23:34:39] 预构建所有自旋相关操作符...
[2025-10-06 23:34:39] 开始计算自旋相关函数...
[2025-10-06 23:34:47] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.706s
[2025-10-06 23:34:56] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 9.347s
[2025-10-06 23:35:01] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 5.176s
[2025-10-06 23:35:06] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 5.167s
[2025-10-06 23:35:11] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 5.135s
[2025-10-06 23:35:17] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 5.199s
[2025-10-06 23:35:22] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 5.153s
[2025-10-06 23:35:27] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 5.183s
[2025-10-06 23:35:32] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 5.155s
[2025-10-06 23:35:37] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 5.188s
[2025-10-06 23:35:42] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 5.147s
[2025-10-06 23:35:48] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 5.180s
[2025-10-06 23:35:53] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 5.126s
[2025-10-06 23:35:58] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 5.180s
[2025-10-06 23:36:03] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 5.141s
[2025-10-06 23:36:08] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 5.215s
[2025-10-06 23:36:13] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 5.182s
[2025-10-06 23:36:19] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 5.218s
[2025-10-06 23:36:24] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 5.137s
[2025-10-06 23:36:29] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 5.183s
[2025-10-06 23:36:34] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 5.163s
[2025-10-06 23:36:39] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 5.214s
[2025-10-06 23:36:45] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 5.157s
[2025-10-06 23:36:50] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 5.220s
[2025-10-06 23:36:55] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 5.150s
[2025-10-06 23:37:00] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 5.202s
[2025-10-06 23:37:05] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 5.183s
[2025-10-06 23:37:11] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 5.194s
[2025-10-06 23:37:16] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 5.181s
[2025-10-06 23:37:21] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 5.196s
[2025-10-06 23:37:26] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 5.146s
[2025-10-06 23:37:31] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 5.181s
[2025-10-06 23:37:36] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 5.210s
[2025-10-06 23:37:42] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 5.175s
[2025-10-06 23:37:47] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.895s
[2025-10-06 23:37:51] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.206s
[2025-10-06 23:37:55] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.200s
[2025-10-06 23:37:59] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.206s
[2025-10-06 23:38:03] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.196s
[2025-10-06 23:38:08] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.220s
[2025-10-06 23:38:12] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.211s
[2025-10-06 23:38:16] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.213s
[2025-10-06 23:38:20] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.203s
[2025-10-06 23:38:24] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.209s
[2025-10-06 23:38:29] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.199s
[2025-10-06 23:38:33] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.198s
[2025-10-06 23:38:37] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.213s
[2025-10-06 23:38:41] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.199s
[2025-10-06 23:38:45] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.212s
[2025-10-06 23:38:50] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.215s
[2025-10-06 23:38:54] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.198s
[2025-10-06 23:38:58] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.214s
[2025-10-06 23:39:02] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.206s
[2025-10-06 23:39:07] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.207s
[2025-10-06 23:39:11] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.198s
[2025-10-06 23:39:15] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.215s
[2025-10-06 23:39:19] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.199s
[2025-10-06 23:39:23] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.203s
[2025-10-06 23:39:28] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.199s
[2025-10-06 23:39:32] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.215s
[2025-10-06 23:39:36] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.204s
[2025-10-06 23:39:40] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.198s
[2025-10-06 23:39:44] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.215s
[2025-10-06 23:39:49] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.206s
[2025-10-06 23:39:49] 自旋相关函数计算完成,总耗时 309.77 秒
[2025-10-06 23:39:49] 计算傅里叶变换...
[2025-10-06 23:39:55] 自旋结构因子计算完成
[2025-10-06 23:39:55] 自旋相关函数平均误差: 0.000564
