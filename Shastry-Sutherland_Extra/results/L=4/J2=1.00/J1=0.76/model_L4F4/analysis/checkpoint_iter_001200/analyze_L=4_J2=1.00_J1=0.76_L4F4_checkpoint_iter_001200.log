[2025-10-06 22:56:06] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/checkpoint_iter_001200.pkl
[2025-10-06 22:56:22] ✓ 从checkpoint加载参数: 1200
[2025-10-06 22:56:22]   - 能量: -27.104231-0.000804j ± 0.006782
[2025-10-06 22:56:22] ================================================================================
[2025-10-06 22:56:22] 加载量子态: L=4, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_001200
[2025-10-06 22:56:22] 使用采样数目: 1048576
[2025-10-06 22:56:22] 设置样本数为: 1048576
[2025-10-06 22:56:22] 开始生成共享样本集...
[2025-10-06 22:57:44] 样本生成完成,耗时: 81.502 秒
[2025-10-06 22:57:44] ================================================================================
[2025-10-06 22:57:44] 开始计算自旋结构因子...
[2025-10-06 22:57:44] 初始化操作符缓存...
[2025-10-06 22:57:44] 预构建所有自旋相关操作符...
[2025-10-06 22:57:44] 开始计算自旋相关函数...
[2025-10-06 22:57:51] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 6.955s
[2025-10-06 22:57:59] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.409s
[2025-10-06 22:58:03] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.199s
[2025-10-06 22:58:07] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.207s
[2025-10-06 22:58:12] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.209s
[2025-10-06 22:58:16] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.206s
[2025-10-06 22:58:20] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.197s
[2025-10-06 22:58:24] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.213s
[2025-10-06 22:58:28] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.197s
[2025-10-06 22:58:33] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.227s
[2025-10-06 22:58:37] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.208s
[2025-10-06 22:58:41] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.211s
[2025-10-06 22:58:45] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.198s
[2025-10-06 22:58:50] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.206s
[2025-10-06 22:58:54] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.210s
[2025-10-06 22:58:58] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.206s
[2025-10-06 22:59:02] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.205s
[2025-10-06 22:59:06] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.214s
[2025-10-06 22:59:11] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.200s
[2025-10-06 22:59:15] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.217s
[2025-10-06 22:59:19] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.206s
[2025-10-06 22:59:23] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.211s
[2025-10-06 22:59:27] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.199s
[2025-10-06 22:59:32] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.217s
[2025-10-06 22:59:36] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.206s
[2025-10-06 22:59:40] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.207s
[2025-10-06 22:59:44] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.200s
[2025-10-06 22:59:48] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.222s
[2025-10-06 22:59:53] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.208s
[2025-10-06 22:59:57] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.198s
[2025-10-06 23:00:01] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.198s
[2025-10-06 23:00:05] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.207s
[2025-10-06 23:00:10] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.205s
[2025-10-06 23:00:14] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.206s
[2025-10-06 23:00:18] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.208s
[2025-10-06 23:00:22] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.207s
[2025-10-06 23:00:26] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.200s
[2025-10-06 23:00:31] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.207s
[2025-10-06 23:00:35] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.201s
[2025-10-06 23:00:39] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.211s
[2025-10-06 23:00:43] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.200s
[2025-10-06 23:00:47] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.207s
[2025-10-06 23:00:52] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.206s
[2025-10-06 23:00:56] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.208s
[2025-10-06 23:01:00] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.202s
[2025-10-06 23:01:04] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.209s
[2025-10-06 23:01:08] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.216s
[2025-10-06 23:01:13] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.209s
[2025-10-06 23:01:17] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.210s
[2025-10-06 23:01:21] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.214s
[2025-10-06 23:01:25] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.199s
[2025-10-06 23:01:30] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.218s
[2025-10-06 23:01:34] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.207s
[2025-10-06 23:01:38] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.207s
[2025-10-06 23:01:42] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.197s
[2025-10-06 23:01:46] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.220s
[2025-10-06 23:01:51] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.198s
[2025-10-06 23:01:55] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.198s
[2025-10-06 23:01:59] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.198s
[2025-10-06 23:02:03] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.213s
[2025-10-06 23:02:07] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.206s
[2025-10-06 23:02:12] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.197s
[2025-10-06 23:02:16] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.211s
[2025-10-06 23:02:20] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.205s
[2025-10-06 23:02:20] 自旋相关函数计算完成,总耗时 276.41 秒
[2025-10-06 23:02:20] 计算傅里叶变换...
[2025-10-06 23:02:26] 自旋结构因子计算完成
[2025-10-06 23:02:27] 自旋相关函数平均误差: 0.000564
