[2025-10-06 21:33:51] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.79/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 21:33:51]   - 迭代次数: final
[2025-10-06 21:33:51]   - 能量: -28.363602+0.000156j ± 0.004938, Var: 0.099878
[2025-10-06 21:33:51]   - 时间戳: 2025-10-06T21:33:30.906048+08:00
[2025-10-06 21:34:08] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 21:34:08] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 21:34:08] ======================================================================================================
[2025-10-06 21:34:08] GCNN for Shastry-Sutherland Model
[2025-10-06 21:34:08] ======================================================================================================
[2025-10-06 21:34:08] System parameters:
[2025-10-06 21:34:08]   - System size: L=4, N=64
[2025-10-06 21:34:08]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-10-06 21:34:08] ------------------------------------------------------------------------------------------------------
[2025-10-06 21:34:08] Model parameters:
[2025-10-06 21:34:08]   - Number of layers = 6
[2025-10-06 21:34:08]   - Number of features = 4
[2025-10-06 21:34:08]   - Total parameters = 20780
[2025-10-06 21:34:08] ------------------------------------------------------------------------------------------------------
[2025-10-06 21:34:08] Training parameters:
[2025-10-06 21:34:08]   - Total iterations: 1050
[2025-10-06 21:34:08]   - Annealing cycles: 3
[2025-10-06 21:34:08]   - Initial period: 150
[2025-10-06 21:34:08]   - Period multiplier: 2.0
[2025-10-06 21:34:08]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 21:34:08]   - Samples: 4096
[2025-10-06 21:34:08]   - Discarded samples: 0
[2025-10-06 21:34:08]   - Chunk size: 4096
[2025-10-06 21:34:08]   - Diagonal shift: 0.15
[2025-10-06 21:34:08]   - Gradient clipping: 1.0
[2025-10-06 21:34:08]   - Checkpoint enabled: interval=100
[2025-10-06 21:34:08]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.80/model_L6F4/training/checkpoints
[2025-10-06 21:34:08] ------------------------------------------------------------------------------------------------------
[2025-10-06 21:34:08] Device status:
[2025-10-06 21:34:08]   - Devices model: NVIDIA H200 NVL
[2025-10-06 21:34:08]   - Number of devices: 1
[2025-10-06 21:34:08]   - Sharding: True
[2025-10-06 21:34:09] ======================================================================================================
[2025-10-06 21:34:53] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -28.772073 | E_var:     0.2815 | E_err:   0.008290
[2025-10-06 21:35:19] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -28.770570 | E_var:     0.1885 | E_err:   0.006785
[2025-10-06 21:35:23] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -28.769603 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 21:35:26] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -28.782182 | E_var:     0.1061 | E_err:   0.005089
[2025-10-06 21:35:30] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -28.765958 | E_var:     0.1340 | E_err:   0.005719
[2025-10-06 21:35:34] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -28.769869 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 21:35:37] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -28.771247 | E_var:     0.1254 | E_err:   0.005532
[2025-10-06 21:35:41] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -28.774640 | E_var:     0.1212 | E_err:   0.005440
[2025-10-06 21:35:45] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -28.784167 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 21:35:48] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -28.782544 | E_var:     0.0983 | E_err:   0.004900
[2025-10-06 21:35:52] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -28.781295 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 21:35:56] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -28.769339 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 21:35:59] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -28.771791 | E_var:     0.1190 | E_err:   0.005391
[2025-10-06 21:36:03] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -28.772754 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 21:36:06] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -28.777317 | E_var:     0.0768 | E_err:   0.004329
[2025-10-06 21:36:10] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -28.776013 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 21:36:14] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -28.781358 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 21:36:17] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -28.763349 | E_var:     0.1032 | E_err:   0.005021
[2025-10-06 21:36:21] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -28.776361 | E_var:     0.1639 | E_err:   0.006326
[2025-10-06 21:36:25] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -28.764458 | E_var:     0.1047 | E_err:   0.005055
[2025-10-06 21:36:28] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -28.775474 | E_var:     0.0891 | E_err:   0.004665
[2025-10-06 21:36:32] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -28.778409 | E_var:     0.0909 | E_err:   0.004711
[2025-10-06 21:36:36] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -28.777984 | E_var:     0.0759 | E_err:   0.004303
[2025-10-06 21:36:39] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -28.776396 | E_var:     0.0805 | E_err:   0.004434
[2025-10-06 21:36:43] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -28.768941 | E_var:     0.0975 | E_err:   0.004879
[2025-10-06 21:36:47] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -28.766880 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 21:36:50] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -28.772608 | E_var:     0.0862 | E_err:   0.004589
[2025-10-06 21:36:54] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -28.774842 | E_var:     0.1055 | E_err:   0.005074
[2025-10-06 21:36:58] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -28.776551 | E_var:     0.0876 | E_err:   0.004625
[2025-10-06 21:37:01] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -28.770443 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 21:37:05] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -28.782974 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 21:37:09] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -28.775447 | E_var:     0.1137 | E_err:   0.005268
[2025-10-06 21:37:12] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -28.778191 | E_var:     0.0915 | E_err:   0.004727
[2025-10-06 21:37:16] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -28.778322 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 21:37:19] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -28.777102 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 21:37:23] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -28.768659 | E_var:     0.1432 | E_err:   0.005912
[2025-10-06 21:37:27] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -28.764955 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 21:37:30] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -28.773956 | E_var:     0.0803 | E_err:   0.004428
[2025-10-06 21:37:34] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -28.775639 | E_var:     0.0835 | E_err:   0.004515
[2025-10-06 21:37:38] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -28.778436 | E_var:     0.0888 | E_err:   0.004655
[2025-10-06 21:37:41] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -28.777111 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 21:37:45] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -28.771435 | E_var:     0.0751 | E_err:   0.004283
[2025-10-06 21:37:49] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -28.773554 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 21:37:52] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -28.772332 | E_var:     0.1123 | E_err:   0.005236
[2025-10-06 21:37:56] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -28.776089 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 21:38:00] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -28.777119 | E_var:     0.1349 | E_err:   0.005740
[2025-10-06 21:38:03] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -28.774157 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 21:38:07] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -28.774538 | E_var:     0.0734 | E_err:   0.004232
[2025-10-06 21:38:11] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -28.779737 | E_var:     0.0968 | E_err:   0.004862
[2025-10-06 21:38:14] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -28.769581 | E_var:     0.1003 | E_err:   0.004949
[2025-10-06 21:38:18] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -28.772790 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 21:38:22] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -28.777089 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 21:38:25] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -28.778739 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 21:38:29] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -28.781824 | E_var:     0.0931 | E_err:   0.004767
[2025-10-06 21:38:32] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -28.769274 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 21:38:36] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -28.777310 | E_var:     0.1136 | E_err:   0.005267
[2025-10-06 21:38:40] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -28.763676 | E_var:     0.0874 | E_err:   0.004621
[2025-10-06 21:38:43] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -28.777802 | E_var:     0.0850 | E_err:   0.004554
[2025-10-06 21:38:47] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -28.779346 | E_var:     0.0772 | E_err:   0.004341
[2025-10-06 21:38:51] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -28.781279 | E_var:     0.1054 | E_err:   0.005074
[2025-10-06 21:38:54] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -28.785248 | E_var:     0.0697 | E_err:   0.004126
[2025-10-06 21:38:58] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -28.773101 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 21:39:02] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -28.771673 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 21:39:05] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -28.777265 | E_var:     0.1021 | E_err:   0.004993
[2025-10-06 21:39:09] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -28.770546 | E_var:     0.1254 | E_err:   0.005534
[2025-10-06 21:39:13] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -28.774185 | E_var:     0.1593 | E_err:   0.006237
[2025-10-06 21:39:16] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -28.776674 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 21:39:20] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -28.773750 | E_var:     0.0891 | E_err:   0.004663
[2025-10-06 21:39:24] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -28.787987 | E_var:     0.1611 | E_err:   0.006272
[2025-10-06 21:39:27] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -28.773203 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 21:39:31] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -28.766882 | E_var:     0.0756 | E_err:   0.004296
[2025-10-06 21:39:35] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -28.767999 | E_var:     0.1572 | E_err:   0.006195
[2025-10-06 21:39:38] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -28.774475 | E_var:     0.1020 | E_err:   0.004991
[2025-10-06 21:39:42] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -28.775953 | E_var:     0.0889 | E_err:   0.004660
[2025-10-06 21:39:46] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -28.776849 | E_var:     0.0960 | E_err:   0.004841
[2025-10-06 21:39:49] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -28.772958 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 21:39:53] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -28.776887 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 21:39:56] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -28.778266 | E_var:     0.0847 | E_err:   0.004548
[2025-10-06 21:40:00] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -28.775997 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 21:40:04] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -28.773923 | E_var:     0.1355 | E_err:   0.005751
[2025-10-06 21:40:07] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -28.783261 | E_var:     0.9313 | E_err:   0.015079
[2025-10-06 21:40:11] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -28.776351 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 21:40:15] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -28.775580 | E_var:     0.1245 | E_err:   0.005513
[2025-10-06 21:40:18] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -28.775450 | E_var:     0.1045 | E_err:   0.005052
[2025-10-06 21:40:22] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -28.770319 | E_var:     0.0731 | E_err:   0.004223
[2025-10-06 21:40:26] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -28.776811 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 21:40:29] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -28.785605 | E_var:     0.1265 | E_err:   0.005558
[2025-10-06 21:40:33] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -28.767121 | E_var:     0.1219 | E_err:   0.005456
[2025-10-06 21:40:37] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -28.773743 | E_var:     0.0831 | E_err:   0.004504
[2025-10-06 21:40:40] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -28.770655 | E_var:     0.1155 | E_err:   0.005309
[2025-10-06 21:40:44] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -28.770974 | E_var:     0.0776 | E_err:   0.004354
[2025-10-06 21:40:48] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -28.775764 | E_var:     0.0876 | E_err:   0.004624
[2025-10-06 21:40:51] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -28.775878 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 21:40:55] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -28.766106 | E_var:     0.0840 | E_err:   0.004527
[2025-10-06 21:40:59] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -28.779489 | E_var:     0.0873 | E_err:   0.004616
[2025-10-06 21:41:02] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -28.776065 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 21:41:06] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -28.777875 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 21:41:10] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -28.774676 | E_var:     0.0726 | E_err:   0.004211
[2025-10-06 21:41:13] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -28.766831 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 21:41:17] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -28.770239 | E_var:     0.0829 | E_err:   0.004500
[2025-10-06 21:41:17] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 21:41:21] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -28.775331 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 21:41:24] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -28.777891 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 21:41:28] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -28.774628 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 21:41:32] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -28.780546 | E_var:     0.0868 | E_err:   0.004602
[2025-10-06 21:41:35] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -28.769381 | E_var:     0.0763 | E_err:   0.004315
[2025-10-06 21:41:39] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -28.781609 | E_var:     0.0853 | E_err:   0.004563
[2025-10-06 21:41:43] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -28.771343 | E_var:     0.0845 | E_err:   0.004543
[2025-10-06 21:41:46] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -28.772541 | E_var:     0.0978 | E_err:   0.004886
[2025-10-06 21:41:50] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -28.775866 | E_var:     0.1251 | E_err:   0.005527
[2025-10-06 21:41:53] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -28.774712 | E_var:     0.1025 | E_err:   0.005003
[2025-10-06 21:41:57] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -28.766161 | E_var:     0.1146 | E_err:   0.005289
[2025-10-06 21:42:01] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -28.771490 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 21:42:04] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -28.774361 | E_var:     0.0859 | E_err:   0.004579
[2025-10-06 21:42:08] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -28.773556 | E_var:     0.0800 | E_err:   0.004420
[2025-10-06 21:42:12] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -28.769917 | E_var:     0.0942 | E_err:   0.004796
[2025-10-06 21:42:15] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -28.771605 | E_var:     0.1462 | E_err:   0.005975
[2025-10-06 21:42:19] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -28.777520 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 21:42:23] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -28.775402 | E_var:     0.0849 | E_err:   0.004552
[2025-10-06 21:42:26] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -28.775788 | E_var:     0.0839 | E_err:   0.004527
[2025-10-06 21:42:30] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -28.770278 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 21:42:34] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -28.777371 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 21:42:37] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -28.784188 | E_var:     0.1340 | E_err:   0.005720
[2025-10-06 21:42:41] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -28.776140 | E_var:     0.1024 | E_err:   0.005001
[2025-10-06 21:42:45] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -28.766677 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 21:42:48] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -28.772045 | E_var:     0.1373 | E_err:   0.005791
[2025-10-06 21:42:52] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -28.779824 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 21:42:56] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -28.777689 | E_var:     0.0872 | E_err:   0.004615
[2025-10-06 21:42:59] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -28.770508 | E_var:     0.0692 | E_err:   0.004111
[2025-10-06 21:43:03] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -28.778845 | E_var:     0.1088 | E_err:   0.005154
[2025-10-06 21:43:07] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -28.777767 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 21:43:10] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -28.789893 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 21:43:14] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -28.776193 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 21:43:17] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -28.779084 | E_var:     0.0966 | E_err:   0.004855
[2025-10-06 21:43:21] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -28.778965 | E_var:     0.1055 | E_err:   0.005075
[2025-10-06 21:43:25] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -28.774777 | E_var:     0.0808 | E_err:   0.004443
[2025-10-06 21:43:28] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -28.778703 | E_var:     0.1033 | E_err:   0.005021
[2025-10-06 21:43:32] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -28.771181 | E_var:     0.0792 | E_err:   0.004398
[2025-10-06 21:43:36] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -28.767498 | E_var:     0.0862 | E_err:   0.004589
[2025-10-06 21:43:39] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -28.771684 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 21:43:43] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -28.773140 | E_var:     0.1005 | E_err:   0.004953
[2025-10-06 21:43:47] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -28.777873 | E_var:     0.1141 | E_err:   0.005279
[2025-10-06 21:43:50] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -28.779102 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 21:43:54] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -28.781914 | E_var:     0.1187 | E_err:   0.005383
[2025-10-06 21:43:58] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -28.775324 | E_var:     0.0806 | E_err:   0.004435
[2025-10-06 21:44:01] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -28.772880 | E_var:     0.0978 | E_err:   0.004887
[2025-10-06 21:44:05] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -28.778134 | E_var:     0.0910 | E_err:   0.004713
[2025-10-06 21:44:09] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -28.778137 | E_var:     0.0771 | E_err:   0.004337
[2025-10-06 21:44:12] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -28.778644 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 21:44:16] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -28.779618 | E_var:     0.1065 | E_err:   0.005099
[2025-10-06 21:44:20] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -28.778940 | E_var:     0.1221 | E_err:   0.005459
[2025-10-06 21:44:20] 🔄 RESTART #1 | Period: 300
[2025-10-06 21:44:23] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -28.773038 | E_var:     0.1500 | E_err:   0.006051
[2025-10-06 21:44:27] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -28.775866 | E_var:     0.0711 | E_err:   0.004166
[2025-10-06 21:44:30] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -28.785466 | E_var:     0.1130 | E_err:   0.005252
[2025-10-06 21:44:34] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -28.764430 | E_var:     0.1131 | E_err:   0.005255
[2025-10-06 21:44:38] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -28.772938 | E_var:     0.0856 | E_err:   0.004570
[2025-10-06 21:44:41] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -28.781049 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 21:44:45] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -28.776519 | E_var:     0.2458 | E_err:   0.007746
[2025-10-06 21:44:49] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -28.778360 | E_var:     0.0802 | E_err:   0.004425
[2025-10-06 21:44:52] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -28.771838 | E_var:     0.0982 | E_err:   0.004897
[2025-10-06 21:44:56] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -28.773310 | E_var:     0.1621 | E_err:   0.006291
[2025-10-06 21:45:00] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -28.770415 | E_var:     0.1487 | E_err:   0.006024
[2025-10-06 21:45:03] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -28.778734 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 21:45:07] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -28.773981 | E_var:     0.1118 | E_err:   0.005223
[2025-10-06 21:45:11] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -28.777357 | E_var:     0.0953 | E_err:   0.004823
[2025-10-06 21:45:14] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -28.777236 | E_var:     0.1247 | E_err:   0.005517
[2025-10-06 21:45:18] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -28.772750 | E_var:     0.0761 | E_err:   0.004310
[2025-10-06 21:45:22] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -28.783727 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 21:45:25] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -28.770616 | E_var:     0.1153 | E_err:   0.005305
[2025-10-06 21:45:29] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -28.781121 | E_var:     0.1021 | E_err:   0.004992
[2025-10-06 21:45:33] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -28.771287 | E_var:     0.0917 | E_err:   0.004732
[2025-10-06 21:45:36] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -28.776646 | E_var:     0.1401 | E_err:   0.005849
[2025-10-06 21:45:40] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -28.782920 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 21:45:44] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -28.774608 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 21:45:47] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -28.771492 | E_var:     0.1206 | E_err:   0.005426
[2025-10-06 21:45:51] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -28.774423 | E_var:     0.0697 | E_err:   0.004124
[2025-10-06 21:45:54] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -28.777666 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 21:45:58] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -28.768367 | E_var:     0.7875 | E_err:   0.013866
[2025-10-06 21:46:02] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -28.772384 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 21:46:05] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -28.779055 | E_var:     0.1451 | E_err:   0.005951
[2025-10-06 21:46:09] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -28.778740 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 21:46:13] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -28.778940 | E_var:     0.0837 | E_err:   0.004522
[2025-10-06 21:46:16] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -28.766225 | E_var:     0.0860 | E_err:   0.004583
[2025-10-06 21:46:20] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -28.769647 | E_var:     0.0846 | E_err:   0.004546
[2025-10-06 21:46:24] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -28.775302 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 21:46:27] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -28.777660 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 21:46:31] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -28.783220 | E_var:     0.0872 | E_err:   0.004613
[2025-10-06 21:46:35] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -28.775066 | E_var:     0.0915 | E_err:   0.004727
[2025-10-06 21:46:38] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -28.779900 | E_var:     0.0665 | E_err:   0.004030
[2025-10-06 21:46:42] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -28.779896 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 21:46:46] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -28.775710 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 21:46:49] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -28.773423 | E_var:     0.1137 | E_err:   0.005269
[2025-10-06 21:46:53] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -28.778239 | E_var:     0.0824 | E_err:   0.004486
[2025-10-06 21:46:57] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -28.783716 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 21:47:00] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -28.775735 | E_var:     0.0798 | E_err:   0.004415
[2025-10-06 21:47:04] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -28.775829 | E_var:     0.0668 | E_err:   0.004040
[2025-10-06 21:47:08] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -28.774115 | E_var:     0.0981 | E_err:   0.004895
[2025-10-06 21:47:11] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -28.774149 | E_var:     0.0872 | E_err:   0.004615
[2025-10-06 21:47:15] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -28.774680 | E_var:     0.0791 | E_err:   0.004396
[2025-10-06 21:47:18] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -28.780989 | E_var:     0.1475 | E_err:   0.006001
[2025-10-06 21:47:22] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -28.784842 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 21:47:22] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 21:47:26] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -28.777202 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 21:47:30] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -28.781496 | E_var:     0.0907 | E_err:   0.004706
[2025-10-06 21:47:33] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -28.776608 | E_var:     0.0682 | E_err:   0.004081
[2025-10-06 21:47:37] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -28.783102 | E_var:     0.0837 | E_err:   0.004519
[2025-10-06 21:47:40] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -28.767712 | E_var:     0.1973 | E_err:   0.006941
[2025-10-06 21:47:44] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -28.769656 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 21:47:48] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -28.778637 | E_var:     0.0883 | E_err:   0.004644
[2025-10-06 21:47:51] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -28.766825 | E_var:     0.1240 | E_err:   0.005502
[2025-10-06 21:47:55] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -28.773937 | E_var:     0.0752 | E_err:   0.004285
[2025-10-06 21:47:59] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -28.780062 | E_var:     0.0804 | E_err:   0.004430
[2025-10-06 21:48:02] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -28.781737 | E_var:     0.1013 | E_err:   0.004973
[2025-10-06 21:48:06] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -28.766297 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 21:48:10] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -28.772809 | E_var:     0.2220 | E_err:   0.007362
[2025-10-06 21:48:13] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -28.785372 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 21:48:17] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -28.767927 | E_var:     0.0817 | E_err:   0.004467
[2025-10-06 21:48:21] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -28.775829 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 21:48:24] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -28.779916 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 21:48:28] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -28.769301 | E_var:     0.0712 | E_err:   0.004169
[2025-10-06 21:48:32] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -28.771002 | E_var:     0.1031 | E_err:   0.005018
[2025-10-06 21:48:35] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -28.771392 | E_var:     0.1367 | E_err:   0.005777
[2025-10-06 21:48:39] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -28.776682 | E_var:     0.0879 | E_err:   0.004632
[2025-10-06 21:48:43] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -28.772423 | E_var:     0.0831 | E_err:   0.004503
[2025-10-06 21:48:46] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -28.778139 | E_var:     0.0730 | E_err:   0.004221
[2025-10-06 21:48:50] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -28.771574 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 21:48:53] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -28.780445 | E_var:     0.0697 | E_err:   0.004126
[2025-10-06 21:48:57] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -28.777563 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 21:49:01] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -28.774601 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 21:49:04] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -28.777252 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 21:49:08] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -28.780494 | E_var:     0.0941 | E_err:   0.004793
[2025-10-06 21:49:12] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -28.778725 | E_var:     0.0915 | E_err:   0.004725
[2025-10-06 21:49:15] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -28.773515 | E_var:     0.0955 | E_err:   0.004829
[2025-10-06 21:49:19] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -28.773122 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 21:49:23] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -28.766105 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 21:49:26] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -28.773142 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 21:49:30] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -28.781968 | E_var:     0.1256 | E_err:   0.005538
[2025-10-06 21:49:34] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -28.778638 | E_var:     0.0916 | E_err:   0.004728
[2025-10-06 21:49:37] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -28.775527 | E_var:     0.0770 | E_err:   0.004336
[2025-10-06 21:49:41] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -28.773691 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 21:49:45] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -28.776240 | E_var:     0.1019 | E_err:   0.004988
[2025-10-06 21:49:48] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -28.780925 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 21:49:52] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -28.772821 | E_var:     0.0820 | E_err:   0.004473
[2025-10-06 21:49:56] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -28.781283 | E_var:     0.0825 | E_err:   0.004487
[2025-10-06 21:49:59] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -28.777720 | E_var:     0.0656 | E_err:   0.004001
[2025-10-06 21:50:03] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -28.776350 | E_var:     0.0784 | E_err:   0.004374
[2025-10-06 21:50:07] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -28.769437 | E_var:     0.1105 | E_err:   0.005194
[2025-10-06 21:50:10] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -28.784070 | E_var:     0.1053 | E_err:   0.005069
[2025-10-06 21:50:14] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -28.785811 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 21:50:17] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -28.785368 | E_var:     0.0946 | E_err:   0.004805
[2025-10-06 21:50:21] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -28.772775 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 21:50:25] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -28.781474 | E_var:     0.0997 | E_err:   0.004935
[2025-10-06 21:50:28] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -28.773651 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 21:50:32] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -28.764929 | E_var:     0.0814 | E_err:   0.004459
[2025-10-06 21:50:36] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -28.780256 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 21:50:39] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -28.771875 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 21:50:43] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -28.764778 | E_var:     0.2080 | E_err:   0.007125
[2025-10-06 21:50:47] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -28.774122 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 21:50:50] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -28.770557 | E_var:     0.0948 | E_err:   0.004810
[2025-10-06 21:50:54] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -28.775702 | E_var:     0.0689 | E_err:   0.004101
[2025-10-06 21:50:58] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -28.771402 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 21:51:01] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -28.781568 | E_var:     0.0869 | E_err:   0.004605
[2025-10-06 21:51:05] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -28.774495 | E_var:     0.0873 | E_err:   0.004618
[2025-10-06 21:51:09] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -28.774952 | E_var:     0.0639 | E_err:   0.003949
[2025-10-06 21:51:12] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -28.776961 | E_var:     0.0689 | E_err:   0.004100
[2025-10-06 21:51:16] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -28.777392 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 21:51:20] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -28.776721 | E_var:     0.0921 | E_err:   0.004743
[2025-10-06 21:51:23] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -28.774297 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 21:51:27] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -28.776655 | E_var:     0.0771 | E_err:   0.004339
[2025-10-06 21:51:31] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -28.785885 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 21:51:34] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -28.769169 | E_var:     0.0921 | E_err:   0.004742
[2025-10-06 21:51:38] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -28.778435 | E_var:     0.0873 | E_err:   0.004615
[2025-10-06 21:51:41] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -28.773130 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 21:51:45] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -28.768506 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 21:51:49] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -28.773402 | E_var:     0.1190 | E_err:   0.005389
[2025-10-06 21:51:52] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -28.777487 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 21:51:56] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -28.782749 | E_var:     0.1045 | E_err:   0.005050
[2025-10-06 21:52:00] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -28.781633 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 21:52:03] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -28.766636 | E_var:     0.1303 | E_err:   0.005641
[2025-10-06 21:52:07] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -28.773980 | E_var:     0.0795 | E_err:   0.004404
[2025-10-06 21:52:11] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -28.778062 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 21:52:14] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -28.769583 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 21:52:18] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -28.764469 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 21:52:22] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -28.781908 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 21:52:25] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -28.781239 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 21:52:29] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -28.760304 | E_var:     0.2562 | E_err:   0.007909
[2025-10-06 21:52:33] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -28.776470 | E_var:     0.0906 | E_err:   0.004702
[2025-10-06 21:52:36] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -28.775310 | E_var:     0.1010 | E_err:   0.004965
[2025-10-06 21:52:40] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -28.775478 | E_var:     0.0956 | E_err:   0.004832
[2025-10-06 21:52:44] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -28.783292 | E_var:     0.1303 | E_err:   0.005640
[2025-10-06 21:52:47] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -28.773010 | E_var:     0.0899 | E_err:   0.004684
[2025-10-06 21:52:51] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -28.761433 | E_var:     0.1459 | E_err:   0.005968
[2025-10-06 21:52:55] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -28.774366 | E_var:     0.0713 | E_err:   0.004172
[2025-10-06 21:52:58] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -28.770260 | E_var:     0.0772 | E_err:   0.004340
[2025-10-06 21:53:02] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -28.779559 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 21:53:05] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -28.772133 | E_var:     0.1031 | E_err:   0.005016
[2025-10-06 21:53:09] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -28.775236 | E_var:     0.1018 | E_err:   0.004985
[2025-10-06 21:53:13] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -28.774661 | E_var:     0.1127 | E_err:   0.005246
[2025-10-06 21:53:16] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -28.777551 | E_var:     0.0743 | E_err:   0.004260
[2025-10-06 21:53:20] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -28.775766 | E_var:     0.0607 | E_err:   0.003850
[2025-10-06 21:53:24] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -28.765739 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 21:53:27] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -28.774048 | E_var:     0.0726 | E_err:   0.004209
[2025-10-06 21:53:27] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 21:53:31] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -28.774115 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 21:53:35] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -28.771825 | E_var:     0.0847 | E_err:   0.004548
[2025-10-06 21:53:38] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -28.776449 | E_var:     0.0965 | E_err:   0.004853
[2025-10-06 21:53:42] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -28.770360 | E_var:     0.1144 | E_err:   0.005285
[2025-10-06 21:53:46] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -28.777410 | E_var:     0.0819 | E_err:   0.004473
[2025-10-06 21:53:49] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -28.772526 | E_var:     0.0968 | E_err:   0.004862
[2025-10-06 21:53:53] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -28.775866 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 21:53:57] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -28.773551 | E_var:     0.0869 | E_err:   0.004607
[2025-10-06 21:54:00] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -28.775595 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 21:54:04] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -28.770276 | E_var:     0.1452 | E_err:   0.005955
[2025-10-06 21:54:08] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -28.778211 | E_var:     0.0825 | E_err:   0.004487
[2025-10-06 21:54:11] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -28.782851 | E_var:     0.0925 | E_err:   0.004751
[2025-10-06 21:54:15] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -28.766202 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 21:54:19] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -28.780167 | E_var:     0.0656 | E_err:   0.004003
[2025-10-06 21:54:22] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -28.772382 | E_var:     0.0830 | E_err:   0.004502
[2025-10-06 21:54:26] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -28.763722 | E_var:     0.0738 | E_err:   0.004245
[2025-10-06 21:54:29] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -28.773619 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 21:54:33] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -28.771703 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 21:54:37] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -28.770752 | E_var:     0.0905 | E_err:   0.004700
[2025-10-06 21:54:40] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -28.773844 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 21:54:44] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -28.766873 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 21:54:48] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -28.784749 | E_var:     0.0885 | E_err:   0.004650
[2025-10-06 21:54:51] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -28.782402 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 21:54:55] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -28.771054 | E_var:     0.0903 | E_err:   0.004696
[2025-10-06 21:54:59] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -28.773555 | E_var:     0.0874 | E_err:   0.004620
[2025-10-06 21:55:02] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -28.772958 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 21:55:06] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -28.773271 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 21:55:10] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -28.776563 | E_var:     0.0720 | E_err:   0.004193
[2025-10-06 21:55:13] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -28.768173 | E_var:     2.5664 | E_err:   0.025031
[2025-10-06 21:55:17] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -28.777569 | E_var:     0.0692 | E_err:   0.004110
[2025-10-06 21:55:21] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -28.779245 | E_var:     0.1031 | E_err:   0.005018
[2025-10-06 21:55:24] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -28.769975 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 21:55:28] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -28.775323 | E_var:     0.0870 | E_err:   0.004610
[2025-10-06 21:55:32] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -28.776992 | E_var:     0.1179 | E_err:   0.005364
[2025-10-06 21:55:35] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -28.781774 | E_var:     0.0845 | E_err:   0.004543
[2025-10-06 21:55:39] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -28.774035 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 21:55:43] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -28.774969 | E_var:     0.0956 | E_err:   0.004830
[2025-10-06 21:55:46] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -28.769513 | E_var:     0.0715 | E_err:   0.004177
[2025-10-06 21:55:50] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -28.778051 | E_var:     0.0809 | E_err:   0.004445
[2025-10-06 21:55:53] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -28.779760 | E_var:     0.0741 | E_err:   0.004254
[2025-10-06 21:55:57] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -28.773577 | E_var:     0.0730 | E_err:   0.004220
[2025-10-06 21:56:01] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -28.780162 | E_var:     0.1405 | E_err:   0.005857
[2025-10-06 21:56:04] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -28.771619 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 21:56:08] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -28.766970 | E_var:     0.1870 | E_err:   0.006757
[2025-10-06 21:56:12] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -28.774875 | E_var:     0.0727 | E_err:   0.004213
[2025-10-06 21:56:15] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -28.776602 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 21:56:19] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -28.782299 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 21:56:23] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -28.776506 | E_var:     0.0717 | E_err:   0.004185
[2025-10-06 21:56:26] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -28.772134 | E_var:     0.0793 | E_err:   0.004400
[2025-10-06 21:56:30] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -28.774196 | E_var:     0.0707 | E_err:   0.004155
[2025-10-06 21:56:34] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -28.772126 | E_var:     0.1450 | E_err:   0.005949
[2025-10-06 21:56:37] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -28.776340 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 21:56:41] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -28.769182 | E_var:     0.0983 | E_err:   0.004898
[2025-10-06 21:56:45] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -28.771546 | E_var:     0.0938 | E_err:   0.004786
[2025-10-06 21:56:48] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -28.766581 | E_var:     0.0710 | E_err:   0.004164
[2025-10-06 21:56:52] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -28.775018 | E_var:     0.1276 | E_err:   0.005582
[2025-10-06 21:56:56] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -28.781184 | E_var:     0.0915 | E_err:   0.004725
[2025-10-06 21:56:59] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -28.785155 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 21:57:03] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -28.766398 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 21:57:07] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -28.775289 | E_var:     0.0785 | E_err:   0.004379
[2025-10-06 21:57:10] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -28.774465 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 21:57:14] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -28.786150 | E_var:     0.0899 | E_err:   0.004684
[2025-10-06 21:57:17] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -28.771956 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 21:57:21] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -28.772166 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 21:57:25] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -28.765734 | E_var:     0.1072 | E_err:   0.005116
[2025-10-06 21:57:28] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -28.769551 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 21:57:32] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -28.781467 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 21:57:36] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -28.773940 | E_var:     0.0883 | E_err:   0.004642
[2025-10-06 21:57:39] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -28.765947 | E_var:     0.0759 | E_err:   0.004306
[2025-10-06 21:57:43] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -28.782470 | E_var:     0.0796 | E_err:   0.004408
[2025-10-06 21:57:47] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -28.774333 | E_var:     0.0933 | E_err:   0.004773
[2025-10-06 21:57:50] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -28.778996 | E_var:     0.0855 | E_err:   0.004569
[2025-10-06 21:57:54] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -28.779575 | E_var:     0.0836 | E_err:   0.004517
[2025-10-06 21:57:58] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -28.783968 | E_var:     0.1164 | E_err:   0.005331
[2025-10-06 21:58:01] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -28.783230 | E_var:     0.0708 | E_err:   0.004156
[2025-10-06 21:58:05] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -28.773741 | E_var:     0.1610 | E_err:   0.006269
[2025-10-06 21:58:09] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -28.776528 | E_var:     0.0697 | E_err:   0.004124
[2025-10-06 21:58:12] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -28.783188 | E_var:     0.0782 | E_err:   0.004368
[2025-10-06 21:58:16] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -28.780239 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 21:58:20] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -28.777643 | E_var:     0.0684 | E_err:   0.004085
[2025-10-06 21:58:23] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -28.777755 | E_var:     0.0770 | E_err:   0.004337
[2025-10-06 21:58:27] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -28.770348 | E_var:     0.0783 | E_err:   0.004373
[2025-10-06 21:58:31] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -28.775695 | E_var:     0.0684 | E_err:   0.004085
[2025-10-06 21:58:34] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -28.775575 | E_var:     0.0738 | E_err:   0.004246
[2025-10-06 21:58:38] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -28.775624 | E_var:     0.1120 | E_err:   0.005229
[2025-10-06 21:58:41] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -28.782140 | E_var:     0.0815 | E_err:   0.004460
[2025-10-06 21:58:45] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -28.773075 | E_var:     0.2539 | E_err:   0.007872
[2025-10-06 21:58:49] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -28.773764 | E_var:     0.1206 | E_err:   0.005426
[2025-10-06 21:58:52] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -28.777229 | E_var:     0.0942 | E_err:   0.004795
[2025-10-06 21:58:56] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -28.781537 | E_var:     0.0896 | E_err:   0.004678
[2025-10-06 21:59:00] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -28.774465 | E_var:     0.0890 | E_err:   0.004661
[2025-10-06 21:59:03] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -28.776913 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 21:59:07] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -28.780544 | E_var:     0.0999 | E_err:   0.004939
[2025-10-06 21:59:11] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -28.768124 | E_var:     0.1355 | E_err:   0.005751
[2025-10-06 21:59:14] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -28.778890 | E_var:     0.0970 | E_err:   0.004867
[2025-10-06 21:59:18] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -28.775744 | E_var:     0.0869 | E_err:   0.004606
[2025-10-06 21:59:22] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -28.779293 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 21:59:25] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -28.773500 | E_var:     0.0941 | E_err:   0.004794
[2025-10-06 21:59:29] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -28.775745 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 21:59:33] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -28.771947 | E_var:     0.0955 | E_err:   0.004829
[2025-10-06 21:59:33] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 21:59:36] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -28.776404 | E_var:     0.0772 | E_err:   0.004341
[2025-10-06 21:59:40] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -28.772007 | E_var:     0.1044 | E_err:   0.005048
[2025-10-06 21:59:44] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -28.783011 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 21:59:47] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -28.779805 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 21:59:51] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -28.777063 | E_var:     0.0610 | E_err:   0.003858
[2025-10-06 21:59:55] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -28.774812 | E_var:     0.1065 | E_err:   0.005099
[2025-10-06 21:59:58] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -28.777770 | E_var:     0.1489 | E_err:   0.006029
[2025-10-06 22:00:02] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -28.776881 | E_var:     0.0761 | E_err:   0.004312
[2025-10-06 22:00:06] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -28.767282 | E_var:     0.0803 | E_err:   0.004429
[2025-10-06 22:00:09] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -28.778183 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 22:00:13] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -28.774333 | E_var:     0.0769 | E_err:   0.004333
[2025-10-06 22:00:17] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -28.777440 | E_var:     0.0873 | E_err:   0.004618
[2025-10-06 22:00:20] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -28.761023 | E_var:     0.2597 | E_err:   0.007963
[2025-10-06 22:00:24] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -28.775846 | E_var:     0.0735 | E_err:   0.004236
[2025-10-06 22:00:27] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -28.776555 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 22:00:31] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -28.778105 | E_var:     0.0632 | E_err:   0.003927
[2025-10-06 22:00:35] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -28.777427 | E_var:     0.0771 | E_err:   0.004339
[2025-10-06 22:00:38] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -28.782684 | E_var:     0.0813 | E_err:   0.004454
[2025-10-06 22:00:42] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -28.779244 | E_var:     0.1327 | E_err:   0.005692
[2025-10-06 22:00:46] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -28.773604 | E_var:     0.0696 | E_err:   0.004121
[2025-10-06 22:00:49] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -28.774704 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 22:00:53] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -28.778073 | E_var:     0.0888 | E_err:   0.004655
[2025-10-06 22:00:57] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -28.775599 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 22:01:00] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -28.783226 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 22:01:04] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -28.775292 | E_var:     0.0862 | E_err:   0.004586
[2025-10-06 22:01:08] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -28.781298 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 22:01:11] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -28.781995 | E_var:     0.0691 | E_err:   0.004108
[2025-10-06 22:01:15] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -28.780645 | E_var:     0.0706 | E_err:   0.004151
[2025-10-06 22:01:19] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -28.775318 | E_var:     0.0875 | E_err:   0.004621
[2025-10-06 22:01:22] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -28.777706 | E_var:     0.2957 | E_err:   0.008497
[2025-10-06 22:01:26] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -28.779121 | E_var:     0.0930 | E_err:   0.004766
[2025-10-06 22:01:30] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -28.775793 | E_var:     0.0821 | E_err:   0.004478
[2025-10-06 22:01:33] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -28.777116 | E_var:     0.0826 | E_err:   0.004490
[2025-10-06 22:01:37] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -28.781126 | E_var:     0.1059 | E_err:   0.005085
[2025-10-06 22:01:41] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -28.773741 | E_var:     0.1144 | E_err:   0.005285
[2025-10-06 22:01:44] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -28.765398 | E_var:     0.2939 | E_err:   0.008470
[2025-10-06 22:01:48] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -28.768590 | E_var:     0.0760 | E_err:   0.004307
[2025-10-06 22:01:51] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -28.789524 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 22:01:55] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -28.765717 | E_var:     0.3760 | E_err:   0.009581
[2025-10-06 22:01:59] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -28.776730 | E_var:     0.0711 | E_err:   0.004166
[2025-10-06 22:02:02] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -28.777545 | E_var:     0.0780 | E_err:   0.004365
[2025-10-06 22:02:06] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -28.779312 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 22:02:10] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -28.772495 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 22:02:13] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -28.775298 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 22:02:17] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -28.777581 | E_var:     0.0816 | E_err:   0.004462
[2025-10-06 22:02:21] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -28.775792 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 22:02:24] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -28.774465 | E_var:     0.0889 | E_err:   0.004658
[2025-10-06 22:02:28] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -28.784790 | E_var:     0.0868 | E_err:   0.004605
[2025-10-06 22:02:32] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -28.782103 | E_var:     0.1011 | E_err:   0.004968
[2025-10-06 22:02:35] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -28.766611 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 22:02:35] 🔄 RESTART #2 | Period: 600
[2025-10-06 22:02:39] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -28.777650 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 22:02:43] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -28.783277 | E_var:     0.2651 | E_err:   0.008046
[2025-10-06 22:02:46] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -28.778132 | E_var:     0.0797 | E_err:   0.004410
[2025-10-06 22:02:50] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -28.776830 | E_var:     0.0774 | E_err:   0.004347
[2025-10-06 22:02:54] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -28.780818 | E_var:     0.1497 | E_err:   0.006046
[2025-10-06 22:02:57] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -28.780263 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 22:03:01] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -28.777527 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 22:03:05] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -28.781859 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 22:03:08] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -28.780987 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 22:03:12] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -28.777770 | E_var:     0.0970 | E_err:   0.004866
[2025-10-06 22:03:15] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -28.777306 | E_var:     0.0771 | E_err:   0.004339
[2025-10-06 22:03:19] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -28.776421 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 22:03:23] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -28.770540 | E_var:     0.0815 | E_err:   0.004459
[2025-10-06 22:03:26] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -28.770156 | E_var:     0.0917 | E_err:   0.004732
[2025-10-06 22:03:30] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -28.780079 | E_var:     0.1065 | E_err:   0.005100
[2025-10-06 22:03:34] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -28.776667 | E_var:     0.1290 | E_err:   0.005613
[2025-10-06 22:03:37] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -28.780661 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 22:03:41] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -28.775432 | E_var:     0.0774 | E_err:   0.004348
[2025-10-06 22:03:45] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -28.775503 | E_var:     0.0778 | E_err:   0.004360
[2025-10-06 22:03:48] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -28.776853 | E_var:     0.0855 | E_err:   0.004569
[2025-10-06 22:03:52] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -28.772654 | E_var:     0.0736 | E_err:   0.004238
[2025-10-06 22:03:56] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -28.771468 | E_var:     0.0851 | E_err:   0.004557
[2025-10-06 22:03:59] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -28.778229 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 22:04:03] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -28.770766 | E_var:     0.0819 | E_err:   0.004470
[2025-10-06 22:04:07] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -28.774973 | E_var:     0.0911 | E_err:   0.004716
[2025-10-06 22:04:10] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -28.780786 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 22:04:14] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -28.779381 | E_var:     0.1208 | E_err:   0.005431
[2025-10-06 22:04:18] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -28.772105 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 22:04:21] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -28.773990 | E_var:     0.0754 | E_err:   0.004292
[2025-10-06 22:04:25] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -28.773135 | E_var:     0.0771 | E_err:   0.004338
[2025-10-06 22:04:29] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -28.783807 | E_var:     0.0905 | E_err:   0.004701
[2025-10-06 22:04:32] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -28.778988 | E_var:     0.0841 | E_err:   0.004530
[2025-10-06 22:04:36] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -28.776184 | E_var:     0.0861 | E_err:   0.004585
[2025-10-06 22:04:39] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -28.784100 | E_var:     0.0995 | E_err:   0.004930
[2025-10-06 22:04:43] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -28.770548 | E_var:     0.1088 | E_err:   0.005155
[2025-10-06 22:04:47] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -28.770324 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 22:04:50] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -28.779557 | E_var:     0.0990 | E_err:   0.004916
[2025-10-06 22:04:54] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -28.772275 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 22:04:58] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -28.781029 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 22:05:01] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -28.768615 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 22:05:05] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -28.774676 | E_var:     0.1539 | E_err:   0.006130
[2025-10-06 22:05:09] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -28.774114 | E_var:     0.1405 | E_err:   0.005856
[2025-10-06 22:05:12] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -28.775977 | E_var:     0.1186 | E_err:   0.005381
[2025-10-06 22:05:16] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -28.776600 | E_var:     0.0718 | E_err:   0.004186
[2025-10-06 22:05:20] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -28.771203 | E_var:     0.0744 | E_err:   0.004261
[2025-10-06 22:05:23] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -28.770417 | E_var:     0.0790 | E_err:   0.004393
[2025-10-06 22:05:27] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -28.778387 | E_var:     0.0952 | E_err:   0.004820
[2025-10-06 22:05:31] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -28.772366 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 22:05:34] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -28.770284 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 22:05:38] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -28.773115 | E_var:     0.1033 | E_err:   0.005022
[2025-10-06 22:05:38] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 22:05:42] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -28.779456 | E_var:     0.0889 | E_err:   0.004658
[2025-10-06 22:05:45] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -28.773856 | E_var:     0.0786 | E_err:   0.004382
[2025-10-06 22:05:49] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -28.776133 | E_var:     0.1095 | E_err:   0.005172
[2025-10-06 22:05:53] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -28.777267 | E_var:     0.0944 | E_err:   0.004800
[2025-10-06 22:05:56] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -28.773817 | E_var:     0.1145 | E_err:   0.005287
[2025-10-06 22:06:00] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -28.773734 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 22:06:04] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -28.771866 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 22:06:07] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -28.778029 | E_var:     0.0803 | E_err:   0.004429
[2025-10-06 22:06:11] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -28.773120 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 22:06:14] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -28.771987 | E_var:     0.1456 | E_err:   0.005961
[2025-10-06 22:06:18] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -28.777082 | E_var:     0.1794 | E_err:   0.006618
[2025-10-06 22:06:22] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -28.779682 | E_var:     0.1111 | E_err:   0.005209
[2025-10-06 22:06:25] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -28.774349 | E_var:     0.0899 | E_err:   0.004686
[2025-10-06 22:06:29] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -28.777239 | E_var:     0.1634 | E_err:   0.006315
[2025-10-06 22:06:33] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -28.776043 | E_var:     0.0903 | E_err:   0.004696
[2025-10-06 22:06:36] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -28.776715 | E_var:     0.1645 | E_err:   0.006336
[2025-10-06 22:06:40] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -28.771072 | E_var:     0.0851 | E_err:   0.004558
[2025-10-06 22:06:44] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -28.767919 | E_var:     0.0863 | E_err:   0.004591
[2025-10-06 22:06:47] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -28.777744 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 22:06:51] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -28.781035 | E_var:     0.1149 | E_err:   0.005297
[2025-10-06 22:06:55] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -28.776834 | E_var:     0.0918 | E_err:   0.004734
[2025-10-06 22:06:58] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -28.773410 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 22:07:02] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -28.777475 | E_var:     0.0752 | E_err:   0.004284
[2025-10-06 22:07:06] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -28.779558 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 22:07:09] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -28.782736 | E_var:     0.1115 | E_err:   0.005219
[2025-10-06 22:07:13] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -28.779605 | E_var:     0.1542 | E_err:   0.006136
[2025-10-06 22:07:17] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -28.780303 | E_var:     0.1070 | E_err:   0.005110
[2025-10-06 22:07:20] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -28.775774 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 22:07:24] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -28.786791 | E_var:     0.0812 | E_err:   0.004453
[2025-10-06 22:07:28] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -28.774132 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 22:07:31] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -28.770562 | E_var:     0.0721 | E_err:   0.004195
[2025-10-06 22:07:35] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -28.782737 | E_var:     0.1288 | E_err:   0.005608
[2025-10-06 22:07:38] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -28.771900 | E_var:     0.0890 | E_err:   0.004661
[2025-10-06 22:07:42] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -28.778903 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 22:07:46] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -28.776960 | E_var:     0.0627 | E_err:   0.003911
[2025-10-06 22:07:49] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -28.774560 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 22:07:53] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -28.779126 | E_var:     0.0768 | E_err:   0.004329
[2025-10-06 22:07:57] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -28.776266 | E_var:     0.1176 | E_err:   0.005357
[2025-10-06 22:08:00] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -28.772683 | E_var:     0.0670 | E_err:   0.004044
[2025-10-06 22:08:04] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -28.778991 | E_var:     0.1062 | E_err:   0.005091
[2025-10-06 22:08:08] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -28.773673 | E_var:     0.0951 | E_err:   0.004817
[2025-10-06 22:08:11] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -28.773864 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 22:08:15] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -28.773230 | E_var:     0.0725 | E_err:   0.004207
[2025-10-06 22:08:19] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -28.777817 | E_var:     0.0714 | E_err:   0.004176
[2025-10-06 22:08:22] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -28.774762 | E_var:     0.0787 | E_err:   0.004382
[2025-10-06 22:08:26] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -28.774780 | E_var:     0.0779 | E_err:   0.004360
[2025-10-06 22:08:30] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -28.776546 | E_var:     0.1300 | E_err:   0.005633
[2025-10-06 22:08:33] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -28.777319 | E_var:     0.1503 | E_err:   0.006057
[2025-10-06 22:08:37] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -28.770840 | E_var:     0.0707 | E_err:   0.004154
[2025-10-06 22:08:41] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -28.773241 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 22:08:44] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -28.776287 | E_var:     0.0913 | E_err:   0.004721
[2025-10-06 22:08:48] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -28.772642 | E_var:     0.0823 | E_err:   0.004482
[2025-10-06 22:08:52] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -28.779359 | E_var:     0.0660 | E_err:   0.004016
[2025-10-06 22:08:55] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -28.778213 | E_var:     0.0723 | E_err:   0.004202
[2025-10-06 22:08:59] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -28.776987 | E_var:     0.0867 | E_err:   0.004602
[2025-10-06 22:09:02] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -28.773462 | E_var:     0.2237 | E_err:   0.007390
[2025-10-06 22:09:06] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -28.769982 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 22:09:10] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -28.775687 | E_var:     0.0797 | E_err:   0.004410
[2025-10-06 22:09:13] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -28.769701 | E_var:     0.0776 | E_err:   0.004352
[2025-10-06 22:09:17] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -28.771793 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 22:09:21] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -28.776532 | E_var:     0.1119 | E_err:   0.005226
[2025-10-06 22:09:24] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -28.774777 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 22:09:28] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -28.772892 | E_var:     0.0725 | E_err:   0.004206
[2025-10-06 22:09:32] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -28.772099 | E_var:     0.0736 | E_err:   0.004239
[2025-10-06 22:09:35] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -28.777271 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 22:09:39] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -28.780721 | E_var:     0.0770 | E_err:   0.004336
[2025-10-06 22:09:43] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -28.778854 | E_var:     0.0780 | E_err:   0.004362
[2025-10-06 22:09:46] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -28.768065 | E_var:     0.0824 | E_err:   0.004486
[2025-10-06 22:09:50] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -28.772470 | E_var:     0.0731 | E_err:   0.004225
[2025-10-06 22:09:54] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -28.771169 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 22:09:57] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -28.779915 | E_var:     0.0891 | E_err:   0.004663
[2025-10-06 22:10:01] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -28.771146 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 22:10:05] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -28.774772 | E_var:     0.0765 | E_err:   0.004320
[2025-10-06 22:10:08] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -28.777929 | E_var:     0.0863 | E_err:   0.004589
[2025-10-06 22:10:12] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -28.783005 | E_var:     0.0757 | E_err:   0.004298
[2025-10-06 22:10:16] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -28.787228 | E_var:     0.1102 | E_err:   0.005187
[2025-10-06 22:10:19] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -28.774387 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 22:10:23] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -28.771106 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 22:10:27] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -28.775886 | E_var:     0.0650 | E_err:   0.003983
[2025-10-06 22:10:30] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -28.774680 | E_var:     0.0904 | E_err:   0.004699
[2025-10-06 22:10:34] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -28.768601 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 22:10:38] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -28.782826 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 22:10:41] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -28.779642 | E_var:     0.0837 | E_err:   0.004520
[2025-10-06 22:10:45] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -28.778806 | E_var:     0.0775 | E_err:   0.004351
[2025-10-06 22:10:48] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -28.779827 | E_var:     0.0941 | E_err:   0.004792
[2025-10-06 22:10:52] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -28.787679 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 22:10:56] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -28.776918 | E_var:     0.0935 | E_err:   0.004777
[2025-10-06 22:10:59] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -28.766664 | E_var:     0.0754 | E_err:   0.004289
[2025-10-06 22:11:03] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -28.782875 | E_var:     0.0762 | E_err:   0.004314
[2025-10-06 22:11:07] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -28.776282 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 22:11:10] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -28.781420 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 22:11:14] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -28.778675 | E_var:     0.0808 | E_err:   0.004441
[2025-10-06 22:11:18] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -28.783599 | E_var:     0.0920 | E_err:   0.004739
[2025-10-06 22:11:21] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -28.786364 | E_var:     0.3914 | E_err:   0.009776
[2025-10-06 22:11:25] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -28.775635 | E_var:     0.1201 | E_err:   0.005415
[2025-10-06 22:11:29] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -28.773848 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 22:11:32] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -28.780549 | E_var:     0.0775 | E_err:   0.004349
[2025-10-06 22:11:36] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -28.774908 | E_var:     0.0934 | E_err:   0.004774
[2025-10-06 22:11:40] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -28.777459 | E_var:     0.0976 | E_err:   0.004881
[2025-10-06 22:11:43] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -28.788076 | E_var:     0.1366 | E_err:   0.005775
[2025-10-06 22:11:43] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 22:11:47] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -28.780430 | E_var:     0.0761 | E_err:   0.004311
[2025-10-06 22:11:51] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -28.774211 | E_var:     0.1084 | E_err:   0.005144
[2025-10-06 22:11:54] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -28.777006 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 22:11:58] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -28.773979 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 22:12:02] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -28.782278 | E_var:     0.0992 | E_err:   0.004922
[2025-10-06 22:12:05] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -28.783394 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 22:12:09] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -28.773988 | E_var:     0.0721 | E_err:   0.004196
[2025-10-06 22:12:13] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -28.782712 | E_var:     0.0776 | E_err:   0.004354
[2025-10-06 22:12:16] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -28.779943 | E_var:     0.1474 | E_err:   0.005999
[2025-10-06 22:12:20] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -28.773713 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 22:12:24] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -28.777049 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 22:12:27] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -28.776822 | E_var:     0.0790 | E_err:   0.004392
[2025-10-06 22:12:31] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -28.778535 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 22:12:35] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -28.772871 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 22:12:38] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -28.778501 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 22:12:42] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -28.782660 | E_var:     0.0964 | E_err:   0.004850
[2025-10-06 22:12:46] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -28.780941 | E_var:     0.0721 | E_err:   0.004195
[2025-10-06 22:12:49] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -28.779842 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 22:12:53] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -28.776929 | E_var:     0.0998 | E_err:   0.004937
[2025-10-06 22:12:57] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -28.783586 | E_var:     0.0725 | E_err:   0.004206
[2025-10-06 22:13:00] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -28.774962 | E_var:     0.1146 | E_err:   0.005289
[2025-10-06 22:13:04] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -28.776985 | E_var:     0.0851 | E_err:   0.004559
[2025-10-06 22:13:08] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -28.784358 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 22:13:11] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -28.775693 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 22:13:15] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -28.775764 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 22:13:18] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -28.771885 | E_var:     0.0677 | E_err:   0.004064
[2025-10-06 22:13:22] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -28.779158 | E_var:     0.1489 | E_err:   0.006029
[2025-10-06 22:13:26] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -28.774120 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 22:13:29] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -28.769605 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 22:13:33] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -28.779430 | E_var:     0.0701 | E_err:   0.004137
[2025-10-06 22:13:37] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -28.771477 | E_var:     0.1032 | E_err:   0.005019
[2025-10-06 22:13:40] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -28.783422 | E_var:     0.0727 | E_err:   0.004214
[2025-10-06 22:13:44] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -28.771018 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 22:13:48] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -28.778762 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 22:13:51] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -28.774294 | E_var:     0.0896 | E_err:   0.004676
[2025-10-06 22:13:55] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -28.775848 | E_var:     0.0885 | E_err:   0.004648
[2025-10-06 22:13:59] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -28.778168 | E_var:     0.1070 | E_err:   0.005110
[2025-10-06 22:14:02] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -28.781939 | E_var:     0.0731 | E_err:   0.004223
[2025-10-06 22:14:06] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -28.777091 | E_var:     0.0867 | E_err:   0.004601
[2025-10-06 22:14:10] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -28.772327 | E_var:     0.1215 | E_err:   0.005446
[2025-10-06 22:14:13] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -28.768203 | E_var:     0.1721 | E_err:   0.006482
[2025-10-06 22:14:17] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -28.782447 | E_var:     0.0828 | E_err:   0.004495
[2025-10-06 22:14:21] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -28.782615 | E_var:     0.0927 | E_err:   0.004757
[2025-10-06 22:14:24] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -28.766984 | E_var:     0.0959 | E_err:   0.004839
[2025-10-06 22:14:28] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -28.778016 | E_var:     0.1208 | E_err:   0.005432
[2025-10-06 22:14:32] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -28.773439 | E_var:     0.0846 | E_err:   0.004546
[2025-10-06 22:14:35] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -28.778459 | E_var:     0.0692 | E_err:   0.004109
[2025-10-06 22:14:39] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -28.776915 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 22:14:43] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -28.779043 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 22:14:46] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -28.765744 | E_var:     0.2884 | E_err:   0.008391
[2025-10-06 22:14:50] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -28.778999 | E_var:     0.1165 | E_err:   0.005333
[2025-10-06 22:14:54] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -28.785305 | E_var:     0.0813 | E_err:   0.004456
[2025-10-06 22:14:57] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -28.779242 | E_var:     0.0720 | E_err:   0.004193
[2025-10-06 22:15:01] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -28.776445 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 22:15:04] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -28.768322 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 22:15:08] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -28.782359 | E_var:     0.0989 | E_err:   0.004914
[2025-10-06 22:15:12] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -28.783722 | E_var:     0.0806 | E_err:   0.004435
[2025-10-06 22:15:15] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -28.781881 | E_var:     0.0713 | E_err:   0.004172
[2025-10-06 22:15:19] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -28.776584 | E_var:     0.1037 | E_err:   0.005032
[2025-10-06 22:15:23] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -28.772691 | E_var:     0.0963 | E_err:   0.004849
[2025-10-06 22:15:26] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -28.780136 | E_var:     0.0757 | E_err:   0.004298
[2025-10-06 22:15:30] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -28.775029 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 22:15:34] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -28.769055 | E_var:     0.0910 | E_err:   0.004712
[2025-10-06 22:15:37] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -28.786269 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 22:15:41] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -28.774556 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 22:15:45] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -28.776805 | E_var:     0.0932 | E_err:   0.004769
[2025-10-06 22:15:48] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -28.768668 | E_var:     0.0848 | E_err:   0.004551
[2025-10-06 22:15:52] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -28.777347 | E_var:     0.0670 | E_err:   0.004046
[2025-10-06 22:15:56] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -28.778370 | E_var:     0.0836 | E_err:   0.004518
[2025-10-06 22:15:59] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -28.780610 | E_var:     0.1306 | E_err:   0.005648
[2025-10-06 22:16:03] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -28.776765 | E_var:     0.0707 | E_err:   0.004153
[2025-10-06 22:16:07] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -28.781056 | E_var:     0.0865 | E_err:   0.004594
[2025-10-06 22:16:10] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -28.778349 | E_var:     0.1396 | E_err:   0.005838
[2025-10-06 22:16:14] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -28.772006 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 22:16:18] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -28.774267 | E_var:     0.0967 | E_err:   0.004858
[2025-10-06 22:16:21] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -28.780348 | E_var:     0.0919 | E_err:   0.004737
[2025-10-06 22:16:25] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -28.780699 | E_var:     0.0738 | E_err:   0.004245
[2025-10-06 22:16:28] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -28.772318 | E_var:     0.0655 | E_err:   0.004000
[2025-10-06 22:16:32] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -28.774268 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 22:16:36] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -28.778458 | E_var:     0.0725 | E_err:   0.004208
[2025-10-06 22:16:39] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -28.783565 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 22:16:43] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -28.782718 | E_var:     0.0769 | E_err:   0.004334
[2025-10-06 22:16:47] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -28.783596 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 22:16:50] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -28.775465 | E_var:     0.0732 | E_err:   0.004226
[2025-10-06 22:16:54] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -28.774952 | E_var:     0.0737 | E_err:   0.004241
[2025-10-06 22:16:58] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -28.776277 | E_var:     0.0832 | E_err:   0.004508
[2025-10-06 22:17:01] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -28.768377 | E_var:     0.1027 | E_err:   0.005006
[2025-10-06 22:17:05] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -28.779162 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 22:17:09] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -28.774612 | E_var:     0.0840 | E_err:   0.004528
[2025-10-06 22:17:12] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -28.772611 | E_var:     0.0651 | E_err:   0.003986
[2025-10-06 22:17:16] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -28.780410 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 22:17:20] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -28.781011 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 22:17:23] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -28.773735 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 22:17:27] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -28.778200 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 22:17:31] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -28.779209 | E_var:     0.0754 | E_err:   0.004290
[2025-10-06 22:17:34] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -28.782268 | E_var:     0.0813 | E_err:   0.004455
[2025-10-06 22:17:38] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -28.774189 | E_var:     0.0733 | E_err:   0.004230
[2025-10-06 22:17:42] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -28.780123 | E_var:     0.0719 | E_err:   0.004188
[2025-10-06 22:17:45] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -28.778233 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 22:17:49] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -28.784766 | E_var:     0.0798 | E_err:   0.004415
[2025-10-06 22:17:49] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 22:17:53] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -28.775830 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 22:17:56] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -28.769400 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 22:18:00] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -28.777625 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 22:18:04] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -28.775976 | E_var:     0.0979 | E_err:   0.004889
[2025-10-06 22:18:07] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -28.775199 | E_var:     0.0724 | E_err:   0.004205
[2025-10-06 22:18:11] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -28.769228 | E_var:     0.0818 | E_err:   0.004469
[2025-10-06 22:18:15] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -28.781095 | E_var:     0.0752 | E_err:   0.004285
[2025-10-06 22:18:18] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -28.780055 | E_var:     0.0889 | E_err:   0.004658
[2025-10-06 22:18:22] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -28.775695 | E_var:     0.0706 | E_err:   0.004150
[2025-10-06 22:18:25] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -28.772166 | E_var:     0.0722 | E_err:   0.004197
[2025-10-06 22:18:29] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -28.771076 | E_var:     0.0839 | E_err:   0.004525
[2025-10-06 22:18:33] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -28.771048 | E_var:     0.0804 | E_err:   0.004430
[2025-10-06 22:18:36] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -28.769640 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 22:18:40] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -28.779424 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 22:18:44] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -28.773351 | E_var:     0.0905 | E_err:   0.004699
[2025-10-06 22:18:47] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -28.770612 | E_var:     0.0742 | E_err:   0.004255
[2025-10-06 22:18:51] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -28.770959 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 22:18:55] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -28.776335 | E_var:     0.0844 | E_err:   0.004538
[2025-10-06 22:18:58] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -28.775853 | E_var:     0.0964 | E_err:   0.004851
[2025-10-06 22:19:02] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -28.775962 | E_var:     0.0894 | E_err:   0.004672
[2025-10-06 22:19:06] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -28.771760 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 22:19:09] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -28.774556 | E_var:     0.0835 | E_err:   0.004515
[2025-10-06 22:19:13] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -28.771691 | E_var:     0.0928 | E_err:   0.004761
[2025-10-06 22:19:17] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -28.774000 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 22:19:20] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -28.770816 | E_var:     0.0710 | E_err:   0.004162
[2025-10-06 22:19:24] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -28.774120 | E_var:     0.1209 | E_err:   0.005432
[2025-10-06 22:19:28] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -28.776155 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 22:19:31] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -28.775508 | E_var:     0.1061 | E_err:   0.005089
[2025-10-06 22:19:35] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -28.773508 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 22:19:38] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -28.775312 | E_var:     0.0992 | E_err:   0.004922
[2025-10-06 22:19:42] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -28.782132 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 22:19:46] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -28.778243 | E_var:     0.0823 | E_err:   0.004481
[2025-10-06 22:19:49] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -28.776579 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 22:19:53] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -28.777602 | E_var:     0.0698 | E_err:   0.004129
[2025-10-06 22:19:57] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -28.783010 | E_var:     0.1070 | E_err:   0.005110
[2025-10-06 22:20:00] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -28.780078 | E_var:     0.0972 | E_err:   0.004872
[2025-10-06 22:20:04] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -28.776408 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 22:20:08] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -28.783167 | E_var:     0.0674 | E_err:   0.004056
[2025-10-06 22:20:11] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -28.771698 | E_var:     0.0851 | E_err:   0.004558
[2025-10-06 22:20:15] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -28.782059 | E_var:     0.0930 | E_err:   0.004765
[2025-10-06 22:20:19] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -28.773627 | E_var:     0.0689 | E_err:   0.004101
[2025-10-06 22:20:22] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -28.777748 | E_var:     0.0899 | E_err:   0.004686
[2025-10-06 22:20:26] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -28.772197 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 22:20:30] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -28.780137 | E_var:     0.6345 | E_err:   0.012447
[2025-10-06 22:20:33] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -28.776457 | E_var:     0.0779 | E_err:   0.004362
[2025-10-06 22:20:37] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -28.778187 | E_var:     0.0665 | E_err:   0.004030
[2025-10-06 22:20:41] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -28.774856 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 22:20:44] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -28.768359 | E_var:     0.1146 | E_err:   0.005289
[2025-10-06 22:20:48] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -28.780309 | E_var:     0.0962 | E_err:   0.004847
[2025-10-06 22:20:51] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -28.770799 | E_var:     0.1305 | E_err:   0.005644
[2025-10-06 22:20:55] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -28.777102 | E_var:     0.1083 | E_err:   0.005143
[2025-10-06 22:20:59] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -28.777002 | E_var:     0.1040 | E_err:   0.005039
[2025-10-06 22:21:02] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -28.771435 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 22:21:06] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -28.776642 | E_var:     0.0726 | E_err:   0.004211
[2025-10-06 22:21:10] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -28.774622 | E_var:     0.0886 | E_err:   0.004652
[2025-10-06 22:21:13] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -28.771836 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 22:21:17] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -28.782940 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 22:21:21] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -28.775583 | E_var:     0.0902 | E_err:   0.004694
[2025-10-06 22:21:24] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -28.777073 | E_var:     0.0851 | E_err:   0.004557
[2025-10-06 22:21:28] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -28.774142 | E_var:     0.0876 | E_err:   0.004626
[2025-10-06 22:21:32] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -28.774338 | E_var:     0.1077 | E_err:   0.005129
[2025-10-06 22:21:35] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -28.771714 | E_var:     0.1280 | E_err:   0.005591
[2025-10-06 22:21:39] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -28.773539 | E_var:     0.1054 | E_err:   0.005072
[2025-10-06 22:21:43] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -28.775749 | E_var:     0.0716 | E_err:   0.004180
[2025-10-06 22:21:46] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -28.771630 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 22:21:50] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -28.770427 | E_var:     0.0876 | E_err:   0.004624
[2025-10-06 22:21:54] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -28.770569 | E_var:     0.1035 | E_err:   0.005028
[2025-10-06 22:21:57] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -28.780146 | E_var:     0.0945 | E_err:   0.004804
[2025-10-06 22:22:01] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -28.772311 | E_var:     0.0674 | E_err:   0.004055
[2025-10-06 22:22:05] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -28.778868 | E_var:     0.0748 | E_err:   0.004275
[2025-10-06 22:22:08] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -28.776431 | E_var:     0.0857 | E_err:   0.004573
[2025-10-06 22:22:12] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -28.773115 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 22:22:15] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -28.771429 | E_var:     0.0815 | E_err:   0.004462
[2025-10-06 22:22:19] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -28.777019 | E_var:     0.1536 | E_err:   0.006124
[2025-10-06 22:22:23] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -28.776862 | E_var:     0.1189 | E_err:   0.005387
[2025-10-06 22:22:26] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -28.773970 | E_var:     0.1156 | E_err:   0.005314
[2025-10-06 22:22:30] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -28.769058 | E_var:     0.1381 | E_err:   0.005807
[2025-10-06 22:22:34] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -28.775370 | E_var:     0.0838 | E_err:   0.004524
[2025-10-06 22:22:37] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -28.777308 | E_var:     0.1102 | E_err:   0.005187
[2025-10-06 22:22:41] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -28.779735 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 22:22:45] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -28.771671 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 22:22:48] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -28.780159 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 22:22:52] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -28.786893 | E_var:     0.0842 | E_err:   0.004534
[2025-10-06 22:22:56] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -28.774707 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 22:22:59] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -28.779737 | E_var:     0.0823 | E_err:   0.004481
[2025-10-06 22:23:03] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -28.772654 | E_var:     0.1256 | E_err:   0.005538
[2025-10-06 22:23:07] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -28.769129 | E_var:     0.2242 | E_err:   0.007398
[2025-10-06 22:23:11] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -28.777936 | E_var:     0.1219 | E_err:   0.005454
[2025-10-06 22:23:14] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -28.778562 | E_var:     0.0751 | E_err:   0.004281
[2025-10-06 22:23:18] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -28.779783 | E_var:     0.2373 | E_err:   0.007611
[2025-10-06 22:23:22] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -28.780381 | E_var:     0.1138 | E_err:   0.005271
[2025-10-06 22:23:25] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -28.770116 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 22:23:29] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -28.769887 | E_var:     0.0804 | E_err:   0.004429
[2025-10-06 22:23:33] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -28.782379 | E_var:     0.1091 | E_err:   0.005162
[2025-10-06 22:23:36] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -28.772242 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 22:23:40] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -28.780747 | E_var:     0.0910 | E_err:   0.004714
[2025-10-06 22:23:44] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -28.780261 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 22:23:47] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -28.785404 | E_var:     0.0983 | E_err:   0.004898
[2025-10-06 22:23:51] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -28.780308 | E_var:     0.0919 | E_err:   0.004736
[2025-10-06 22:23:54] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -28.773554 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 22:23:55] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 22:23:58] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -28.771728 | E_var:     0.0706 | E_err:   0.004150
[2025-10-06 22:24:02] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -28.769042 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 22:24:05] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -28.774518 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 22:24:09] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -28.769898 | E_var:     0.1155 | E_err:   0.005310
[2025-10-06 22:24:13] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -28.770020 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 22:24:16] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -28.777819 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 22:24:20] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -28.780020 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 22:24:24] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -28.778933 | E_var:     0.1039 | E_err:   0.005037
[2025-10-06 22:24:27] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -28.776359 | E_var:     0.0809 | E_err:   0.004443
[2025-10-06 22:24:31] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -28.775919 | E_var:     0.0675 | E_err:   0.004060
[2025-10-06 22:24:35] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -28.774669 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 22:24:38] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -28.782398 | E_var:     0.0729 | E_err:   0.004220
[2025-10-06 22:24:42] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -28.779493 | E_var:     0.0819 | E_err:   0.004472
[2025-10-06 22:24:46] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -28.778250 | E_var:     0.0851 | E_err:   0.004558
[2025-10-06 22:24:49] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -28.778797 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 22:24:53] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -28.776168 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 22:24:57] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -28.774695 | E_var:     0.0768 | E_err:   0.004331
[2025-10-06 22:25:00] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -28.778318 | E_var:     0.0940 | E_err:   0.004789
[2025-10-06 22:25:04] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -28.770040 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 22:25:08] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -28.774417 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 22:25:11] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -28.776908 | E_var:     0.0640 | E_err:   0.003953
[2025-10-06 22:25:15] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -28.774935 | E_var:     0.0653 | E_err:   0.003993
[2025-10-06 22:25:19] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -28.778864 | E_var:     0.0724 | E_err:   0.004203
[2025-10-06 22:25:22] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -28.779776 | E_var:     0.0943 | E_err:   0.004797
[2025-10-06 22:25:26] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -28.784178 | E_var:     0.0886 | E_err:   0.004650
[2025-10-06 22:25:29] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -28.775769 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 22:25:33] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -28.769431 | E_var:     0.1124 | E_err:   0.005239
[2025-10-06 22:25:37] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -28.769361 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 22:25:40] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -28.771890 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 22:25:44] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -28.780326 | E_var:     0.0796 | E_err:   0.004410
[2025-10-06 22:25:48] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -28.778365 | E_var:     0.1020 | E_err:   0.004991
[2025-10-06 22:25:51] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -28.777736 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 22:25:55] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -28.767595 | E_var:     0.0883 | E_err:   0.004644
[2025-10-06 22:25:59] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -28.780343 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 22:26:02] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -28.776468 | E_var:     0.0753 | E_err:   0.004289
[2025-10-06 22:26:06] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -28.774125 | E_var:     0.0736 | E_err:   0.004240
[2025-10-06 22:26:10] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -28.780942 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 22:26:13] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -28.778677 | E_var:     0.1199 | E_err:   0.005411
[2025-10-06 22:26:17] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -28.773782 | E_var:     0.1105 | E_err:   0.005195
[2025-10-06 22:26:21] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -28.777039 | E_var:     0.0829 | E_err:   0.004500
[2025-10-06 22:26:24] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -28.775018 | E_var:     0.0696 | E_err:   0.004123
[2025-10-06 22:26:28] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -28.781803 | E_var:     0.1283 | E_err:   0.005596
[2025-10-06 22:26:32] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -28.777254 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 22:26:35] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -28.774363 | E_var:     0.0688 | E_err:   0.004099
[2025-10-06 22:26:39] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -28.783208 | E_var:     0.0800 | E_err:   0.004421
[2025-10-06 22:26:43] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -28.775259 | E_var:     0.1090 | E_err:   0.005158
[2025-10-06 22:26:46] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -28.776340 | E_var:     0.0866 | E_err:   0.004599
[2025-10-06 22:26:50] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -28.779536 | E_var:     0.0886 | E_err:   0.004652
[2025-10-06 22:26:53] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -28.771906 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 22:26:57] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -28.778476 | E_var:     0.0724 | E_err:   0.004205
[2025-10-06 22:27:01] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -28.774745 | E_var:     0.0657 | E_err:   0.004004
[2025-10-06 22:27:04] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -28.772126 | E_var:     0.1123 | E_err:   0.005236
[2025-10-06 22:27:08] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -28.787591 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 22:27:12] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -28.774313 | E_var:     0.0692 | E_err:   0.004111
[2025-10-06 22:27:15] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -28.783051 | E_var:     0.0941 | E_err:   0.004794
[2025-10-06 22:27:19] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -28.768698 | E_var:     0.1055 | E_err:   0.005074
[2025-10-06 22:27:23] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -28.778924 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 22:27:26] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -28.772544 | E_var:     0.0817 | E_err:   0.004466
[2025-10-06 22:27:30] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -28.783838 | E_var:     0.0897 | E_err:   0.004678
[2025-10-06 22:27:34] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -28.777416 | E_var:     0.0878 | E_err:   0.004631
[2025-10-06 22:27:37] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -28.775836 | E_var:     0.0799 | E_err:   0.004418
[2025-10-06 22:27:41] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -28.775080 | E_var:     0.0821 | E_err:   0.004477
[2025-10-06 22:27:45] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -28.774374 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 22:27:48] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -28.777127 | E_var:     0.0877 | E_err:   0.004626
[2025-10-06 22:27:52] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -28.780914 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 22:27:56] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -28.776848 | E_var:     0.0753 | E_err:   0.004288
[2025-10-06 22:27:59] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -28.785547 | E_var:     0.0821 | E_err:   0.004476
[2025-10-06 22:28:03] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -28.782809 | E_var:     0.0701 | E_err:   0.004138
[2025-10-06 22:28:07] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -28.778315 | E_var:     0.0691 | E_err:   0.004107
[2025-10-06 22:28:10] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -28.771763 | E_var:     0.1726 | E_err:   0.006492
[2025-10-06 22:28:14] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -28.779556 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 22:28:17] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -28.761554 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 22:28:21] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -28.774362 | E_var:     0.0850 | E_err:   0.004557
[2025-10-06 22:28:25] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -28.777416 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 22:28:28] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -28.774401 | E_var:     0.0785 | E_err:   0.004379
[2025-10-06 22:28:32] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -28.777834 | E_var:     0.0772 | E_err:   0.004342
[2025-10-06 22:28:36] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -28.778504 | E_var:     0.0689 | E_err:   0.004100
[2025-10-06 22:28:39] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -28.772733 | E_var:     0.0705 | E_err:   0.004149
[2025-10-06 22:28:43] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -28.769208 | E_var:     0.0858 | E_err:   0.004577
[2025-10-06 22:28:47] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -28.775363 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 22:28:50] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -28.771086 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 22:28:54] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -28.776108 | E_var:     0.1006 | E_err:   0.004955
[2025-10-06 22:28:58] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -28.781185 | E_var:     0.0845 | E_err:   0.004541
[2025-10-06 22:29:01] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -28.777576 | E_var:     0.0795 | E_err:   0.004404
[2025-10-06 22:29:05] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -28.779689 | E_var:     0.0968 | E_err:   0.004860
[2025-10-06 22:29:09] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -28.777160 | E_var:     0.0816 | E_err:   0.004462
[2025-10-06 22:29:12] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -28.775080 | E_var:     0.0729 | E_err:   0.004218
[2025-10-06 22:29:16] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -28.774837 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 22:29:20] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -28.784646 | E_var:     0.0752 | E_err:   0.004284
[2025-10-06 22:29:23] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -28.770557 | E_var:     0.1140 | E_err:   0.005276
[2025-10-06 22:29:27] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -28.769618 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 22:29:31] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -28.772908 | E_var:     0.0659 | E_err:   0.004012
[2025-10-06 22:29:34] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -28.778514 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 22:29:38] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -28.768894 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 22:29:42] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -28.776883 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 22:29:49] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -28.774072 | E_var:     0.0838 | E_err:   0.004523
[2025-10-06 22:29:54] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -28.779945 | E_var:     0.0635 | E_err:   0.003937
[2025-10-06 22:29:58] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -28.781218 | E_var:     0.1298 | E_err:   0.005630
[2025-10-06 22:30:02] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -28.782671 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 22:30:05] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -28.782758 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 22:30:05] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 22:30:09] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -28.771870 | E_var:     0.0843 | E_err:   0.004536
[2025-10-06 22:30:13] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -28.770683 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 22:30:16] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -28.777184 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 22:30:20] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -28.781599 | E_var:     0.0791 | E_err:   0.004393
[2025-10-06 22:30:24] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -28.778630 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 22:30:27] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -28.769063 | E_var:     0.1205 | E_err:   0.005424
[2025-10-06 22:30:31] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -28.773897 | E_var:     0.0704 | E_err:   0.004145
[2025-10-06 22:30:35] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -28.778506 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 22:30:38] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -28.772601 | E_var:     0.0833 | E_err:   0.004511
[2025-10-06 22:30:42] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -28.774144 | E_var:     0.0968 | E_err:   0.004862
[2025-10-06 22:30:45] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -28.777151 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 22:30:49] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -28.781775 | E_var:     0.0888 | E_err:   0.004655
[2025-10-06 22:30:53] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -28.777022 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 22:30:56] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -28.775551 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 22:31:00] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -28.776390 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 22:31:04] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -28.776936 | E_var:     0.1002 | E_err:   0.004946
[2025-10-06 22:31:07] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -28.777785 | E_var:     0.0704 | E_err:   0.004145
[2025-10-06 22:31:11] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -28.771947 | E_var:     0.1252 | E_err:   0.005529
[2025-10-06 22:31:15] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -28.777154 | E_var:     0.1022 | E_err:   0.004996
[2025-10-06 22:31:18] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -28.782150 | E_var:     0.1008 | E_err:   0.004962
[2025-10-06 22:31:22] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -28.776720 | E_var:     0.0616 | E_err:   0.003878
[2025-10-06 22:31:26] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -28.773103 | E_var:     0.0791 | E_err:   0.004395
[2025-10-06 22:31:29] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -28.776906 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 22:31:33] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -28.777776 | E_var:     0.0953 | E_err:   0.004823
[2025-10-06 22:31:37] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -28.777287 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 22:31:40] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -28.773021 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 22:31:44] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -28.772251 | E_var:     0.0790 | E_err:   0.004391
[2025-10-06 22:31:48] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -28.776504 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 22:31:51] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -28.779155 | E_var:     0.0813 | E_err:   0.004455
[2025-10-06 22:31:55] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -28.786484 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 22:31:59] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -28.771455 | E_var:     0.0735 | E_err:   0.004235
[2025-10-06 22:32:02] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -28.780083 | E_var:     0.1001 | E_err:   0.004944
[2025-10-06 22:32:06] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -28.769882 | E_var:     0.0780 | E_err:   0.004364
[2025-10-06 22:32:09] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -28.785915 | E_var:     0.1039 | E_err:   0.005037
[2025-10-06 22:32:13] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -28.777644 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 22:32:17] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -28.773197 | E_var:     0.0833 | E_err:   0.004508
[2025-10-06 22:32:20] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -28.776710 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 22:32:24] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -28.777616 | E_var:     0.0723 | E_err:   0.004200
[2025-10-06 22:32:28] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -28.773411 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 22:32:31] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -28.780408 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 22:32:35] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -28.773651 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 22:32:39] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -28.774274 | E_var:     0.0920 | E_err:   0.004738
[2025-10-06 22:32:42] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -28.775088 | E_var:     0.0748 | E_err:   0.004273
[2025-10-06 22:32:46] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -28.789653 | E_var:     0.0744 | E_err:   0.004262
[2025-10-06 22:32:50] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -28.771007 | E_var:     0.1064 | E_err:   0.005097
[2025-10-06 22:32:53] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -28.772027 | E_var:     0.0773 | E_err:   0.004346
[2025-10-06 22:32:57] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -28.781952 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 22:33:01] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -28.773412 | E_var:     0.0977 | E_err:   0.004885
[2025-10-06 22:33:04] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -28.779488 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 22:33:08] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -28.776686 | E_var:     0.0834 | E_err:   0.004512
[2025-10-06 22:33:12] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -28.780778 | E_var:     0.0766 | E_err:   0.004325
[2025-10-06 22:33:15] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -28.784792 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 22:33:19] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -28.776082 | E_var:     0.0789 | E_err:   0.004388
[2025-10-06 22:33:23] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -28.769361 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 22:33:26] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -28.771928 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 22:33:30] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -28.773995 | E_var:     0.0668 | E_err:   0.004040
[2025-10-06 22:33:34] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -28.771311 | E_var:     0.0697 | E_err:   0.004126
[2025-10-06 22:33:37] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -28.777207 | E_var:     0.0848 | E_err:   0.004551
[2025-10-06 22:33:41] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -28.769311 | E_var:     0.0793 | E_err:   0.004399
[2025-10-06 22:33:44] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -28.771148 | E_var:     0.1044 | E_err:   0.005049
[2025-10-06 22:33:48] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -28.773587 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 22:33:52] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -28.773734 | E_var:     0.0719 | E_err:   0.004190
[2025-10-06 22:33:55] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -28.773745 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 22:33:59] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -28.773817 | E_var:     0.1095 | E_err:   0.005170
[2025-10-06 22:34:03] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -28.779098 | E_var:     0.0747 | E_err:   0.004271
[2025-10-06 22:34:06] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -28.778630 | E_var:     0.4133 | E_err:   0.010045
[2025-10-06 22:34:10] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -28.784111 | E_var:     0.0645 | E_err:   0.003970
[2025-10-06 22:34:14] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -28.774266 | E_var:     0.0953 | E_err:   0.004822
[2025-10-06 22:34:17] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -28.771797 | E_var:     0.0663 | E_err:   0.004024
[2025-10-06 22:34:21] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -28.777683 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 22:34:25] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -28.775591 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 22:34:28] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -28.777743 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 22:34:32] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -28.774739 | E_var:     0.1198 | E_err:   0.005408
[2025-10-06 22:34:36] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -28.774190 | E_var:     0.0732 | E_err:   0.004227
[2025-10-06 22:34:39] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -28.772358 | E_var:     0.1049 | E_err:   0.005062
[2025-10-06 22:34:43] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -28.778487 | E_var:     0.0973 | E_err:   0.004873
[2025-10-06 22:34:47] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -28.772701 | E_var:     0.0656 | E_err:   0.004003
[2025-10-06 22:34:50] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -28.775717 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 22:34:54] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -28.773272 | E_var:     0.1089 | E_err:   0.005155
[2025-10-06 22:34:58] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -28.782765 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 22:35:01] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -28.778973 | E_var:     0.0778 | E_err:   0.004359
[2025-10-06 22:35:05] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -28.785929 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 22:35:09] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -28.766720 | E_var:     0.0899 | E_err:   0.004686
[2025-10-06 22:35:12] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -28.775410 | E_var:     0.0748 | E_err:   0.004273
[2025-10-06 22:35:16] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -28.780812 | E_var:     0.0804 | E_err:   0.004432
[2025-10-06 22:35:19] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -28.789286 | E_var:     0.0966 | E_err:   0.004855
[2025-10-06 22:35:23] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -28.776179 | E_var:     0.0849 | E_err:   0.004554
[2025-10-06 22:35:27] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -28.784312 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 22:35:30] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -28.773207 | E_var:     0.0900 | E_err:   0.004688
[2025-10-06 22:35:34] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -28.780047 | E_var:     0.1292 | E_err:   0.005617
[2025-10-06 22:35:38] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -28.780697 | E_var:     0.1021 | E_err:   0.004992
[2025-10-06 22:35:41] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -28.782263 | E_var:     0.0743 | E_err:   0.004259
[2025-10-06 22:35:45] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -28.784444 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 22:35:49] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -28.775541 | E_var:     0.0874 | E_err:   0.004620
[2025-10-06 22:35:52] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -28.774819 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 22:35:56] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -28.773875 | E_var:     0.1128 | E_err:   0.005249
[2025-10-06 22:36:00] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -28.766272 | E_var:     0.0956 | E_err:   0.004831
[2025-10-06 22:36:03] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -28.782214 | E_var:     0.0658 | E_err:   0.004009
[2025-10-06 22:36:07] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -28.777067 | E_var:     0.1008 | E_err:   0.004962
[2025-10-06 22:36:11] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -28.778366 | E_var:     0.0650 | E_err:   0.003982
[2025-10-06 22:36:11] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 22:36:14] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -28.777688 | E_var:     0.0756 | E_err:   0.004296
[2025-10-06 22:36:18] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -28.777068 | E_var:     0.0848 | E_err:   0.004551
[2025-10-06 22:36:22] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -28.776925 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 22:36:25] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -28.774454 | E_var:     0.0762 | E_err:   0.004313
[2025-10-06 22:36:29] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -28.765903 | E_var:     0.0867 | E_err:   0.004600
[2025-10-06 22:36:33] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -28.775549 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 22:36:37] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -28.768350 | E_var:     0.0717 | E_err:   0.004184
[2025-10-06 22:36:40] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -28.773090 | E_var:     0.0769 | E_err:   0.004334
[2025-10-06 22:36:44] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -28.776741 | E_var:     0.0747 | E_err:   0.004271
[2025-10-06 22:36:48] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -28.782546 | E_var:     0.0850 | E_err:   0.004555
[2025-10-06 22:36:51] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -28.779201 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 22:36:55] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -28.775037 | E_var:     0.0781 | E_err:   0.004366
[2025-10-06 22:36:59] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -28.775693 | E_var:     0.0795 | E_err:   0.004406
[2025-10-06 22:37:02] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -28.781242 | E_var:     0.0676 | E_err:   0.004062
[2025-10-06 22:37:06] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -28.772902 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 22:37:10] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -28.766918 | E_var:     0.1222 | E_err:   0.005462
[2025-10-06 22:37:13] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -28.776335 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 22:37:17] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -28.777156 | E_var:     0.1090 | E_err:   0.005158
[2025-10-06 22:37:21] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -28.765812 | E_var:     0.1990 | E_err:   0.006970
[2025-10-06 22:37:24] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -28.769206 | E_var:     0.0712 | E_err:   0.004169
[2025-10-06 22:37:28] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -28.779767 | E_var:     0.0578 | E_err:   0.003756
[2025-10-06 22:37:32] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -28.768562 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 22:37:35] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -28.776711 | E_var:     0.0638 | E_err:   0.003947
[2025-10-06 22:37:39] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -28.776464 | E_var:     0.0716 | E_err:   0.004180
[2025-10-06 22:37:43] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -28.775366 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 22:37:46] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -28.776958 | E_var:     0.1701 | E_err:   0.006445
[2025-10-06 22:37:50] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -28.784220 | E_var:     0.0881 | E_err:   0.004638
[2025-10-06 22:37:53] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -28.778204 | E_var:     0.0697 | E_err:   0.004125
[2025-10-06 22:37:57] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -28.782478 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 22:38:01] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -28.778594 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 22:38:04] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -28.783274 | E_var:     0.0741 | E_err:   0.004254
[2025-10-06 22:38:08] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -28.783133 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 22:38:12] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -28.772409 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 22:38:15] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -28.780242 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 22:38:19] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -28.773203 | E_var:     0.0899 | E_err:   0.004684
[2025-10-06 22:38:23] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -28.776484 | E_var:     0.1097 | E_err:   0.005175
[2025-10-06 22:38:26] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -28.775504 | E_var:     0.0808 | E_err:   0.004443
[2025-10-06 22:38:30] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -28.781432 | E_var:     0.0714 | E_err:   0.004176
[2025-10-06 22:38:34] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -28.783572 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 22:38:37] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -28.776581 | E_var:     0.0719 | E_err:   0.004188
[2025-10-06 22:38:41] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -28.773400 | E_var:     0.0799 | E_err:   0.004415
[2025-10-06 22:38:45] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -28.777884 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 22:38:48] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -28.775372 | E_var:     0.0710 | E_err:   0.004163
[2025-10-06 22:38:52] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -28.777109 | E_var:     0.0647 | E_err:   0.003976
[2025-10-06 22:38:56] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -28.781567 | E_var:     0.0745 | E_err:   0.004265
[2025-10-06 22:38:59] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -28.778100 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 22:39:03] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -28.774945 | E_var:     0.0827 | E_err:   0.004495
[2025-10-06 22:39:07] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -28.780675 | E_var:     0.0716 | E_err:   0.004182
[2025-10-06 22:39:10] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -28.782420 | E_var:     0.2212 | E_err:   0.007349
[2025-10-06 22:39:14] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -28.779189 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 22:39:14] ======================================================================================================
[2025-10-06 22:39:14] ✅ Training completed successfully
[2025-10-06 22:39:14] Total restarts: 2
[2025-10-06 22:39:15] Final Energy: -28.77918913 ± 0.00479934
[2025-10-06 22:39:15] Final Variance: 0.094346
[2025-10-06 22:39:15] ======================================================================================================
[2025-10-06 22:39:15] ======================================================================================================
[2025-10-06 22:39:15] Training completed | Runtime: 3906.9s
[2025-10-06 22:39:16] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 22:39:16] ======================================================================================================
