[2025-10-06 02:27:05] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.79/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 02:27:05]   - 迭代次数: final
[2025-10-06 02:27:05]   - 能量: -28.361681-0.000933j ± 0.005115, Var: 0.107181
[2025-10-06 02:27:05]   - 时间戳: 2025-10-06T02:26:45.036437+08:00
[2025-10-06 02:27:20] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 02:27:20] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 02:27:20] ======================================================================================================
[2025-10-06 02:27:20] GCNN for Shastry-Sutherland Model
[2025-10-06 02:27:20] ======================================================================================================
[2025-10-06 02:27:20] System parameters:
[2025-10-06 02:27:20]   - System size: L=4, N=64
[2025-10-06 02:27:20]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-10-06 02:27:20] ------------------------------------------------------------------------------------------------------
[2025-10-06 02:27:20] Model parameters:
[2025-10-06 02:27:20]   - Number of layers = 4
[2025-10-06 02:27:20]   - Number of features = 4
[2025-10-06 02:27:20]   - Total parameters = 12572
[2025-10-06 02:27:20] ------------------------------------------------------------------------------------------------------
[2025-10-06 02:27:20] Training parameters:
[2025-10-06 02:27:20]   - Total iterations: 1050
[2025-10-06 02:27:20]   - Annealing cycles: 3
[2025-10-06 02:27:20]   - Initial period: 150
[2025-10-06 02:27:20]   - Period multiplier: 2.0
[2025-10-06 02:27:20]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 02:27:20]   - Samples: 4096
[2025-10-06 02:27:20]   - Discarded samples: 0
[2025-10-06 02:27:20]   - Chunk size: 4096
[2025-10-06 02:27:20]   - Diagonal shift: 0.15
[2025-10-06 02:27:20]   - Gradient clipping: 1.0
[2025-10-06 02:27:20]   - Checkpoint enabled: interval=100
[2025-10-06 02:27:20]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.80/model_L4F4/training/checkpoints
[2025-10-06 02:27:20] ------------------------------------------------------------------------------------------------------
[2025-10-06 02:27:20] Device status:
[2025-10-06 02:27:20]   - Devices model: NVIDIA H200 NVL
[2025-10-06 02:27:20]   - Number of devices: 1
[2025-10-06 02:27:20]   - Sharding: True
[2025-10-06 02:27:20] ======================================================================================================
[2025-10-06 02:27:53] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -28.786617 | E_var:     0.3487 | E_err:   0.009227
[2025-10-06 02:28:12] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -28.778910 | E_var:     0.2001 | E_err:   0.006989
[2025-10-06 02:28:14] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -28.771704 | E_var:     0.1194 | E_err:   0.005398
[2025-10-06 02:28:17] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -28.779651 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 02:28:19] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -28.773092 | E_var:     0.3017 | E_err:   0.008582
[2025-10-06 02:28:21] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -28.765223 | E_var:     0.1024 | E_err:   0.004999
[2025-10-06 02:28:24] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -28.774590 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 02:28:26] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -28.769837 | E_var:     0.1557 | E_err:   0.006166
[2025-10-06 02:28:29] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -28.765871 | E_var:     0.2160 | E_err:   0.007262
[2025-10-06 02:28:31] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -28.771764 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 02:28:33] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -28.769155 | E_var:     0.1002 | E_err:   0.004947
[2025-10-06 02:28:36] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -28.770913 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 02:28:38] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -28.771917 | E_var:     0.1139 | E_err:   0.005273
[2025-10-06 02:28:41] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -28.766976 | E_var:     0.1383 | E_err:   0.005811
[2025-10-06 02:28:43] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -28.761625 | E_var:     0.1032 | E_err:   0.005019
[2025-10-06 02:28:46] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -28.770845 | E_var:     0.0993 | E_err:   0.004925
[2025-10-06 02:28:48] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -28.767391 | E_var:     0.1390 | E_err:   0.005826
[2025-10-06 02:28:50] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -28.772241 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 02:28:53] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -28.769452 | E_var:     0.1069 | E_err:   0.005110
[2025-10-06 02:28:55] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -28.768064 | E_var:     0.1000 | E_err:   0.004940
[2025-10-06 02:28:58] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -28.763644 | E_var:     0.0973 | E_err:   0.004873
[2025-10-06 02:29:00] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -28.768764 | E_var:     0.1431 | E_err:   0.005912
[2025-10-06 02:29:03] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -28.769518 | E_var:     0.1022 | E_err:   0.004995
[2025-10-06 02:29:05] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -28.771499 | E_var:     0.1452 | E_err:   0.005955
[2025-10-06 02:29:07] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -28.765954 | E_var:     0.1363 | E_err:   0.005769
[2025-10-06 02:29:10] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -28.773921 | E_var:     0.0922 | E_err:   0.004743
[2025-10-06 02:29:12] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -28.771733 | E_var:     0.1427 | E_err:   0.005903
[2025-10-06 02:29:15] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -28.772841 | E_var:     0.1106 | E_err:   0.005197
[2025-10-06 02:29:17] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -28.771710 | E_var:     0.1104 | E_err:   0.005191
[2025-10-06 02:29:20] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -28.766006 | E_var:     0.1854 | E_err:   0.006728
[2025-10-06 02:29:22] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -28.776846 | E_var:     0.0951 | E_err:   0.004818
[2025-10-06 02:29:24] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -28.774043 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 02:29:27] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -28.775145 | E_var:     0.1199 | E_err:   0.005410
[2025-10-06 02:29:29] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -28.765238 | E_var:     0.1274 | E_err:   0.005577
[2025-10-06 02:29:32] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -28.760551 | E_var:     0.2159 | E_err:   0.007261
[2025-10-06 02:29:34] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -28.774035 | E_var:     0.1100 | E_err:   0.005182
[2025-10-06 02:29:37] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -28.775861 | E_var:     0.1175 | E_err:   0.005355
[2025-10-06 02:29:39] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -28.767729 | E_var:     0.1385 | E_err:   0.005815
[2025-10-06 02:29:42] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -28.769770 | E_var:     0.0985 | E_err:   0.004903
[2025-10-06 02:29:44] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -28.769272 | E_var:     0.1262 | E_err:   0.005550
[2025-10-06 02:29:47] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -28.762441 | E_var:     0.1275 | E_err:   0.005580
[2025-10-06 02:29:49] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -28.768340 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 02:29:52] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -28.769767 | E_var:     0.0934 | E_err:   0.004775
[2025-10-06 02:29:54] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -28.761147 | E_var:     0.1240 | E_err:   0.005503
[2025-10-06 02:29:57] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -28.773676 | E_var:     0.0926 | E_err:   0.004755
[2025-10-06 02:29:59] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -28.766312 | E_var:     0.0921 | E_err:   0.004742
[2025-10-06 02:30:02] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -28.774717 | E_var:     0.1201 | E_err:   0.005414
[2025-10-06 02:30:04] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -28.775062 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 02:30:07] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -28.773206 | E_var:     0.1260 | E_err:   0.005547
[2025-10-06 02:30:09] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -28.774816 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 02:30:12] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -28.769240 | E_var:     0.0994 | E_err:   0.004927
[2025-10-06 02:30:14] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -28.772872 | E_var:     0.0971 | E_err:   0.004870
[2025-10-06 02:30:17] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -28.773759 | E_var:     0.1123 | E_err:   0.005236
[2025-10-06 02:30:19] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -28.777450 | E_var:     0.1451 | E_err:   0.005952
[2025-10-06 02:30:22] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -28.776240 | E_var:     0.0981 | E_err:   0.004893
[2025-10-06 02:30:24] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -28.773053 | E_var:     0.0923 | E_err:   0.004747
[2025-10-06 02:30:26] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -28.765227 | E_var:     0.1047 | E_err:   0.005055
[2025-10-06 02:30:29] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -28.780112 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 02:30:31] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -28.776363 | E_var:     0.0971 | E_err:   0.004868
[2025-10-06 02:30:34] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -28.770567 | E_var:     0.1028 | E_err:   0.005011
[2025-10-06 02:30:36] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -28.770294 | E_var:     0.0977 | E_err:   0.004885
[2025-10-06 02:30:39] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -28.774267 | E_var:     0.0840 | E_err:   0.004530
[2025-10-06 02:30:41] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -28.768623 | E_var:     0.0950 | E_err:   0.004815
[2025-10-06 02:30:44] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -28.770480 | E_var:     0.0959 | E_err:   0.004840
[2025-10-06 02:30:46] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -28.772215 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 02:30:48] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -28.772121 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 02:30:51] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -28.772970 | E_var:     0.1387 | E_err:   0.005820
[2025-10-06 02:30:53] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -28.766859 | E_var:     0.1287 | E_err:   0.005605
[2025-10-06 02:30:56] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -28.773730 | E_var:     0.1046 | E_err:   0.005054
[2025-10-06 02:30:58] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -28.769557 | E_var:     0.1087 | E_err:   0.005152
[2025-10-06 02:31:01] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -28.766335 | E_var:     0.0921 | E_err:   0.004742
[2025-10-06 02:31:03] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -28.769691 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 02:31:05] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -28.772712 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 02:31:08] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -28.768692 | E_var:     0.0952 | E_err:   0.004820
[2025-10-06 02:31:10] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -28.770904 | E_var:     0.0935 | E_err:   0.004779
[2025-10-06 02:31:13] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -28.772121 | E_var:     0.1199 | E_err:   0.005410
[2025-10-06 02:31:15] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -28.762311 | E_var:     0.1204 | E_err:   0.005421
[2025-10-06 02:31:18] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -28.768813 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 02:31:20] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -28.768762 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 02:31:22] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -28.780512 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 02:31:25] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -28.772601 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 02:31:27] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -28.771932 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 02:31:30] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -28.774087 | E_var:     0.1096 | E_err:   0.005172
[2025-10-06 02:31:32] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -28.777203 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 02:31:34] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -28.769331 | E_var:     0.1085 | E_err:   0.005146
[2025-10-06 02:31:37] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -28.772838 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 02:31:39] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -28.762456 | E_var:     0.2692 | E_err:   0.008107
[2025-10-06 02:31:42] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -28.769566 | E_var:     0.1066 | E_err:   0.005102
[2025-10-06 02:31:44] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -28.779387 | E_var:     0.1523 | E_err:   0.006098
[2025-10-06 02:31:47] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -28.776062 | E_var:     0.1120 | E_err:   0.005229
[2025-10-06 02:31:49] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -28.773640 | E_var:     0.1026 | E_err:   0.005006
[2025-10-06 02:31:51] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -28.776523 | E_var:     0.1087 | E_err:   0.005150
[2025-10-06 02:31:54] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -28.759935 | E_var:     0.0931 | E_err:   0.004766
[2025-10-06 02:31:56] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -28.771665 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 02:31:59] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -28.774487 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 02:32:01] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -28.761325 | E_var:     0.1221 | E_err:   0.005461
[2025-10-06 02:32:04] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -28.771813 | E_var:     0.0975 | E_err:   0.004880
[2025-10-06 02:32:06] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -28.778532 | E_var:     0.1490 | E_err:   0.006031
[2025-10-06 02:32:08] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -28.770676 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 02:32:11] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -28.769014 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 02:32:11] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 02:32:13] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -28.778132 | E_var:     0.0923 | E_err:   0.004747
[2025-10-06 02:32:16] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -28.772331 | E_var:     0.1068 | E_err:   0.005105
[2025-10-06 02:32:18] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -28.769030 | E_var:     0.1089 | E_err:   0.005157
[2025-10-06 02:32:21] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -28.767307 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 02:32:23] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -28.769030 | E_var:     0.1201 | E_err:   0.005416
[2025-10-06 02:32:25] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -28.763105 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 02:32:28] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -28.779204 | E_var:     0.1043 | E_err:   0.005045
[2025-10-06 02:32:30] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -28.769013 | E_var:     0.1150 | E_err:   0.005299
[2025-10-06 02:32:33] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -28.772856 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 02:32:35] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -28.768111 | E_var:     0.1127 | E_err:   0.005246
[2025-10-06 02:32:38] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -28.762920 | E_var:     0.1477 | E_err:   0.006005
[2025-10-06 02:32:40] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -28.774522 | E_var:     0.1142 | E_err:   0.005280
[2025-10-06 02:32:42] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -28.768734 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 02:32:45] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -28.775054 | E_var:     0.0832 | E_err:   0.004507
[2025-10-06 02:32:47] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -28.766493 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 02:32:50] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -28.768995 | E_var:     0.1252 | E_err:   0.005528
[2025-10-06 02:32:52] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -28.775107 | E_var:     0.1222 | E_err:   0.005463
[2025-10-06 02:32:55] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -28.766652 | E_var:     0.0907 | E_err:   0.004706
[2025-10-06 02:32:57] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -28.776991 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 02:32:59] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -28.771826 | E_var:     0.0939 | E_err:   0.004787
[2025-10-06 02:33:02] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -28.770661 | E_var:     0.1464 | E_err:   0.005979
[2025-10-06 02:33:04] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -28.768350 | E_var:     0.1166 | E_err:   0.005336
[2025-10-06 02:33:07] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -28.766638 | E_var:     0.1237 | E_err:   0.005495
[2025-10-06 02:33:09] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -28.778304 | E_var:     0.0997 | E_err:   0.004932
[2025-10-06 02:33:12] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -28.768892 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 02:33:14] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -28.767731 | E_var:     0.1121 | E_err:   0.005232
[2025-10-06 02:33:16] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -28.770105 | E_var:     0.0900 | E_err:   0.004688
[2025-10-06 02:33:19] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -28.778564 | E_var:     0.1018 | E_err:   0.004986
[2025-10-06 02:33:21] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -28.768224 | E_var:     0.1141 | E_err:   0.005279
[2025-10-06 02:33:24] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -28.772560 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 02:33:26] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -28.774497 | E_var:     0.1212 | E_err:   0.005439
[2025-10-06 02:33:29] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -28.774725 | E_var:     0.1172 | E_err:   0.005350
[2025-10-06 02:33:31] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -28.759113 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 02:33:33] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -28.771306 | E_var:     0.0900 | E_err:   0.004687
[2025-10-06 02:33:36] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -28.772571 | E_var:     0.1150 | E_err:   0.005298
[2025-10-06 02:33:38] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -28.761576 | E_var:     0.1169 | E_err:   0.005341
[2025-10-06 02:33:41] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -28.766670 | E_var:     0.0840 | E_err:   0.004530
[2025-10-06 02:33:43] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -28.777067 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 02:33:46] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -28.770823 | E_var:     0.1028 | E_err:   0.005010
[2025-10-06 02:33:48] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -28.770913 | E_var:     0.1176 | E_err:   0.005359
[2025-10-06 02:33:50] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -28.764550 | E_var:     0.1096 | E_err:   0.005174
[2025-10-06 02:33:53] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -28.766171 | E_var:     0.1039 | E_err:   0.005035
[2025-10-06 02:33:55] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -28.770036 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 02:33:58] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -28.771157 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 02:34:00] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -28.767375 | E_var:     0.1666 | E_err:   0.006378
[2025-10-06 02:34:03] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -28.772954 | E_var:     0.1118 | E_err:   0.005225
[2025-10-06 02:34:05] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -28.770738 | E_var:     0.0929 | E_err:   0.004763
[2025-10-06 02:34:07] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -28.773059 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 02:34:10] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -28.775102 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 02:34:12] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -28.779564 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 02:34:12] 🔄 RESTART #1 | Period: 300
[2025-10-06 02:34:15] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -28.768587 | E_var:     0.0989 | E_err:   0.004913
[2025-10-06 02:34:17] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -28.771801 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 02:34:20] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -28.772597 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 02:34:22] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -28.765708 | E_var:     0.1570 | E_err:   0.006192
[2025-10-06 02:34:24] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -28.767754 | E_var:     0.1534 | E_err:   0.006120
[2025-10-06 02:34:27] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -28.768568 | E_var:     0.1122 | E_err:   0.005234
[2025-10-06 02:34:29] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -28.781435 | E_var:     0.2110 | E_err:   0.007177
[2025-10-06 02:34:32] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -28.761906 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 02:34:34] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -28.777197 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 02:34:36] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -28.769043 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 02:34:39] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -28.773440 | E_var:     0.4247 | E_err:   0.010182
[2025-10-06 02:34:41] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -28.775113 | E_var:     0.1636 | E_err:   0.006320
[2025-10-06 02:34:44] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -28.770296 | E_var:     0.1103 | E_err:   0.005189
[2025-10-06 02:34:46] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -28.767782 | E_var:     0.0807 | E_err:   0.004439
[2025-10-06 02:34:49] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -28.765537 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 02:34:51] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -28.778862 | E_var:     0.2040 | E_err:   0.007057
[2025-10-06 02:34:53] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -28.758950 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 02:34:56] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -28.756305 | E_var:     0.1422 | E_err:   0.005893
[2025-10-06 02:34:58] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -28.769670 | E_var:     0.0901 | E_err:   0.004691
[2025-10-06 02:35:01] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -28.764328 | E_var:     0.1180 | E_err:   0.005366
[2025-10-06 02:35:03] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -28.776777 | E_var:     0.0852 | E_err:   0.004562
[2025-10-06 02:35:06] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -28.769561 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 02:35:08] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -28.770650 | E_var:     0.1192 | E_err:   0.005394
[2025-10-06 02:35:10] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -28.758764 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 02:35:13] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -28.763814 | E_var:     0.0937 | E_err:   0.004783
[2025-10-06 02:35:15] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -28.768193 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 02:35:18] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -28.776147 | E_var:     0.0877 | E_err:   0.004627
[2025-10-06 02:35:20] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -28.777739 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 02:35:23] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -28.777915 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 02:35:25] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -28.772730 | E_var:     0.0871 | E_err:   0.004612
[2025-10-06 02:35:27] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -28.778198 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 02:35:30] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -28.772593 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 02:35:32] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -28.777245 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 02:35:35] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -28.773453 | E_var:     0.1068 | E_err:   0.005106
[2025-10-06 02:35:37] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -28.775085 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 02:35:39] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -28.774854 | E_var:     0.1077 | E_err:   0.005127
[2025-10-06 02:35:42] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -28.767238 | E_var:     0.2117 | E_err:   0.007189
[2025-10-06 02:35:44] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -28.773424 | E_var:     0.1504 | E_err:   0.006060
[2025-10-06 02:35:47] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -28.768902 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 02:35:49] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -28.771868 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 02:35:52] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -28.767221 | E_var:     0.1192 | E_err:   0.005395
[2025-10-06 02:35:54] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -28.771633 | E_var:     0.1546 | E_err:   0.006143
[2025-10-06 02:35:56] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -28.762372 | E_var:     0.1154 | E_err:   0.005307
[2025-10-06 02:35:59] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -28.762834 | E_var:     0.0938 | E_err:   0.004785
[2025-10-06 02:36:01] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -28.769767 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 02:36:04] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -28.761861 | E_var:     0.1019 | E_err:   0.004987
[2025-10-06 02:36:06] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -28.789340 | E_var:     0.2318 | E_err:   0.007523
[2025-10-06 02:36:09] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -28.770145 | E_var:     0.1081 | E_err:   0.005136
[2025-10-06 02:36:11] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -28.768389 | E_var:     0.1166 | E_err:   0.005334
[2025-10-06 02:36:13] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -28.765335 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 02:36:14] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 02:36:16] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -28.777907 | E_var:     0.1703 | E_err:   0.006449
[2025-10-06 02:36:18] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -28.772110 | E_var:     0.1148 | E_err:   0.005294
[2025-10-06 02:36:21] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -28.769813 | E_var:     0.0832 | E_err:   0.004506
[2025-10-06 02:36:23] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -28.765683 | E_var:     0.0992 | E_err:   0.004921
[2025-10-06 02:36:26] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -28.778177 | E_var:     0.1402 | E_err:   0.005850
[2025-10-06 02:36:28] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -28.769396 | E_var:     0.2078 | E_err:   0.007122
[2025-10-06 02:36:30] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -28.776443 | E_var:     0.0981 | E_err:   0.004895
[2025-10-06 02:36:33] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -28.778434 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 02:36:35] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -28.772122 | E_var:     0.1125 | E_err:   0.005241
[2025-10-06 02:36:38] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -28.770563 | E_var:     0.0826 | E_err:   0.004492
[2025-10-06 02:36:40] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -28.765407 | E_var:     0.1151 | E_err:   0.005301
[2025-10-06 02:36:43] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -28.772053 | E_var:     0.0903 | E_err:   0.004696
[2025-10-06 02:36:45] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -28.768461 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 02:36:47] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -28.771906 | E_var:     0.1094 | E_err:   0.005167
[2025-10-06 02:36:50] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -28.772324 | E_var:     0.1179 | E_err:   0.005365
[2025-10-06 02:36:52] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -28.775661 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 02:36:55] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -28.774012 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 02:36:57] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -28.778676 | E_var:     0.1102 | E_err:   0.005187
[2025-10-06 02:37:00] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -28.763872 | E_var:     0.1295 | E_err:   0.005622
[2025-10-06 02:37:02] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -28.769822 | E_var:     0.0857 | E_err:   0.004573
[2025-10-06 02:37:05] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -28.766828 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 02:37:07] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -28.761917 | E_var:     0.0862 | E_err:   0.004588
[2025-10-06 02:37:09] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -28.771676 | E_var:     0.1013 | E_err:   0.004973
[2025-10-06 02:37:12] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -28.767761 | E_var:     0.1029 | E_err:   0.005011
[2025-10-06 02:37:14] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -28.771355 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 02:37:17] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -28.771812 | E_var:     0.1352 | E_err:   0.005744
[2025-10-06 02:37:19] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -28.766717 | E_var:     0.1166 | E_err:   0.005335
[2025-10-06 02:37:21] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -28.762549 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 02:37:24] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -28.774595 | E_var:     0.0948 | E_err:   0.004811
[2025-10-06 02:37:26] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -28.773883 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 02:37:29] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -28.767788 | E_var:     0.0960 | E_err:   0.004840
[2025-10-06 02:37:31] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -28.767847 | E_var:     0.1035 | E_err:   0.005026
[2025-10-06 02:37:34] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -28.782950 | E_var:     0.1644 | E_err:   0.006335
[2025-10-06 02:37:36] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -28.765691 | E_var:     0.0891 | E_err:   0.004664
[2025-10-06 02:37:39] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -28.768515 | E_var:     0.1539 | E_err:   0.006129
[2025-10-06 02:37:41] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -28.770427 | E_var:     0.1261 | E_err:   0.005549
[2025-10-06 02:37:43] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -28.779795 | E_var:     0.0797 | E_err:   0.004412
[2025-10-06 02:37:46] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -28.769635 | E_var:     0.1544 | E_err:   0.006140
[2025-10-06 02:37:48] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -28.772666 | E_var:     0.0879 | E_err:   0.004631
[2025-10-06 02:37:51] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -28.776963 | E_var:     0.0953 | E_err:   0.004823
[2025-10-06 02:37:53] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -28.770080 | E_var:     0.1057 | E_err:   0.005079
[2025-10-06 02:37:56] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -28.781607 | E_var:     0.0891 | E_err:   0.004664
[2025-10-06 02:37:58] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -28.777456 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 02:38:00] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -28.761643 | E_var:     0.0933 | E_err:   0.004774
[2025-10-06 02:38:03] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -28.766596 | E_var:     0.0913 | E_err:   0.004722
[2025-10-06 02:38:05] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -28.773576 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 02:38:08] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -28.761452 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 02:38:10] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -28.759445 | E_var:     0.1317 | E_err:   0.005671
[2025-10-06 02:38:12] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -28.781646 | E_var:     0.1350 | E_err:   0.005742
[2025-10-06 02:38:15] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -28.773344 | E_var:     0.1012 | E_err:   0.004971
[2025-10-06 02:38:17] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -28.765592 | E_var:     0.1454 | E_err:   0.005958
[2025-10-06 02:38:20] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -28.762979 | E_var:     0.1346 | E_err:   0.005732
[2025-10-06 02:38:22] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -28.770367 | E_var:     0.0951 | E_err:   0.004817
[2025-10-06 02:38:25] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -28.774714 | E_var:     0.0871 | E_err:   0.004611
[2025-10-06 02:38:27] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -28.772637 | E_var:     0.0970 | E_err:   0.004865
[2025-10-06 02:38:29] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -28.770459 | E_var:     0.1020 | E_err:   0.004991
[2025-10-06 02:38:32] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -28.771633 | E_var:     0.0968 | E_err:   0.004862
[2025-10-06 02:38:34] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -28.769873 | E_var:     0.0954 | E_err:   0.004827
[2025-10-06 02:38:37] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -28.767058 | E_var:     0.0844 | E_err:   0.004538
[2025-10-06 02:38:39] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -28.768927 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 02:38:42] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -28.774308 | E_var:     0.1479 | E_err:   0.006009
[2025-10-06 02:38:44] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -28.769777 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 02:38:46] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -28.772298 | E_var:     0.1019 | E_err:   0.004987
[2025-10-06 02:38:49] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -28.766939 | E_var:     0.1029 | E_err:   0.005012
[2025-10-06 02:38:51] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -28.774567 | E_var:     0.2333 | E_err:   0.007547
[2025-10-06 02:38:54] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -28.765010 | E_var:     0.1221 | E_err:   0.005461
[2025-10-06 02:38:56] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -28.767904 | E_var:     0.1004 | E_err:   0.004951
[2025-10-06 02:38:59] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -28.771804 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 02:39:01] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -28.767126 | E_var:     0.1354 | E_err:   0.005750
[2025-10-06 02:39:03] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -28.759708 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 02:39:06] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -28.775328 | E_var:     0.1012 | E_err:   0.004971
[2025-10-06 02:39:08] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -28.769974 | E_var:     0.1338 | E_err:   0.005715
[2025-10-06 02:39:11] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -28.770842 | E_var:     0.0970 | E_err:   0.004868
[2025-10-06 02:39:13] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -28.775612 | E_var:     0.1167 | E_err:   0.005337
[2025-10-06 02:39:16] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -28.763178 | E_var:     0.1460 | E_err:   0.005971
[2025-10-06 02:39:18] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -28.766991 | E_var:     0.1007 | E_err:   0.004958
[2025-10-06 02:39:20] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -28.775634 | E_var:     0.1261 | E_err:   0.005548
[2025-10-06 02:39:23] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -28.773397 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 02:39:25] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -28.772408 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 02:39:28] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -28.768665 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 02:39:30] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -28.763747 | E_var:     0.0938 | E_err:   0.004786
[2025-10-06 02:39:33] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -28.772866 | E_var:     0.1115 | E_err:   0.005217
[2025-10-06 02:39:35] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -28.769512 | E_var:     0.1210 | E_err:   0.005436
[2025-10-06 02:39:37] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -28.767446 | E_var:     0.0945 | E_err:   0.004804
[2025-10-06 02:39:40] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -28.764977 | E_var:     0.1045 | E_err:   0.005051
[2025-10-06 02:39:42] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -28.770124 | E_var:     0.1068 | E_err:   0.005105
[2025-10-06 02:39:45] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -28.775797 | E_var:     0.0978 | E_err:   0.004885
[2025-10-06 02:39:47] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -28.768394 | E_var:     0.1042 | E_err:   0.005045
[2025-10-06 02:39:50] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -28.764268 | E_var:     0.1198 | E_err:   0.005408
[2025-10-06 02:39:52] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -28.773078 | E_var:     0.1137 | E_err:   0.005270
[2025-10-06 02:39:55] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -28.772082 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 02:39:57] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -28.770656 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 02:40:00] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -28.772034 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 02:40:02] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -28.776991 | E_var:     0.1043 | E_err:   0.005047
[2025-10-06 02:40:04] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -28.770975 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 02:40:07] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -28.772371 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 02:40:09] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -28.774415 | E_var:     0.0961 | E_err:   0.004845
[2025-10-06 02:40:12] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -28.766789 | E_var:     0.1146 | E_err:   0.005289
[2025-10-06 02:40:14] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -28.775027 | E_var:     0.1416 | E_err:   0.005880
[2025-10-06 02:40:17] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -28.775608 | E_var:     0.0909 | E_err:   0.004711
[2025-10-06 02:40:17] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 02:40:19] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -28.768448 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 02:40:21] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -28.774128 | E_var:     0.1401 | E_err:   0.005849
[2025-10-06 02:40:24] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -28.778452 | E_var:     0.1035 | E_err:   0.005027
[2025-10-06 02:40:26] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -28.777801 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 02:40:29] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -28.761406 | E_var:     0.1143 | E_err:   0.005282
[2025-10-06 02:40:31] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -28.757151 | E_var:     0.0823 | E_err:   0.004481
[2025-10-06 02:40:34] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -28.778124 | E_var:     0.1050 | E_err:   0.005063
[2025-10-06 02:40:36] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -28.772084 | E_var:     0.1233 | E_err:   0.005486
[2025-10-06 02:40:38] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -28.774003 | E_var:     0.1174 | E_err:   0.005353
[2025-10-06 02:40:41] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -28.766582 | E_var:     0.0941 | E_err:   0.004794
[2025-10-06 02:40:43] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -28.770793 | E_var:     0.1149 | E_err:   0.005298
[2025-10-06 02:40:46] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -28.771987 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 02:40:48] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -28.782103 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 02:40:51] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -28.766142 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 02:40:53] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -28.764331 | E_var:     0.0865 | E_err:   0.004595
[2025-10-06 02:40:55] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -28.766561 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 02:40:58] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -28.764854 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 02:41:00] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -28.772738 | E_var:     0.1151 | E_err:   0.005302
[2025-10-06 02:41:03] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -28.773257 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 02:41:05] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -28.768681 | E_var:     0.1220 | E_err:   0.005457
[2025-10-06 02:41:08] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -28.770584 | E_var:     0.0991 | E_err:   0.004919
[2025-10-06 02:41:10] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -28.767224 | E_var:     0.1014 | E_err:   0.004976
[2025-10-06 02:41:12] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -28.770763 | E_var:     0.0958 | E_err:   0.004836
[2025-10-06 02:41:15] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -28.771693 | E_var:     0.1088 | E_err:   0.005153
[2025-10-06 02:41:17] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -28.771211 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 02:41:20] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -28.774562 | E_var:     0.0921 | E_err:   0.004742
[2025-10-06 02:41:22] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -28.772491 | E_var:     0.1189 | E_err:   0.005387
[2025-10-06 02:41:25] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -28.775329 | E_var:     0.1092 | E_err:   0.005164
[2025-10-06 02:41:27] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -28.766719 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 02:41:29] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -28.770837 | E_var:     0.1100 | E_err:   0.005181
[2025-10-06 02:41:32] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -28.773435 | E_var:     0.1083 | E_err:   0.005142
[2025-10-06 02:41:34] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -28.761369 | E_var:     0.1020 | E_err:   0.004989
[2025-10-06 02:41:37] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -28.770007 | E_var:     0.1056 | E_err:   0.005078
[2025-10-06 02:41:39] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -28.770874 | E_var:     0.1242 | E_err:   0.005507
[2025-10-06 02:41:42] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -28.771692 | E_var:     0.1084 | E_err:   0.005145
[2025-10-06 02:41:44] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -28.765041 | E_var:     0.0989 | E_err:   0.004915
[2025-10-06 02:41:46] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -28.774195 | E_var:     0.0987 | E_err:   0.004909
[2025-10-06 02:41:49] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -28.769924 | E_var:     0.2450 | E_err:   0.007735
[2025-10-06 02:41:51] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -28.768226 | E_var:     0.1022 | E_err:   0.004996
[2025-10-06 02:41:54] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -28.773646 | E_var:     0.0953 | E_err:   0.004824
[2025-10-06 02:41:56] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -28.770762 | E_var:     0.1530 | E_err:   0.006111
[2025-10-06 02:41:59] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -28.782312 | E_var:     0.1311 | E_err:   0.005657
[2025-10-06 02:42:01] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -28.782746 | E_var:     0.1464 | E_err:   0.005978
[2025-10-06 02:42:03] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -28.768008 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 02:42:06] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -28.769718 | E_var:     0.1305 | E_err:   0.005645
[2025-10-06 02:42:08] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -28.760439 | E_var:     0.1227 | E_err:   0.005473
[2025-10-06 02:42:11] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -28.767412 | E_var:     0.0944 | E_err:   0.004801
[2025-10-06 02:42:13] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -28.773936 | E_var:     0.0940 | E_err:   0.004791
[2025-10-06 02:42:16] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -28.784821 | E_var:     0.1237 | E_err:   0.005495
[2025-10-06 02:42:18] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -28.771719 | E_var:     0.1143 | E_err:   0.005284
[2025-10-06 02:42:20] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -28.774187 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 02:42:23] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -28.769498 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 02:42:25] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -28.771386 | E_var:     0.1465 | E_err:   0.005980
[2025-10-06 02:42:28] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -28.768380 | E_var:     0.1069 | E_err:   0.005109
[2025-10-06 02:42:30] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -28.772047 | E_var:     0.0929 | E_err:   0.004761
[2025-10-06 02:42:33] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -28.762002 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 02:42:35] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -28.771738 | E_var:     0.0956 | E_err:   0.004831
[2025-10-06 02:42:37] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -28.762452 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 02:42:40] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -28.767856 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 02:42:42] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -28.775018 | E_var:     0.1095 | E_err:   0.005170
[2025-10-06 02:42:45] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -28.766516 | E_var:     0.1456 | E_err:   0.005961
[2025-10-06 02:42:47] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -28.774304 | E_var:     0.0897 | E_err:   0.004679
[2025-10-06 02:42:49] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -28.775872 | E_var:     0.1013 | E_err:   0.004974
[2025-10-06 02:42:52] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -28.770513 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 02:42:54] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -28.765442 | E_var:     0.1481 | E_err:   0.006013
[2025-10-06 02:42:57] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -28.777234 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 02:42:59] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -28.763556 | E_var:     0.1031 | E_err:   0.005016
[2025-10-06 02:43:02] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -28.770643 | E_var:     0.0909 | E_err:   0.004712
[2025-10-06 02:43:04] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -28.774895 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 02:43:06] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -28.776936 | E_var:     0.0986 | E_err:   0.004905
[2025-10-06 02:43:09] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -28.776181 | E_var:     0.1336 | E_err:   0.005711
[2025-10-06 02:43:11] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -28.771174 | E_var:     0.1266 | E_err:   0.005560
[2025-10-06 02:43:14] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -28.764500 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 02:43:16] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -28.767653 | E_var:     0.0834 | E_err:   0.004512
[2025-10-06 02:43:19] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -28.758232 | E_var:     0.1475 | E_err:   0.006001
[2025-10-06 02:43:21] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -28.772490 | E_var:     0.1208 | E_err:   0.005430
[2025-10-06 02:43:23] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -28.770180 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 02:43:26] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -28.773766 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 02:43:28] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -28.764907 | E_var:     0.0930 | E_err:   0.004765
[2025-10-06 02:43:31] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -28.760634 | E_var:     0.1483 | E_err:   0.006017
[2025-10-06 02:43:33] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -28.773499 | E_var:     0.1239 | E_err:   0.005500
[2025-10-06 02:43:36] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -28.764937 | E_var:     0.1280 | E_err:   0.005590
[2025-10-06 02:43:38] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -28.783260 | E_var:     0.1026 | E_err:   0.005004
[2025-10-06 02:43:40] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -28.781351 | E_var:     0.1010 | E_err:   0.004965
[2025-10-06 02:43:43] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -28.773800 | E_var:     0.0906 | E_err:   0.004703
[2025-10-06 02:43:45] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -28.772980 | E_var:     0.1054 | E_err:   0.005073
[2025-10-06 02:43:48] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -28.775645 | E_var:     0.0826 | E_err:   0.004490
[2025-10-06 02:43:50] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -28.769282 | E_var:     0.1248 | E_err:   0.005520
[2025-10-06 02:43:52] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -28.772002 | E_var:     0.1211 | E_err:   0.005437
[2025-10-06 02:43:55] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -28.774790 | E_var:     0.1107 | E_err:   0.005199
[2025-10-06 02:43:57] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -28.770335 | E_var:     0.1032 | E_err:   0.005019
[2025-10-06 02:44:00] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -28.771583 | E_var:     0.0972 | E_err:   0.004872
[2025-10-06 02:44:02] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -28.770826 | E_var:     0.1030 | E_err:   0.005015
[2025-10-06 02:44:05] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -28.774908 | E_var:     0.1222 | E_err:   0.005462
[2025-10-06 02:44:07] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -28.768999 | E_var:     0.1422 | E_err:   0.005893
[2025-10-06 02:44:10] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -28.767300 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 02:44:12] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -28.771936 | E_var:     0.0795 | E_err:   0.004407
[2025-10-06 02:44:14] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -28.768947 | E_var:     0.1081 | E_err:   0.005136
[2025-10-06 02:44:17] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -28.767055 | E_var:     0.1198 | E_err:   0.005408
[2025-10-06 02:44:19] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -28.765254 | E_var:     0.1186 | E_err:   0.005380
[2025-10-06 02:44:19] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 02:44:22] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -28.780217 | E_var:     0.1098 | E_err:   0.005177
[2025-10-06 02:44:24] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -28.768828 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 02:44:27] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -28.772824 | E_var:     0.0845 | E_err:   0.004541
[2025-10-06 02:44:29] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -28.768299 | E_var:     0.0694 | E_err:   0.004115
[2025-10-06 02:44:31] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -28.775497 | E_var:     0.0934 | E_err:   0.004775
[2025-10-06 02:44:34] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -28.754439 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 02:44:36] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -28.765157 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 02:44:39] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -28.777260 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 02:44:41] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -28.771139 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 02:44:44] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -28.767248 | E_var:     0.1354 | E_err:   0.005749
[2025-10-06 02:44:46] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -28.763161 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 02:44:48] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -28.771241 | E_var:     0.1199 | E_err:   0.005410
[2025-10-06 02:44:51] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -28.772696 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 02:44:53] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -28.761637 | E_var:     0.0968 | E_err:   0.004862
[2025-10-06 02:44:56] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -28.777249 | E_var:     0.1182 | E_err:   0.005372
[2025-10-06 02:44:58] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -28.766090 | E_var:     0.1098 | E_err:   0.005177
[2025-10-06 02:45:01] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -28.770719 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 02:45:03] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -28.770917 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 02:45:05] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -28.778223 | E_var:     0.1080 | E_err:   0.005135
[2025-10-06 02:45:08] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -28.781175 | E_var:     0.1110 | E_err:   0.005205
[2025-10-06 02:45:10] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -28.769515 | E_var:     0.1072 | E_err:   0.005116
[2025-10-06 02:45:13] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -28.770023 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 02:45:15] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -28.768891 | E_var:     0.1086 | E_err:   0.005150
[2025-10-06 02:45:18] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -28.775176 | E_var:     0.1196 | E_err:   0.005404
[2025-10-06 02:45:20] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -28.771602 | E_var:     0.0958 | E_err:   0.004836
[2025-10-06 02:45:22] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -28.774960 | E_var:     0.1176 | E_err:   0.005358
[2025-10-06 02:45:25] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -28.770552 | E_var:     0.0870 | E_err:   0.004609
[2025-10-06 02:45:27] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -28.767583 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 02:45:30] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -28.768740 | E_var:     0.1099 | E_err:   0.005179
[2025-10-06 02:45:32] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -28.767855 | E_var:     0.1139 | E_err:   0.005272
[2025-10-06 02:45:35] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -28.773250 | E_var:     0.1327 | E_err:   0.005692
[2025-10-06 02:45:37] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -28.772226 | E_var:     0.1078 | E_err:   0.005130
[2025-10-06 02:45:39] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -28.767327 | E_var:     0.0932 | E_err:   0.004769
[2025-10-06 02:45:42] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -28.774426 | E_var:     0.1167 | E_err:   0.005337
[2025-10-06 02:45:44] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -28.769433 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 02:45:47] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -28.770095 | E_var:     0.1961 | E_err:   0.006920
[2025-10-06 02:45:49] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -28.766147 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 02:45:52] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -28.765601 | E_var:     0.3959 | E_err:   0.009831
[2025-10-06 02:45:54] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -28.771434 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 02:45:56] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -28.770948 | E_var:     0.0988 | E_err:   0.004910
[2025-10-06 02:45:59] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -28.763968 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 02:46:01] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -28.767005 | E_var:     0.1013 | E_err:   0.004972
[2025-10-06 02:46:04] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -28.766942 | E_var:     0.1499 | E_err:   0.006049
[2025-10-06 02:46:06] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -28.771900 | E_var:     0.1499 | E_err:   0.006049
[2025-10-06 02:46:09] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -28.762483 | E_var:     0.9156 | E_err:   0.014951
[2025-10-06 02:46:11] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -28.775802 | E_var:     0.1106 | E_err:   0.005196
[2025-10-06 02:46:13] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -28.766940 | E_var:     0.0950 | E_err:   0.004815
[2025-10-06 02:46:16] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -28.770914 | E_var:     0.0719 | E_err:   0.004191
[2025-10-06 02:46:18] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -28.772706 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 02:46:21] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -28.773284 | E_var:     0.1131 | E_err:   0.005254
[2025-10-06 02:46:21] 🔄 RESTART #2 | Period: 600
[2025-10-06 02:46:23] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -28.770705 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 02:46:26] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -28.766351 | E_var:     0.1021 | E_err:   0.004993
[2025-10-06 02:46:28] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -28.767927 | E_var:     0.0985 | E_err:   0.004903
[2025-10-06 02:46:30] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -28.768088 | E_var:     0.1621 | E_err:   0.006290
[2025-10-06 02:46:33] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -28.773084 | E_var:     0.0895 | E_err:   0.004674
[2025-10-06 02:46:35] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -28.768784 | E_var:     0.1003 | E_err:   0.004948
[2025-10-06 02:46:38] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -28.771974 | E_var:     0.0967 | E_err:   0.004859
[2025-10-06 02:46:40] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -28.765411 | E_var:     0.1262 | E_err:   0.005550
[2025-10-06 02:46:43] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -28.777625 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 02:46:45] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -28.776493 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 02:46:47] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -28.769869 | E_var:     0.1126 | E_err:   0.005243
[2025-10-06 02:46:50] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -28.776870 | E_var:     0.0951 | E_err:   0.004818
[2025-10-06 02:46:52] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -28.775025 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 02:46:55] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -28.765020 | E_var:     0.1219 | E_err:   0.005456
[2025-10-06 02:46:57] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -28.769473 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 02:47:00] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -28.773302 | E_var:     0.0910 | E_err:   0.004715
[2025-10-06 02:47:02] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -28.777401 | E_var:     0.1006 | E_err:   0.004957
[2025-10-06 02:47:04] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -28.769521 | E_var:     0.2194 | E_err:   0.007318
[2025-10-06 02:47:07] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -28.768756 | E_var:     0.1034 | E_err:   0.005024
[2025-10-06 02:47:09] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -28.767817 | E_var:     0.1232 | E_err:   0.005485
[2025-10-06 02:47:12] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -28.764440 | E_var:     0.1401 | E_err:   0.005848
[2025-10-06 02:47:14] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -28.768804 | E_var:     0.1174 | E_err:   0.005353
[2025-10-06 02:47:17] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -28.768280 | E_var:     0.1020 | E_err:   0.004990
[2025-10-06 02:47:19] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -28.763695 | E_var:     0.1002 | E_err:   0.004945
[2025-10-06 02:47:21] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -28.764121 | E_var:     0.0958 | E_err:   0.004836
[2025-10-06 02:47:24] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -28.778996 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 02:47:26] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -28.768243 | E_var:     0.1429 | E_err:   0.005907
[2025-10-06 02:47:29] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -28.777912 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 02:47:31] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -28.773834 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 02:47:34] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -28.769644 | E_var:     0.1164 | E_err:   0.005332
[2025-10-06 02:47:36] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -28.771888 | E_var:     0.1068 | E_err:   0.005106
[2025-10-06 02:47:38] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -28.767521 | E_var:     0.1287 | E_err:   0.005605
[2025-10-06 02:47:41] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -28.769570 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 02:47:43] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -28.771543 | E_var:     0.1808 | E_err:   0.006643
[2025-10-06 02:47:46] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -28.775078 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 02:47:48] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -28.778401 | E_var:     0.1643 | E_err:   0.006333
[2025-10-06 02:47:51] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -28.772290 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 02:47:53] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -28.760490 | E_var:     0.0966 | E_err:   0.004857
[2025-10-06 02:47:55] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -28.765792 | E_var:     0.1182 | E_err:   0.005373
[2025-10-06 02:47:58] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -28.771903 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 02:48:00] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -28.778723 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 02:48:03] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -28.793226 | E_var:     0.1543 | E_err:   0.006137
[2025-10-06 02:48:05] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -28.776147 | E_var:     0.1113 | E_err:   0.005214
[2025-10-06 02:48:08] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -28.769152 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 02:48:10] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -28.771632 | E_var:     0.0834 | E_err:   0.004512
[2025-10-06 02:48:12] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -28.768761 | E_var:     0.0995 | E_err:   0.004928
[2025-10-06 02:48:15] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -28.763897 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 02:48:17] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -28.769742 | E_var:     0.1113 | E_err:   0.005213
[2025-10-06 02:48:20] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -28.775086 | E_var:     0.0917 | E_err:   0.004732
[2025-10-06 02:48:22] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -28.766890 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 02:48:22] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 02:48:25] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -28.774057 | E_var:     0.1238 | E_err:   0.005497
[2025-10-06 02:48:27] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -28.771148 | E_var:     0.1011 | E_err:   0.004969
[2025-10-06 02:48:29] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -28.768545 | E_var:     0.0827 | E_err:   0.004492
[2025-10-06 02:48:32] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -28.778158 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 02:48:34] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -28.774392 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 02:48:37] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -28.780559 | E_var:     0.1101 | E_err:   0.005186
[2025-10-06 02:48:39] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -28.765373 | E_var:     0.1132 | E_err:   0.005258
[2025-10-06 02:48:42] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -28.773676 | E_var:     0.1137 | E_err:   0.005269
[2025-10-06 02:48:44] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -28.775797 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 02:48:46] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -28.771693 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 02:48:49] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -28.772408 | E_var:     0.0922 | E_err:   0.004744
[2025-10-06 02:48:51] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -28.775961 | E_var:     0.0955 | E_err:   0.004829
[2025-10-06 02:48:54] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -28.767304 | E_var:     0.1008 | E_err:   0.004960
[2025-10-06 02:48:56] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -28.768424 | E_var:     0.0897 | E_err:   0.004681
[2025-10-06 02:48:59] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -28.776737 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 02:49:01] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -28.767769 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 02:49:04] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -28.762209 | E_var:     0.0885 | E_err:   0.004649
[2025-10-06 02:49:06] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -28.774349 | E_var:     0.0930 | E_err:   0.004765
[2025-10-06 02:49:08] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -28.770521 | E_var:     0.1061 | E_err:   0.005091
[2025-10-06 02:49:11] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -28.765553 | E_var:     0.1467 | E_err:   0.005984
[2025-10-06 02:49:13] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -28.760321 | E_var:     0.0766 | E_err:   0.004323
[2025-10-06 02:49:16] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -28.779379 | E_var:     0.1847 | E_err:   0.006716
[2025-10-06 02:49:18] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -28.779061 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 02:49:20] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -28.762021 | E_var:     0.1181 | E_err:   0.005370
[2025-10-06 02:49:23] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -28.773519 | E_var:     0.0923 | E_err:   0.004747
[2025-10-06 02:49:25] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -28.767899 | E_var:     0.1004 | E_err:   0.004951
[2025-10-06 02:49:28] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -28.772747 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 02:49:30] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -28.768238 | E_var:     0.1101 | E_err:   0.005185
[2025-10-06 02:49:33] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -28.784137 | E_var:     0.5467 | E_err:   0.011553
[2025-10-06 02:49:35] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -28.778385 | E_var:     0.1198 | E_err:   0.005408
[2025-10-06 02:49:38] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -28.765163 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 02:49:40] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -28.774936 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 02:49:42] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -28.763308 | E_var:     0.0933 | E_err:   0.004773
[2025-10-06 02:49:45] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -28.765769 | E_var:     0.1364 | E_err:   0.005771
[2025-10-06 02:49:47] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -28.768676 | E_var:     0.1271 | E_err:   0.005570
[2025-10-06 02:49:50] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -28.768671 | E_var:     0.1253 | E_err:   0.005530
[2025-10-06 02:49:52] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -28.757962 | E_var:     0.1134 | E_err:   0.005263
[2025-10-06 02:49:55] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -28.779977 | E_var:     0.0894 | E_err:   0.004673
[2025-10-06 02:49:57] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -28.771198 | E_var:     0.1084 | E_err:   0.005145
[2025-10-06 02:49:59] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -28.770695 | E_var:     0.1055 | E_err:   0.005076
[2025-10-06 02:50:02] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -28.768121 | E_var:     0.1126 | E_err:   0.005242
[2025-10-06 02:50:04] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -28.775388 | E_var:     0.1122 | E_err:   0.005233
[2025-10-06 02:50:07] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -28.775957 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 02:50:09] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -28.762165 | E_var:     0.1141 | E_err:   0.005277
[2025-10-06 02:50:12] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -28.768719 | E_var:     0.1395 | E_err:   0.005837
[2025-10-06 02:50:14] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -28.766774 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 02:50:16] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -28.779352 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 02:50:19] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -28.765209 | E_var:     0.1356 | E_err:   0.005754
[2025-10-06 02:50:21] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -28.776500 | E_var:     0.0852 | E_err:   0.004560
[2025-10-06 02:50:24] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -28.773393 | E_var:     0.1043 | E_err:   0.005047
[2025-10-06 02:50:26] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -28.771706 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 02:50:28] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -28.767804 | E_var:     0.1060 | E_err:   0.005087
[2025-10-06 02:50:31] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -28.766690 | E_var:     0.0978 | E_err:   0.004885
[2025-10-06 02:50:33] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -28.762363 | E_var:     0.3017 | E_err:   0.008583
[2025-10-06 02:50:36] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -28.776334 | E_var:     0.2181 | E_err:   0.007297
[2025-10-06 02:50:38] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -28.766785 | E_var:     0.1216 | E_err:   0.005449
[2025-10-06 02:50:41] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -28.772365 | E_var:     0.1177 | E_err:   0.005361
[2025-10-06 02:50:43] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -28.772274 | E_var:     0.0905 | E_err:   0.004699
[2025-10-06 02:50:46] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -28.761549 | E_var:     0.0977 | E_err:   0.004884
[2025-10-06 02:50:48] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -28.778688 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 02:50:50] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -28.770264 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 02:50:53] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -28.777637 | E_var:     0.1308 | E_err:   0.005651
[2025-10-06 02:50:55] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -28.765685 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 02:50:58] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -28.762340 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 02:51:00] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -28.773989 | E_var:     0.1625 | E_err:   0.006299
[2025-10-06 02:51:02] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -28.769877 | E_var:     0.0959 | E_err:   0.004840
[2025-10-06 02:51:05] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -28.774763 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 02:51:07] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -28.780609 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 02:51:10] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -28.775521 | E_var:     0.1056 | E_err:   0.005077
[2025-10-06 02:51:12] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -28.767534 | E_var:     0.1183 | E_err:   0.005374
[2025-10-06 02:51:15] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -28.774262 | E_var:     0.1143 | E_err:   0.005281
[2025-10-06 02:51:17] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -28.772593 | E_var:     0.1601 | E_err:   0.006253
[2025-10-06 02:51:19] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -28.764704 | E_var:     0.1050 | E_err:   0.005062
[2025-10-06 02:51:22] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -28.769101 | E_var:     0.0952 | E_err:   0.004822
[2025-10-06 02:51:24] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -28.778319 | E_var:     0.1032 | E_err:   0.005019
[2025-10-06 02:51:27] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -28.776104 | E_var:     0.0956 | E_err:   0.004831
[2025-10-06 02:51:29] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -28.776677 | E_var:     0.1456 | E_err:   0.005962
[2025-10-06 02:51:32] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -28.762821 | E_var:     0.1176 | E_err:   0.005358
[2025-10-06 02:51:34] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -28.771735 | E_var:     0.0991 | E_err:   0.004919
[2025-10-06 02:51:36] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -28.775735 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 02:51:39] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -28.761323 | E_var:     0.2029 | E_err:   0.007038
[2025-10-06 02:51:41] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -28.777650 | E_var:     0.1114 | E_err:   0.005215
[2025-10-06 02:51:44] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -28.773102 | E_var:     0.0835 | E_err:   0.004516
[2025-10-06 02:51:46] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -28.764109 | E_var:     0.1345 | E_err:   0.005729
[2025-10-06 02:51:49] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -28.771141 | E_var:     0.1090 | E_err:   0.005159
[2025-10-06 02:51:51] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -28.772094 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 02:51:53] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -28.768438 | E_var:     0.1754 | E_err:   0.006544
[2025-10-06 02:51:56] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -28.772856 | E_var:     0.1013 | E_err:   0.004974
[2025-10-06 02:51:58] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -28.772419 | E_var:     0.1178 | E_err:   0.005363
[2025-10-06 02:52:01] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -28.768688 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 02:52:03] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -28.763448 | E_var:     0.1088 | E_err:   0.005155
[2025-10-06 02:52:06] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -28.772820 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 02:52:08] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -28.766247 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 02:52:10] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -28.767996 | E_var:     0.1753 | E_err:   0.006542
[2025-10-06 02:52:13] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -28.774318 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 02:52:15] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -28.778701 | E_var:     0.1077 | E_err:   0.005127
[2025-10-06 02:52:18] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -28.769690 | E_var:     0.1344 | E_err:   0.005727
[2025-10-06 02:52:20] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -28.769095 | E_var:     0.1630 | E_err:   0.006309
[2025-10-06 02:52:23] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -28.773983 | E_var:     0.0933 | E_err:   0.004774
[2025-10-06 02:52:25] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -28.769468 | E_var:     0.0996 | E_err:   0.004932
[2025-10-06 02:52:25] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 02:52:27] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -28.766967 | E_var:     0.0984 | E_err:   0.004901
[2025-10-06 02:52:30] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -28.770600 | E_var:     0.1198 | E_err:   0.005409
[2025-10-06 02:52:32] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -28.780593 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 02:52:35] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -28.766410 | E_var:     0.1092 | E_err:   0.005164
[2025-10-06 02:52:37] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -28.775474 | E_var:     0.1082 | E_err:   0.005139
[2025-10-06 02:52:40] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -28.764461 | E_var:     0.1466 | E_err:   0.005982
[2025-10-06 02:52:42] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -28.773723 | E_var:     0.1036 | E_err:   0.005028
[2025-10-06 02:52:44] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -28.769170 | E_var:     0.1063 | E_err:   0.005095
[2025-10-06 02:52:47] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -28.764486 | E_var:     0.1338 | E_err:   0.005715
[2025-10-06 02:52:49] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -28.773404 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 02:52:52] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -28.779744 | E_var:     0.1155 | E_err:   0.005309
[2025-10-06 02:52:54] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -28.770911 | E_var:     0.0949 | E_err:   0.004814
[2025-10-06 02:52:57] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -28.764679 | E_var:     0.0817 | E_err:   0.004465
[2025-10-06 02:52:59] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -28.768330 | E_var:     0.1236 | E_err:   0.005494
[2025-10-06 02:53:01] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -28.769629 | E_var:     0.1214 | E_err:   0.005445
[2025-10-06 02:53:04] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -28.778075 | E_var:     0.1171 | E_err:   0.005346
[2025-10-06 02:53:06] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -28.773432 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 02:53:09] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -28.759207 | E_var:     0.1205 | E_err:   0.005423
[2025-10-06 02:53:11] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -28.763078 | E_var:     0.0956 | E_err:   0.004832
[2025-10-06 02:53:14] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -28.773303 | E_var:     0.0973 | E_err:   0.004875
[2025-10-06 02:53:16] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -28.769951 | E_var:     0.0957 | E_err:   0.004835
[2025-10-06 02:53:18] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -28.770357 | E_var:     0.0779 | E_err:   0.004362
[2025-10-06 02:53:21] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -28.769578 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 02:53:23] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -28.768639 | E_var:     0.1014 | E_err:   0.004976
[2025-10-06 02:53:26] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -28.774886 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 02:53:28] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -28.776644 | E_var:     0.1089 | E_err:   0.005157
[2025-10-06 02:53:31] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -28.777588 | E_var:     0.1000 | E_err:   0.004942
[2025-10-06 02:53:33] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -28.771075 | E_var:     0.1371 | E_err:   0.005786
[2025-10-06 02:53:35] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -28.763643 | E_var:     0.1199 | E_err:   0.005410
[2025-10-06 02:53:38] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -28.766571 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 02:53:40] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -28.775275 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 02:53:43] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -28.773157 | E_var:     0.1201 | E_err:   0.005416
[2025-10-06 02:53:45] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -28.774302 | E_var:     0.1754 | E_err:   0.006544
[2025-10-06 02:53:48] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -28.771084 | E_var:     0.1077 | E_err:   0.005128
[2025-10-06 02:53:50] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -28.779924 | E_var:     0.1078 | E_err:   0.005129
[2025-10-06 02:53:52] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -28.763993 | E_var:     0.0866 | E_err:   0.004598
[2025-10-06 02:53:55] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -28.766416 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 02:53:57] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -28.767971 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 02:54:00] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -28.778207 | E_var:     0.1096 | E_err:   0.005173
[2025-10-06 02:54:02] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -28.770009 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 02:54:05] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -28.778982 | E_var:     0.1058 | E_err:   0.005082
[2025-10-06 02:54:07] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -28.761039 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 02:54:09] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -28.767168 | E_var:     0.0965 | E_err:   0.004853
[2025-10-06 02:54:12] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -28.765362 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 02:54:14] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -28.773510 | E_var:     0.1216 | E_err:   0.005450
[2025-10-06 02:54:17] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -28.777660 | E_var:     0.1239 | E_err:   0.005499
[2025-10-06 02:54:19] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -28.766670 | E_var:     0.1080 | E_err:   0.005135
[2025-10-06 02:54:22] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -28.762927 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 02:54:24] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -28.770023 | E_var:     0.0986 | E_err:   0.004906
[2025-10-06 02:54:26] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -28.776474 | E_var:     0.1041 | E_err:   0.005041
[2025-10-06 02:54:29] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -28.774026 | E_var:     0.1271 | E_err:   0.005570
[2025-10-06 02:54:31] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -28.766560 | E_var:     0.1343 | E_err:   0.005725
[2025-10-06 02:54:34] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -28.771474 | E_var:     0.1143 | E_err:   0.005283
[2025-10-06 02:54:36] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -28.760081 | E_var:     0.1585 | E_err:   0.006220
[2025-10-06 02:54:38] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -28.763711 | E_var:     0.3517 | E_err:   0.009267
[2025-10-06 02:54:41] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -28.772393 | E_var:     0.1001 | E_err:   0.004942
[2025-10-06 02:54:43] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -28.774010 | E_var:     0.1002 | E_err:   0.004946
[2025-10-06 02:54:46] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -28.769301 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 02:54:48] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -28.770327 | E_var:     0.1207 | E_err:   0.005427
[2025-10-06 02:54:51] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -28.771557 | E_var:     0.0951 | E_err:   0.004819
[2025-10-06 02:54:53] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -28.773776 | E_var:     0.0916 | E_err:   0.004729
[2025-10-06 02:54:55] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -28.774653 | E_var:     0.1091 | E_err:   0.005161
[2025-10-06 02:54:58] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -28.772048 | E_var:     0.1035 | E_err:   0.005026
[2025-10-06 02:55:00] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -28.779329 | E_var:     0.1161 | E_err:   0.005324
[2025-10-06 02:55:03] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -28.779591 | E_var:     0.1116 | E_err:   0.005220
[2025-10-06 02:55:05] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -28.776907 | E_var:     0.0952 | E_err:   0.004821
[2025-10-06 02:55:08] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -28.777879 | E_var:     0.1454 | E_err:   0.005959
[2025-10-06 02:55:10] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -28.765424 | E_var:     0.1201 | E_err:   0.005414
[2025-10-06 02:55:12] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -28.763726 | E_var:     0.0774 | E_err:   0.004347
[2025-10-06 02:55:15] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -28.774943 | E_var:     0.1010 | E_err:   0.004966
[2025-10-06 02:55:17] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -28.765735 | E_var:     0.0974 | E_err:   0.004877
[2025-10-06 02:55:20] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -28.769151 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 02:55:22] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -28.772189 | E_var:     0.1098 | E_err:   0.005177
[2025-10-06 02:55:25] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -28.772412 | E_var:     0.0966 | E_err:   0.004855
[2025-10-06 02:55:27] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -28.767479 | E_var:     0.0973 | E_err:   0.004873
[2025-10-06 02:55:29] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -28.771302 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 02:55:32] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -28.763420 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 02:55:34] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -28.768616 | E_var:     0.1215 | E_err:   0.005446
[2025-10-06 02:55:37] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -28.774332 | E_var:     0.0937 | E_err:   0.004782
[2025-10-06 02:55:39] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -28.769404 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 02:55:42] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -28.774618 | E_var:     0.1150 | E_err:   0.005300
[2025-10-06 02:55:44] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -28.763987 | E_var:     0.1020 | E_err:   0.004991
[2025-10-06 02:55:46] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -28.769861 | E_var:     0.1014 | E_err:   0.004975
[2025-10-06 02:55:49] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -28.777098 | E_var:     0.1391 | E_err:   0.005828
[2025-10-06 02:55:51] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -28.764534 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 02:55:54] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -28.771505 | E_var:     0.1351 | E_err:   0.005743
[2025-10-06 02:55:56] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -28.774825 | E_var:     0.1555 | E_err:   0.006161
[2025-10-06 02:55:59] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -28.763818 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 02:56:01] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -28.775245 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 02:56:03] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -28.775566 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 02:56:06] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -28.777915 | E_var:     0.0875 | E_err:   0.004621
[2025-10-06 02:56:08] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -28.771637 | E_var:     0.1289 | E_err:   0.005610
[2025-10-06 02:56:11] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -28.771746 | E_var:     0.1091 | E_err:   0.005162
[2025-10-06 02:56:13] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -28.759338 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 02:56:16] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -28.776842 | E_var:     0.0960 | E_err:   0.004841
[2025-10-06 02:56:18] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -28.776224 | E_var:     0.1029 | E_err:   0.005013
[2025-10-06 02:56:20] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -28.768679 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 02:56:23] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -28.773721 | E_var:     0.1213 | E_err:   0.005442
[2025-10-06 02:56:25] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -28.765635 | E_var:     0.1227 | E_err:   0.005472
[2025-10-06 02:56:28] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -28.775278 | E_var:     0.1219 | E_err:   0.005456
[2025-10-06 02:56:28] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 02:56:30] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -28.771131 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 02:56:33] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -28.763550 | E_var:     0.0976 | E_err:   0.004880
[2025-10-06 02:56:35] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -28.775708 | E_var:     0.1085 | E_err:   0.005146
[2025-10-06 02:56:37] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -28.776973 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 02:56:40] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -28.775425 | E_var:     0.0957 | E_err:   0.004833
[2025-10-06 02:56:42] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -28.776776 | E_var:     0.1399 | E_err:   0.005845
[2025-10-06 02:56:45] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -28.765022 | E_var:     0.1016 | E_err:   0.004980
[2025-10-06 02:56:47] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -28.771670 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 02:56:49] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -28.777992 | E_var:     0.1181 | E_err:   0.005371
[2025-10-06 02:56:52] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -28.772002 | E_var:     0.1114 | E_err:   0.005214
[2025-10-06 02:56:54] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -28.776670 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 02:56:57] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -28.775375 | E_var:     0.0934 | E_err:   0.004776
[2025-10-06 02:56:59] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -28.774208 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 02:57:02] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -28.768502 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 02:57:04] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -28.773428 | E_var:     0.0995 | E_err:   0.004929
[2025-10-06 02:57:07] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -28.769466 | E_var:     0.0740 | E_err:   0.004251
[2025-10-06 02:57:09] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -28.770745 | E_var:     0.1087 | E_err:   0.005151
[2025-10-06 02:57:11] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -28.767985 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 02:57:14] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -28.774707 | E_var:     0.1066 | E_err:   0.005102
[2025-10-06 02:57:16] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -28.767023 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 02:57:19] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -28.763685 | E_var:     0.1085 | E_err:   0.005148
[2025-10-06 02:57:21] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -28.766046 | E_var:     0.1384 | E_err:   0.005813
[2025-10-06 02:57:23] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -28.773212 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 02:57:26] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -28.775610 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 02:57:28] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -28.763220 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 02:57:31] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -28.763184 | E_var:     0.1220 | E_err:   0.005458
[2025-10-06 02:57:33] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -28.767728 | E_var:     0.1299 | E_err:   0.005630
[2025-10-06 02:57:36] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -28.770382 | E_var:     0.1259 | E_err:   0.005543
[2025-10-06 02:57:38] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -28.768431 | E_var:     0.0914 | E_err:   0.004723
[2025-10-06 02:57:41] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -28.776949 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 02:57:43] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -28.779690 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 02:57:45] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -28.777891 | E_var:     0.0997 | E_err:   0.004934
[2025-10-06 02:57:48] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -28.776753 | E_var:     0.0769 | E_err:   0.004332
[2025-10-06 02:57:50] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -28.774595 | E_var:     0.1038 | E_err:   0.005035
[2025-10-06 02:57:53] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -28.766867 | E_var:     0.1540 | E_err:   0.006131
[2025-10-06 02:57:55] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -28.771039 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 02:57:57] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -28.769708 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 02:58:00] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -28.772952 | E_var:     0.1048 | E_err:   0.005059
[2025-10-06 02:58:02] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -28.772608 | E_var:     0.0893 | E_err:   0.004670
[2025-10-06 02:58:05] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -28.777204 | E_var:     0.1676 | E_err:   0.006396
[2025-10-06 02:58:07] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -28.763359 | E_var:     0.1010 | E_err:   0.004965
[2025-10-06 02:58:10] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -28.769982 | E_var:     0.3644 | E_err:   0.009432
[2025-10-06 02:58:12] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -28.765232 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 02:58:14] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -28.770787 | E_var:     0.0996 | E_err:   0.004932
[2025-10-06 02:58:17] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -28.772503 | E_var:     0.1757 | E_err:   0.006549
[2025-10-06 02:58:19] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -28.768402 | E_var:     0.0864 | E_err:   0.004593
[2025-10-06 02:58:22] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -28.764656 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 02:58:24] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -28.767173 | E_var:     0.1664 | E_err:   0.006374
[2025-10-06 02:58:27] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -28.769075 | E_var:     0.1206 | E_err:   0.005426
[2025-10-06 02:58:29] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -28.767238 | E_var:     0.1172 | E_err:   0.005349
[2025-10-06 02:58:31] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -28.768512 | E_var:     0.0933 | E_err:   0.004774
[2025-10-06 02:58:34] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -28.771414 | E_var:     0.1208 | E_err:   0.005430
[2025-10-06 02:58:36] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -28.776110 | E_var:     0.0868 | E_err:   0.004604
[2025-10-06 02:58:39] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -28.760024 | E_var:     0.1234 | E_err:   0.005489
[2025-10-06 02:58:41] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -28.771730 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 02:58:44] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -28.769725 | E_var:     0.1066 | E_err:   0.005101
[2025-10-06 02:58:46] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -28.773030 | E_var:     0.1105 | E_err:   0.005194
[2025-10-06 02:58:48] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -28.771614 | E_var:     0.1124 | E_err:   0.005239
[2025-10-06 02:58:51] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -28.770948 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 02:58:53] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -28.771563 | E_var:     0.1234 | E_err:   0.005488
[2025-10-06 02:58:56] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -28.769966 | E_var:     0.1479 | E_err:   0.006010
[2025-10-06 02:58:58] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -28.769324 | E_var:     0.1119 | E_err:   0.005227
[2025-10-06 02:59:01] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -28.768783 | E_var:     0.1090 | E_err:   0.005159
[2025-10-06 02:59:03] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -28.768749 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 02:59:05] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -28.767686 | E_var:     0.0986 | E_err:   0.004906
[2025-10-06 02:59:08] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -28.777411 | E_var:     0.1094 | E_err:   0.005167
[2025-10-06 02:59:10] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -28.768514 | E_var:     0.1009 | E_err:   0.004962
[2025-10-06 02:59:13] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -28.781145 | E_var:     0.1047 | E_err:   0.005056
[2025-10-06 02:59:15] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -28.771194 | E_var:     0.0883 | E_err:   0.004642
[2025-10-06 02:59:18] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -28.775380 | E_var:     0.0990 | E_err:   0.004915
[2025-10-06 02:59:20] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -28.783272 | E_var:     0.4590 | E_err:   0.010586
[2025-10-06 02:59:22] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -28.767376 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 02:59:25] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -28.765645 | E_var:     0.1077 | E_err:   0.005127
[2025-10-06 02:59:27] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -28.769707 | E_var:     0.0929 | E_err:   0.004762
[2025-10-06 02:59:30] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -28.773005 | E_var:     0.0829 | E_err:   0.004500
[2025-10-06 02:59:32] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -28.760510 | E_var:     0.0945 | E_err:   0.004803
[2025-10-06 02:59:35] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -28.768768 | E_var:     0.1172 | E_err:   0.005348
[2025-10-06 02:59:37] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -28.774822 | E_var:     0.1231 | E_err:   0.005482
[2025-10-06 02:59:39] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -28.772581 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 02:59:42] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -28.765056 | E_var:     0.1260 | E_err:   0.005547
[2025-10-06 02:59:44] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -28.777059 | E_var:     0.1046 | E_err:   0.005055
[2025-10-06 02:59:47] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -28.766679 | E_var:     0.0872 | E_err:   0.004615
[2025-10-06 02:59:49] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -28.779819 | E_var:     0.0988 | E_err:   0.004912
[2025-10-06 02:59:52] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -28.773242 | E_var:     0.1824 | E_err:   0.006673
[2025-10-06 02:59:54] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -28.765694 | E_var:     0.3936 | E_err:   0.009802
[2025-10-06 02:59:56] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -28.771196 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 02:59:59] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -28.768279 | E_var:     0.1023 | E_err:   0.004998
[2025-10-06 03:00:01] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -28.774989 | E_var:     0.1577 | E_err:   0.006204
[2025-10-06 03:00:04] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -28.778098 | E_var:     0.1317 | E_err:   0.005671
[2025-10-06 03:00:06] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -28.777470 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 03:00:09] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -28.776860 | E_var:     0.0981 | E_err:   0.004895
[2025-10-06 03:00:11] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -28.773343 | E_var:     0.0990 | E_err:   0.004917
[2025-10-06 03:00:13] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -28.764200 | E_var:     0.1368 | E_err:   0.005780
[2025-10-06 03:00:16] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -28.770707 | E_var:     0.1169 | E_err:   0.005342
[2025-10-06 03:00:18] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -28.766949 | E_var:     0.1210 | E_err:   0.005435
[2025-10-06 03:00:21] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -28.769099 | E_var:     0.1438 | E_err:   0.005926
[2025-10-06 03:00:23] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -28.766139 | E_var:     0.0947 | E_err:   0.004807
[2025-10-06 03:00:25] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -28.774691 | E_var:     0.1150 | E_err:   0.005298
[2025-10-06 03:00:28] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -28.776469 | E_var:     0.1867 | E_err:   0.006752
[2025-10-06 03:00:30] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -28.768310 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 03:00:30] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 03:00:33] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -28.771391 | E_var:     0.1231 | E_err:   0.005483
[2025-10-06 03:00:35] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -28.761431 | E_var:     0.2386 | E_err:   0.007632
[2025-10-06 03:00:38] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -28.765504 | E_var:     0.1220 | E_err:   0.005458
[2025-10-06 03:00:40] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -28.770971 | E_var:     0.1198 | E_err:   0.005407
[2025-10-06 03:00:42] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -28.776110 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 03:00:45] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -28.773077 | E_var:     0.1305 | E_err:   0.005644
[2025-10-06 03:00:47] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -28.763901 | E_var:     0.1008 | E_err:   0.004962
[2025-10-06 03:00:50] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -28.765488 | E_var:     0.0912 | E_err:   0.004718
[2025-10-06 03:00:52] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -28.778809 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 03:00:55] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -28.767484 | E_var:     0.1184 | E_err:   0.005376
[2025-10-06 03:00:57] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -28.770038 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 03:00:59] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -28.772145 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 03:01:02] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -28.769462 | E_var:     0.1104 | E_err:   0.005193
[2025-10-06 03:01:04] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -28.768661 | E_var:     0.1112 | E_err:   0.005211
[2025-10-06 03:01:07] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -28.774706 | E_var:     0.0937 | E_err:   0.004784
[2025-10-06 03:01:09] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -28.775673 | E_var:     0.1128 | E_err:   0.005249
[2025-10-06 03:01:12] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -28.768642 | E_var:     0.1328 | E_err:   0.005693
[2025-10-06 03:01:14] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -28.777509 | E_var:     0.0920 | E_err:   0.004739
[2025-10-06 03:01:16] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -28.767020 | E_var:     0.3352 | E_err:   0.009047
[2025-10-06 03:01:19] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -28.764648 | E_var:     0.1289 | E_err:   0.005609
[2025-10-06 03:01:21] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -28.767175 | E_var:     0.0751 | E_err:   0.004283
[2025-10-06 03:01:24] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -28.779560 | E_var:     0.1041 | E_err:   0.005041
[2025-10-06 03:01:26] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -28.768544 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 03:01:29] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -28.765777 | E_var:     0.0996 | E_err:   0.004932
[2025-10-06 03:01:31] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -28.767493 | E_var:     0.0921 | E_err:   0.004741
[2025-10-06 03:01:33] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -28.774696 | E_var:     0.1259 | E_err:   0.005544
[2025-10-06 03:01:36] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -28.769453 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 03:01:38] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -28.775587 | E_var:     0.1696 | E_err:   0.006435
[2025-10-06 03:01:41] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -28.767039 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 03:01:43] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -28.776278 | E_var:     0.1016 | E_err:   0.004980
[2025-10-06 03:01:46] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -28.777821 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 03:01:48] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -28.771832 | E_var:     0.1136 | E_err:   0.005267
[2025-10-06 03:01:50] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -28.776989 | E_var:     0.0795 | E_err:   0.004405
[2025-10-06 03:01:53] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -28.769855 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 03:01:55] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -28.770494 | E_var:     0.1011 | E_err:   0.004968
[2025-10-06 03:01:58] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -28.771696 | E_var:     0.0976 | E_err:   0.004880
[2025-10-06 03:02:00] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -28.775074 | E_var:     0.1248 | E_err:   0.005519
[2025-10-06 03:02:03] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -28.772813 | E_var:     0.1042 | E_err:   0.005044
[2025-10-06 03:02:05] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -28.768117 | E_var:     0.0958 | E_err:   0.004836
[2025-10-06 03:02:07] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -28.771270 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 03:02:10] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -28.775315 | E_var:     0.0978 | E_err:   0.004887
[2025-10-06 03:02:12] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -28.779582 | E_var:     0.1720 | E_err:   0.006479
[2025-10-06 03:02:15] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -28.772695 | E_var:     0.1110 | E_err:   0.005206
[2025-10-06 03:02:17] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -28.766020 | E_var:     0.1500 | E_err:   0.006051
[2025-10-06 03:02:20] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -28.770382 | E_var:     0.1285 | E_err:   0.005600
[2025-10-06 03:02:22] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -28.758916 | E_var:     0.1854 | E_err:   0.006728
[2025-10-06 03:02:24] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -28.770763 | E_var:     0.0960 | E_err:   0.004841
[2025-10-06 03:02:27] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -28.775394 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 03:02:29] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -28.775868 | E_var:     0.1094 | E_err:   0.005167
[2025-10-06 03:02:32] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -28.767874 | E_var:     0.1227 | E_err:   0.005472
[2025-10-06 03:02:34] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -28.775125 | E_var:     0.1175 | E_err:   0.005356
[2025-10-06 03:02:37] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -28.776433 | E_var:     0.0926 | E_err:   0.004755
[2025-10-06 03:02:39] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -28.785127 | E_var:     0.1845 | E_err:   0.006711
[2025-10-06 03:02:41] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -28.778174 | E_var:     0.1365 | E_err:   0.005772
[2025-10-06 03:02:44] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -28.765065 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 03:02:46] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -28.779664 | E_var:     0.1227 | E_err:   0.005473
[2025-10-06 03:02:49] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -28.766311 | E_var:     0.1349 | E_err:   0.005738
[2025-10-06 03:02:51] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -28.773503 | E_var:     0.0991 | E_err:   0.004918
[2025-10-06 03:02:54] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -28.773275 | E_var:     0.2249 | E_err:   0.007410
[2025-10-06 03:02:56] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -28.760955 | E_var:     0.1481 | E_err:   0.006014
[2025-10-06 03:02:58] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -28.763694 | E_var:     0.1105 | E_err:   0.005193
[2025-10-06 03:03:01] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -28.772016 | E_var:     0.1005 | E_err:   0.004953
[2025-10-06 03:03:03] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -28.769546 | E_var:     0.1064 | E_err:   0.005096
[2025-10-06 03:03:06] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -28.770704 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 03:03:08] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -28.767688 | E_var:     0.0986 | E_err:   0.004907
[2025-10-06 03:03:11] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -28.771362 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 03:03:13] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -28.761164 | E_var:     0.1012 | E_err:   0.004970
[2025-10-06 03:03:15] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -28.766968 | E_var:     0.1513 | E_err:   0.006077
[2025-10-06 03:03:18] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -28.776829 | E_var:     0.0976 | E_err:   0.004881
[2025-10-06 03:03:20] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -28.777354 | E_var:     0.0960 | E_err:   0.004840
[2025-10-06 03:03:23] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -28.771786 | E_var:     0.1228 | E_err:   0.005474
[2025-10-06 03:03:25] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -28.764288 | E_var:     0.0985 | E_err:   0.004904
[2025-10-06 03:03:28] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -28.778064 | E_var:     0.1064 | E_err:   0.005097
[2025-10-06 03:03:30] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -28.778185 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 03:03:32] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -28.765230 | E_var:     0.0862 | E_err:   0.004588
[2025-10-06 03:03:35] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -28.769723 | E_var:     0.1040 | E_err:   0.005040
[2025-10-06 03:03:37] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -28.769036 | E_var:     0.1600 | E_err:   0.006250
[2025-10-06 03:03:40] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -28.768239 | E_var:     0.1027 | E_err:   0.005006
[2025-10-06 03:03:42] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -28.763930 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 03:03:44] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -28.767189 | E_var:     0.0931 | E_err:   0.004767
[2025-10-06 03:03:47] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -28.776418 | E_var:     0.0910 | E_err:   0.004715
[2025-10-06 03:03:49] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -28.774082 | E_var:     0.0892 | E_err:   0.004665
[2025-10-06 03:03:52] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -28.775726 | E_var:     0.1005 | E_err:   0.004954
[2025-10-06 03:03:54] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -28.778517 | E_var:     0.0902 | E_err:   0.004692
[2025-10-06 03:03:57] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -28.772963 | E_var:     0.0839 | E_err:   0.004526
[2025-10-06 03:03:59] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -28.779043 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 03:04:01] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -28.773215 | E_var:     0.1272 | E_err:   0.005572
[2025-10-06 03:04:04] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -28.775061 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 03:04:06] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -28.763908 | E_var:     0.1333 | E_err:   0.005706
[2025-10-06 03:04:09] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -28.775877 | E_var:     0.1175 | E_err:   0.005357
[2025-10-06 03:04:11] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -28.761939 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 03:04:14] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -28.775008 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 03:04:16] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -28.774757 | E_var:     0.0766 | E_err:   0.004323
[2025-10-06 03:04:18] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -28.773110 | E_var:     0.1036 | E_err:   0.005029
[2025-10-06 03:04:21] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -28.771166 | E_var:     0.1025 | E_err:   0.005001
[2025-10-06 03:04:23] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -28.768513 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 03:04:26] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -28.774883 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 03:04:28] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -28.769127 | E_var:     0.1284 | E_err:   0.005599
[2025-10-06 03:04:31] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -28.768594 | E_var:     0.0993 | E_err:   0.004923
[2025-10-06 03:04:33] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -28.771357 | E_var:     0.0951 | E_err:   0.004818
[2025-10-06 03:04:33] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 03:04:35] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -28.767811 | E_var:     0.1006 | E_err:   0.004956
[2025-10-06 03:04:38] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -28.764714 | E_var:     0.1088 | E_err:   0.005155
[2025-10-06 03:04:40] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -28.772501 | E_var:     0.0882 | E_err:   0.004641
[2025-10-06 03:04:43] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -28.770995 | E_var:     0.0880 | E_err:   0.004634
[2025-10-06 03:04:45] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -28.769031 | E_var:     0.1575 | E_err:   0.006202
[2025-10-06 03:04:48] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -28.775945 | E_var:     0.1014 | E_err:   0.004977
[2025-10-06 03:04:50] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -28.774238 | E_var:     0.0994 | E_err:   0.004926
[2025-10-06 03:04:52] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -28.769964 | E_var:     0.0979 | E_err:   0.004890
[2025-10-06 03:04:55] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -28.767346 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 03:04:57] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -28.778998 | E_var:     0.0945 | E_err:   0.004802
[2025-10-06 03:05:00] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -28.770319 | E_var:     0.1151 | E_err:   0.005301
[2025-10-06 03:05:02] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -28.766188 | E_var:     0.1002 | E_err:   0.004946
[2025-10-06 03:05:04] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -28.769707 | E_var:     0.1110 | E_err:   0.005205
[2025-10-06 03:05:07] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -28.765734 | E_var:     0.1859 | E_err:   0.006737
[2025-10-06 03:05:09] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -28.773081 | E_var:     0.0847 | E_err:   0.004546
[2025-10-06 03:05:12] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -28.768972 | E_var:     0.0996 | E_err:   0.004930
[2025-10-06 03:05:14] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -28.767393 | E_var:     0.1197 | E_err:   0.005407
[2025-10-06 03:05:17] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -28.765391 | E_var:     0.1101 | E_err:   0.005184
[2025-10-06 03:05:19] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -28.772951 | E_var:     0.1295 | E_err:   0.005623
[2025-10-06 03:05:22] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -28.772596 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 03:05:24] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -28.772371 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 03:05:26] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -28.771568 | E_var:     0.1589 | E_err:   0.006229
[2025-10-06 03:05:29] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -28.773798 | E_var:     0.0977 | E_err:   0.004883
[2025-10-06 03:05:31] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -28.768394 | E_var:     0.1424 | E_err:   0.005896
[2025-10-06 03:05:34] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -28.769287 | E_var:     0.1334 | E_err:   0.005707
[2025-10-06 03:05:36] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -28.768646 | E_var:     0.1151 | E_err:   0.005302
[2025-10-06 03:05:39] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -28.771898 | E_var:     0.1325 | E_err:   0.005688
[2025-10-06 03:05:41] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -28.773147 | E_var:     0.0923 | E_err:   0.004748
[2025-10-06 03:05:43] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -28.764647 | E_var:     0.1514 | E_err:   0.006079
[2025-10-06 03:05:46] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -28.773478 | E_var:     0.0978 | E_err:   0.004887
[2025-10-06 03:05:48] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -28.770834 | E_var:     0.0805 | E_err:   0.004434
[2025-10-06 03:05:51] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -28.772791 | E_var:     0.1061 | E_err:   0.005090
[2025-10-06 03:05:53] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -28.765601 | E_var:     0.3700 | E_err:   0.009505
[2025-10-06 03:05:55] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -28.779412 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 03:05:58] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -28.773326 | E_var:     0.0955 | E_err:   0.004827
[2025-10-06 03:06:00] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -28.766462 | E_var:     0.1155 | E_err:   0.005311
[2025-10-06 03:06:03] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -28.772998 | E_var:     0.0990 | E_err:   0.004918
[2025-10-06 03:06:05] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -28.769649 | E_var:     0.1041 | E_err:   0.005042
[2025-10-06 03:06:08] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -28.771163 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 03:06:10] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -28.769378 | E_var:     0.0971 | E_err:   0.004869
[2025-10-06 03:06:12] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -28.775133 | E_var:     0.1016 | E_err:   0.004980
[2025-10-06 03:06:15] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -28.774900 | E_var:     0.1210 | E_err:   0.005434
[2025-10-06 03:06:17] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -28.761430 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 03:06:20] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -28.766230 | E_var:     0.1538 | E_err:   0.006128
[2025-10-06 03:06:22] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -28.771465 | E_var:     0.1014 | E_err:   0.004976
[2025-10-06 03:06:25] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -28.777547 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 03:06:27] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -28.767863 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 03:06:29] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -28.771264 | E_var:     0.0885 | E_err:   0.004648
[2025-10-06 03:06:32] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -28.770704 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 03:06:34] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -28.775062 | E_var:     0.0946 | E_err:   0.004806
[2025-10-06 03:06:37] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -28.775651 | E_var:     0.1083 | E_err:   0.005141
[2025-10-06 03:06:39] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -28.772388 | E_var:     0.1358 | E_err:   0.005758
[2025-10-06 03:06:42] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -28.764389 | E_var:     0.1635 | E_err:   0.006319
[2025-10-06 03:06:44] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -28.773784 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 03:06:46] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -28.759356 | E_var:     0.1225 | E_err:   0.005469
[2025-10-06 03:06:49] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -28.768126 | E_var:     0.2431 | E_err:   0.007704
[2025-10-06 03:06:51] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -28.768711 | E_var:     0.1026 | E_err:   0.005004
[2025-10-06 03:06:54] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -28.779015 | E_var:     0.1175 | E_err:   0.005356
[2025-10-06 03:06:56] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -28.772648 | E_var:     0.1181 | E_err:   0.005369
[2025-10-06 03:06:59] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -28.773682 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 03:07:01] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -28.769724 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 03:07:03] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -28.771904 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 03:07:06] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -28.764295 | E_var:     0.1093 | E_err:   0.005165
[2025-10-06 03:07:08] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -28.771174 | E_var:     0.1122 | E_err:   0.005234
[2025-10-06 03:07:11] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -28.772228 | E_var:     0.1142 | E_err:   0.005281
[2025-10-06 03:07:13] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -28.765288 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 03:07:16] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -28.768278 | E_var:     0.1083 | E_err:   0.005143
[2025-10-06 03:07:18] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -28.774034 | E_var:     0.1040 | E_err:   0.005040
[2025-10-06 03:07:20] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -28.773275 | E_var:     0.1134 | E_err:   0.005261
[2025-10-06 03:07:23] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -28.759619 | E_var:     0.1189 | E_err:   0.005388
[2025-10-06 03:07:25] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -28.773599 | E_var:     0.1345 | E_err:   0.005731
[2025-10-06 03:07:28] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -28.763693 | E_var:     0.1230 | E_err:   0.005480
[2025-10-06 03:07:30] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -28.773262 | E_var:     0.0939 | E_err:   0.004788
[2025-10-06 03:07:33] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -28.767961 | E_var:     0.1087 | E_err:   0.005152
[2025-10-06 03:07:35] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -28.766435 | E_var:     0.1055 | E_err:   0.005076
[2025-10-06 03:07:38] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -28.773468 | E_var:     0.0845 | E_err:   0.004542
[2025-10-06 03:07:40] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -28.770615 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 03:07:42] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -28.778744 | E_var:     0.0977 | E_err:   0.004885
[2025-10-06 03:07:45] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -28.772989 | E_var:     0.1496 | E_err:   0.006043
[2025-10-06 03:07:47] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -28.767003 | E_var:     0.1049 | E_err:   0.005062
[2025-10-06 03:07:50] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -28.774061 | E_var:     0.1268 | E_err:   0.005563
[2025-10-06 03:07:52] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -28.765368 | E_var:     0.0954 | E_err:   0.004826
[2025-10-06 03:07:55] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -28.775627 | E_var:     0.0909 | E_err:   0.004710
[2025-10-06 03:07:57] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -28.771678 | E_var:     0.0969 | E_err:   0.004864
[2025-10-06 03:08:00] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -28.767093 | E_var:     0.1166 | E_err:   0.005335
[2025-10-06 03:08:02] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -28.763276 | E_var:     0.1111 | E_err:   0.005208
[2025-10-06 03:08:04] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -28.772597 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 03:08:07] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -28.774705 | E_var:     0.1031 | E_err:   0.005018
[2025-10-06 03:08:09] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -28.773500 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 03:08:12] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -28.780650 | E_var:     0.1395 | E_err:   0.005836
[2025-10-06 03:08:14] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -28.777851 | E_var:     0.0824 | E_err:   0.004486
[2025-10-06 03:08:17] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -28.767813 | E_var:     0.1263 | E_err:   0.005553
[2025-10-06 03:08:19] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -28.770413 | E_var:     0.0900 | E_err:   0.004687
[2025-10-06 03:08:22] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -28.770133 | E_var:     0.0910 | E_err:   0.004714
[2025-10-06 03:08:24] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -28.779741 | E_var:     0.0943 | E_err:   0.004799
[2025-10-06 03:08:26] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -28.766819 | E_var:     0.1118 | E_err:   0.005225
[2025-10-06 03:08:29] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -28.774332 | E_var:     0.0956 | E_err:   0.004832
[2025-10-06 03:08:31] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -28.771007 | E_var:     0.0837 | E_err:   0.004520
[2025-10-06 03:08:34] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -28.769634 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 03:08:36] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -28.757734 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 03:08:36] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 03:08:39] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -28.771625 | E_var:     0.0818 | E_err:   0.004468
[2025-10-06 03:08:41] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -28.780759 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 03:08:44] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -28.786838 | E_var:     0.1222 | E_err:   0.005463
[2025-10-06 03:08:46] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -28.771588 | E_var:     0.0920 | E_err:   0.004738
[2025-10-06 03:08:48] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -28.767756 | E_var:     0.0975 | E_err:   0.004880
[2025-10-06 03:08:51] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -28.779988 | E_var:     0.0820 | E_err:   0.004476
[2025-10-06 03:08:53] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -28.764607 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 03:08:56] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -28.774953 | E_var:     0.1205 | E_err:   0.005424
[2025-10-06 03:08:58] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -28.760259 | E_var:     0.1025 | E_err:   0.005002
[2025-10-06 03:09:01] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -28.779587 | E_var:     0.0843 | E_err:   0.004535
[2025-10-06 03:09:03] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -28.763690 | E_var:     0.1845 | E_err:   0.006712
[2025-10-06 03:09:05] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -28.767474 | E_var:     0.0855 | E_err:   0.004569
[2025-10-06 03:09:08] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -28.768296 | E_var:     0.0839 | E_err:   0.004527
[2025-10-06 03:09:10] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -28.780198 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 03:09:13] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -28.773063 | E_var:     0.0890 | E_err:   0.004662
[2025-10-06 03:09:15] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -28.775144 | E_var:     0.0955 | E_err:   0.004828
[2025-10-06 03:09:18] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -28.772737 | E_var:     0.1952 | E_err:   0.006904
[2025-10-06 03:09:20] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -28.773488 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 03:09:22] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -28.777615 | E_var:     0.1143 | E_err:   0.005283
[2025-10-06 03:09:25] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -28.781594 | E_var:     0.1028 | E_err:   0.005009
[2025-10-06 03:09:27] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -28.763214 | E_var:     0.1052 | E_err:   0.005068
[2025-10-06 03:09:30] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -28.775564 | E_var:     0.1141 | E_err:   0.005278
[2025-10-06 03:09:32] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -28.774747 | E_var:     0.1321 | E_err:   0.005680
[2025-10-06 03:09:35] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -28.778635 | E_var:     0.1151 | E_err:   0.005300
[2025-10-06 03:09:37] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -28.771362 | E_var:     0.0870 | E_err:   0.004608
[2025-10-06 03:09:40] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -28.775119 | E_var:     0.1473 | E_err:   0.005997
[2025-10-06 03:09:42] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -28.771819 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 03:09:44] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -28.774298 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 03:09:47] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -28.776332 | E_var:     0.0845 | E_err:   0.004541
[2025-10-06 03:09:49] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -28.772383 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 03:09:52] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -28.762974 | E_var:     0.1138 | E_err:   0.005270
[2025-10-06 03:09:54] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -28.769750 | E_var:     0.1258 | E_err:   0.005542
[2025-10-06 03:09:57] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -28.775794 | E_var:     0.1023 | E_err:   0.004997
[2025-10-06 03:09:59] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -28.778676 | E_var:     0.1320 | E_err:   0.005677
[2025-10-06 03:10:01] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -28.766330 | E_var:     0.1344 | E_err:   0.005728
[2025-10-06 03:10:04] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -28.777416 | E_var:     0.1107 | E_err:   0.005199
[2025-10-06 03:10:06] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -28.770973 | E_var:     0.1475 | E_err:   0.006001
[2025-10-06 03:10:09] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -28.776210 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 03:10:11] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -28.768836 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 03:10:13] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -28.766557 | E_var:     0.0806 | E_err:   0.004436
[2025-10-06 03:10:16] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -28.772189 | E_var:     0.0969 | E_err:   0.004864
[2025-10-06 03:10:18] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -28.772264 | E_var:     0.0988 | E_err:   0.004910
[2025-10-06 03:10:21] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -28.774356 | E_var:     0.0818 | E_err:   0.004469
[2025-10-06 03:10:23] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -28.769448 | E_var:     0.0969 | E_err:   0.004863
[2025-10-06 03:10:26] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -28.766004 | E_var:     0.0958 | E_err:   0.004837
[2025-10-06 03:10:28] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -28.773695 | E_var:     0.1073 | E_err:   0.005119
[2025-10-06 03:10:31] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -28.772418 | E_var:     0.1360 | E_err:   0.005763
[2025-10-06 03:10:33] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -28.763290 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 03:10:35] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -28.767947 | E_var:     0.1446 | E_err:   0.005941
[2025-10-06 03:10:38] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -28.768129 | E_var:     0.1905 | E_err:   0.006820
[2025-10-06 03:10:38] ======================================================================================================
[2025-10-06 03:10:38] ✅ Training completed successfully
[2025-10-06 03:10:38] Total restarts: 2
[2025-10-06 03:10:39] Final Energy: -28.76812945 ± 0.00682041
[2025-10-06 03:10:39] Final Variance: 0.190538
[2025-10-06 03:10:39] ======================================================================================================
[2025-10-06 03:10:39] ======================================================================================================
[2025-10-06 03:10:39] Training completed | Runtime: 2598.9s
[2025-10-06 03:10:39] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 03:10:39] ======================================================================================================
