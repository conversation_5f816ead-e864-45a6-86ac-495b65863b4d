[2025-10-06 18:16:37] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.76/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 18:16:37]   - 迭代次数: final
[2025-10-06 18:16:37]   - 能量: -27.129715-0.002179j ± 0.006878, Var: 0.193771
[2025-10-06 18:16:37]   - 时间戳: 2025-10-02T21:58:51.047883+08:00
[2025-10-06 18:16:55] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 18:16:55] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 18:16:55] ======================================================================================================
[2025-10-06 18:16:55] GCNN for Shastry-Sutherland Model
[2025-10-06 18:16:55] ======================================================================================================
[2025-10-06 18:16:55] System parameters:
[2025-10-06 18:16:55]   - System size: L=4, N=64
[2025-10-06 18:16:55]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-10-06 18:16:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 18:16:55] Model parameters:
[2025-10-06 18:16:55]   - Number of layers = 6
[2025-10-06 18:16:55]   - Number of features = 4
[2025-10-06 18:16:55]   - Total parameters = 20780
[2025-10-06 18:16:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 18:16:55] Training parameters:
[2025-10-06 18:16:55]   - Total iterations: 1050
[2025-10-06 18:16:55]   - Annealing cycles: 3
[2025-10-06 18:16:55]   - Initial period: 150
[2025-10-06 18:16:55]   - Period multiplier: 2.0
[2025-10-06 18:16:55]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 18:16:55]   - Samples: 4096
[2025-10-06 18:16:55]   - Discarded samples: 0
[2025-10-06 18:16:55]   - Chunk size: 4096
[2025-10-06 18:16:55]   - Diagonal shift: 0.15
[2025-10-06 18:16:55]   - Gradient clipping: 1.0
[2025-10-06 18:16:55]   - Checkpoint enabled: interval=100
[2025-10-06 18:16:55]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.77/model_L6F4/training/checkpoints
[2025-10-06 18:16:55] ------------------------------------------------------------------------------------------------------
[2025-10-06 18:16:55] Device status:
[2025-10-06 18:16:55]   - Devices model: NVIDIA H200 NVL
[2025-10-06 18:16:55]   - Number of devices: 1
[2025-10-06 18:16:55]   - Sharding: True
[2025-10-06 18:16:55] ======================================================================================================
[2025-10-06 18:17:39] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -27.504042 | E_var:     1.2415 | E_err:   0.017410
[2025-10-06 18:18:05] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -27.532310 | E_var:     0.2548 | E_err:   0.007887
[2025-10-06 18:18:09] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -27.538220 | E_var:     0.2028 | E_err:   0.007036
[2025-10-06 18:18:12] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -27.548885 | E_var:     0.2862 | E_err:   0.008359
[2025-10-06 18:18:16] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -27.538986 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 18:18:20] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -27.537515 | E_var:     0.2617 | E_err:   0.007993
[2025-10-06 18:18:23] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -27.542608 | E_var:     0.1565 | E_err:   0.006182
[2025-10-06 18:18:27] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -27.526182 | E_var:     0.1382 | E_err:   0.005809
[2025-10-06 18:18:31] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -27.537079 | E_var:     0.1964 | E_err:   0.006925
[2025-10-06 18:18:34] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -27.538410 | E_var:     0.1605 | E_err:   0.006261
[2025-10-06 18:18:38] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -27.539825 | E_var:     0.2196 | E_err:   0.007322
[2025-10-06 18:18:42] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -27.538524 | E_var:     0.1671 | E_err:   0.006387
[2025-10-06 18:18:45] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -27.533860 | E_var:     0.1789 | E_err:   0.006609
[2025-10-06 18:18:49] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -27.540883 | E_var:     0.1316 | E_err:   0.005669
[2025-10-06 18:18:53] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -27.533453 | E_var:     0.1362 | E_err:   0.005767
[2025-10-06 18:18:56] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -27.542823 | E_var:     0.1524 | E_err:   0.006099
[2025-10-06 18:19:00] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -27.542009 | E_var:     0.1214 | E_err:   0.005445
[2025-10-06 18:19:04] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -27.534189 | E_var:     0.1601 | E_err:   0.006253
[2025-10-06 18:19:07] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -27.547886 | E_var:     0.1399 | E_err:   0.005844
[2025-10-06 18:19:11] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -27.552010 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 18:19:14] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -27.532000 | E_var:     0.1525 | E_err:   0.006101
[2025-10-06 18:19:18] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -27.544382 | E_var:     0.1494 | E_err:   0.006039
[2025-10-06 18:19:22] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -27.544504 | E_var:     0.1565 | E_err:   0.006181
[2025-10-06 18:19:25] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -27.539637 | E_var:     0.1578 | E_err:   0.006208
[2025-10-06 18:19:29] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -27.544135 | E_var:     0.1799 | E_err:   0.006627
[2025-10-06 18:19:33] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -27.523665 | E_var:     0.2743 | E_err:   0.008183
[2025-10-06 18:19:36] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -27.552365 | E_var:     0.1227 | E_err:   0.005474
[2025-10-06 18:19:40] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -27.540771 | E_var:     0.1427 | E_err:   0.005903
[2025-10-06 18:19:44] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -27.548299 | E_var:     0.1699 | E_err:   0.006440
[2025-10-06 18:19:48] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -27.540784 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 18:19:51] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -27.536648 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 18:19:55] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -27.547865 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 18:19:59] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -27.555152 | E_var:     0.1429 | E_err:   0.005907
[2025-10-06 18:20:02] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -27.541357 | E_var:     0.1587 | E_err:   0.006224
[2025-10-06 18:20:06] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -27.544299 | E_var:     0.1488 | E_err:   0.006027
[2025-10-06 18:20:10] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -27.545231 | E_var:     0.1219 | E_err:   0.005455
[2025-10-06 18:20:13] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -27.535051 | E_var:     0.2894 | E_err:   0.008406
[2025-10-06 18:20:17] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -27.544622 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 18:20:21] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -27.542835 | E_var:     0.1284 | E_err:   0.005598
[2025-10-06 18:20:24] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -27.543574 | E_var:     0.1325 | E_err:   0.005689
[2025-10-06 18:20:28] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -27.543913 | E_var:     0.1927 | E_err:   0.006860
[2025-10-06 18:20:32] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -27.539043 | E_var:     0.1568 | E_err:   0.006188
[2025-10-06 18:20:35] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -27.549343 | E_var:     0.1441 | E_err:   0.005932
[2025-10-06 18:20:39] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -27.539530 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 18:20:43] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -27.539978 | E_var:     0.1813 | E_err:   0.006654
[2025-10-06 18:20:46] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -27.541921 | E_var:     0.1238 | E_err:   0.005497
[2025-10-06 18:20:50] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -27.537527 | E_var:     0.1297 | E_err:   0.005628
[2025-10-06 18:20:54] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -27.546374 | E_var:     0.1900 | E_err:   0.006811
[2025-10-06 18:20:57] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -27.550290 | E_var:     0.1365 | E_err:   0.005774
[2025-10-06 18:21:01] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -27.544003 | E_var:     0.1392 | E_err:   0.005830
[2025-10-06 18:21:05] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -27.536009 | E_var:     0.2249 | E_err:   0.007410
[2025-10-06 18:21:08] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -27.544700 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 18:21:12] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -27.547732 | E_var:     0.1270 | E_err:   0.005567
[2025-10-06 18:21:16] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -27.539434 | E_var:     0.2088 | E_err:   0.007140
[2025-10-06 18:21:19] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -27.542252 | E_var:     0.1590 | E_err:   0.006231
[2025-10-06 18:21:23] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -27.539262 | E_var:     0.1512 | E_err:   0.006076
[2025-10-06 18:21:27] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -27.539217 | E_var:     0.2125 | E_err:   0.007203
[2025-10-06 18:21:30] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -27.543614 | E_var:     0.1366 | E_err:   0.005776
[2025-10-06 18:21:34] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -27.540697 | E_var:     0.1492 | E_err:   0.006036
[2025-10-06 18:21:38] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -27.533546 | E_var:     0.1453 | E_err:   0.005956
[2025-10-06 18:21:41] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -27.539728 | E_var:     0.1707 | E_err:   0.006456
[2025-10-06 18:21:45] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -27.542860 | E_var:     0.1526 | E_err:   0.006105
[2025-10-06 18:21:49] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -27.538597 | E_var:     0.1338 | E_err:   0.005715
[2025-10-06 18:21:52] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -27.550519 | E_var:     0.1430 | E_err:   0.005908
[2025-10-06 18:21:56] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -27.550296 | E_var:     0.1700 | E_err:   0.006442
[2025-10-06 18:22:00] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -27.546203 | E_var:     0.1896 | E_err:   0.006804
[2025-10-06 18:22:03] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -27.542959 | E_var:     0.1167 | E_err:   0.005339
[2025-10-06 18:22:07] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -27.540116 | E_var:     0.1309 | E_err:   0.005653
[2025-10-06 18:22:11] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -27.537545 | E_var:     0.1339 | E_err:   0.005717
[2025-10-06 18:22:14] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -27.539399 | E_var:     0.1457 | E_err:   0.005964
[2025-10-06 18:22:18] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -27.544571 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 18:22:22] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -27.544466 | E_var:     0.1476 | E_err:   0.006003
[2025-10-06 18:22:25] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -27.539941 | E_var:     0.1262 | E_err:   0.005551
[2025-10-06 18:22:29] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -27.542990 | E_var:     0.2037 | E_err:   0.007052
[2025-10-06 18:22:33] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -27.537835 | E_var:     0.1340 | E_err:   0.005720
[2025-10-06 18:22:36] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -27.545916 | E_var:     0.1105 | E_err:   0.005195
[2025-10-06 18:22:40] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -27.538579 | E_var:     0.1359 | E_err:   0.005761
[2025-10-06 18:22:44] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -27.544978 | E_var:     0.1335 | E_err:   0.005710
[2025-10-06 18:22:47] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -27.540506 | E_var:     0.1749 | E_err:   0.006535
[2025-10-06 18:22:51] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -27.532311 | E_var:     0.1244 | E_err:   0.005510
[2025-10-06 18:22:55] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -27.536706 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 18:22:58] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -27.547114 | E_var:     0.1363 | E_err:   0.005769
[2025-10-06 18:23:02] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -27.541143 | E_var:     0.1374 | E_err:   0.005792
[2025-10-06 18:23:06] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -27.542603 | E_var:     0.1408 | E_err:   0.005863
[2025-10-06 18:23:09] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -27.548779 | E_var:     0.1704 | E_err:   0.006450
[2025-10-06 18:23:13] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -27.540193 | E_var:     0.1211 | E_err:   0.005438
[2025-10-06 18:23:17] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -27.537763 | E_var:     0.1502 | E_err:   0.006057
[2025-10-06 18:23:20] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -27.553032 | E_var:     0.1463 | E_err:   0.005977
[2025-10-06 18:23:24] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -27.541738 | E_var:     0.1493 | E_err:   0.006038
[2025-10-06 18:23:28] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -27.535407 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 18:23:31] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -27.543229 | E_var:     0.1891 | E_err:   0.006794
[2025-10-06 18:23:35] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -27.534580 | E_var:     0.1851 | E_err:   0.006723
[2025-10-06 18:23:39] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -27.541189 | E_var:     0.1572 | E_err:   0.006196
[2025-10-06 18:23:42] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -27.543676 | E_var:     0.1909 | E_err:   0.006828
[2025-10-06 18:23:46] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -27.555399 | E_var:     0.1651 | E_err:   0.006349
[2025-10-06 18:23:50] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -27.547517 | E_var:     0.1402 | E_err:   0.005852
[2025-10-06 18:23:53] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -27.555068 | E_var:     0.1521 | E_err:   0.006094
[2025-10-06 18:23:57] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -27.541813 | E_var:     0.1136 | E_err:   0.005266
[2025-10-06 18:24:01] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -27.546940 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 18:24:04] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -27.541863 | E_var:     0.1286 | E_err:   0.005604
[2025-10-06 18:24:04] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 18:24:08] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -27.541767 | E_var:     0.1213 | E_err:   0.005441
[2025-10-06 18:24:12] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -27.541968 | E_var:     0.1563 | E_err:   0.006177
[2025-10-06 18:24:15] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -27.548513 | E_var:     0.1291 | E_err:   0.005613
[2025-10-06 18:24:19] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -27.534469 | E_var:     0.2716 | E_err:   0.008142
[2025-10-06 18:24:23] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -27.549884 | E_var:     0.1571 | E_err:   0.006192
[2025-10-06 18:24:26] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -27.543848 | E_var:     0.1355 | E_err:   0.005752
[2025-10-06 18:24:30] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -27.547671 | E_var:     0.1414 | E_err:   0.005875
[2025-10-06 18:24:34] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -27.543300 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 18:24:37] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -27.536767 | E_var:     0.1503 | E_err:   0.006058
[2025-10-06 18:24:41] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -27.542770 | E_var:     0.1365 | E_err:   0.005774
[2025-10-06 18:24:45] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -27.555591 | E_var:     0.1427 | E_err:   0.005903
[2025-10-06 18:24:48] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -27.543889 | E_var:     0.1407 | E_err:   0.005862
[2025-10-06 18:24:52] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -27.544673 | E_var:     0.1116 | E_err:   0.005221
[2025-10-06 18:24:56] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -27.534217 | E_var:     0.1366 | E_err:   0.005774
[2025-10-06 18:24:59] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -27.541428 | E_var:     0.1454 | E_err:   0.005958
[2025-10-06 18:25:03] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -27.540144 | E_var:     0.1162 | E_err:   0.005326
[2025-10-06 18:25:07] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -27.536241 | E_var:     0.1274 | E_err:   0.005577
[2025-10-06 18:25:10] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -27.546069 | E_var:     0.1355 | E_err:   0.005751
[2025-10-06 18:25:14] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -27.540113 | E_var:     0.1528 | E_err:   0.006108
[2025-10-06 18:25:18] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -27.543708 | E_var:     0.1261 | E_err:   0.005549
[2025-10-06 18:25:21] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -27.530344 | E_var:     0.1312 | E_err:   0.005659
[2025-10-06 18:25:25] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -27.543093 | E_var:     0.1541 | E_err:   0.006134
[2025-10-06 18:25:29] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -27.544433 | E_var:     0.1186 | E_err:   0.005381
[2025-10-06 18:25:32] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -27.542097 | E_var:     0.1357 | E_err:   0.005755
[2025-10-06 18:25:36] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -27.547623 | E_var:     0.1365 | E_err:   0.005774
[2025-10-06 18:25:40] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -27.540552 | E_var:     0.1783 | E_err:   0.006597
[2025-10-06 18:25:43] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -27.555204 | E_var:     0.1387 | E_err:   0.005819
[2025-10-06 18:25:47] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -27.542469 | E_var:     0.1437 | E_err:   0.005924
[2025-10-06 18:25:51] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -27.545374 | E_var:     0.1455 | E_err:   0.005960
[2025-10-06 18:25:54] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -27.540188 | E_var:     0.1463 | E_err:   0.005976
[2025-10-06 18:25:58] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -27.532758 | E_var:     0.1763 | E_err:   0.006560
[2025-10-06 18:26:04] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -27.536179 | E_var:     0.1614 | E_err:   0.006278
[2025-10-06 18:26:07] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -27.554409 | E_var:     0.1311 | E_err:   0.005657
[2025-10-06 18:26:12] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -27.550812 | E_var:     0.2693 | E_err:   0.008109
[2025-10-06 18:26:15] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -27.542651 | E_var:     0.1708 | E_err:   0.006458
[2025-10-06 18:26:19] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -27.555591 | E_var:     0.1108 | E_err:   0.005202
[2025-10-06 18:26:23] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -27.549664 | E_var:     0.1308 | E_err:   0.005650
[2025-10-06 18:26:26] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -27.545072 | E_var:     0.1584 | E_err:   0.006219
[2025-10-06 18:26:30] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -27.544604 | E_var:     0.1297 | E_err:   0.005627
[2025-10-06 18:26:34] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -27.543669 | E_var:     0.1181 | E_err:   0.005369
[2025-10-06 18:26:37] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -27.544519 | E_var:     0.1222 | E_err:   0.005463
[2025-10-06 18:26:41] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -27.538824 | E_var:     0.2358 | E_err:   0.007587
[2025-10-06 18:26:45] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -27.548421 | E_var:     0.1572 | E_err:   0.006195
[2025-10-06 18:26:48] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -27.542824 | E_var:     0.1666 | E_err:   0.006378
[2025-10-06 18:26:52] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -27.551141 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 18:26:56] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -27.542887 | E_var:     0.1689 | E_err:   0.006422
[2025-10-06 18:26:59] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -27.544229 | E_var:     0.1409 | E_err:   0.005865
[2025-10-06 18:27:03] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -27.536528 | E_var:     0.1754 | E_err:   0.006543
[2025-10-06 18:27:07] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -27.533555 | E_var:     0.1449 | E_err:   0.005947
[2025-10-06 18:27:10] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -27.542163 | E_var:     0.1556 | E_err:   0.006163
[2025-10-06 18:27:10] 🔄 RESTART #1 | Period: 300
[2025-10-06 18:27:14] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -27.557295 | E_var:     0.1469 | E_err:   0.005988
[2025-10-06 18:27:18] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -27.549515 | E_var:     0.1229 | E_err:   0.005477
[2025-10-06 18:27:21] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -27.544908 | E_var:     0.1676 | E_err:   0.006396
[2025-10-06 18:27:25] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -27.543228 | E_var:     0.1218 | E_err:   0.005454
[2025-10-06 18:27:29] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -27.539172 | E_var:     0.1638 | E_err:   0.006323
[2025-10-06 18:27:32] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -27.548450 | E_var:     0.1444 | E_err:   0.005936
[2025-10-06 18:27:36] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -27.544300 | E_var:     0.1415 | E_err:   0.005877
[2025-10-06 18:27:40] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -27.545896 | E_var:     0.1359 | E_err:   0.005759
[2025-10-06 18:27:43] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -27.544508 | E_var:     0.1150 | E_err:   0.005299
[2025-10-06 18:27:47] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -27.544391 | E_var:     0.1604 | E_err:   0.006257
[2025-10-06 18:27:51] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -27.550047 | E_var:     0.1396 | E_err:   0.005839
[2025-10-06 18:27:54] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -27.544722 | E_var:     0.1416 | E_err:   0.005879
[2025-10-06 18:27:58] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -27.530079 | E_var:     0.1374 | E_err:   0.005792
[2025-10-06 18:28:02] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -27.547312 | E_var:     0.1303 | E_err:   0.005640
[2025-10-06 18:28:05] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -27.542510 | E_var:     0.1893 | E_err:   0.006798
[2025-10-06 18:28:09] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -27.543158 | E_var:     0.1378 | E_err:   0.005800
[2025-10-06 18:28:13] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -27.541823 | E_var:     0.1139 | E_err:   0.005273
[2025-10-06 18:28:16] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -27.536208 | E_var:     0.1357 | E_err:   0.005756
[2025-10-06 18:28:20] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -27.551659 | E_var:     0.1367 | E_err:   0.005778
[2025-10-06 18:28:24] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -27.547602 | E_var:     0.1126 | E_err:   0.005244
[2025-10-06 18:28:27] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -27.552140 | E_var:     0.1457 | E_err:   0.005964
[2025-10-06 18:28:31] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -27.534423 | E_var:     0.2654 | E_err:   0.008049
[2025-10-06 18:28:35] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -27.546845 | E_var:     0.1386 | E_err:   0.005817
[2025-10-06 18:28:38] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -27.547956 | E_var:     0.1129 | E_err:   0.005251
[2025-10-06 18:28:42] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -27.545614 | E_var:     0.1241 | E_err:   0.005503
[2025-10-06 18:28:46] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -27.538554 | E_var:     0.1074 | E_err:   0.005121
[2025-10-06 18:28:49] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -27.550499 | E_var:     0.1490 | E_err:   0.006031
[2025-10-06 18:28:53] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -27.544993 | E_var:     0.1507 | E_err:   0.006065
[2025-10-06 18:28:57] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -27.541098 | E_var:     0.1683 | E_err:   0.006409
[2025-10-06 18:29:00] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -27.533649 | E_var:     0.1404 | E_err:   0.005855
[2025-10-06 18:29:04] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -27.545345 | E_var:     0.1807 | E_err:   0.006642
[2025-10-06 18:29:08] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -27.546260 | E_var:     0.1343 | E_err:   0.005727
[2025-10-06 18:29:11] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -27.533988 | E_var:     0.1255 | E_err:   0.005536
[2025-10-06 18:29:15] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -27.544128 | E_var:     0.1307 | E_err:   0.005649
[2025-10-06 18:29:19] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -27.550116 | E_var:     0.1736 | E_err:   0.006511
[2025-10-06 18:29:22] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -27.533647 | E_var:     0.1389 | E_err:   0.005824
[2025-10-06 18:29:26] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -27.547302 | E_var:     0.1208 | E_err:   0.005431
[2025-10-06 18:29:30] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -27.536428 | E_var:     0.1233 | E_err:   0.005486
[2025-10-06 18:29:33] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -27.545046 | E_var:     0.1558 | E_err:   0.006168
[2025-10-06 18:29:37] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -27.539054 | E_var:     0.1522 | E_err:   0.006095
[2025-10-06 18:29:41] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -27.538890 | E_var:     0.1265 | E_err:   0.005557
[2025-10-06 18:29:44] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -27.551444 | E_var:     0.1909 | E_err:   0.006827
[2025-10-06 18:29:48] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -27.531497 | E_var:     0.1404 | E_err:   0.005854
[2025-10-06 18:29:52] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -27.544575 | E_var:     0.2024 | E_err:   0.007030
[2025-10-06 18:29:55] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -27.540615 | E_var:     0.1680 | E_err:   0.006404
[2025-10-06 18:29:59] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -27.540985 | E_var:     0.1294 | E_err:   0.005620
[2025-10-06 18:30:03] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -27.551818 | E_var:     0.1073 | E_err:   0.005118
[2025-10-06 18:30:06] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -27.552972 | E_var:     0.1681 | E_err:   0.006406
[2025-10-06 18:30:10] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -27.548687 | E_var:     0.1400 | E_err:   0.005846
[2025-10-06 18:30:14] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -27.543495 | E_var:     0.1144 | E_err:   0.005285
[2025-10-06 18:30:14] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 18:30:17] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -27.545809 | E_var:     0.1286 | E_err:   0.005602
[2025-10-06 18:30:21] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -27.538861 | E_var:     0.4684 | E_err:   0.010694
[2025-10-06 18:30:25] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -27.548990 | E_var:     0.1514 | E_err:   0.006079
[2025-10-06 18:30:28] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -27.539123 | E_var:     0.1436 | E_err:   0.005922
[2025-10-06 18:30:32] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -27.534677 | E_var:     0.1629 | E_err:   0.006306
[2025-10-06 18:30:36] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -27.536409 | E_var:     0.1488 | E_err:   0.006027
[2025-10-06 18:30:39] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -27.536408 | E_var:     0.1332 | E_err:   0.005702
[2025-10-06 18:30:43] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -27.553518 | E_var:     0.1251 | E_err:   0.005525
[2025-10-06 18:30:47] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -27.552953 | E_var:     0.1480 | E_err:   0.006011
[2025-10-06 18:30:50] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -27.554384 | E_var:     0.1346 | E_err:   0.005732
[2025-10-06 18:30:54] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -27.549304 | E_var:     0.1619 | E_err:   0.006288
[2025-10-06 18:30:58] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -27.544432 | E_var:     0.1372 | E_err:   0.005787
[2025-10-06 18:31:01] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -27.553300 | E_var:     0.1531 | E_err:   0.006114
[2025-10-06 18:31:05] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -27.544659 | E_var:     0.1448 | E_err:   0.005945
[2025-10-06 18:31:09] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -27.548228 | E_var:     0.1291 | E_err:   0.005614
[2025-10-06 18:31:12] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -27.549988 | E_var:     0.1502 | E_err:   0.006056
[2025-10-06 18:31:16] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -27.551240 | E_var:     0.2786 | E_err:   0.008247
[2025-10-06 18:31:20] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -27.544348 | E_var:     0.1694 | E_err:   0.006430
[2025-10-06 18:31:23] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -27.538163 | E_var:     0.2059 | E_err:   0.007091
[2025-10-06 18:31:27] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -27.540901 | E_var:     0.1732 | E_err:   0.006502
[2025-10-06 18:31:31] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -27.537030 | E_var:     0.1489 | E_err:   0.006028
[2025-10-06 18:31:34] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -27.554652 | E_var:     0.1558 | E_err:   0.006168
[2025-10-06 18:31:38] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -27.547418 | E_var:     0.1400 | E_err:   0.005847
[2025-10-06 18:31:42] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -27.540216 | E_var:     0.2297 | E_err:   0.007489
[2025-10-06 18:31:45] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -27.542828 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 18:31:49] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -27.541950 | E_var:     0.1576 | E_err:   0.006204
[2025-10-06 18:31:53] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -27.547386 | E_var:     0.2416 | E_err:   0.007680
[2025-10-06 18:31:56] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -27.548746 | E_var:     0.1674 | E_err:   0.006392
[2025-10-06 18:32:00] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -27.539114 | E_var:     0.1603 | E_err:   0.006257
[2025-10-06 18:32:04] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -27.551347 | E_var:     0.1196 | E_err:   0.005404
[2025-10-06 18:32:08] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -27.544586 | E_var:     0.1290 | E_err:   0.005613
[2025-10-06 18:32:12] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -27.545072 | E_var:     0.1452 | E_err:   0.005954
[2025-10-06 18:32:15] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -27.542972 | E_var:     0.1700 | E_err:   0.006442
[2025-10-06 18:32:19] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -27.546706 | E_var:     0.1063 | E_err:   0.005094
[2025-10-06 18:32:23] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -27.543995 | E_var:     0.1287 | E_err:   0.005605
[2025-10-06 18:32:26] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -27.545231 | E_var:     0.2429 | E_err:   0.007701
[2025-10-06 18:32:30] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -27.547750 | E_var:     0.1338 | E_err:   0.005716
[2025-10-06 18:32:33] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -27.542728 | E_var:     0.1461 | E_err:   0.005972
[2025-10-06 18:32:37] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -27.541989 | E_var:     0.1271 | E_err:   0.005570
[2025-10-06 18:32:41] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -27.539416 | E_var:     0.1010 | E_err:   0.004967
[2025-10-06 18:32:44] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -27.550469 | E_var:     0.1231 | E_err:   0.005482
[2025-10-06 18:32:48] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -27.546465 | E_var:     0.1271 | E_err:   0.005569
[2025-10-06 18:32:52] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -27.535194 | E_var:     0.1406 | E_err:   0.005859
[2025-10-06 18:32:55] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -27.537177 | E_var:     0.1444 | E_err:   0.005937
[2025-10-06 18:32:59] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -27.543340 | E_var:     0.1183 | E_err:   0.005373
[2025-10-06 18:33:03] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -27.541271 | E_var:     0.2527 | E_err:   0.007855
[2025-10-06 18:33:06] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -27.552390 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 18:33:10] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -27.538152 | E_var:     0.1358 | E_err:   0.005758
[2025-10-06 18:33:14] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -27.547647 | E_var:     0.2345 | E_err:   0.007567
[2025-10-06 18:33:17] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -27.548102 | E_var:     0.1290 | E_err:   0.005612
[2025-10-06 18:33:21] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -27.544885 | E_var:     0.1152 | E_err:   0.005304
[2025-10-06 18:33:25] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -27.550845 | E_var:     0.1496 | E_err:   0.006043
[2025-10-06 18:33:28] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -27.534305 | E_var:     0.1366 | E_err:   0.005776
[2025-10-06 18:33:32] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -27.536941 | E_var:     0.1700 | E_err:   0.006442
[2025-10-06 18:33:36] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -27.531758 | E_var:     0.1232 | E_err:   0.005483
[2025-10-06 18:33:39] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -27.537529 | E_var:     0.1224 | E_err:   0.005466
[2025-10-06 18:33:43] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -27.542236 | E_var:     0.1729 | E_err:   0.006497
[2025-10-06 18:33:47] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -27.542614 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 18:33:50] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -27.537717 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 18:33:54] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -27.550360 | E_var:     0.1289 | E_err:   0.005611
[2025-10-06 18:33:58] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -27.552152 | E_var:     0.1100 | E_err:   0.005182
[2025-10-06 18:34:01] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -27.545730 | E_var:     0.1327 | E_err:   0.005691
[2025-10-06 18:34:05] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -27.534006 | E_var:     0.1604 | E_err:   0.006258
[2025-10-06 18:34:09] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -27.548783 | E_var:     0.1296 | E_err:   0.005625
[2025-10-06 18:34:12] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -27.544329 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 18:34:16] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -27.544307 | E_var:     0.1645 | E_err:   0.006337
[2025-10-06 18:34:20] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -27.539266 | E_var:     0.1498 | E_err:   0.006048
[2025-10-06 18:34:23] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -27.542368 | E_var:     0.1258 | E_err:   0.005543
[2025-10-06 18:34:27] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -27.532198 | E_var:     0.1588 | E_err:   0.006227
[2025-10-06 18:34:31] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -27.548956 | E_var:     0.1383 | E_err:   0.005811
[2025-10-06 18:34:34] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -27.547947 | E_var:     0.1422 | E_err:   0.005893
[2025-10-06 18:34:38] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -27.548745 | E_var:     0.1633 | E_err:   0.006315
[2025-10-06 18:34:42] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -27.545094 | E_var:     0.1351 | E_err:   0.005743
[2025-10-06 18:34:45] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -27.548290 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 18:34:49] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -27.543837 | E_var:     0.1468 | E_err:   0.005986
[2025-10-06 18:34:53] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -27.537897 | E_var:     0.1240 | E_err:   0.005501
[2025-10-06 18:34:56] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -27.535574 | E_var:     0.1448 | E_err:   0.005945
[2025-10-06 18:35:00] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -27.539686 | E_var:     0.1295 | E_err:   0.005623
[2025-10-06 18:35:04] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -27.534641 | E_var:     0.1415 | E_err:   0.005877
[2025-10-06 18:35:07] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -27.547019 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 18:35:11] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -27.538005 | E_var:     0.1608 | E_err:   0.006265
[2025-10-06 18:35:15] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -27.554756 | E_var:     0.1398 | E_err:   0.005841
[2025-10-06 18:35:18] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -27.542040 | E_var:     0.1718 | E_err:   0.006477
[2025-10-06 18:35:22] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -27.548934 | E_var:     0.1442 | E_err:   0.005933
[2025-10-06 18:35:26] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -27.545647 | E_var:     0.1125 | E_err:   0.005242
[2025-10-06 18:35:29] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -27.544748 | E_var:     0.1575 | E_err:   0.006201
[2025-10-06 18:35:33] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -27.544594 | E_var:     0.1467 | E_err:   0.005985
[2025-10-06 18:35:37] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -27.537083 | E_var:     0.1533 | E_err:   0.006119
[2025-10-06 18:35:40] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -27.540157 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 18:35:44] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -27.535963 | E_var:     0.2355 | E_err:   0.007583
[2025-10-06 18:35:48] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -27.529357 | E_var:     0.1883 | E_err:   0.006781
[2025-10-06 18:35:51] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -27.536288 | E_var:     0.1634 | E_err:   0.006316
[2025-10-06 18:35:55] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -27.541687 | E_var:     0.1250 | E_err:   0.005525
[2025-10-06 18:35:59] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -27.550026 | E_var:     0.1630 | E_err:   0.006309
[2025-10-06 18:36:02] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -27.550530 | E_var:     0.1283 | E_err:   0.005597
[2025-10-06 18:36:06] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -27.544598 | E_var:     0.1338 | E_err:   0.005715
[2025-10-06 18:36:10] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -27.545924 | E_var:     0.1260 | E_err:   0.005547
[2025-10-06 18:36:13] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -27.543747 | E_var:     0.1173 | E_err:   0.005352
[2025-10-06 18:36:17] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -27.542851 | E_var:     0.1058 | E_err:   0.005083
[2025-10-06 18:36:21] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -27.545590 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 18:36:21] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 18:36:24] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -27.544942 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 18:36:28] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -27.552387 | E_var:     0.1400 | E_err:   0.005846
[2025-10-06 18:36:32] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -27.547951 | E_var:     0.1190 | E_err:   0.005390
[2025-10-06 18:36:35] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -27.548309 | E_var:     0.1215 | E_err:   0.005446
[2025-10-06 18:36:39] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -27.545184 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 18:36:43] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -27.544365 | E_var:     0.1396 | E_err:   0.005837
[2025-10-06 18:36:46] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -27.558094 | E_var:     0.1104 | E_err:   0.005191
[2025-10-06 18:36:50] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -27.547131 | E_var:     0.1107 | E_err:   0.005200
[2025-10-06 18:36:54] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -27.540167 | E_var:     0.1486 | E_err:   0.006023
[2025-10-06 18:36:57] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -27.540175 | E_var:     0.1799 | E_err:   0.006628
[2025-10-06 18:37:01] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -27.542317 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 18:37:05] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -27.537987 | E_var:     0.1343 | E_err:   0.005726
[2025-10-06 18:37:08] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -27.536714 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 18:37:12] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -27.554410 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 18:37:16] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -27.538761 | E_var:     0.1505 | E_err:   0.006062
[2025-10-06 18:37:19] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -27.535825 | E_var:     0.1654 | E_err:   0.006354
[2025-10-06 18:37:23] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -27.549426 | E_var:     0.1248 | E_err:   0.005519
[2025-10-06 18:37:27] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -27.552593 | E_var:     0.1574 | E_err:   0.006199
[2025-10-06 18:37:30] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -27.541706 | E_var:     0.1166 | E_err:   0.005335
[2025-10-06 18:37:34] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -27.537666 | E_var:     0.1641 | E_err:   0.006330
[2025-10-06 18:37:38] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -27.542684 | E_var:     0.1431 | E_err:   0.005910
[2025-10-06 18:37:41] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -27.552782 | E_var:     0.1300 | E_err:   0.005634
[2025-10-06 18:37:45] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -27.549379 | E_var:     0.1177 | E_err:   0.005361
[2025-10-06 18:37:49] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -27.541298 | E_var:     0.1581 | E_err:   0.006212
[2025-10-06 18:37:52] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -27.547719 | E_var:     0.1940 | E_err:   0.006882
[2025-10-06 18:37:56] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -27.542907 | E_var:     0.1280 | E_err:   0.005589
[2025-10-06 18:38:00] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -27.542797 | E_var:     0.1366 | E_err:   0.005775
[2025-10-06 18:38:03] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -27.546290 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 18:38:07] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -27.542375 | E_var:     0.1262 | E_err:   0.005550
[2025-10-06 18:38:11] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -27.541178 | E_var:     0.1526 | E_err:   0.006104
[2025-10-06 18:38:14] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -27.540563 | E_var:     0.1309 | E_err:   0.005653
[2025-10-06 18:38:18] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -27.545009 | E_var:     0.1148 | E_err:   0.005295
[2025-10-06 18:38:22] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -27.541092 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 18:38:25] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -27.533403 | E_var:     0.1551 | E_err:   0.006154
[2025-10-06 18:38:29] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -27.541655 | E_var:     0.1260 | E_err:   0.005546
[2025-10-06 18:38:33] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -27.555378 | E_var:     0.1321 | E_err:   0.005679
[2025-10-06 18:38:36] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -27.551000 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 18:38:40] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -27.542874 | E_var:     0.1353 | E_err:   0.005748
[2025-10-06 18:38:44] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -27.547397 | E_var:     0.1132 | E_err:   0.005257
[2025-10-06 18:38:47] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -27.549492 | E_var:     0.1184 | E_err:   0.005376
[2025-10-06 18:38:51] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -27.538440 | E_var:     0.1442 | E_err:   0.005934
[2025-10-06 18:38:55] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -27.543384 | E_var:     0.1217 | E_err:   0.005451
[2025-10-06 18:38:58] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -27.547857 | E_var:     0.1470 | E_err:   0.005991
[2025-10-06 18:39:02] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -27.534653 | E_var:     0.1314 | E_err:   0.005665
[2025-10-06 18:39:06] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -27.539693 | E_var:     0.1134 | E_err:   0.005261
[2025-10-06 18:39:09] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -27.555289 | E_var:     0.1365 | E_err:   0.005774
[2025-10-06 18:39:13] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -27.551251 | E_var:     0.1292 | E_err:   0.005616
[2025-10-06 18:39:17] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -27.537496 | E_var:     0.1224 | E_err:   0.005467
[2025-10-06 18:39:20] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -27.547487 | E_var:     0.1213 | E_err:   0.005442
[2025-10-06 18:39:24] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -27.540271 | E_var:     0.1339 | E_err:   0.005718
[2025-10-06 18:39:28] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -27.535768 | E_var:     0.1502 | E_err:   0.006056
[2025-10-06 18:39:31] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -27.546360 | E_var:     0.1507 | E_err:   0.006066
[2025-10-06 18:39:35] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -27.552763 | E_var:     0.1675 | E_err:   0.006394
[2025-10-06 18:39:39] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -27.548000 | E_var:     0.1087 | E_err:   0.005152
[2025-10-06 18:39:42] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -27.541360 | E_var:     0.1541 | E_err:   0.006133
[2025-10-06 18:39:46] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -27.545903 | E_var:     0.1716 | E_err:   0.006473
[2025-10-06 18:39:50] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -27.537153 | E_var:     0.1950 | E_err:   0.006899
[2025-10-06 18:39:53] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -27.536674 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 18:39:57] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -27.560527 | E_var:     0.1715 | E_err:   0.006471
[2025-10-06 18:40:01] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -27.545516 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 18:40:04] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -27.545717 | E_var:     0.1114 | E_err:   0.005214
[2025-10-06 18:40:08] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -27.545815 | E_var:     0.1615 | E_err:   0.006279
[2025-10-06 18:40:12] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -27.547133 | E_var:     0.1105 | E_err:   0.005193
[2025-10-06 18:40:15] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -27.551321 | E_var:     0.1262 | E_err:   0.005551
[2025-10-06 18:40:19] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -27.537362 | E_var:     0.1257 | E_err:   0.005539
[2025-10-06 18:40:23] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -27.543203 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 18:40:26] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -27.537579 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 18:40:30] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -27.541781 | E_var:     0.1038 | E_err:   0.005033
[2025-10-06 18:40:34] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -27.539833 | E_var:     0.1293 | E_err:   0.005618
[2025-10-06 18:40:37] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -27.546134 | E_var:     0.1308 | E_err:   0.005651
[2025-10-06 18:40:41] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -27.541015 | E_var:     0.1079 | E_err:   0.005132
[2025-10-06 18:40:45] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -27.553923 | E_var:     0.1295 | E_err:   0.005623
[2025-10-06 18:40:48] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -27.538999 | E_var:     0.1968 | E_err:   0.006932
[2025-10-06 18:40:52] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -27.542273 | E_var:     0.1287 | E_err:   0.005606
[2025-10-06 18:40:56] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -27.550173 | E_var:     0.1228 | E_err:   0.005475
[2025-10-06 18:40:59] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -27.547401 | E_var:     0.1341 | E_err:   0.005721
[2025-10-06 18:41:03] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -27.543452 | E_var:     0.1992 | E_err:   0.006974
[2025-10-06 18:41:07] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -27.553689 | E_var:     0.1618 | E_err:   0.006284
[2025-10-06 18:41:10] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -27.535552 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 18:41:14] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -27.553117 | E_var:     0.2005 | E_err:   0.006997
[2025-10-06 18:41:18] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -27.539622 | E_var:     0.1284 | E_err:   0.005598
[2025-10-06 18:41:21] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -27.541933 | E_var:     0.1182 | E_err:   0.005372
[2025-10-06 18:41:25] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -27.538567 | E_var:     0.1791 | E_err:   0.006612
[2025-10-06 18:41:29] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -27.541514 | E_var:     0.1161 | E_err:   0.005324
[2025-10-06 18:41:32] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -27.547825 | E_var:     0.1559 | E_err:   0.006170
[2025-10-06 18:41:36] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -27.548138 | E_var:     0.1870 | E_err:   0.006758
[2025-10-06 18:41:40] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -27.544782 | E_var:     0.1967 | E_err:   0.006930
[2025-10-06 18:41:43] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -27.539230 | E_var:     0.1616 | E_err:   0.006280
[2025-10-06 18:41:47] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -27.535498 | E_var:     0.2565 | E_err:   0.007913
[2025-10-06 18:41:51] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -27.541484 | E_var:     0.1385 | E_err:   0.005815
[2025-10-06 18:41:54] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -27.540293 | E_var:     0.1310 | E_err:   0.005656
[2025-10-06 18:41:58] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -27.543075 | E_var:     0.0993 | E_err:   0.004924
[2025-10-06 18:42:02] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -27.546615 | E_var:     0.1710 | E_err:   0.006462
[2025-10-06 18:42:05] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -27.553865 | E_var:     0.1704 | E_err:   0.006450
[2025-10-06 18:42:09] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -27.548559 | E_var:     0.1783 | E_err:   0.006598
[2025-10-06 18:42:13] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -27.559281 | E_var:     0.1754 | E_err:   0.006543
[2025-10-06 18:42:16] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -27.546439 | E_var:     0.1381 | E_err:   0.005807
[2025-10-06 18:42:20] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -27.549643 | E_var:     0.1234 | E_err:   0.005490
[2025-10-06 18:42:23] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -27.552637 | E_var:     0.1711 | E_err:   0.006463
[2025-10-06 18:42:27] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -27.547448 | E_var:     0.1086 | E_err:   0.005149
[2025-10-06 18:42:27] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 18:42:31] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -27.543207 | E_var:     0.2268 | E_err:   0.007442
[2025-10-06 18:42:35] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -27.541571 | E_var:     0.3389 | E_err:   0.009096
[2025-10-06 18:42:38] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -27.541879 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 18:42:42] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -27.552897 | E_var:     0.1152 | E_err:   0.005304
[2025-10-06 18:42:46] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -27.555576 | E_var:     0.1308 | E_err:   0.005651
[2025-10-06 18:42:49] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -27.542714 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 18:42:53] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -27.544381 | E_var:     0.1419 | E_err:   0.005887
[2025-10-06 18:42:57] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -27.551151 | E_var:     0.1113 | E_err:   0.005213
[2025-10-06 18:43:00] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -27.539517 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 18:43:04] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -27.544626 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 18:43:08] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -27.527915 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 18:43:11] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -27.542747 | E_var:     0.1735 | E_err:   0.006509
[2025-10-06 18:43:15] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -27.547961 | E_var:     0.2544 | E_err:   0.007881
[2025-10-06 18:43:19] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -27.548857 | E_var:     0.2572 | E_err:   0.007924
[2025-10-06 18:43:22] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -27.548962 | E_var:     0.1187 | E_err:   0.005384
[2025-10-06 18:43:26] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -27.541864 | E_var:     0.1177 | E_err:   0.005360
[2025-10-06 18:43:30] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -27.550606 | E_var:     0.1625 | E_err:   0.006299
[2025-10-06 18:43:33] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -27.542772 | E_var:     0.1305 | E_err:   0.005644
[2025-10-06 18:43:37] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -27.539244 | E_var:     0.1277 | E_err:   0.005583
[2025-10-06 18:43:41] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -27.544493 | E_var:     0.1461 | E_err:   0.005972
[2025-10-06 18:43:44] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -27.543676 | E_var:     0.1500 | E_err:   0.006052
[2025-10-06 18:43:48] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -27.547208 | E_var:     0.1104 | E_err:   0.005192
[2025-10-06 18:43:52] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -27.541850 | E_var:     0.1024 | E_err:   0.005000
[2025-10-06 18:43:55] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -27.549227 | E_var:     0.1579 | E_err:   0.006209
[2025-10-06 18:43:59] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -27.542800 | E_var:     0.1285 | E_err:   0.005602
[2025-10-06 18:44:02] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -27.546075 | E_var:     0.1270 | E_err:   0.005568
[2025-10-06 18:44:06] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -27.555956 | E_var:     0.1097 | E_err:   0.005174
[2025-10-06 18:44:10] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -27.551563 | E_var:     0.1346 | E_err:   0.005732
[2025-10-06 18:44:13] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -27.555912 | E_var:     0.3893 | E_err:   0.009749
[2025-10-06 18:44:17] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -27.553132 | E_var:     0.1708 | E_err:   0.006458
[2025-10-06 18:44:21] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -27.537194 | E_var:     0.2378 | E_err:   0.007620
[2025-10-06 18:44:24] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -27.541994 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 18:44:28] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -27.544109 | E_var:     0.1213 | E_err:   0.005442
[2025-10-06 18:44:32] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -27.546569 | E_var:     0.1392 | E_err:   0.005829
[2025-10-06 18:44:35] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -27.540646 | E_var:     0.1104 | E_err:   0.005193
[2025-10-06 18:44:39] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -27.539828 | E_var:     0.1496 | E_err:   0.006044
[2025-10-06 18:44:43] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -27.542664 | E_var:     0.1530 | E_err:   0.006111
[2025-10-06 18:44:46] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -27.546338 | E_var:     0.1380 | E_err:   0.005805
[2025-10-06 18:44:50] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -27.553178 | E_var:     0.1324 | E_err:   0.005686
[2025-10-06 18:44:54] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -27.535315 | E_var:     0.1527 | E_err:   0.006106
[2025-10-06 18:44:57] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -27.550358 | E_var:     0.1214 | E_err:   0.005444
[2025-10-06 18:45:01] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -27.536494 | E_var:     0.2062 | E_err:   0.007095
[2025-10-06 18:45:05] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -27.537464 | E_var:     0.1984 | E_err:   0.006960
[2025-10-06 18:45:08] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -27.533709 | E_var:     0.1365 | E_err:   0.005773
[2025-10-06 18:45:12] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -27.552256 | E_var:     0.2063 | E_err:   0.007098
[2025-10-06 18:45:16] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -27.533679 | E_var:     0.1588 | E_err:   0.006226
[2025-10-06 18:45:19] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -27.541061 | E_var:     0.1452 | E_err:   0.005954
[2025-10-06 18:45:23] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -27.539955 | E_var:     0.1373 | E_err:   0.005790
[2025-10-06 18:45:27] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -27.551506 | E_var:     0.1328 | E_err:   0.005694
[2025-10-06 18:45:30] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -27.554235 | E_var:     0.1316 | E_err:   0.005669
[2025-10-06 18:45:30] 🔄 RESTART #2 | Period: 600
[2025-10-06 18:45:34] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -27.538656 | E_var:     0.1214 | E_err:   0.005445
[2025-10-06 18:45:38] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -27.543026 | E_var:     0.1081 | E_err:   0.005136
[2025-10-06 18:45:41] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -27.543473 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 18:45:45] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -27.554807 | E_var:     0.1064 | E_err:   0.005096
[2025-10-06 18:45:49] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -27.542103 | E_var:     0.1296 | E_err:   0.005625
[2025-10-06 18:45:52] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -27.530462 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 18:45:56] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -27.549888 | E_var:     0.1011 | E_err:   0.004968
[2025-10-06 18:46:00] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -27.548094 | E_var:     0.1176 | E_err:   0.005359
[2025-10-06 18:46:03] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -27.550633 | E_var:     0.1384 | E_err:   0.005812
[2025-10-06 18:46:07] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -27.528607 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 18:46:11] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -27.547466 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 18:46:14] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -27.546665 | E_var:     0.1989 | E_err:   0.006969
[2025-10-06 18:46:18] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -27.554411 | E_var:     0.1290 | E_err:   0.005612
[2025-10-06 18:46:22] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -27.527406 | E_var:     0.1333 | E_err:   0.005705
[2025-10-06 18:46:25] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -27.538806 | E_var:     0.1169 | E_err:   0.005343
[2025-10-06 18:46:29] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -27.546721 | E_var:     0.1296 | E_err:   0.005625
[2025-10-06 18:46:33] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -27.537977 | E_var:     0.1093 | E_err:   0.005166
[2025-10-06 18:46:36] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -27.543990 | E_var:     0.1517 | E_err:   0.006086
[2025-10-06 18:46:40] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -27.539865 | E_var:     0.1235 | E_err:   0.005492
[2025-10-06 18:46:44] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -27.541248 | E_var:     0.1501 | E_err:   0.006054
[2025-10-06 18:46:47] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -27.538384 | E_var:     0.1241 | E_err:   0.005505
[2025-10-06 18:46:51] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -27.550690 | E_var:     0.1435 | E_err:   0.005920
[2025-10-06 18:46:55] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -27.538297 | E_var:     0.1291 | E_err:   0.005613
[2025-10-06 18:46:58] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -27.539631 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 18:47:02] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -27.545640 | E_var:     0.1240 | E_err:   0.005503
[2025-10-06 18:47:06] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -27.549899 | E_var:     0.1330 | E_err:   0.005698
[2025-10-06 18:47:09] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -27.544188 | E_var:     0.3350 | E_err:   0.009044
[2025-10-06 18:47:13] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -27.547902 | E_var:     0.1382 | E_err:   0.005808
[2025-10-06 18:47:17] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -27.547879 | E_var:     0.1251 | E_err:   0.005526
[2025-10-06 18:47:20] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -27.557001 | E_var:     0.1469 | E_err:   0.005990
[2025-10-06 18:47:24] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -27.539003 | E_var:     0.1687 | E_err:   0.006418
[2025-10-06 18:47:28] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -27.547717 | E_var:     0.1598 | E_err:   0.006246
[2025-10-06 18:47:31] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -27.540938 | E_var:     0.1238 | E_err:   0.005499
[2025-10-06 18:47:35] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -27.552479 | E_var:     0.1444 | E_err:   0.005937
[2025-10-06 18:47:39] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -27.540861 | E_var:     0.1325 | E_err:   0.005687
[2025-10-06 18:47:42] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -27.541098 | E_var:     0.1580 | E_err:   0.006210
[2025-10-06 18:47:46] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -27.547561 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 18:47:50] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -27.538826 | E_var:     0.1595 | E_err:   0.006240
[2025-10-06 18:47:53] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -27.546073 | E_var:     0.1398 | E_err:   0.005842
[2025-10-06 18:47:57] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -27.537743 | E_var:     0.1244 | E_err:   0.005511
[2025-10-06 18:48:01] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -27.541477 | E_var:     0.1739 | E_err:   0.006515
[2025-10-06 18:48:04] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -27.546232 | E_var:     0.1359 | E_err:   0.005759
[2025-10-06 18:48:08] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -27.553054 | E_var:     0.1237 | E_err:   0.005495
[2025-10-06 18:48:12] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -27.553797 | E_var:     0.1299 | E_err:   0.005632
[2025-10-06 18:48:15] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -27.557432 | E_var:     0.1489 | E_err:   0.006029
[2025-10-06 18:48:19] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -27.543659 | E_var:     0.1719 | E_err:   0.006479
[2025-10-06 18:48:22] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -27.543327 | E_var:     0.1223 | E_err:   0.005465
[2025-10-06 18:48:26] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -27.542057 | E_var:     0.1212 | E_err:   0.005440
[2025-10-06 18:48:30] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -27.546176 | E_var:     0.1162 | E_err:   0.005327
[2025-10-06 18:48:33] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -27.543559 | E_var:     0.1137 | E_err:   0.005269
[2025-10-06 18:48:34] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 18:48:37] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -27.530251 | E_var:     0.1484 | E_err:   0.006020
[2025-10-06 18:48:41] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -27.554604 | E_var:     0.1505 | E_err:   0.006062
[2025-10-06 18:48:45] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -27.550939 | E_var:     0.1606 | E_err:   0.006262
[2025-10-06 18:48:48] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -27.541245 | E_var:     0.1642 | E_err:   0.006331
[2025-10-06 18:48:52] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -27.539354 | E_var:     0.1773 | E_err:   0.006579
[2025-10-06 18:48:56] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -27.551095 | E_var:     0.1793 | E_err:   0.006616
[2025-10-06 18:48:59] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -27.547064 | E_var:     0.1323 | E_err:   0.005683
[2025-10-06 18:49:03] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -27.532582 | E_var:     0.1601 | E_err:   0.006253
[2025-10-06 18:49:07] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -27.539183 | E_var:     0.1294 | E_err:   0.005620
[2025-10-06 18:49:10] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -27.536788 | E_var:     0.1749 | E_err:   0.006534
[2025-10-06 18:49:14] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -27.541457 | E_var:     0.0984 | E_err:   0.004902
[2025-10-06 18:49:18] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -27.551675 | E_var:     0.1357 | E_err:   0.005755
[2025-10-06 18:49:21] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -27.544506 | E_var:     0.1196 | E_err:   0.005404
[2025-10-06 18:49:25] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -27.549002 | E_var:     0.1490 | E_err:   0.006031
[2025-10-06 18:49:29] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -27.534874 | E_var:     0.1820 | E_err:   0.006666
[2025-10-06 18:49:32] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -27.550403 | E_var:     0.1708 | E_err:   0.006458
[2025-10-06 18:49:36] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -27.548374 | E_var:     0.1342 | E_err:   0.005725
[2025-10-06 18:49:40] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -27.541340 | E_var:     0.1404 | E_err:   0.005854
[2025-10-06 18:49:43] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -27.546139 | E_var:     0.1325 | E_err:   0.005687
[2025-10-06 18:49:47] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -27.549208 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 18:49:50] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -27.549651 | E_var:     0.1375 | E_err:   0.005793
[2025-10-06 18:49:54] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -27.545803 | E_var:     0.1208 | E_err:   0.005430
[2025-10-06 18:49:58] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -27.556069 | E_var:     0.1436 | E_err:   0.005920
[2025-10-06 18:50:02] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -27.548487 | E_var:     0.1132 | E_err:   0.005257
[2025-10-06 18:50:05] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -27.551082 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 18:50:09] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -27.550187 | E_var:     0.1493 | E_err:   0.006038
[2025-10-06 18:50:12] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -27.548047 | E_var:     0.1832 | E_err:   0.006688
[2025-10-06 18:50:16] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -27.543263 | E_var:     0.1051 | E_err:   0.005067
[2025-10-06 18:50:20] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -27.540438 | E_var:     0.1274 | E_err:   0.005577
[2025-10-06 18:50:23] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -27.543827 | E_var:     0.1329 | E_err:   0.005696
[2025-10-06 18:50:27] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -27.540300 | E_var:     0.1044 | E_err:   0.005048
[2025-10-06 18:50:31] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -27.541760 | E_var:     0.1255 | E_err:   0.005534
[2025-10-06 18:50:34] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -27.539903 | E_var:     0.1520 | E_err:   0.006091
[2025-10-06 18:50:38] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -27.539726 | E_var:     0.1381 | E_err:   0.005806
[2025-10-06 18:50:42] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -27.554893 | E_var:     0.1408 | E_err:   0.005864
[2025-10-06 18:50:45] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -27.546501 | E_var:     0.1489 | E_err:   0.006030
[2025-10-06 18:50:49] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -27.545433 | E_var:     0.1252 | E_err:   0.005530
[2025-10-06 18:50:53] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -27.547201 | E_var:     0.1026 | E_err:   0.005006
[2025-10-06 18:50:56] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -27.546277 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 18:51:00] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -27.546556 | E_var:     0.2567 | E_err:   0.007916
[2025-10-06 18:51:04] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -27.550738 | E_var:     0.3712 | E_err:   0.009520
[2025-10-06 18:51:07] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -27.541201 | E_var:     0.1735 | E_err:   0.006508
[2025-10-06 18:51:11] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -27.549035 | E_var:     0.1161 | E_err:   0.005324
[2025-10-06 18:51:15] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -27.540138 | E_var:     0.1233 | E_err:   0.005487
[2025-10-06 18:51:18] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -27.538029 | E_var:     0.2111 | E_err:   0.007179
[2025-10-06 18:51:22] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -27.538495 | E_var:     0.1439 | E_err:   0.005927
[2025-10-06 18:51:26] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -27.537947 | E_var:     0.1438 | E_err:   0.005924
[2025-10-06 18:51:29] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -27.546315 | E_var:     0.1069 | E_err:   0.005109
[2025-10-06 18:51:33] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -27.557029 | E_var:     0.1728 | E_err:   0.006495
[2025-10-06 18:51:37] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -27.547540 | E_var:     0.1491 | E_err:   0.006033
[2025-10-06 18:51:40] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -27.552423 | E_var:     0.1416 | E_err:   0.005880
[2025-10-06 18:51:44] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -27.541645 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 18:51:48] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -27.544680 | E_var:     0.1071 | E_err:   0.005113
[2025-10-06 18:51:51] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -27.541638 | E_var:     0.1288 | E_err:   0.005608
[2025-10-06 18:51:55] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -27.545699 | E_var:     0.1780 | E_err:   0.006593
[2025-10-06 18:51:59] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -27.543380 | E_var:     0.0976 | E_err:   0.004880
[2025-10-06 18:52:02] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -27.548081 | E_var:     0.1092 | E_err:   0.005162
[2025-10-06 18:52:06] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -27.545240 | E_var:     0.1194 | E_err:   0.005400
[2025-10-06 18:52:10] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -27.545899 | E_var:     0.1184 | E_err:   0.005376
[2025-10-06 18:52:13] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -27.550194 | E_var:     0.1291 | E_err:   0.005615
[2025-10-06 18:52:17] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -27.545894 | E_var:     0.1471 | E_err:   0.005993
[2025-10-06 18:52:21] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -27.550245 | E_var:     0.1343 | E_err:   0.005727
[2025-10-06 18:52:24] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -27.541736 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 18:52:28] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -27.551742 | E_var:     0.1546 | E_err:   0.006144
[2025-10-06 18:52:32] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -27.546785 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 18:52:35] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -27.551104 | E_var:     0.1249 | E_err:   0.005521
[2025-10-06 18:52:39] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -27.544518 | E_var:     0.1131 | E_err:   0.005254
[2025-10-06 18:52:43] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -27.545267 | E_var:     0.1117 | E_err:   0.005223
[2025-10-06 18:52:46] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -27.543495 | E_var:     0.1423 | E_err:   0.005895
[2025-10-06 18:52:50] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -27.549194 | E_var:     0.1879 | E_err:   0.006774
[2025-10-06 18:52:54] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -27.544547 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 18:52:57] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -27.543952 | E_var:     0.1819 | E_err:   0.006664
[2025-10-06 18:53:01] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -27.558089 | E_var:     0.1113 | E_err:   0.005212
[2025-10-06 18:53:05] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -27.541998 | E_var:     0.1087 | E_err:   0.005150
[2025-10-06 18:53:08] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -27.545890 | E_var:     0.1569 | E_err:   0.006189
[2025-10-06 18:53:12] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -27.542388 | E_var:     0.1396 | E_err:   0.005837
[2025-10-06 18:53:16] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -27.546029 | E_var:     0.1534 | E_err:   0.006120
[2025-10-06 18:53:19] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -27.539492 | E_var:     0.1164 | E_err:   0.005332
[2025-10-06 18:53:23] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -27.544393 | E_var:     0.0997 | E_err:   0.004935
[2025-10-06 18:53:27] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -27.541040 | E_var:     0.1465 | E_err:   0.005980
[2025-10-06 18:53:30] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -27.542644 | E_var:     0.1257 | E_err:   0.005539
[2025-10-06 18:53:34] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -27.547878 | E_var:     0.1250 | E_err:   0.005525
[2025-10-06 18:53:37] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -27.537683 | E_var:     0.1443 | E_err:   0.005935
[2025-10-06 18:53:41] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -27.551932 | E_var:     0.1412 | E_err:   0.005872
[2025-10-06 18:53:45] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -27.544640 | E_var:     0.1649 | E_err:   0.006344
[2025-10-06 18:53:48] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -27.544157 | E_var:     0.1532 | E_err:   0.006115
[2025-10-06 18:53:52] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -27.546556 | E_var:     0.1195 | E_err:   0.005401
[2025-10-06 18:53:56] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -27.541070 | E_var:     0.1366 | E_err:   0.005775
[2025-10-06 18:53:59] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -27.535995 | E_var:     0.1739 | E_err:   0.006515
[2025-10-06 18:54:03] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -27.537206 | E_var:     0.1539 | E_err:   0.006129
[2025-10-06 18:54:07] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -27.557196 | E_var:     0.1420 | E_err:   0.005888
[2025-10-06 18:54:10] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -27.533093 | E_var:     0.5721 | E_err:   0.011818
[2025-10-06 18:54:14] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -27.549752 | E_var:     0.1272 | E_err:   0.005572
[2025-10-06 18:54:18] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -27.540674 | E_var:     0.2049 | E_err:   0.007073
[2025-10-06 18:54:21] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -27.554017 | E_var:     0.0983 | E_err:   0.004899
[2025-10-06 18:54:25] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -27.549957 | E_var:     0.1529 | E_err:   0.006110
[2025-10-06 18:54:29] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -27.555546 | E_var:     0.1241 | E_err:   0.005504
[2025-10-06 18:54:32] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -27.545168 | E_var:     0.1157 | E_err:   0.005315
[2025-10-06 18:54:36] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -27.554350 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 18:54:40] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -27.548772 | E_var:     0.1499 | E_err:   0.006050
[2025-10-06 18:54:40] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 18:54:43] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -27.545557 | E_var:     0.1539 | E_err:   0.006130
[2025-10-06 18:54:47] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -27.545532 | E_var:     0.1059 | E_err:   0.005084
[2025-10-06 18:54:51] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -27.548914 | E_var:     0.1305 | E_err:   0.005644
[2025-10-06 18:54:54] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -27.552590 | E_var:     0.1124 | E_err:   0.005238
[2025-10-06 18:54:58] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -27.549422 | E_var:     0.1260 | E_err:   0.005545
[2025-10-06 18:55:02] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -27.543684 | E_var:     0.1829 | E_err:   0.006682
[2025-10-06 18:55:05] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -27.549374 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 18:55:09] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -27.536541 | E_var:     0.1123 | E_err:   0.005236
[2025-10-06 18:55:13] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -27.551534 | E_var:     0.1243 | E_err:   0.005509
[2025-10-06 18:55:16] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -27.545079 | E_var:     0.1222 | E_err:   0.005463
[2025-10-06 18:55:20] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -27.544234 | E_var:     0.1165 | E_err:   0.005333
[2025-10-06 18:55:24] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -27.540387 | E_var:     0.1372 | E_err:   0.005788
[2025-10-06 18:55:27] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -27.541047 | E_var:     0.2112 | E_err:   0.007181
[2025-10-06 18:55:31] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -27.557463 | E_var:     0.1223 | E_err:   0.005463
[2025-10-06 18:55:35] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -27.535929 | E_var:     0.1854 | E_err:   0.006727
[2025-10-06 18:55:38] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -27.546117 | E_var:     0.1218 | E_err:   0.005454
[2025-10-06 18:55:42] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -27.554624 | E_var:     0.1296 | E_err:   0.005625
[2025-10-06 18:55:46] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -27.553514 | E_var:     0.1129 | E_err:   0.005250
[2025-10-06 18:55:49] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -27.543648 | E_var:     0.1095 | E_err:   0.005171
[2025-10-06 18:55:53] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -27.544554 | E_var:     0.1457 | E_err:   0.005964
[2025-10-06 18:55:57] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -27.542785 | E_var:     0.1130 | E_err:   0.005252
[2025-10-06 18:56:00] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -27.547450 | E_var:     0.1763 | E_err:   0.006561
[2025-10-06 18:56:04] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -27.544738 | E_var:     0.1454 | E_err:   0.005959
[2025-10-06 18:56:08] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -27.542178 | E_var:     0.1293 | E_err:   0.005619
[2025-10-06 18:56:11] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -27.553419 | E_var:     0.2083 | E_err:   0.007132
[2025-10-06 18:56:15] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -27.545637 | E_var:     0.1387 | E_err:   0.005818
[2025-10-06 18:56:19] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -27.548396 | E_var:     0.1387 | E_err:   0.005820
[2025-10-06 18:56:22] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -27.538801 | E_var:     0.1295 | E_err:   0.005623
[2025-10-06 18:56:26] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -27.554412 | E_var:     0.1360 | E_err:   0.005763
[2025-10-06 18:56:30] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -27.532341 | E_var:     0.1309 | E_err:   0.005653
[2025-10-06 18:56:33] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -27.542802 | E_var:     0.1407 | E_err:   0.005862
[2025-10-06 18:56:37] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -27.560080 | E_var:     0.2173 | E_err:   0.007284
[2025-10-06 18:56:41] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -27.545135 | E_var:     0.1229 | E_err:   0.005477
[2025-10-06 18:56:44] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -27.546869 | E_var:     0.1225 | E_err:   0.005468
[2025-10-06 18:56:48] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -27.542180 | E_var:     0.1473 | E_err:   0.005996
[2025-10-06 18:56:52] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -27.546508 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 18:56:55] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -27.535388 | E_var:     0.1247 | E_err:   0.005519
[2025-10-06 18:56:59] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -27.539682 | E_var:     0.1539 | E_err:   0.006129
[2025-10-06 18:57:03] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -27.552260 | E_var:     0.1092 | E_err:   0.005164
[2025-10-06 18:57:06] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -27.550440 | E_var:     0.1259 | E_err:   0.005544
[2025-10-06 18:57:10] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -27.537753 | E_var:     0.1415 | E_err:   0.005878
[2025-10-06 18:57:14] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -27.543496 | E_var:     0.1383 | E_err:   0.005810
[2025-10-06 18:57:17] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -27.547030 | E_var:     0.1322 | E_err:   0.005680
[2025-10-06 18:57:21] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -27.540304 | E_var:     0.1179 | E_err:   0.005365
[2025-10-06 18:57:25] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -27.550597 | E_var:     0.1356 | E_err:   0.005754
[2025-10-06 18:57:28] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -27.543638 | E_var:     0.1447 | E_err:   0.005943
[2025-10-06 18:57:32] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -27.536874 | E_var:     0.1678 | E_err:   0.006401
[2025-10-06 18:57:36] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -27.547531 | E_var:     0.1104 | E_err:   0.005192
[2025-10-06 18:57:39] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -27.554211 | E_var:     0.1441 | E_err:   0.005931
[2025-10-06 18:57:43] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -27.549439 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 18:57:46] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -27.544868 | E_var:     0.1306 | E_err:   0.005647
[2025-10-06 18:57:50] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -27.541484 | E_var:     0.1118 | E_err:   0.005224
[2025-10-06 18:57:54] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -27.533227 | E_var:     0.1298 | E_err:   0.005630
[2025-10-06 18:57:57] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -27.538332 | E_var:     0.1341 | E_err:   0.005722
[2025-10-06 18:58:01] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -27.546988 | E_var:     0.1087 | E_err:   0.005153
[2025-10-06 18:58:05] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -27.551700 | E_var:     0.2206 | E_err:   0.007338
[2025-10-06 18:58:08] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -27.542403 | E_var:     0.1365 | E_err:   0.005772
[2025-10-06 18:58:12] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -27.540208 | E_var:     0.1307 | E_err:   0.005648
[2025-10-06 18:58:16] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -27.546071 | E_var:     0.1471 | E_err:   0.005994
[2025-10-06 18:58:19] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -27.547288 | E_var:     0.1248 | E_err:   0.005521
[2025-10-06 18:58:23] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -27.549990 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 18:58:27] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -27.538471 | E_var:     0.1074 | E_err:   0.005121
[2025-10-06 18:58:30] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -27.554953 | E_var:     0.1305 | E_err:   0.005645
[2025-10-06 18:58:34] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -27.538914 | E_var:     0.2633 | E_err:   0.008018
[2025-10-06 18:58:38] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -27.540743 | E_var:     0.1888 | E_err:   0.006789
[2025-10-06 18:58:41] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -27.542496 | E_var:     0.1373 | E_err:   0.005790
[2025-10-06 18:58:45] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -27.553087 | E_var:     0.1720 | E_err:   0.006481
[2025-10-06 18:58:49] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -27.536223 | E_var:     0.1225 | E_err:   0.005469
[2025-10-06 18:58:52] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -27.545638 | E_var:     0.1648 | E_err:   0.006343
[2025-10-06 18:58:56] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -27.554760 | E_var:     0.1390 | E_err:   0.005826
[2025-10-06 18:59:00] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -27.547633 | E_var:     0.1057 | E_err:   0.005080
[2025-10-06 18:59:03] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -27.550407 | E_var:     0.1236 | E_err:   0.005492
[2025-10-06 18:59:07] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -27.541423 | E_var:     0.1262 | E_err:   0.005551
[2025-10-06 18:59:11] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -27.548109 | E_var:     0.1210 | E_err:   0.005436
[2025-10-06 18:59:14] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -27.534299 | E_var:     0.1128 | E_err:   0.005249
[2025-10-06 18:59:18] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -27.549420 | E_var:     0.1686 | E_err:   0.006415
[2025-10-06 18:59:22] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -27.544014 | E_var:     0.1053 | E_err:   0.005071
[2025-10-06 18:59:25] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -27.542218 | E_var:     0.1110 | E_err:   0.005205
[2025-10-06 18:59:29] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -27.548816 | E_var:     0.1104 | E_err:   0.005191
[2025-10-06 18:59:33] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -27.544280 | E_var:     0.1192 | E_err:   0.005395
[2025-10-06 18:59:36] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -27.543694 | E_var:     0.1255 | E_err:   0.005534
[2025-10-06 18:59:40] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -27.547225 | E_var:     0.1771 | E_err:   0.006575
[2025-10-06 18:59:44] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -27.546648 | E_var:     0.1364 | E_err:   0.005770
[2025-10-06 18:59:47] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -27.546416 | E_var:     0.1189 | E_err:   0.005388
[2025-10-06 18:59:51] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -27.536730 | E_var:     0.1507 | E_err:   0.006066
[2025-10-06 18:59:55] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -27.547499 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 18:59:58] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -27.545810 | E_var:     0.1166 | E_err:   0.005336
[2025-10-06 19:00:02] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -27.544687 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 19:00:06] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -27.560086 | E_var:     0.3676 | E_err:   0.009474
[2025-10-06 19:00:09] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -27.543268 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 19:00:13] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -27.542028 | E_var:     0.1161 | E_err:   0.005325
[2025-10-06 19:00:17] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -27.550741 | E_var:     0.1514 | E_err:   0.006081
[2025-10-06 19:00:20] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -27.548794 | E_var:     0.1163 | E_err:   0.005328
[2025-10-06 19:00:24] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -27.553421 | E_var:     0.1086 | E_err:   0.005150
[2025-10-06 19:00:28] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -27.543460 | E_var:     0.1244 | E_err:   0.005510
[2025-10-06 19:00:31] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -27.539799 | E_var:     0.1536 | E_err:   0.006124
[2025-10-06 19:00:35] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -27.544214 | E_var:     0.1544 | E_err:   0.006140
[2025-10-06 19:00:39] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -27.553253 | E_var:     0.1338 | E_err:   0.005716
[2025-10-06 19:00:42] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -27.540545 | E_var:     0.1119 | E_err:   0.005226
[2025-10-06 19:00:46] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -27.545151 | E_var:     0.1331 | E_err:   0.005701
[2025-10-06 19:00:46] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 19:00:50] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -27.549213 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 19:00:53] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -27.544534 | E_var:     0.1436 | E_err:   0.005920
[2025-10-06 19:00:57] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -27.547239 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 19:01:01] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -27.551668 | E_var:     0.1542 | E_err:   0.006136
[2025-10-06 19:01:04] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -27.545309 | E_var:     0.1178 | E_err:   0.005363
[2025-10-06 19:01:08] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -27.544478 | E_var:     0.2766 | E_err:   0.008217
[2025-10-06 19:01:12] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -27.547518 | E_var:     0.1444 | E_err:   0.005937
[2025-10-06 19:01:15] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -27.543411 | E_var:     0.1352 | E_err:   0.005745
[2025-10-06 19:01:19] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -27.538958 | E_var:     0.1196 | E_err:   0.005404
[2025-10-06 19:01:23] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -27.539446 | E_var:     0.1525 | E_err:   0.006102
[2025-10-06 19:01:26] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -27.538013 | E_var:     0.1050 | E_err:   0.005064
[2025-10-06 19:01:30] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -27.549577 | E_var:     0.1115 | E_err:   0.005217
[2025-10-06 19:01:33] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -27.550291 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 19:01:37] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -27.550869 | E_var:     0.1414 | E_err:   0.005875
[2025-10-06 19:01:41] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -27.549859 | E_var:     0.1471 | E_err:   0.005992
[2025-10-06 19:01:44] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -27.543726 | E_var:     0.1128 | E_err:   0.005248
[2025-10-06 19:01:48] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -27.545087 | E_var:     0.1662 | E_err:   0.006369
[2025-10-06 19:01:52] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -27.538284 | E_var:     0.0861 | E_err:   0.004586
[2025-10-06 19:01:55] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -27.548759 | E_var:     0.1375 | E_err:   0.005794
[2025-10-06 19:01:59] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -27.547594 | E_var:     0.1257 | E_err:   0.005540
[2025-10-06 19:02:03] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -27.540191 | E_var:     0.1267 | E_err:   0.005561
[2025-10-06 19:02:06] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -27.559924 | E_var:     0.1270 | E_err:   0.005568
[2025-10-06 19:02:10] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -27.529236 | E_var:     0.1328 | E_err:   0.005693
[2025-10-06 19:02:14] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -27.547631 | E_var:     0.1609 | E_err:   0.006268
[2025-10-06 19:02:17] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -27.544662 | E_var:     0.1363 | E_err:   0.005768
[2025-10-06 19:02:21] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -27.553637 | E_var:     0.1621 | E_err:   0.006290
[2025-10-06 19:02:25] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -27.550388 | E_var:     0.1215 | E_err:   0.005447
[2025-10-06 19:02:28] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -27.549214 | E_var:     0.1175 | E_err:   0.005355
[2025-10-06 19:02:32] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -27.547611 | E_var:     0.1413 | E_err:   0.005874
[2025-10-06 19:02:36] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -27.553455 | E_var:     0.1333 | E_err:   0.005705
[2025-10-06 19:02:39] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -27.541727 | E_var:     0.1234 | E_err:   0.005489
[2025-10-06 19:02:43] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -27.545398 | E_var:     0.1247 | E_err:   0.005519
[2025-10-06 19:02:47] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -27.544978 | E_var:     0.1395 | E_err:   0.005835
[2025-10-06 19:02:50] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -27.547079 | E_var:     0.1098 | E_err:   0.005178
[2025-10-06 19:02:54] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -27.546185 | E_var:     0.1499 | E_err:   0.006049
[2025-10-06 19:02:58] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -27.553150 | E_var:     0.1441 | E_err:   0.005931
[2025-10-06 19:03:01] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -27.550333 | E_var:     0.1181 | E_err:   0.005369
[2025-10-06 19:03:05] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -27.542377 | E_var:     0.1460 | E_err:   0.005969
[2025-10-06 19:03:09] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -27.545299 | E_var:     0.1175 | E_err:   0.005356
[2025-10-06 19:03:12] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -27.538141 | E_var:     0.0996 | E_err:   0.004931
[2025-10-06 19:03:16] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -27.537670 | E_var:     0.1168 | E_err:   0.005341
[2025-10-06 19:03:20] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -27.552355 | E_var:     0.1180 | E_err:   0.005366
[2025-10-06 19:03:23] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -27.548551 | E_var:     0.2007 | E_err:   0.006999
[2025-10-06 19:03:27] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -27.547464 | E_var:     0.1510 | E_err:   0.006071
[2025-10-06 19:03:31] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -27.551656 | E_var:     0.1461 | E_err:   0.005973
[2025-10-06 19:03:34] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -27.549607 | E_var:     0.1239 | E_err:   0.005500
[2025-10-06 19:03:38] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -27.552449 | E_var:     0.1511 | E_err:   0.006074
[2025-10-06 19:03:42] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -27.556115 | E_var:     0.1156 | E_err:   0.005313
[2025-10-06 19:03:45] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -27.556522 | E_var:     0.1360 | E_err:   0.005763
[2025-10-06 19:03:49] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -27.553688 | E_var:     0.1248 | E_err:   0.005519
[2025-10-06 19:03:53] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -27.541149 | E_var:     0.2463 | E_err:   0.007754
[2025-10-06 19:03:56] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -27.539052 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 19:04:00] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -27.547696 | E_var:     0.1536 | E_err:   0.006124
[2025-10-06 19:04:04] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -27.533903 | E_var:     0.5348 | E_err:   0.011427
[2025-10-06 19:04:07] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -27.551837 | E_var:     0.1511 | E_err:   0.006073
[2025-10-06 19:04:11] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -27.537329 | E_var:     0.1212 | E_err:   0.005439
[2025-10-06 19:04:15] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -27.541641 | E_var:     0.1412 | E_err:   0.005871
[2025-10-06 19:04:18] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -27.546328 | E_var:     0.1084 | E_err:   0.005145
[2025-10-06 19:04:22] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -27.547280 | E_var:     0.1813 | E_err:   0.006654
[2025-10-06 19:04:26] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -27.543772 | E_var:     0.1435 | E_err:   0.005920
[2025-10-06 19:04:29] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -27.546868 | E_var:     0.1928 | E_err:   0.006860
[2025-10-06 19:04:33] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -27.546564 | E_var:     0.1089 | E_err:   0.005155
[2025-10-06 19:04:37] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -27.543051 | E_var:     0.2055 | E_err:   0.007083
[2025-10-06 19:04:40] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -27.533476 | E_var:     0.1194 | E_err:   0.005399
[2025-10-06 19:04:44] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -27.549172 | E_var:     0.1231 | E_err:   0.005483
[2025-10-06 19:04:48] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -27.541788 | E_var:     0.1359 | E_err:   0.005761
[2025-10-06 19:04:51] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -27.547464 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 19:04:55] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -27.541427 | E_var:     0.1108 | E_err:   0.005201
[2025-10-06 19:04:59] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -27.547542 | E_var:     0.1080 | E_err:   0.005134
[2025-10-06 19:05:02] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -27.550045 | E_var:     0.1278 | E_err:   0.005585
[2025-10-06 19:05:06] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -27.551808 | E_var:     0.1338 | E_err:   0.005715
[2025-10-06 19:05:10] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -27.538082 | E_var:     0.1266 | E_err:   0.005560
[2025-10-06 19:05:13] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -27.547095 | E_var:     0.1117 | E_err:   0.005223
[2025-10-06 19:05:17] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -27.549506 | E_var:     0.1304 | E_err:   0.005643
[2025-10-06 19:05:20] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -27.548141 | E_var:     0.1319 | E_err:   0.005674
[2025-10-06 19:05:24] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -27.549426 | E_var:     0.1468 | E_err:   0.005986
[2025-10-06 19:05:28] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -27.546699 | E_var:     0.1231 | E_err:   0.005482
[2025-10-06 19:05:31] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -27.545352 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 19:05:35] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -27.545044 | E_var:     0.1298 | E_err:   0.005629
[2025-10-06 19:05:39] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -27.539931 | E_var:     0.1391 | E_err:   0.005827
[2025-10-06 19:05:42] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -27.556954 | E_var:     0.1352 | E_err:   0.005746
[2025-10-06 19:05:46] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -27.538156 | E_var:     0.3286 | E_err:   0.008957
[2025-10-06 19:05:50] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -27.539386 | E_var:     0.1159 | E_err:   0.005319
[2025-10-06 19:05:53] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -27.551413 | E_var:     0.1520 | E_err:   0.006093
[2025-10-06 19:05:57] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -27.556950 | E_var:     0.1422 | E_err:   0.005891
[2025-10-06 19:06:01] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -27.541679 | E_var:     0.1208 | E_err:   0.005430
[2025-10-06 19:06:04] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -27.547804 | E_var:     0.1091 | E_err:   0.005160
[2025-10-06 19:06:08] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -27.548812 | E_var:     0.1003 | E_err:   0.004950
[2025-10-06 19:06:12] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -27.545555 | E_var:     0.1417 | E_err:   0.005882
[2025-10-06 19:06:15] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -27.555819 | E_var:     0.1086 | E_err:   0.005150
[2025-10-06 19:06:19] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -27.546287 | E_var:     0.1189 | E_err:   0.005389
[2025-10-06 19:06:23] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -27.539713 | E_var:     0.1304 | E_err:   0.005642
[2025-10-06 19:06:26] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -27.546105 | E_var:     0.1181 | E_err:   0.005371
[2025-10-06 19:06:30] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -27.551982 | E_var:     0.1485 | E_err:   0.006022
[2025-10-06 19:06:34] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -27.545514 | E_var:     0.2594 | E_err:   0.007957
[2025-10-06 19:06:37] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -27.551637 | E_var:     0.1461 | E_err:   0.005973
[2025-10-06 19:06:41] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -27.534200 | E_var:     0.1440 | E_err:   0.005930
[2025-10-06 19:06:45] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -27.542223 | E_var:     0.1769 | E_err:   0.006571
[2025-10-06 19:06:48] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -27.540218 | E_var:     0.2380 | E_err:   0.007623
[2025-10-06 19:06:52] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -27.536613 | E_var:     0.1102 | E_err:   0.005186
[2025-10-06 19:06:52] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 19:06:56] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -27.542527 | E_var:     0.1171 | E_err:   0.005348
[2025-10-06 19:06:59] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -27.553366 | E_var:     0.2024 | E_err:   0.007030
[2025-10-06 19:07:03] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -27.551197 | E_var:     0.1223 | E_err:   0.005464
[2025-10-06 19:07:07] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -27.540386 | E_var:     0.1329 | E_err:   0.005697
[2025-10-06 19:07:10] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -27.540677 | E_var:     0.1817 | E_err:   0.006660
[2025-10-06 19:07:14] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -27.557721 | E_var:     0.1152 | E_err:   0.005304
[2025-10-06 19:07:18] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -27.546824 | E_var:     0.1133 | E_err:   0.005258
[2025-10-06 19:07:21] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -27.548924 | E_var:     0.1143 | E_err:   0.005283
[2025-10-06 19:07:25] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -27.539690 | E_var:     0.1192 | E_err:   0.005395
[2025-10-06 19:07:29] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -27.546894 | E_var:     0.1158 | E_err:   0.005318
[2025-10-06 19:07:32] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -27.546190 | E_var:     0.1697 | E_err:   0.006437
[2025-10-06 19:07:36] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -27.549370 | E_var:     0.1344 | E_err:   0.005729
[2025-10-06 19:07:40] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -27.534266 | E_var:     0.1069 | E_err:   0.005109
[2025-10-06 19:07:43] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -27.540862 | E_var:     0.1385 | E_err:   0.005816
[2025-10-06 19:07:47] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -27.547129 | E_var:     0.1313 | E_err:   0.005662
[2025-10-06 19:07:51] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -27.555701 | E_var:     0.1490 | E_err:   0.006031
[2025-10-06 19:07:54] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -27.554167 | E_var:     0.2210 | E_err:   0.007346
[2025-10-06 19:07:58] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -27.552218 | E_var:     0.1627 | E_err:   0.006302
[2025-10-06 19:08:02] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -27.546833 | E_var:     0.1448 | E_err:   0.005945
[2025-10-06 19:08:05] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -27.540904 | E_var:     0.1450 | E_err:   0.005949
[2025-10-06 19:08:09] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -27.553543 | E_var:     0.1438 | E_err:   0.005924
[2025-10-06 19:08:13] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -27.555698 | E_var:     0.1611 | E_err:   0.006271
[2025-10-06 19:08:16] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -27.543881 | E_var:     0.0970 | E_err:   0.004867
[2025-10-06 19:08:20] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -27.546823 | E_var:     0.1281 | E_err:   0.005593
[2025-10-06 19:08:24] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -27.543427 | E_var:     0.1250 | E_err:   0.005524
[2025-10-06 19:08:27] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -27.539040 | E_var:     0.1192 | E_err:   0.005394
[2025-10-06 19:08:31] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -27.559828 | E_var:     0.1184 | E_err:   0.005376
[2025-10-06 19:08:35] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -27.539222 | E_var:     0.1205 | E_err:   0.005424
[2025-10-06 19:08:38] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -27.552737 | E_var:     0.1494 | E_err:   0.006039
[2025-10-06 19:08:42] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -27.545546 | E_var:     0.1225 | E_err:   0.005470
[2025-10-06 19:08:46] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -27.548561 | E_var:     0.1100 | E_err:   0.005182
[2025-10-06 19:08:49] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -27.546226 | E_var:     0.1400 | E_err:   0.005846
[2025-10-06 19:08:53] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -27.537915 | E_var:     0.2182 | E_err:   0.007299
[2025-10-06 19:08:57] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -27.555431 | E_var:     0.1171 | E_err:   0.005347
[2025-10-06 19:09:00] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -27.545019 | E_var:     0.1432 | E_err:   0.005913
[2025-10-06 19:09:04] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -27.551249 | E_var:     0.1428 | E_err:   0.005904
[2025-10-06 19:09:08] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -27.554334 | E_var:     0.1318 | E_err:   0.005672
[2025-10-06 19:09:11] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -27.544830 | E_var:     0.1458 | E_err:   0.005967
[2025-10-06 19:09:15] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -27.543751 | E_var:     0.1189 | E_err:   0.005387
[2025-10-06 19:09:19] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -27.552567 | E_var:     0.1436 | E_err:   0.005920
[2025-10-06 19:09:22] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -27.543956 | E_var:     0.1180 | E_err:   0.005367
[2025-10-06 19:09:26] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -27.538936 | E_var:     0.1408 | E_err:   0.005863
[2025-10-06 19:09:29] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -27.541149 | E_var:     0.1861 | E_err:   0.006741
[2025-10-06 19:09:33] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -27.544597 | E_var:     0.1133 | E_err:   0.005259
[2025-10-06 19:09:37] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -27.549898 | E_var:     0.1370 | E_err:   0.005783
[2025-10-06 19:09:40] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -27.554192 | E_var:     0.1493 | E_err:   0.006038
[2025-10-06 19:09:44] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -27.548869 | E_var:     0.1392 | E_err:   0.005830
[2025-10-06 19:09:48] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -27.536142 | E_var:     0.1085 | E_err:   0.005147
[2025-10-06 19:09:51] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -27.551844 | E_var:     0.1498 | E_err:   0.006047
[2025-10-06 19:09:55] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -27.543066 | E_var:     0.1379 | E_err:   0.005803
[2025-10-06 19:09:59] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -27.541620 | E_var:     0.1076 | E_err:   0.005125
[2025-10-06 19:10:02] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -27.546119 | E_var:     0.1186 | E_err:   0.005382
[2025-10-06 19:10:06] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -27.560260 | E_var:     0.1633 | E_err:   0.006314
[2025-10-06 19:10:10] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -27.544895 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 19:10:13] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -27.541648 | E_var:     0.1205 | E_err:   0.005425
[2025-10-06 19:10:17] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -27.544828 | E_var:     0.1295 | E_err:   0.005623
[2025-10-06 19:10:21] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -27.546248 | E_var:     0.1308 | E_err:   0.005651
[2025-10-06 19:10:24] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -27.546891 | E_var:     0.1233 | E_err:   0.005486
[2025-10-06 19:10:28] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -27.548649 | E_var:     0.1085 | E_err:   0.005146
[2025-10-06 19:10:32] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -27.544290 | E_var:     0.1583 | E_err:   0.006216
[2025-10-06 19:10:35] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -27.541608 | E_var:     0.1268 | E_err:   0.005564
[2025-10-06 19:10:39] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -27.550499 | E_var:     0.1303 | E_err:   0.005640
[2025-10-06 19:10:43] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -27.548268 | E_var:     0.1152 | E_err:   0.005304
[2025-10-06 19:10:46] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -27.549486 | E_var:     0.1168 | E_err:   0.005339
[2025-10-06 19:10:50] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -27.548335 | E_var:     0.1057 | E_err:   0.005081
[2025-10-06 19:10:54] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -27.546822 | E_var:     0.1221 | E_err:   0.005461
[2025-10-06 19:10:57] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -27.540321 | E_var:     0.1957 | E_err:   0.006912
[2025-10-06 19:11:01] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -27.547758 | E_var:     0.1245 | E_err:   0.005514
[2025-10-06 19:11:05] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -27.545043 | E_var:     0.1037 | E_err:   0.005031
[2025-10-06 19:11:08] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -27.550641 | E_var:     0.1275 | E_err:   0.005579
[2025-10-06 19:11:12] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -27.550279 | E_var:     0.1167 | E_err:   0.005337
[2025-10-06 19:11:16] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -27.543923 | E_var:     0.1168 | E_err:   0.005340
[2025-10-06 19:11:19] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -27.550879 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 19:11:23] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -27.548151 | E_var:     0.1326 | E_err:   0.005691
[2025-10-06 19:11:27] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -27.543967 | E_var:     0.1236 | E_err:   0.005493
[2025-10-06 19:11:30] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -27.548442 | E_var:     0.1066 | E_err:   0.005102
[2025-10-06 19:11:34] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -27.537312 | E_var:     0.1301 | E_err:   0.005637
[2025-10-06 19:11:38] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -27.543481 | E_var:     0.1103 | E_err:   0.005190
[2025-10-06 19:11:41] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -27.541538 | E_var:     0.1214 | E_err:   0.005444
[2025-10-06 19:11:45] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -27.542907 | E_var:     0.1203 | E_err:   0.005419
[2025-10-06 19:11:49] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -27.536235 | E_var:     0.1327 | E_err:   0.005691
[2025-10-06 19:11:52] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -27.549409 | E_var:     0.1157 | E_err:   0.005316
[2025-10-06 19:11:56] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -27.551551 | E_var:     0.1397 | E_err:   0.005841
[2025-10-06 19:12:00] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -27.543245 | E_var:     0.1312 | E_err:   0.005660
[2025-10-06 19:12:03] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -27.549205 | E_var:     0.1418 | E_err:   0.005884
[2025-10-06 19:12:07] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -27.545365 | E_var:     0.2222 | E_err:   0.007365
[2025-10-06 19:12:11] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -27.542701 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 19:12:14] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -27.548507 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 19:12:18] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -27.543934 | E_var:     0.1660 | E_err:   0.006366
[2025-10-06 19:12:22] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -27.551361 | E_var:     0.1310 | E_err:   0.005655
[2025-10-06 19:12:25] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -27.544556 | E_var:     0.1402 | E_err:   0.005851
[2025-10-06 19:12:29] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -27.557295 | E_var:     0.1043 | E_err:   0.005047
[2025-10-06 19:12:33] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -27.553141 | E_var:     0.1263 | E_err:   0.005553
[2025-10-06 19:12:36] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -27.547164 | E_var:     0.1226 | E_err:   0.005472
[2025-10-06 19:12:40] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -27.565479 | E_var:     0.1864 | E_err:   0.006747
[2025-10-06 19:12:44] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -27.552771 | E_var:     0.1189 | E_err:   0.005388
[2025-10-06 19:12:47] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -27.541796 | E_var:     0.1211 | E_err:   0.005436
[2025-10-06 19:12:51] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -27.546394 | E_var:     0.1602 | E_err:   0.006254
[2025-10-06 19:12:54] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -27.543869 | E_var:     0.1421 | E_err:   0.005890
[2025-10-06 19:12:58] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -27.547264 | E_var:     0.1570 | E_err:   0.006192
[2025-10-06 19:12:58] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 19:13:02] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -27.546463 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 19:13:05] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -27.542794 | E_var:     0.1673 | E_err:   0.006392
[2025-10-06 19:13:09] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -27.543965 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 19:13:13] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -27.539428 | E_var:     0.1714 | E_err:   0.006469
[2025-10-06 19:13:16] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -27.553827 | E_var:     0.1259 | E_err:   0.005544
[2025-10-06 19:13:20] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -27.551764 | E_var:     0.1145 | E_err:   0.005286
[2025-10-06 19:13:24] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -27.539552 | E_var:     0.3096 | E_err:   0.008694
[2025-10-06 19:13:27] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -27.550775 | E_var:     0.1454 | E_err:   0.005958
[2025-10-06 19:13:31] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -27.538252 | E_var:     0.1333 | E_err:   0.005704
[2025-10-06 19:13:35] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -27.551665 | E_var:     0.1174 | E_err:   0.005354
[2025-10-06 19:13:38] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -27.547340 | E_var:     0.1540 | E_err:   0.006132
[2025-10-06 19:13:42] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -27.543080 | E_var:     0.1154 | E_err:   0.005308
[2025-10-06 19:13:46] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -27.548303 | E_var:     0.1064 | E_err:   0.005096
[2025-10-06 19:13:49] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -27.546189 | E_var:     0.1157 | E_err:   0.005316
[2025-10-06 19:13:53] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -27.542200 | E_var:     0.1158 | E_err:   0.005317
[2025-10-06 19:13:57] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -27.536492 | E_var:     0.1490 | E_err:   0.006032
[2025-10-06 19:14:00] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -27.547837 | E_var:     0.1305 | E_err:   0.005645
[2025-10-06 19:14:04] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -27.544707 | E_var:     0.1606 | E_err:   0.006261
[2025-10-06 19:14:08] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -27.552117 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 19:14:11] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -27.544638 | E_var:     0.1097 | E_err:   0.005176
[2025-10-06 19:14:15] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -27.547503 | E_var:     0.1682 | E_err:   0.006408
[2025-10-06 19:14:19] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -27.553546 | E_var:     0.1444 | E_err:   0.005938
[2025-10-06 19:14:22] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -27.548484 | E_var:     0.1001 | E_err:   0.004943
[2025-10-06 19:14:26] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -27.536778 | E_var:     0.1307 | E_err:   0.005648
[2025-10-06 19:14:30] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -27.547832 | E_var:     0.0995 | E_err:   0.004928
[2025-10-06 19:14:33] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -27.554342 | E_var:     0.1153 | E_err:   0.005305
[2025-10-06 19:14:37] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -27.546519 | E_var:     0.1229 | E_err:   0.005477
[2025-10-06 19:14:41] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -27.547660 | E_var:     0.1044 | E_err:   0.005047
[2025-10-06 19:14:44] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -27.545608 | E_var:     0.1372 | E_err:   0.005787
[2025-10-06 19:14:48] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -27.537729 | E_var:     0.1530 | E_err:   0.006112
[2025-10-06 19:14:52] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -27.549759 | E_var:     0.1656 | E_err:   0.006358
[2025-10-06 19:14:55] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -27.547305 | E_var:     0.1352 | E_err:   0.005746
[2025-10-06 19:14:59] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -27.542780 | E_var:     0.1459 | E_err:   0.005969
[2025-10-06 19:15:03] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -27.540702 | E_var:     0.1884 | E_err:   0.006782
[2025-10-06 19:15:06] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -27.543407 | E_var:     0.1017 | E_err:   0.004984
[2025-10-06 19:15:10] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -27.552466 | E_var:     0.1422 | E_err:   0.005893
[2025-10-06 19:15:14] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -27.549562 | E_var:     0.1161 | E_err:   0.005324
[2025-10-06 19:15:17] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -27.543952 | E_var:     0.1079 | E_err:   0.005134
[2025-10-06 19:15:21] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -27.545579 | E_var:     0.2061 | E_err:   0.007093
[2025-10-06 19:15:25] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -27.539425 | E_var:     0.1322 | E_err:   0.005681
[2025-10-06 19:15:28] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -27.539313 | E_var:     0.1371 | E_err:   0.005784
[2025-10-06 19:15:32] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -27.544893 | E_var:     0.1175 | E_err:   0.005356
[2025-10-06 19:15:36] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -27.538218 | E_var:     0.1459 | E_err:   0.005969
[2025-10-06 19:15:39] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -27.545682 | E_var:     0.1267 | E_err:   0.005563
[2025-10-06 19:15:43] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -27.546665 | E_var:     0.1205 | E_err:   0.005424
[2025-10-06 19:15:47] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -27.538102 | E_var:     0.1120 | E_err:   0.005230
[2025-10-06 19:15:50] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -27.552293 | E_var:     0.0975 | E_err:   0.004879
[2025-10-06 19:15:54] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -27.544798 | E_var:     0.1260 | E_err:   0.005547
[2025-10-06 19:15:58] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -27.547437 | E_var:     0.1432 | E_err:   0.005912
[2025-10-06 19:16:01] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -27.541006 | E_var:     0.1173 | E_err:   0.005350
[2025-10-06 19:16:05] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -27.552173 | E_var:     0.1544 | E_err:   0.006140
[2025-10-06 19:16:09] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -27.553209 | E_var:     0.1389 | E_err:   0.005823
[2025-10-06 19:16:12] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -27.545221 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 19:16:16] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -27.549544 | E_var:     0.1444 | E_err:   0.005938
[2025-10-06 19:16:20] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -27.539352 | E_var:     0.1575 | E_err:   0.006200
[2025-10-06 19:16:23] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -27.552639 | E_var:     0.1337 | E_err:   0.005714
[2025-10-06 19:16:27] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -27.551628 | E_var:     0.1686 | E_err:   0.006416
[2025-10-06 19:16:31] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -27.549416 | E_var:     0.1330 | E_err:   0.005697
[2025-10-06 19:16:34] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -27.544122 | E_var:     0.1099 | E_err:   0.005181
[2025-10-06 19:16:38] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -27.553590 | E_var:     0.1429 | E_err:   0.005907
[2025-10-06 19:16:41] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -27.546854 | E_var:     0.1367 | E_err:   0.005777
[2025-10-06 19:16:45] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -27.556735 | E_var:     0.1275 | E_err:   0.005579
[2025-10-06 19:16:49] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -27.546067 | E_var:     0.1109 | E_err:   0.005204
[2025-10-06 19:16:52] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -27.546667 | E_var:     0.1203 | E_err:   0.005420
[2025-10-06 19:16:56] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -27.542946 | E_var:     0.1396 | E_err:   0.005839
[2025-10-06 19:17:00] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -27.557709 | E_var:     0.1186 | E_err:   0.005381
[2025-10-06 19:17:03] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -27.549036 | E_var:     0.1203 | E_err:   0.005420
[2025-10-06 19:17:07] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -27.546003 | E_var:     0.1356 | E_err:   0.005753
[2025-10-06 19:17:11] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -27.550130 | E_var:     0.1053 | E_err:   0.005070
[2025-10-06 19:17:14] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -27.538867 | E_var:     0.1292 | E_err:   0.005615
[2025-10-06 19:17:18] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -27.544851 | E_var:     0.1335 | E_err:   0.005709
[2025-10-06 19:17:22] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -27.543738 | E_var:     0.1560 | E_err:   0.006170
[2025-10-06 19:17:25] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -27.543173 | E_var:     0.1377 | E_err:   0.005798
[2025-10-06 19:17:29] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -27.541136 | E_var:     0.1376 | E_err:   0.005796
[2025-10-06 19:17:33] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -27.548433 | E_var:     0.1229 | E_err:   0.005477
[2025-10-06 19:17:36] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -27.555466 | E_var:     0.1408 | E_err:   0.005862
[2025-10-06 19:17:40] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -27.545628 | E_var:     0.1392 | E_err:   0.005830
[2025-10-06 19:17:44] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -27.542357 | E_var:     0.1286 | E_err:   0.005604
[2025-10-06 19:17:47] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -27.543572 | E_var:     0.1259 | E_err:   0.005544
[2025-10-06 19:17:51] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -27.542455 | E_var:     0.1211 | E_err:   0.005438
[2025-10-06 19:17:55] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -27.550579 | E_var:     0.1276 | E_err:   0.005582
[2025-10-06 19:17:58] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -27.540620 | E_var:     0.1441 | E_err:   0.005932
[2025-10-06 19:18:02] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -27.542109 | E_var:     0.1524 | E_err:   0.006100
[2025-10-06 19:18:06] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -27.547451 | E_var:     0.1471 | E_err:   0.005992
[2025-10-06 19:18:09] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -27.551646 | E_var:     0.1500 | E_err:   0.006051
[2025-10-06 19:18:13] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -27.548888 | E_var:     0.1400 | E_err:   0.005847
[2025-10-06 19:18:17] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -27.542773 | E_var:     0.1616 | E_err:   0.006281
[2025-10-06 19:18:20] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -27.547614 | E_var:     0.1235 | E_err:   0.005491
[2025-10-06 19:18:24] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -27.535643 | E_var:     0.1482 | E_err:   0.006015
[2025-10-06 19:18:28] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -27.555909 | E_var:     0.1314 | E_err:   0.005663
[2025-10-06 19:18:31] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -27.557189 | E_var:     0.1431 | E_err:   0.005911
[2025-10-06 19:18:35] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -27.538832 | E_var:     0.1714 | E_err:   0.006469
[2025-10-06 19:18:39] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -27.538710 | E_var:     0.1847 | E_err:   0.006714
[2025-10-06 19:18:42] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -27.541540 | E_var:     0.1277 | E_err:   0.005584
[2025-10-06 19:18:46] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -27.546040 | E_var:     0.1555 | E_err:   0.006162
[2025-10-06 19:18:50] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -27.540013 | E_var:     0.1107 | E_err:   0.005198
[2025-10-06 19:18:53] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -27.553122 | E_var:     0.1185 | E_err:   0.005378
[2025-10-06 19:18:57] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -27.558023 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 19:19:01] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -27.543231 | E_var:     0.1156 | E_err:   0.005312
[2025-10-06 19:19:04] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -27.565938 | E_var:     0.1594 | E_err:   0.006239
[2025-10-06 19:19:04] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 19:19:08] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -27.556483 | E_var:     0.1462 | E_err:   0.005974
[2025-10-06 19:19:12] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -27.540767 | E_var:     0.1222 | E_err:   0.005461
[2025-10-06 19:19:15] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -27.549426 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 19:19:19] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -27.549444 | E_var:     0.1220 | E_err:   0.005458
[2025-10-06 19:19:23] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -27.547645 | E_var:     0.1076 | E_err:   0.005126
[2025-10-06 19:19:26] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -27.549106 | E_var:     0.1349 | E_err:   0.005739
[2025-10-06 19:19:30] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -27.547321 | E_var:     0.1375 | E_err:   0.005795
[2025-10-06 19:19:34] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -27.547939 | E_var:     0.1113 | E_err:   0.005212
[2025-10-06 19:19:37] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -27.541390 | E_var:     0.0908 | E_err:   0.004708
[2025-10-06 19:19:41] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -27.531540 | E_var:     0.2188 | E_err:   0.007310
[2025-10-06 19:19:45] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -27.541976 | E_var:     0.1125 | E_err:   0.005240
[2025-10-06 19:19:48] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -27.553134 | E_var:     0.1662 | E_err:   0.006370
[2025-10-06 19:19:52] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -27.544341 | E_var:     0.1040 | E_err:   0.005038
[2025-10-06 19:19:56] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -27.544651 | E_var:     0.1067 | E_err:   0.005104
[2025-10-06 19:19:59] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -27.551991 | E_var:     0.1438 | E_err:   0.005925
[2025-10-06 19:20:03] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -27.545962 | E_var:     0.3013 | E_err:   0.008577
[2025-10-06 19:20:07] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -27.546521 | E_var:     0.1494 | E_err:   0.006039
[2025-10-06 19:20:10] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -27.559175 | E_var:     0.0910 | E_err:   0.004714
[2025-10-06 19:20:14] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -27.541682 | E_var:     0.0878 | E_err:   0.004629
[2025-10-06 19:20:18] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -27.554616 | E_var:     0.1769 | E_err:   0.006572
[2025-10-06 19:20:21] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -27.548842 | E_var:     0.0953 | E_err:   0.004824
[2025-10-06 19:20:25] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -27.539812 | E_var:     0.1334 | E_err:   0.005706
[2025-10-06 19:20:29] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -27.545508 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 19:20:32] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -27.543392 | E_var:     0.2925 | E_err:   0.008450
[2025-10-06 19:20:36] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -27.549298 | E_var:     0.1206 | E_err:   0.005426
[2025-10-06 19:20:40] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -27.551553 | E_var:     0.1078 | E_err:   0.005129
[2025-10-06 19:20:43] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -27.551811 | E_var:     0.1182 | E_err:   0.005371
[2025-10-06 19:20:47] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -27.550384 | E_var:     0.1164 | E_err:   0.005332
[2025-10-06 19:20:50] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -27.549245 | E_var:     0.1027 | E_err:   0.005008
[2025-10-06 19:20:54] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -27.545575 | E_var:     0.1079 | E_err:   0.005133
[2025-10-06 19:20:58] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -27.547996 | E_var:     0.1733 | E_err:   0.006504
[2025-10-06 19:21:01] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -27.556700 | E_var:     0.1355 | E_err:   0.005751
[2025-10-06 19:21:05] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -27.537167 | E_var:     0.1226 | E_err:   0.005471
[2025-10-06 19:21:09] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -27.545820 | E_var:     0.1363 | E_err:   0.005768
[2025-10-06 19:21:12] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -27.554415 | E_var:     0.1566 | E_err:   0.006184
[2025-10-06 19:21:16] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -27.537998 | E_var:     0.1324 | E_err:   0.005685
[2025-10-06 19:21:20] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -27.545106 | E_var:     0.2028 | E_err:   0.007037
[2025-10-06 19:21:23] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -27.544907 | E_var:     0.3016 | E_err:   0.008581
[2025-10-06 19:21:27] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -27.541646 | E_var:     0.1126 | E_err:   0.005242
[2025-10-06 19:21:31] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -27.547919 | E_var:     0.1469 | E_err:   0.005988
[2025-10-06 19:21:34] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -27.546088 | E_var:     0.1415 | E_err:   0.005878
[2025-10-06 19:21:38] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -27.538520 | E_var:     0.1093 | E_err:   0.005165
[2025-10-06 19:21:42] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -27.558432 | E_var:     0.1149 | E_err:   0.005295
[2025-10-06 19:21:45] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -27.555306 | E_var:     0.1310 | E_err:   0.005656
[2025-10-06 19:21:49] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -27.552857 | E_var:     0.1624 | E_err:   0.006298
[2025-10-06 19:21:53] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -27.540353 | E_var:     0.1135 | E_err:   0.005264
[2025-10-06 19:21:56] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -27.550597 | E_var:     0.1166 | E_err:   0.005335
[2025-10-06 19:22:00] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -27.546378 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 19:22:04] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -27.546193 | E_var:     0.1258 | E_err:   0.005543
[2025-10-06 19:22:07] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -27.542210 | E_var:     0.1782 | E_err:   0.006596
[2025-10-06 19:22:07] ======================================================================================================
[2025-10-06 19:22:07] ✅ Training completed successfully
[2025-10-06 19:22:07] Total restarts: 2
[2025-10-06 19:22:09] Final Energy: -27.54220994 ± 0.00659586
[2025-10-06 19:22:09] Final Variance: 0.178198
[2025-10-06 19:22:09] ======================================================================================================
[2025-10-06 19:22:09] ======================================================================================================
[2025-10-06 19:22:09] Training completed | Runtime: 3914.0s
[2025-10-06 19:22:10] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 19:22:10] ======================================================================================================
