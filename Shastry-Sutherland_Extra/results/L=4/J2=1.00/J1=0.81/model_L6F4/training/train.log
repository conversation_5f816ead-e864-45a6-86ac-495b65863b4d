[2025-10-06 22:39:28] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.80/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 22:39:28]   - 迭代次数: final
[2025-10-06 22:39:28]   - 能量: -28.779189-0.001441j ± 0.004799, Var: 0.094346
[2025-10-06 22:39:28]   - 时间戳: 2025-10-06T22:39:16.791054+08:00
[2025-10-06 22:39:46] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 22:39:46] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 22:39:46] ======================================================================================================
[2025-10-06 22:39:46] GCNN for Shastry-Sutherland Model
[2025-10-06 22:39:46] ======================================================================================================
[2025-10-06 22:39:46] System parameters:
[2025-10-06 22:39:46]   - System size: L=4, N=64
[2025-10-06 22:39:46]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-10-06 22:39:46] ------------------------------------------------------------------------------------------------------
[2025-10-06 22:39:46] Model parameters:
[2025-10-06 22:39:46]   - Number of layers = 6
[2025-10-06 22:39:46]   - Number of features = 4
[2025-10-06 22:39:46]   - Total parameters = 20780
[2025-10-06 22:39:46] ------------------------------------------------------------------------------------------------------
[2025-10-06 22:39:46] Training parameters:
[2025-10-06 22:39:46]   - Total iterations: 1050
[2025-10-06 22:39:46]   - Annealing cycles: 3
[2025-10-06 22:39:46]   - Initial period: 150
[2025-10-06 22:39:46]   - Period multiplier: 2.0
[2025-10-06 22:39:46]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 22:39:46]   - Samples: 4096
[2025-10-06 22:39:46]   - Discarded samples: 0
[2025-10-06 22:39:46]   - Chunk size: 4096
[2025-10-06 22:39:46]   - Diagonal shift: 0.15
[2025-10-06 22:39:46]   - Gradient clipping: 1.0
[2025-10-06 22:39:46]   - Checkpoint enabled: interval=100
[2025-10-06 22:39:46]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.81/model_L6F4/training/checkpoints
[2025-10-06 22:39:46] ------------------------------------------------------------------------------------------------------
[2025-10-06 22:39:46] Device status:
[2025-10-06 22:39:46]   - Devices model: NVIDIA H200 NVL
[2025-10-06 22:39:46]   - Number of devices: 1
[2025-10-06 22:39:46]   - Sharding: True
[2025-10-06 22:39:46] ======================================================================================================
[2025-10-06 22:40:30] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -29.186459 | E_var:     0.2276 | E_err:   0.007454
[2025-10-06 22:40:56] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -29.182217 | E_var:     0.1454 | E_err:   0.005959
[2025-10-06 22:40:59] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -29.178333 | E_var:     0.1996 | E_err:   0.006981
[2025-10-06 22:41:03] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -29.184237 | E_var:     0.1341 | E_err:   0.005723
[2025-10-06 22:41:07] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -29.187461 | E_var:     0.0874 | E_err:   0.004619
[2025-10-06 22:41:10] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -29.186656 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 22:41:14] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -29.181873 | E_var:     0.1308 | E_err:   0.005651
[2025-10-06 22:41:18] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -29.183395 | E_var:     0.0990 | E_err:   0.004915
[2025-10-06 22:41:21] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -29.186994 | E_var:     0.0771 | E_err:   0.004338
[2025-10-06 22:41:25] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -29.187671 | E_var:     0.0673 | E_err:   0.004054
[2025-10-06 22:41:29] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -29.189564 | E_var:     0.1075 | E_err:   0.005123
[2025-10-06 22:41:32] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -29.184288 | E_var:     0.0813 | E_err:   0.004454
[2025-10-06 22:41:36] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -29.181039 | E_var:     0.0924 | E_err:   0.004749
[2025-10-06 22:41:40] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -29.179741 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 22:41:43] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -29.183541 | E_var:     0.0791 | E_err:   0.004395
[2025-10-06 22:41:47] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -29.182211 | E_var:     0.0739 | E_err:   0.004249
[2025-10-06 22:41:51] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -29.186910 | E_var:     0.0832 | E_err:   0.004506
[2025-10-06 22:41:54] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -29.183985 | E_var:     0.0730 | E_err:   0.004222
[2025-10-06 22:41:58] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -29.189832 | E_var:     0.0690 | E_err:   0.004106
[2025-10-06 22:42:02] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -29.183356 | E_var:     0.0630 | E_err:   0.003922
[2025-10-06 22:42:05] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -29.190390 | E_var:     0.0783 | E_err:   0.004371
[2025-10-06 22:42:09] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -29.192218 | E_var:     0.1092 | E_err:   0.005163
[2025-10-06 22:42:13] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -29.182434 | E_var:     0.0800 | E_err:   0.004421
[2025-10-06 22:42:16] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -29.184578 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 22:42:20] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -29.187385 | E_var:     0.0936 | E_err:   0.004780
[2025-10-06 22:42:24] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -29.189104 | E_var:     0.0725 | E_err:   0.004206
[2025-10-06 22:42:27] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -29.177302 | E_var:     0.0836 | E_err:   0.004519
[2025-10-06 22:42:31] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -29.186118 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 22:42:35] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -29.187847 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 22:42:38] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -29.183729 | E_var:     0.0815 | E_err:   0.004460
[2025-10-06 22:42:42] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -29.185450 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 22:42:46] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -29.176519 | E_var:     0.0759 | E_err:   0.004304
[2025-10-06 22:42:49] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -29.182443 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 22:42:53] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -29.197678 | E_var:     0.0713 | E_err:   0.004173
[2025-10-06 22:42:57] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -29.181700 | E_var:     0.0777 | E_err:   0.004355
[2025-10-06 22:43:01] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -29.180586 | E_var:     0.0815 | E_err:   0.004460
[2025-10-06 22:43:04] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -29.187946 | E_var:     0.0786 | E_err:   0.004381
[2025-10-06 22:43:08] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -29.182553 | E_var:     0.0895 | E_err:   0.004674
[2025-10-06 22:43:12] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -29.182115 | E_var:     0.0939 | E_err:   0.004787
[2025-10-06 22:43:15] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -29.183818 | E_var:     0.0901 | E_err:   0.004689
[2025-10-06 22:43:19] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -29.187230 | E_var:     0.0652 | E_err:   0.003991
[2025-10-06 22:43:23] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -29.186209 | E_var:     0.0714 | E_err:   0.004175
[2025-10-06 22:43:26] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -29.185522 | E_var:     0.0787 | E_err:   0.004383
[2025-10-06 22:43:30] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -29.186314 | E_var:     0.0676 | E_err:   0.004062
[2025-10-06 22:43:34] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -29.178823 | E_var:     0.1708 | E_err:   0.006458
[2025-10-06 22:43:37] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -29.183739 | E_var:     0.0768 | E_err:   0.004331
[2025-10-06 22:43:41] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -29.192899 | E_var:     0.0788 | E_err:   0.004388
[2025-10-06 22:43:45] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -29.191201 | E_var:     0.0740 | E_err:   0.004251
[2025-10-06 22:43:48] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -29.186911 | E_var:     0.0745 | E_err:   0.004266
[2025-10-06 22:43:52] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -29.189901 | E_var:     0.0611 | E_err:   0.003862
[2025-10-06 22:43:56] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -29.187279 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 22:43:59] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -29.181972 | E_var:     0.0728 | E_err:   0.004216
[2025-10-06 22:44:03] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -29.183457 | E_var:     0.0733 | E_err:   0.004229
[2025-10-06 22:44:07] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -29.181934 | E_var:     0.0727 | E_err:   0.004213
[2025-10-06 22:44:10] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -29.184588 | E_var:     0.0784 | E_err:   0.004374
[2025-10-06 22:44:14] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -29.181458 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 22:44:18] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -29.182679 | E_var:     0.0684 | E_err:   0.004085
[2025-10-06 22:44:21] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -29.188344 | E_var:     0.0655 | E_err:   0.003999
[2025-10-06 22:44:25] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -29.185088 | E_var:     0.0653 | E_err:   0.003994
[2025-10-06 22:44:29] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -29.188959 | E_var:     0.0800 | E_err:   0.004420
[2025-10-06 22:44:32] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -29.186273 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 22:44:36] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -29.188082 | E_var:     0.0781 | E_err:   0.004366
[2025-10-06 22:44:40] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -29.187089 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 22:44:43] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -29.189493 | E_var:     0.0735 | E_err:   0.004236
[2025-10-06 22:44:47] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -29.186082 | E_var:     0.0823 | E_err:   0.004483
[2025-10-06 22:44:51] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -29.185614 | E_var:     0.0749 | E_err:   0.004277
[2025-10-06 22:44:54] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -29.188922 | E_var:     0.0616 | E_err:   0.003877
[2025-10-06 22:44:58] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -29.184694 | E_var:     0.0667 | E_err:   0.004036
[2025-10-06 22:45:02] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -29.186090 | E_var:     0.0611 | E_err:   0.003862
[2025-10-06 22:45:05] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -29.184335 | E_var:     0.1090 | E_err:   0.005159
[2025-10-06 22:45:09] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -29.184746 | E_var:     0.0990 | E_err:   0.004915
[2025-10-06 22:45:13] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -29.189558 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 22:45:16] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -29.192895 | E_var:     0.1183 | E_err:   0.005373
[2025-10-06 22:45:20] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -29.192337 | E_var:     0.0926 | E_err:   0.004754
[2025-10-06 22:45:24] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -29.188671 | E_var:     0.0899 | E_err:   0.004685
[2025-10-06 22:45:27] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -29.191069 | E_var:     0.0745 | E_err:   0.004265
[2025-10-06 22:45:31] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -29.189905 | E_var:     0.0862 | E_err:   0.004587
[2025-10-06 22:45:35] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -29.185402 | E_var:     0.0732 | E_err:   0.004228
[2025-10-06 22:45:38] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -29.182268 | E_var:     0.0778 | E_err:   0.004359
[2025-10-06 22:45:42] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -29.181710 | E_var:     0.0624 | E_err:   0.003902
[2025-10-06 22:45:46] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -29.187528 | E_var:     0.0972 | E_err:   0.004871
[2025-10-06 22:45:49] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -29.189266 | E_var:     0.1287 | E_err:   0.005606
[2025-10-06 22:45:53] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -29.189402 | E_var:     0.0825 | E_err:   0.004487
[2025-10-06 22:45:57] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -29.192072 | E_var:     0.1625 | E_err:   0.006298
[2025-10-06 22:46:00] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -29.190063 | E_var:     0.0820 | E_err:   0.004475
[2025-10-06 22:46:04] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -29.183038 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 22:46:08] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -29.180249 | E_var:     0.0988 | E_err:   0.004911
[2025-10-06 22:46:11] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -29.194713 | E_var:     0.0736 | E_err:   0.004240
[2025-10-06 22:46:15] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -29.181326 | E_var:     0.0708 | E_err:   0.004156
[2025-10-06 22:46:19] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -29.181900 | E_var:     0.0568 | E_err:   0.003723
[2025-10-06 22:46:22] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -29.185632 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 22:46:26] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -29.181960 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 22:46:30] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -29.190178 | E_var:     0.0920 | E_err:   0.004740
[2025-10-06 22:46:33] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -29.185074 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 22:46:37] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -29.186136 | E_var:     0.1396 | E_err:   0.005838
[2025-10-06 22:46:40] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -29.187575 | E_var:     0.0692 | E_err:   0.004109
[2025-10-06 22:46:44] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -29.188017 | E_var:     0.0818 | E_err:   0.004470
[2025-10-06 22:46:48] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -29.185699 | E_var:     0.0669 | E_err:   0.004042
[2025-10-06 22:46:51] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -29.183680 | E_var:     0.0884 | E_err:   0.004646
[2025-10-06 22:46:55] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -29.190899 | E_var:     0.0864 | E_err:   0.004592
[2025-10-06 22:46:55] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 22:46:59] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -29.185033 | E_var:     0.0640 | E_err:   0.003953
[2025-10-06 22:47:03] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -29.187943 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 22:47:06] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -29.185119 | E_var:     0.1000 | E_err:   0.004941
[2025-10-06 22:47:10] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -29.187852 | E_var:     0.0653 | E_err:   0.003994
[2025-10-06 22:47:14] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -29.187276 | E_var:     0.0947 | E_err:   0.004809
[2025-10-06 22:47:17] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -29.188406 | E_var:     0.0930 | E_err:   0.004765
[2025-10-06 22:47:21] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -29.183283 | E_var:     0.0696 | E_err:   0.004122
[2025-10-06 22:47:25] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -29.180438 | E_var:     0.0907 | E_err:   0.004707
[2025-10-06 22:47:28] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -29.188922 | E_var:     0.0796 | E_err:   0.004408
[2025-10-06 22:47:32] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -29.185358 | E_var:     0.0664 | E_err:   0.004026
[2025-10-06 22:47:36] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -29.180643 | E_var:     0.0674 | E_err:   0.004057
[2025-10-06 22:47:39] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -29.186587 | E_var:     0.2124 | E_err:   0.007202
[2025-10-06 22:47:43] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -29.183670 | E_var:     0.0681 | E_err:   0.004079
[2025-10-06 22:47:46] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -29.192839 | E_var:     0.0703 | E_err:   0.004143
[2025-10-06 22:47:50] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -29.187894 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 22:47:54] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -29.187374 | E_var:     0.0852 | E_err:   0.004560
[2025-10-06 22:47:57] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -29.182002 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 22:48:01] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -29.188992 | E_var:     0.0682 | E_err:   0.004081
[2025-10-06 22:48:05] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -29.178231 | E_var:     0.0979 | E_err:   0.004888
[2025-10-06 22:48:08] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -29.183712 | E_var:     0.0915 | E_err:   0.004726
[2025-10-06 22:48:12] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -29.185367 | E_var:     0.0734 | E_err:   0.004233
[2025-10-06 22:48:16] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -29.185496 | E_var:     0.0643 | E_err:   0.003962
[2025-10-06 22:48:19] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -29.187867 | E_var:     0.0866 | E_err:   0.004599
[2025-10-06 22:48:23] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -29.182376 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 22:48:27] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -29.188599 | E_var:     0.0728 | E_err:   0.004217
[2025-10-06 22:48:30] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -29.193328 | E_var:     0.0745 | E_err:   0.004264
[2025-10-06 22:48:34] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -29.187326 | E_var:     0.1252 | E_err:   0.005529
[2025-10-06 22:48:38] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -29.192759 | E_var:     0.0819 | E_err:   0.004470
[2025-10-06 22:48:41] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -29.183745 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 22:48:45] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -29.190287 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 22:48:49] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -29.188998 | E_var:     0.0693 | E_err:   0.004113
[2025-10-06 22:48:52] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -29.189577 | E_var:     0.0674 | E_err:   0.004057
[2025-10-06 22:48:56] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -29.181876 | E_var:     0.0811 | E_err:   0.004449
[2025-10-06 22:49:00] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -29.189238 | E_var:     0.0688 | E_err:   0.004099
[2025-10-06 22:49:03] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -29.187968 | E_var:     0.0731 | E_err:   0.004226
[2025-10-06 22:49:07] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -29.187072 | E_var:     0.1188 | E_err:   0.005385
[2025-10-06 22:49:11] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -29.186369 | E_var:     0.0742 | E_err:   0.004256
[2025-10-06 22:49:14] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -29.184478 | E_var:     0.0778 | E_err:   0.004359
[2025-10-06 22:49:18] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -29.185956 | E_var:     0.0675 | E_err:   0.004061
[2025-10-06 22:49:22] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -29.186757 | E_var:     0.0739 | E_err:   0.004248
[2025-10-06 22:49:26] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -29.187700 | E_var:     0.1017 | E_err:   0.004984
[2025-10-06 22:49:29] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -29.181330 | E_var:     0.0812 | E_err:   0.004451
[2025-10-06 22:49:33] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -29.174142 | E_var:     0.1624 | E_err:   0.006297
[2025-10-06 22:49:37] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -29.185467 | E_var:     0.0735 | E_err:   0.004236
[2025-10-06 22:49:40] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -29.188313 | E_var:     0.0713 | E_err:   0.004172
[2025-10-06 22:49:44] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -29.188538 | E_var:     0.0618 | E_err:   0.003884
[2025-10-06 22:49:48] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -29.188899 | E_var:     0.0854 | E_err:   0.004567
[2025-10-06 22:49:51] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -29.192676 | E_var:     0.0720 | E_err:   0.004194
[2025-10-06 22:49:55] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -29.191021 | E_var:     0.0806 | E_err:   0.004435
[2025-10-06 22:49:59] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -29.191329 | E_var:     0.1147 | E_err:   0.005291
[2025-10-06 22:49:59] 🔄 RESTART #1 | Period: 300
[2025-10-06 22:50:02] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -29.187888 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 22:50:06] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -29.176556 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 22:50:10] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -29.192918 | E_var:     0.1485 | E_err:   0.006021
[2025-10-06 22:50:13] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -29.193649 | E_var:     0.0703 | E_err:   0.004143
[2025-10-06 22:50:17] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -29.188627 | E_var:     0.0767 | E_err:   0.004329
[2025-10-06 22:50:21] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -29.192394 | E_var:     0.0980 | E_err:   0.004890
[2025-10-06 22:50:24] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -29.182374 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 22:50:28] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -29.193528 | E_var:     0.0931 | E_err:   0.004767
[2025-10-06 22:50:32] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -29.178204 | E_var:     0.0681 | E_err:   0.004078
[2025-10-06 22:50:35] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -29.194322 | E_var:     0.0606 | E_err:   0.003847
[2025-10-06 22:50:39] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -29.185106 | E_var:     0.0729 | E_err:   0.004220
[2025-10-06 22:50:43] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -29.191168 | E_var:     0.0605 | E_err:   0.003843
[2025-10-06 22:50:46] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -29.192103 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 22:50:50] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -29.185239 | E_var:     0.0965 | E_err:   0.004854
[2025-10-06 22:50:54] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -29.192137 | E_var:     0.0749 | E_err:   0.004276
[2025-10-06 22:50:57] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -29.183668 | E_var:     0.0716 | E_err:   0.004182
[2025-10-06 22:51:01] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -29.183857 | E_var:     0.0829 | E_err:   0.004498
[2025-10-06 22:51:05] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -29.186972 | E_var:     0.0793 | E_err:   0.004400
[2025-10-06 22:51:08] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -29.186395 | E_var:     0.0700 | E_err:   0.004134
[2025-10-06 22:51:12] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -29.189135 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 22:51:16] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -29.182752 | E_var:     0.0876 | E_err:   0.004625
[2025-10-06 22:51:19] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -29.187554 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 22:51:23] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -29.188198 | E_var:     0.0733 | E_err:   0.004232
[2025-10-06 22:51:27] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -29.190180 | E_var:     0.0775 | E_err:   0.004350
[2025-10-06 22:51:30] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -29.186470 | E_var:     0.0889 | E_err:   0.004659
[2025-10-06 22:51:34] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -29.181158 | E_var:     0.1346 | E_err:   0.005734
[2025-10-06 22:51:37] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -29.193084 | E_var:     0.0724 | E_err:   0.004204
[2025-10-06 22:51:41] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -29.193321 | E_var:     0.0711 | E_err:   0.004166
[2025-10-06 22:51:45] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -29.184851 | E_var:     0.0926 | E_err:   0.004756
[2025-10-06 22:51:48] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -29.192112 | E_var:     0.0663 | E_err:   0.004022
[2025-10-06 22:51:52] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -29.183576 | E_var:     0.0666 | E_err:   0.004032
[2025-10-06 22:51:56] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -29.194816 | E_var:     0.0743 | E_err:   0.004260
[2025-10-06 22:51:59] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -29.191898 | E_var:     0.0797 | E_err:   0.004410
[2025-10-06 22:52:03] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -29.193292 | E_var:     0.0965 | E_err:   0.004855
[2025-10-06 22:52:07] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -29.188712 | E_var:     0.0728 | E_err:   0.004217
[2025-10-06 22:52:10] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -29.185578 | E_var:     0.0652 | E_err:   0.003989
[2025-10-06 22:52:14] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -29.194518 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 22:52:18] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -29.191202 | E_var:     0.0866 | E_err:   0.004599
[2025-10-06 22:52:21] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -29.188521 | E_var:     0.0915 | E_err:   0.004727
[2025-10-06 22:52:25] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -29.193120 | E_var:     0.1098 | E_err:   0.005177
[2025-10-06 22:52:29] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -29.189072 | E_var:     0.0704 | E_err:   0.004147
[2025-10-06 22:52:32] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -29.189257 | E_var:     0.0904 | E_err:   0.004698
[2025-10-06 22:52:36] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -29.186675 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 22:52:40] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -29.186505 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 22:52:43] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -29.185388 | E_var:     0.0875 | E_err:   0.004622
[2025-10-06 22:52:47] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -29.185852 | E_var:     0.0766 | E_err:   0.004324
[2025-10-06 22:52:51] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -29.171931 | E_var:     0.1559 | E_err:   0.006169
[2025-10-06 22:52:54] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -29.185903 | E_var:     0.0711 | E_err:   0.004168
[2025-10-06 22:52:58] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -29.190235 | E_var:     0.0708 | E_err:   0.004159
[2025-10-06 22:53:02] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -29.190382 | E_var:     0.0711 | E_err:   0.004166
[2025-10-06 22:53:02] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 22:53:05] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -29.178895 | E_var:     0.0803 | E_err:   0.004429
[2025-10-06 22:53:09] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -29.190385 | E_var:     0.1118 | E_err:   0.005226
[2025-10-06 22:53:13] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -29.178169 | E_var:     0.1202 | E_err:   0.005417
[2025-10-06 22:53:16] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -29.188289 | E_var:     0.0803 | E_err:   0.004428
[2025-10-06 22:53:20] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -29.189817 | E_var:     0.0738 | E_err:   0.004244
[2025-10-06 22:53:24] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -29.190914 | E_var:     0.0700 | E_err:   0.004133
[2025-10-06 22:53:27] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -29.175699 | E_var:     0.0798 | E_err:   0.004414
[2025-10-06 22:53:31] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -29.188650 | E_var:     0.1082 | E_err:   0.005140
[2025-10-06 22:53:35] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -29.185164 | E_var:     0.0772 | E_err:   0.004342
[2025-10-06 22:53:38] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -29.184329 | E_var:     0.1362 | E_err:   0.005766
[2025-10-06 22:53:42] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -29.189475 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 22:53:46] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -29.186252 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 22:53:49] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -29.186262 | E_var:     0.0700 | E_err:   0.004134
[2025-10-06 22:53:53] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -29.190809 | E_var:     0.0872 | E_err:   0.004613
[2025-10-06 22:53:57] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -29.184642 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 22:54:00] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -29.185950 | E_var:     0.0630 | E_err:   0.003922
[2025-10-06 22:54:04] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -29.182123 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 22:54:08] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -29.182784 | E_var:     0.0888 | E_err:   0.004657
[2025-10-06 22:54:11] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -29.182021 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 22:54:15] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -29.182757 | E_var:     0.0669 | E_err:   0.004041
[2025-10-06 22:54:19] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -29.181282 | E_var:     0.0932 | E_err:   0.004771
[2025-10-06 22:54:22] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -29.190255 | E_var:     0.0831 | E_err:   0.004506
[2025-10-06 22:54:26] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -29.189879 | E_var:     0.0754 | E_err:   0.004289
[2025-10-06 22:54:30] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -29.185816 | E_var:     0.0924 | E_err:   0.004750
[2025-10-06 22:54:33] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -29.195115 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 22:54:37] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -29.188717 | E_var:     0.0646 | E_err:   0.003970
[2025-10-06 22:54:41] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -29.186894 | E_var:     0.0873 | E_err:   0.004617
[2025-10-06 22:54:44] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -29.195984 | E_var:     0.0969 | E_err:   0.004864
[2025-10-06 22:54:48] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -29.182774 | E_var:     0.0859 | E_err:   0.004580
[2025-10-06 22:54:52] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -29.188306 | E_var:     0.0905 | E_err:   0.004701
[2025-10-06 22:54:55] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -29.194897 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 22:54:59] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -29.194049 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 22:55:03] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -29.188907 | E_var:     0.1003 | E_err:   0.004947
[2025-10-06 22:55:06] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -29.184715 | E_var:     0.1030 | E_err:   0.005014
[2025-10-06 22:55:10] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -29.188708 | E_var:     0.0843 | E_err:   0.004537
[2025-10-06 22:55:14] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -29.184480 | E_var:     0.0625 | E_err:   0.003906
[2025-10-06 22:55:17] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -29.184161 | E_var:     0.1467 | E_err:   0.005984
[2025-10-06 22:55:21] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -29.186439 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 22:55:25] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -29.187829 | E_var:     0.1039 | E_err:   0.005035
[2025-10-06 22:55:28] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -29.181673 | E_var:     0.0795 | E_err:   0.004405
[2025-10-06 22:55:32] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -29.178670 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 22:55:36] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -29.181280 | E_var:     0.0793 | E_err:   0.004400
[2025-10-06 22:55:39] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -29.195935 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 22:55:43] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -29.187719 | E_var:     0.0655 | E_err:   0.003998
[2025-10-06 22:55:47] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -29.182173 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 22:55:50] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -29.186193 | E_var:     0.0902 | E_err:   0.004693
[2025-10-06 22:55:54] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -29.181976 | E_var:     0.0942 | E_err:   0.004797
[2025-10-06 22:55:58] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -29.186667 | E_var:     0.0909 | E_err:   0.004711
[2025-10-06 22:56:01] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -29.178738 | E_var:     0.0723 | E_err:   0.004202
[2025-10-06 22:56:05] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -29.190775 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 22:56:09] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -29.185997 | E_var:     0.1272 | E_err:   0.005572
[2025-10-06 22:56:12] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -29.184690 | E_var:     0.0911 | E_err:   0.004716
[2025-10-06 22:56:16] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -29.191956 | E_var:     0.1135 | E_err:   0.005265
[2025-10-06 22:56:20] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -29.186262 | E_var:     0.1009 | E_err:   0.004964
[2025-10-06 22:56:23] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -29.182527 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 22:56:27] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -29.193340 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 22:56:31] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -29.187931 | E_var:     0.0870 | E_err:   0.004610
[2025-10-06 22:56:34] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -29.180915 | E_var:     0.0642 | E_err:   0.003959
[2025-10-06 22:56:38] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -29.181117 | E_var:     0.0846 | E_err:   0.004545
[2025-10-06 22:56:42] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -29.187053 | E_var:     0.0881 | E_err:   0.004637
[2025-10-06 22:56:45] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -29.178187 | E_var:     0.0906 | E_err:   0.004704
[2025-10-06 22:56:49] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -29.191048 | E_var:     0.0708 | E_err:   0.004158
[2025-10-06 22:56:53] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -29.185274 | E_var:     0.1043 | E_err:   0.005046
[2025-10-06 22:56:56] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -29.178649 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 22:57:00] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -29.188300 | E_var:     0.0755 | E_err:   0.004295
[2025-10-06 22:57:04] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -29.193025 | E_var:     0.1055 | E_err:   0.005074
[2025-10-06 22:57:07] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -29.187406 | E_var:     0.0824 | E_err:   0.004486
[2025-10-06 22:57:11] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -29.192729 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 22:57:15] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -29.186779 | E_var:     0.0671 | E_err:   0.004048
[2025-10-06 22:57:18] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -29.180346 | E_var:     0.0925 | E_err:   0.004751
[2025-10-06 22:57:22] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -29.189417 | E_var:     0.0700 | E_err:   0.004134
[2025-10-06 22:57:26] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -29.186188 | E_var:     0.0721 | E_err:   0.004196
[2025-10-06 22:57:29] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -29.187769 | E_var:     0.1020 | E_err:   0.004991
[2025-10-06 22:57:33] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -29.185541 | E_var:     0.0679 | E_err:   0.004071
[2025-10-06 22:57:37] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -29.185438 | E_var:     0.0773 | E_err:   0.004344
[2025-10-06 22:57:40] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -29.181337 | E_var:     0.0771 | E_err:   0.004338
[2025-10-06 22:57:44] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -29.184171 | E_var:     0.0631 | E_err:   0.003924
[2025-10-06 22:57:48] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -29.188160 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 22:57:51] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -29.189161 | E_var:     0.1238 | E_err:   0.005498
[2025-10-06 22:57:55] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -29.186251 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 22:57:59] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -29.185760 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 22:58:02] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -29.185677 | E_var:     0.0681 | E_err:   0.004077
[2025-10-06 22:58:06] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -29.188268 | E_var:     0.0679 | E_err:   0.004073
[2025-10-06 22:58:10] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -29.184315 | E_var:     0.0778 | E_err:   0.004360
[2025-10-06 22:58:13] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -29.176623 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 22:58:17] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -29.186467 | E_var:     0.0623 | E_err:   0.003899
[2025-10-06 22:58:21] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -29.190982 | E_var:     0.0720 | E_err:   0.004192
[2025-10-06 22:58:24] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -29.186656 | E_var:     0.1073 | E_err:   0.005117
[2025-10-06 22:58:28] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -29.190989 | E_var:     0.0863 | E_err:   0.004590
[2025-10-06 22:58:32] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -29.192238 | E_var:     0.0790 | E_err:   0.004393
[2025-10-06 22:58:35] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -29.184259 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 22:58:39] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -29.188192 | E_var:     0.0849 | E_err:   0.004553
[2025-10-06 22:58:43] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -29.181897 | E_var:     0.0682 | E_err:   0.004081
[2025-10-06 22:58:46] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -29.188641 | E_var:     0.2027 | E_err:   0.007035
[2025-10-06 22:58:50] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -29.189293 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 22:58:53] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -29.186187 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 22:58:57] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -29.189232 | E_var:     0.0970 | E_err:   0.004865
[2025-10-06 22:59:01] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -29.196379 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 22:59:04] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -29.178588 | E_var:     0.1304 | E_err:   0.005643
[2025-10-06 22:59:08] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -29.186972 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 22:59:08] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 22:59:12] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -29.188191 | E_var:     0.0784 | E_err:   0.004376
[2025-10-06 22:59:16] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -29.189938 | E_var:     0.0821 | E_err:   0.004477
[2025-10-06 22:59:19] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -29.190696 | E_var:     0.0675 | E_err:   0.004059
[2025-10-06 22:59:23] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -29.190379 | E_var:     0.0675 | E_err:   0.004060
[2025-10-06 22:59:26] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -29.181368 | E_var:     0.0662 | E_err:   0.004019
[2025-10-06 22:59:30] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -29.187314 | E_var:     0.1960 | E_err:   0.006917
[2025-10-06 22:59:34] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -29.180244 | E_var:     0.0720 | E_err:   0.004191
[2025-10-06 22:59:37] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -29.183384 | E_var:     0.0781 | E_err:   0.004368
[2025-10-06 22:59:41] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -29.188635 | E_var:     0.0675 | E_err:   0.004060
[2025-10-06 22:59:45] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -29.181421 | E_var:     0.0727 | E_err:   0.004213
[2025-10-06 22:59:48] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -29.187213 | E_var:     0.0892 | E_err:   0.004666
[2025-10-06 22:59:52] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -29.184882 | E_var:     0.0806 | E_err:   0.004435
[2025-10-06 22:59:56] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -29.185443 | E_var:     0.0897 | E_err:   0.004679
[2025-10-06 22:59:59] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -29.187575 | E_var:     0.0878 | E_err:   0.004631
[2025-10-06 23:00:03] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -29.191878 | E_var:     0.0920 | E_err:   0.004740
[2025-10-06 23:00:07] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -29.190671 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 23:00:10] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -29.188201 | E_var:     0.0747 | E_err:   0.004271
[2025-10-06 23:00:14] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -29.186198 | E_var:     0.0802 | E_err:   0.004424
[2025-10-06 23:00:18] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -29.187417 | E_var:     0.0807 | E_err:   0.004438
[2025-10-06 23:00:21] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -29.186094 | E_var:     0.0684 | E_err:   0.004085
[2025-10-06 23:00:25] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -29.181301 | E_var:     0.0781 | E_err:   0.004365
[2025-10-06 23:00:29] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -29.186923 | E_var:     0.0689 | E_err:   0.004101
[2025-10-06 23:00:32] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -29.178584 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 23:00:36] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -29.192889 | E_var:     0.1066 | E_err:   0.005102
[2025-10-06 23:00:40] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -29.191773 | E_var:     0.1271 | E_err:   0.005571
[2025-10-06 23:00:43] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -29.186019 | E_var:     0.1100 | E_err:   0.005183
[2025-10-06 23:00:47] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -29.186493 | E_var:     0.0680 | E_err:   0.004075
[2025-10-06 23:00:51] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -29.186292 | E_var:     0.0827 | E_err:   0.004492
[2025-10-06 23:00:54] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -29.185309 | E_var:     0.0905 | E_err:   0.004699
[2025-10-06 23:00:58] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -29.181732 | E_var:     0.0922 | E_err:   0.004745
[2025-10-06 23:01:02] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -29.194236 | E_var:     0.0902 | E_err:   0.004694
[2025-10-06 23:01:05] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -29.186688 | E_var:     0.0825 | E_err:   0.004488
[2025-10-06 23:01:09] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -29.187101 | E_var:     0.0576 | E_err:   0.003749
[2025-10-06 23:01:13] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -29.189071 | E_var:     0.0879 | E_err:   0.004631
[2025-10-06 23:01:16] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -29.186534 | E_var:     0.0808 | E_err:   0.004440
[2025-10-06 23:01:20] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -29.187336 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 23:01:24] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -29.191073 | E_var:     0.0665 | E_err:   0.004029
[2025-10-06 23:01:27] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -29.191303 | E_var:     0.1438 | E_err:   0.005926
[2025-10-06 23:01:31] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -29.191293 | E_var:     0.0742 | E_err:   0.004257
[2025-10-06 23:01:35] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -29.189846 | E_var:     0.0755 | E_err:   0.004292
[2025-10-06 23:01:38] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -29.179642 | E_var:     0.0982 | E_err:   0.004896
[2025-10-06 23:01:42] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -29.195229 | E_var:     0.0736 | E_err:   0.004239
[2025-10-06 23:01:46] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -29.184622 | E_var:     0.0735 | E_err:   0.004237
[2025-10-06 23:01:49] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -29.191449 | E_var:     0.0733 | E_err:   0.004231
[2025-10-06 23:01:53] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -29.180538 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 23:01:57] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -29.191071 | E_var:     0.0821 | E_err:   0.004478
[2025-10-06 23:02:00] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -29.191086 | E_var:     0.0728 | E_err:   0.004216
[2025-10-06 23:02:04] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -29.190895 | E_var:     0.0777 | E_err:   0.004356
[2025-10-06 23:02:08] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -29.187814 | E_var:     0.0630 | E_err:   0.003922
[2025-10-06 23:02:11] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -29.184306 | E_var:     0.0692 | E_err:   0.004111
[2025-10-06 23:02:15] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -29.199099 | E_var:     0.0657 | E_err:   0.004006
[2025-10-06 23:02:19] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -29.183016 | E_var:     0.0730 | E_err:   0.004220
[2025-10-06 23:02:22] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -29.189107 | E_var:     0.0772 | E_err:   0.004343
[2025-10-06 23:02:26] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -29.186686 | E_var:     0.0783 | E_err:   0.004373
[2025-10-06 23:02:30] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -29.192507 | E_var:     0.0877 | E_err:   0.004627
[2025-10-06 23:02:33] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -29.190334 | E_var:     0.0689 | E_err:   0.004102
[2025-10-06 23:02:37] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -29.193934 | E_var:     0.0730 | E_err:   0.004222
[2025-10-06 23:02:41] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -29.186844 | E_var:     0.0932 | E_err:   0.004770
[2025-10-06 23:02:44] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -29.184676 | E_var:     0.0782 | E_err:   0.004369
[2025-10-06 23:02:48] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -29.191665 | E_var:     0.0703 | E_err:   0.004144
[2025-10-06 23:02:52] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -29.185811 | E_var:     0.0937 | E_err:   0.004783
[2025-10-06 23:02:55] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -29.181586 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 23:02:59] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -29.184333 | E_var:     0.0856 | E_err:   0.004572
[2025-10-06 23:03:03] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -29.189084 | E_var:     0.0831 | E_err:   0.004505
[2025-10-06 23:03:06] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -29.195518 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 23:03:10] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -29.185756 | E_var:     0.0603 | E_err:   0.003837
[2025-10-06 23:03:14] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -29.187337 | E_var:     0.0651 | E_err:   0.003986
[2025-10-06 23:03:17] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -29.185182 | E_var:     0.0681 | E_err:   0.004078
[2025-10-06 23:03:21] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -29.191749 | E_var:     0.0608 | E_err:   0.003854
[2025-10-06 23:03:25] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -29.182363 | E_var:     0.0741 | E_err:   0.004253
[2025-10-06 23:03:28] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -29.190288 | E_var:     0.0638 | E_err:   0.003946
[2025-10-06 23:03:32] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -29.185794 | E_var:     0.0565 | E_err:   0.003714
[2025-10-06 23:03:36] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -29.189670 | E_var:     0.0647 | E_err:   0.003975
[2025-10-06 23:03:39] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -29.192414 | E_var:     0.0808 | E_err:   0.004441
[2025-10-06 23:03:43] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -29.183779 | E_var:     0.0806 | E_err:   0.004436
[2025-10-06 23:03:47] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -29.188108 | E_var:     0.0678 | E_err:   0.004070
[2025-10-06 23:03:50] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -29.189305 | E_var:     0.0792 | E_err:   0.004397
[2025-10-06 23:03:54] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -29.185197 | E_var:     0.0628 | E_err:   0.003914
[2025-10-06 23:03:58] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -29.190574 | E_var:     0.0628 | E_err:   0.003915
[2025-10-06 23:04:01] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -29.189260 | E_var:     0.0599 | E_err:   0.003825
[2025-10-06 23:04:05] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -29.187936 | E_var:     0.0810 | E_err:   0.004446
[2025-10-06 23:04:09] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -29.183141 | E_var:     0.0721 | E_err:   0.004194
[2025-10-06 23:04:12] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -29.186193 | E_var:     0.1466 | E_err:   0.005982
[2025-10-06 23:04:16] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -29.193677 | E_var:     0.0940 | E_err:   0.004790
[2025-10-06 23:04:20] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -29.188730 | E_var:     0.0841 | E_err:   0.004532
[2025-10-06 23:04:23] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -29.195468 | E_var:     0.1149 | E_err:   0.005296
[2025-10-06 23:04:27] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -29.186552 | E_var:     0.0766 | E_err:   0.004325
[2025-10-06 23:04:31] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -29.188353 | E_var:     0.0791 | E_err:   0.004396
[2025-10-06 23:04:34] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -29.182448 | E_var:     0.1190 | E_err:   0.005390
[2025-10-06 23:04:38] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -29.186333 | E_var:     0.0752 | E_err:   0.004284
[2025-10-06 23:04:42] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -29.188904 | E_var:     0.0663 | E_err:   0.004023
[2025-10-06 23:04:45] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -29.177634 | E_var:     0.1135 | E_err:   0.005263
[2025-10-06 23:04:49] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -29.184943 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 23:04:53] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -29.192298 | E_var:     0.0978 | E_err:   0.004887
[2025-10-06 23:04:56] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -29.184650 | E_var:     0.0794 | E_err:   0.004404
[2025-10-06 23:05:00] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -29.186371 | E_var:     0.0713 | E_err:   0.004171
[2025-10-06 23:05:04] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -29.191305 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 23:05:07] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -29.198682 | E_var:     0.0733 | E_err:   0.004230
[2025-10-06 23:05:11] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -29.185790 | E_var:     0.0814 | E_err:   0.004457
[2025-10-06 23:05:15] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -29.193205 | E_var:     0.0765 | E_err:   0.004321
[2025-10-06 23:05:15] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 23:05:18] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -29.185844 | E_var:     0.0880 | E_err:   0.004636
[2025-10-06 23:05:22] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -29.193457 | E_var:     0.0933 | E_err:   0.004774
[2025-10-06 23:05:26] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -29.186191 | E_var:     0.1042 | E_err:   0.005043
[2025-10-06 23:05:29] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -29.186351 | E_var:     0.0748 | E_err:   0.004275
[2025-10-06 23:05:33] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -29.181628 | E_var:     0.0950 | E_err:   0.004815
[2025-10-06 23:05:37] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -29.180811 | E_var:     0.0995 | E_err:   0.004928
[2025-10-06 23:05:40] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -29.187443 | E_var:     0.0749 | E_err:   0.004275
[2025-10-06 23:05:44] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -29.186294 | E_var:     0.0935 | E_err:   0.004779
[2025-10-06 23:05:48] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -29.184570 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 23:05:51] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -29.184816 | E_var:     0.1067 | E_err:   0.005103
[2025-10-06 23:05:55] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -29.186806 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 23:05:59] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -29.181533 | E_var:     0.0736 | E_err:   0.004238
[2025-10-06 23:06:02] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -29.194685 | E_var:     0.1142 | E_err:   0.005280
[2025-10-06 23:06:06] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -29.187919 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 23:06:10] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -29.187246 | E_var:     0.0714 | E_err:   0.004175
[2025-10-06 23:06:13] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -29.185523 | E_var:     0.0673 | E_err:   0.004052
[2025-10-06 23:06:17] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -29.189663 | E_var:     0.0787 | E_err:   0.004384
[2025-10-06 23:06:21] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -29.183462 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 23:06:24] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -29.186454 | E_var:     0.0709 | E_err:   0.004161
[2025-10-06 23:06:28] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -29.185122 | E_var:     0.0744 | E_err:   0.004261
[2025-10-06 23:06:32] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -29.184632 | E_var:     0.0785 | E_err:   0.004379
[2025-10-06 23:06:35] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -29.190348 | E_var:     0.0752 | E_err:   0.004286
[2025-10-06 23:06:39] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -29.180576 | E_var:     0.0743 | E_err:   0.004259
[2025-10-06 23:06:43] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -29.193785 | E_var:     0.0867 | E_err:   0.004602
[2025-10-06 23:06:46] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -29.189920 | E_var:     0.0717 | E_err:   0.004183
[2025-10-06 23:06:50] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -29.191786 | E_var:     0.0787 | E_err:   0.004384
[2025-10-06 23:06:54] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -29.189568 | E_var:     0.0791 | E_err:   0.004393
[2025-10-06 23:06:57] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -29.189154 | E_var:     0.1102 | E_err:   0.005186
[2025-10-06 23:07:01] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -29.185860 | E_var:     0.0776 | E_err:   0.004351
[2025-10-06 23:07:05] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -29.189171 | E_var:     0.0895 | E_err:   0.004675
[2025-10-06 23:07:08] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -29.188412 | E_var:     0.0766 | E_err:   0.004323
[2025-10-06 23:07:12] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -29.191249 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 23:07:16] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -29.193247 | E_var:     0.0747 | E_err:   0.004269
[2025-10-06 23:07:19] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -29.192987 | E_var:     0.0781 | E_err:   0.004368
[2025-10-06 23:07:23] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -29.189047 | E_var:     0.0986 | E_err:   0.004905
[2025-10-06 23:07:27] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -29.187075 | E_var:     0.0872 | E_err:   0.004613
[2025-10-06 23:07:30] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -29.181042 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 23:07:34] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -29.185925 | E_var:     0.0699 | E_err:   0.004131
[2025-10-06 23:07:37] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -29.188542 | E_var:     0.0577 | E_err:   0.003754
[2025-10-06 23:07:41] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -29.181704 | E_var:     0.0651 | E_err:   0.003987
[2025-10-06 23:07:45] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -29.187997 | E_var:     0.0660 | E_err:   0.004013
[2025-10-06 23:07:48] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -29.188604 | E_var:     0.0657 | E_err:   0.004006
[2025-10-06 23:07:52] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -29.186160 | E_var:     0.0980 | E_err:   0.004892
[2025-10-06 23:07:56] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -29.182869 | E_var:     0.0680 | E_err:   0.004076
[2025-10-06 23:07:59] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -29.186363 | E_var:     0.0795 | E_err:   0.004405
[2025-10-06 23:08:03] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -29.178790 | E_var:     0.0651 | E_err:   0.003986
[2025-10-06 23:08:07] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -29.189634 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 23:08:10] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -29.189051 | E_var:     0.0641 | E_err:   0.003957
[2025-10-06 23:08:14] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -29.189947 | E_var:     0.0634 | E_err:   0.003935
[2025-10-06 23:08:18] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -29.191336 | E_var:     0.0683 | E_err:   0.004084
[2025-10-06 23:08:18] 🔄 RESTART #2 | Period: 600
[2025-10-06 23:08:21] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -29.188701 | E_var:     0.0794 | E_err:   0.004402
[2025-10-06 23:08:25] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -29.182055 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 23:08:29] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -29.190691 | E_var:     0.0729 | E_err:   0.004218
[2025-10-06 23:08:32] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -29.193106 | E_var:     0.0961 | E_err:   0.004843
[2025-10-06 23:08:36] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -29.187689 | E_var:     0.0953 | E_err:   0.004824
[2025-10-06 23:08:40] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -29.191839 | E_var:     0.0817 | E_err:   0.004465
[2025-10-06 23:08:43] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -29.186639 | E_var:     0.0899 | E_err:   0.004684
[2025-10-06 23:08:48] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -29.184291 | E_var:     0.0704 | E_err:   0.004145
[2025-10-06 23:08:52] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -29.182329 | E_var:     0.1069 | E_err:   0.005108
[2025-10-06 23:08:56] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -29.182737 | E_var:     0.0631 | E_err:   0.003924
[2025-10-06 23:08:59] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -29.180006 | E_var:     0.0638 | E_err:   0.003947
[2025-10-06 23:09:03] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -29.192490 | E_var:     0.0824 | E_err:   0.004484
[2025-10-06 23:09:07] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -29.183802 | E_var:     0.0666 | E_err:   0.004033
[2025-10-06 23:09:10] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -29.182189 | E_var:     0.1390 | E_err:   0.005825
[2025-10-06 23:09:14] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -29.187370 | E_var:     0.0839 | E_err:   0.004527
[2025-10-06 23:09:18] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -29.191881 | E_var:     0.0869 | E_err:   0.004607
[2025-10-06 23:09:21] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -29.187419 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 23:09:25] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -29.183743 | E_var:     0.1265 | E_err:   0.005557
[2025-10-06 23:09:29] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -29.182630 | E_var:     0.0830 | E_err:   0.004503
[2025-10-06 23:09:32] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -29.182411 | E_var:     0.0765 | E_err:   0.004320
[2025-10-06 23:09:36] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -29.184953 | E_var:     0.0645 | E_err:   0.003967
[2025-10-06 23:09:40] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -29.186278 | E_var:     0.0806 | E_err:   0.004437
[2025-10-06 23:09:43] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -29.182865 | E_var:     0.1051 | E_err:   0.005066
[2025-10-06 23:09:47] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -29.186746 | E_var:     0.0998 | E_err:   0.004936
[2025-10-06 23:09:51] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -29.177732 | E_var:     0.0785 | E_err:   0.004377
[2025-10-06 23:09:54] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -29.185852 | E_var:     0.0749 | E_err:   0.004276
[2025-10-06 23:09:58] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -29.186999 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 23:10:02] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -29.190298 | E_var:     0.0808 | E_err:   0.004441
[2025-10-06 23:10:05] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -29.181706 | E_var:     0.0960 | E_err:   0.004840
[2025-10-06 23:10:09] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -29.193350 | E_var:     0.0815 | E_err:   0.004461
[2025-10-06 23:10:13] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -29.193033 | E_var:     0.1396 | E_err:   0.005838
[2025-10-06 23:10:16] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -29.184417 | E_var:     0.0656 | E_err:   0.004001
[2025-10-06 23:10:20] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -29.183528 | E_var:     0.0647 | E_err:   0.003975
[2025-10-06 23:10:24] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -29.185698 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 23:10:27] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -29.185427 | E_var:     0.1010 | E_err:   0.004966
[2025-10-06 23:10:31] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -29.190054 | E_var:     0.0929 | E_err:   0.004763
[2025-10-06 23:10:35] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -29.187977 | E_var:     0.1043 | E_err:   0.005046
[2025-10-06 23:10:38] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -29.183040 | E_var:     0.1152 | E_err:   0.005303
[2025-10-06 23:10:42] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -29.188066 | E_var:     0.0755 | E_err:   0.004294
[2025-10-06 23:10:46] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -29.188697 | E_var:     0.0877 | E_err:   0.004627
[2025-10-06 23:10:49] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -29.183404 | E_var:     0.0820 | E_err:   0.004474
[2025-10-06 23:10:53] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -29.188945 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 23:10:57] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -29.186910 | E_var:     0.0774 | E_err:   0.004346
[2025-10-06 23:11:00] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -29.181158 | E_var:     0.0815 | E_err:   0.004459
[2025-10-06 23:11:04] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -29.189630 | E_var:     0.0716 | E_err:   0.004180
[2025-10-06 23:11:08] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -29.186492 | E_var:     0.0821 | E_err:   0.004477
[2025-10-06 23:11:11] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -29.194154 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 23:11:15] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -29.184585 | E_var:     0.0647 | E_err:   0.003975
[2025-10-06 23:11:19] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -29.191291 | E_var:     0.1074 | E_err:   0.005120
[2025-10-06 23:11:22] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -29.186654 | E_var:     0.0681 | E_err:   0.004076
[2025-10-06 23:11:22] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 23:11:26] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -29.189862 | E_var:     0.1181 | E_err:   0.005369
[2025-10-06 23:11:30] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -29.190502 | E_var:     0.0986 | E_err:   0.004908
[2025-10-06 23:11:33] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -29.188471 | E_var:     0.0755 | E_err:   0.004293
[2025-10-06 23:11:37] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -29.184564 | E_var:     0.0809 | E_err:   0.004445
[2025-10-06 23:11:41] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -29.178906 | E_var:     0.0938 | E_err:   0.004787
[2025-10-06 23:11:44] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -29.183054 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 23:11:48] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -29.190412 | E_var:     0.1055 | E_err:   0.005075
[2025-10-06 23:11:52] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -29.187795 | E_var:     0.0791 | E_err:   0.004394
[2025-10-06 23:11:55] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -29.188542 | E_var:     0.0723 | E_err:   0.004202
[2025-10-06 23:11:59] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -29.195940 | E_var:     0.0706 | E_err:   0.004153
[2025-10-06 23:12:03] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -29.191161 | E_var:     0.0676 | E_err:   0.004061
[2025-10-06 23:12:06] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -29.184080 | E_var:     0.0770 | E_err:   0.004336
[2025-10-06 23:12:10] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -29.194789 | E_var:     0.1196 | E_err:   0.005403
[2025-10-06 23:12:14] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -29.182181 | E_var:     0.0750 | E_err:   0.004280
[2025-10-06 23:12:17] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -29.186417 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 23:12:21] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -29.188476 | E_var:     0.0635 | E_err:   0.003938
[2025-10-06 23:12:25] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -29.187762 | E_var:     0.0688 | E_err:   0.004100
[2025-10-06 23:12:28] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -29.189782 | E_var:     0.0655 | E_err:   0.003999
[2025-10-06 23:12:32] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -29.186832 | E_var:     0.0569 | E_err:   0.003728
[2025-10-06 23:12:36] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -29.195102 | E_var:     0.1052 | E_err:   0.005067
[2025-10-06 23:12:39] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -29.185435 | E_var:     0.0799 | E_err:   0.004417
[2025-10-06 23:12:43] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -29.196689 | E_var:     0.0612 | E_err:   0.003865
[2025-10-06 23:12:47] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -29.192415 | E_var:     0.0879 | E_err:   0.004633
[2025-10-06 23:12:50] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -29.187376 | E_var:     0.0883 | E_err:   0.004643
[2025-10-06 23:12:54] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -29.188331 | E_var:     0.0762 | E_err:   0.004314
[2025-10-06 23:12:58] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -29.188439 | E_var:     0.0720 | E_err:   0.004193
[2025-10-06 23:13:01] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -29.188337 | E_var:     0.0746 | E_err:   0.004267
[2025-10-06 23:13:05] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -29.193323 | E_var:     0.0930 | E_err:   0.004766
[2025-10-06 23:13:09] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -29.186441 | E_var:     0.0896 | E_err:   0.004677
[2025-10-06 23:13:12] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -29.187726 | E_var:     0.0809 | E_err:   0.004444
[2025-10-06 23:13:16] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -29.189607 | E_var:     0.1175 | E_err:   0.005355
[2025-10-06 23:13:20] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -29.182250 | E_var:     0.0782 | E_err:   0.004368
[2025-10-06 23:13:23] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -29.185671 | E_var:     0.0938 | E_err:   0.004784
[2025-10-06 23:13:27] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -29.192877 | E_var:     0.0889 | E_err:   0.004658
[2025-10-06 23:13:31] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -29.190625 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 23:13:34] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -29.187619 | E_var:     0.0907 | E_err:   0.004705
[2025-10-06 23:13:38] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -29.186359 | E_var:     0.0713 | E_err:   0.004172
[2025-10-06 23:13:42] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -29.189000 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 23:13:45] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -29.186267 | E_var:     0.0830 | E_err:   0.004500
[2025-10-06 23:13:49] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -29.185430 | E_var:     0.0646 | E_err:   0.003972
[2025-10-06 23:13:53] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -29.187743 | E_var:     0.0986 | E_err:   0.004905
[2025-10-06 23:13:56] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -29.177869 | E_var:     0.0747 | E_err:   0.004270
[2025-10-06 23:14:00] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -29.190801 | E_var:     0.0973 | E_err:   0.004874
[2025-10-06 23:14:03] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -29.184135 | E_var:     0.1045 | E_err:   0.005051
[2025-10-06 23:14:07] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -29.186436 | E_var:     0.0723 | E_err:   0.004200
[2025-10-06 23:14:11] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -29.186608 | E_var:     0.0690 | E_err:   0.004105
[2025-10-06 23:14:14] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -29.186236 | E_var:     0.0608 | E_err:   0.003851
[2025-10-06 23:14:18] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -29.185589 | E_var:     0.0655 | E_err:   0.003998
[2025-10-06 23:14:22] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -29.190191 | E_var:     0.0710 | E_err:   0.004164
[2025-10-06 23:14:25] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -29.184579 | E_var:     0.1141 | E_err:   0.005279
[2025-10-06 23:14:29] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -29.189033 | E_var:     0.0799 | E_err:   0.004416
[2025-10-06 23:14:33] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -29.189708 | E_var:     0.0665 | E_err:   0.004030
[2025-10-06 23:14:36] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -29.196048 | E_var:     0.0797 | E_err:   0.004411
[2025-10-06 23:14:40] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -29.185817 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 23:14:44] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -29.182059 | E_var:     0.0722 | E_err:   0.004198
[2025-10-06 23:14:47] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -29.183531 | E_var:     0.0746 | E_err:   0.004269
[2025-10-06 23:14:51] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -29.184927 | E_var:     0.0810 | E_err:   0.004448
[2025-10-06 23:14:55] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -29.186082 | E_var:     0.0601 | E_err:   0.003829
[2025-10-06 23:14:58] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -29.186992 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 23:15:02] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -29.188835 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 23:15:06] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -29.187439 | E_var:     0.0844 | E_err:   0.004538
[2025-10-06 23:15:09] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -29.178046 | E_var:     0.1269 | E_err:   0.005566
[2025-10-06 23:15:13] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -29.193872 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 23:15:17] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -29.191802 | E_var:     0.0664 | E_err:   0.004025
[2025-10-06 23:15:20] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -29.190081 | E_var:     0.0674 | E_err:   0.004057
[2025-10-06 23:15:24] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -29.188099 | E_var:     0.1013 | E_err:   0.004974
[2025-10-06 23:15:28] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -29.180348 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 23:15:31] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -29.182561 | E_var:     0.1081 | E_err:   0.005138
[2025-10-06 23:15:35] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -29.184342 | E_var:     0.0910 | E_err:   0.004712
[2025-10-06 23:15:39] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -29.183955 | E_var:     0.0872 | E_err:   0.004614
[2025-10-06 23:15:42] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -29.186065 | E_var:     0.0830 | E_err:   0.004502
[2025-10-06 23:15:46] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -29.187287 | E_var:     0.0705 | E_err:   0.004150
[2025-10-06 23:15:50] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -29.183946 | E_var:     0.0706 | E_err:   0.004151
[2025-10-06 23:15:53] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -29.184948 | E_var:     0.0764 | E_err:   0.004318
[2025-10-06 23:15:57] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -29.188379 | E_var:     0.0981 | E_err:   0.004893
[2025-10-06 23:16:01] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -29.182423 | E_var:     0.0655 | E_err:   0.003999
[2025-10-06 23:16:04] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -29.188392 | E_var:     0.0645 | E_err:   0.003969
[2025-10-06 23:16:08] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -29.188026 | E_var:     0.0773 | E_err:   0.004345
[2025-10-06 23:16:12] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -29.177681 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 23:16:15] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -29.186932 | E_var:     0.0730 | E_err:   0.004220
[2025-10-06 23:16:19] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -29.183739 | E_var:     0.1068 | E_err:   0.005106
[2025-10-06 23:16:23] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -29.180097 | E_var:     0.0790 | E_err:   0.004392
[2025-10-06 23:16:26] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -29.188160 | E_var:     0.0705 | E_err:   0.004150
[2025-10-06 23:16:30] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -29.187763 | E_var:     0.1040 | E_err:   0.005039
[2025-10-06 23:16:34] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -29.189446 | E_var:     0.0619 | E_err:   0.003888
[2025-10-06 23:16:37] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -29.187807 | E_var:     0.0772 | E_err:   0.004342
[2025-10-06 23:16:41] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -29.184266 | E_var:     0.0622 | E_err:   0.003898
[2025-10-06 23:16:45] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -29.182012 | E_var:     0.0828 | E_err:   0.004496
[2025-10-06 23:16:48] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -29.187200 | E_var:     0.0752 | E_err:   0.004286
[2025-10-06 23:16:52] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -29.181228 | E_var:     0.0820 | E_err:   0.004474
[2025-10-06 23:16:56] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -29.188642 | E_var:     0.0717 | E_err:   0.004182
[2025-10-06 23:16:59] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -29.190253 | E_var:     0.0782 | E_err:   0.004369
[2025-10-06 23:17:03] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -29.184964 | E_var:     0.0665 | E_err:   0.004028
[2025-10-06 23:17:07] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -29.179365 | E_var:     0.0927 | E_err:   0.004757
[2025-10-06 23:17:10] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -29.181962 | E_var:     0.0722 | E_err:   0.004198
[2025-10-06 23:17:14] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -29.191902 | E_var:     0.0731 | E_err:   0.004225
[2025-10-06 23:17:18] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -29.185177 | E_var:     0.0692 | E_err:   0.004110
[2025-10-06 23:17:21] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -29.188707 | E_var:     0.0731 | E_err:   0.004224
[2025-10-06 23:17:25] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -29.176835 | E_var:     0.0928 | E_err:   0.004759
[2025-10-06 23:17:29] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -29.191101 | E_var:     0.0909 | E_err:   0.004710
[2025-10-06 23:17:29] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 23:17:32] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -29.184745 | E_var:     0.1051 | E_err:   0.005065
[2025-10-06 23:17:36] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -29.186968 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 23:17:40] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -29.187770 | E_var:     0.0559 | E_err:   0.003695
[2025-10-06 23:17:43] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -29.186020 | E_var:     0.1227 | E_err:   0.005473
[2025-10-06 23:17:47] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -29.192104 | E_var:     0.0734 | E_err:   0.004234
[2025-10-06 23:17:51] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -29.189427 | E_var:     0.0686 | E_err:   0.004091
[2025-10-06 23:17:54] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -29.183469 | E_var:     0.0911 | E_err:   0.004717
[2025-10-06 23:17:58] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -29.194073 | E_var:     0.0664 | E_err:   0.004025
[2025-10-06 23:18:02] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -29.188154 | E_var:     0.0611 | E_err:   0.003862
[2025-10-06 23:18:05] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -29.180489 | E_var:     0.0691 | E_err:   0.004107
[2025-10-06 23:18:09] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -29.186515 | E_var:     0.0620 | E_err:   0.003890
[2025-10-06 23:18:13] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -29.181276 | E_var:     0.1317 | E_err:   0.005670
[2025-10-06 23:18:16] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -29.184622 | E_var:     0.0602 | E_err:   0.003834
[2025-10-06 23:18:20] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -29.189339 | E_var:     0.0656 | E_err:   0.004001
[2025-10-06 23:18:24] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -29.186177 | E_var:     0.0611 | E_err:   0.003861
[2025-10-06 23:18:27] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -29.184948 | E_var:     0.0776 | E_err:   0.004352
[2025-10-06 23:18:31] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -29.189177 | E_var:     0.1023 | E_err:   0.004999
[2025-10-06 23:18:35] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -29.182043 | E_var:     0.0552 | E_err:   0.003671
[2025-10-06 23:18:38] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -29.179489 | E_var:     0.0666 | E_err:   0.004032
[2025-10-06 23:18:42] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -29.183874 | E_var:     0.0770 | E_err:   0.004335
[2025-10-06 23:18:46] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -29.189204 | E_var:     0.0816 | E_err:   0.004464
[2025-10-06 23:18:49] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -29.195071 | E_var:     0.0649 | E_err:   0.003982
[2025-10-06 23:18:53] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -29.190973 | E_var:     0.0894 | E_err:   0.004673
[2025-10-06 23:18:57] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -29.190329 | E_var:     0.0766 | E_err:   0.004324
[2025-10-06 23:19:00] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -29.188466 | E_var:     0.0722 | E_err:   0.004197
[2025-10-06 23:19:04] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -29.188651 | E_var:     0.0756 | E_err:   0.004295
[2025-10-06 23:19:08] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -29.190731 | E_var:     0.0974 | E_err:   0.004876
[2025-10-06 23:19:11] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -29.186370 | E_var:     0.0553 | E_err:   0.003674
[2025-10-06 23:19:15] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -29.194448 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 23:19:19] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -29.180365 | E_var:     0.0766 | E_err:   0.004325
[2025-10-06 23:19:22] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -29.189215 | E_var:     0.0827 | E_err:   0.004494
[2025-10-06 23:19:26] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -29.185885 | E_var:     0.0697 | E_err:   0.004124
[2025-10-06 23:19:30] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -29.187737 | E_var:     0.0855 | E_err:   0.004570
[2025-10-06 23:19:33] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -29.188466 | E_var:     0.0690 | E_err:   0.004104
[2025-10-06 23:19:37] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -29.186535 | E_var:     0.0684 | E_err:   0.004087
[2025-10-06 23:19:41] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -29.186613 | E_var:     0.0610 | E_err:   0.003860
[2025-10-06 23:19:44] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -29.186435 | E_var:     0.0723 | E_err:   0.004202
[2025-10-06 23:19:48] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -29.191241 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 23:19:51] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -29.189257 | E_var:     0.0713 | E_err:   0.004171
[2025-10-06 23:19:55] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -29.182260 | E_var:     0.0776 | E_err:   0.004352
[2025-10-06 23:19:59] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -29.190230 | E_var:     0.0573 | E_err:   0.003741
[2025-10-06 23:20:02] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -29.182249 | E_var:     0.1112 | E_err:   0.005210
[2025-10-06 23:20:06] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -29.185051 | E_var:     0.0934 | E_err:   0.004774
[2025-10-06 23:20:10] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -29.187654 | E_var:     0.0943 | E_err:   0.004798
[2025-10-06 23:20:13] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -29.179030 | E_var:     0.0840 | E_err:   0.004528
[2025-10-06 23:20:17] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -29.191788 | E_var:     0.0582 | E_err:   0.003771
[2025-10-06 23:20:21] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -29.191233 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 23:20:24] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -29.193485 | E_var:     0.0749 | E_err:   0.004275
[2025-10-06 23:20:28] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -29.193277 | E_var:     0.0821 | E_err:   0.004477
[2025-10-06 23:20:32] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -29.189436 | E_var:     0.0920 | E_err:   0.004740
[2025-10-06 23:20:35] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -29.189189 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 23:20:39] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -29.183504 | E_var:     0.0991 | E_err:   0.004918
[2025-10-06 23:20:43] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -29.190060 | E_var:     0.3516 | E_err:   0.009265
[2025-10-06 23:20:46] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -29.192839 | E_var:     0.0740 | E_err:   0.004251
[2025-10-06 23:20:50] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -29.189870 | E_var:     0.0649 | E_err:   0.003980
[2025-10-06 23:20:54] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -29.185916 | E_var:     0.0837 | E_err:   0.004521
[2025-10-06 23:20:57] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -29.195045 | E_var:     0.1134 | E_err:   0.005262
[2025-10-06 23:21:01] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -29.188910 | E_var:     0.0641 | E_err:   0.003957
[2025-10-06 23:21:05] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -29.182322 | E_var:     0.0782 | E_err:   0.004370
[2025-10-06 23:21:08] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -29.192473 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 23:21:12] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -29.188672 | E_var:     0.0646 | E_err:   0.003971
[2025-10-06 23:21:16] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -29.183228 | E_var:     0.0835 | E_err:   0.004515
[2025-10-06 23:21:19] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -29.194649 | E_var:     0.1008 | E_err:   0.004961
[2025-10-06 23:21:23] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -29.184116 | E_var:     0.0815 | E_err:   0.004462
[2025-10-06 23:21:27] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -29.181048 | E_var:     0.0999 | E_err:   0.004938
[2025-10-06 23:21:30] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -29.191246 | E_var:     0.1036 | E_err:   0.005030
[2025-10-06 23:21:34] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -29.189751 | E_var:     0.0605 | E_err:   0.003845
[2025-10-06 23:21:38] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -29.193957 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 23:21:41] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -29.190677 | E_var:     0.0643 | E_err:   0.003961
[2025-10-06 23:21:45] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -29.189586 | E_var:     0.0641 | E_err:   0.003956
[2025-10-06 23:21:49] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -29.183691 | E_var:     0.0759 | E_err:   0.004303
[2025-10-06 23:21:52] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -29.188827 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 23:21:56] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -29.180410 | E_var:     0.0897 | E_err:   0.004680
[2025-10-06 23:22:00] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -29.181627 | E_var:     0.1140 | E_err:   0.005275
[2025-10-06 23:22:03] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -29.188687 | E_var:     0.0705 | E_err:   0.004147
[2025-10-06 23:22:07] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -29.184502 | E_var:     0.0748 | E_err:   0.004273
[2025-10-06 23:22:11] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -29.180640 | E_var:     0.1170 | E_err:   0.005345
[2025-10-06 23:22:14] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -29.189413 | E_var:     0.0788 | E_err:   0.004386
[2025-10-06 23:22:18] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -29.185093 | E_var:     0.0772 | E_err:   0.004342
[2025-10-06 23:22:22] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -29.185545 | E_var:     0.0850 | E_err:   0.004556
[2025-10-06 23:22:25] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -29.192539 | E_var:     0.0925 | E_err:   0.004752
[2025-10-06 23:22:29] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -29.184881 | E_var:     0.0710 | E_err:   0.004163
[2025-10-06 23:22:33] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -29.194610 | E_var:     0.0770 | E_err:   0.004335
[2025-10-06 23:22:36] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -29.179826 | E_var:     0.0968 | E_err:   0.004861
[2025-10-06 23:22:40] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -29.189902 | E_var:     0.0686 | E_err:   0.004092
[2025-10-06 23:22:44] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -29.185232 | E_var:     0.1200 | E_err:   0.005413
[2025-10-06 23:22:47] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -29.188415 | E_var:     0.0829 | E_err:   0.004499
[2025-10-06 23:22:51] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -29.189717 | E_var:     0.0898 | E_err:   0.004683
[2025-10-06 23:22:55] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -29.186206 | E_var:     0.0826 | E_err:   0.004491
[2025-10-06 23:22:58] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -29.188544 | E_var:     0.1001 | E_err:   0.004944
[2025-10-06 23:23:02] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -29.183563 | E_var:     0.0536 | E_err:   0.003618
[2025-10-06 23:23:06] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -29.189962 | E_var:     0.0778 | E_err:   0.004357
[2025-10-06 23:23:09] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -29.186306 | E_var:     0.0701 | E_err:   0.004136
[2025-10-06 23:23:13] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -29.190812 | E_var:     0.0805 | E_err:   0.004434
[2025-10-06 23:23:16] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -29.186411 | E_var:     0.1009 | E_err:   0.004963
[2025-10-06 23:23:20] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -29.189572 | E_var:     0.0941 | E_err:   0.004793
[2025-10-06 23:23:24] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -29.186524 | E_var:     0.0781 | E_err:   0.004368
[2025-10-06 23:23:27] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -29.186353 | E_var:     0.0789 | E_err:   0.004389
[2025-10-06 23:23:31] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -29.183230 | E_var:     0.0834 | E_err:   0.004512
[2025-10-06 23:23:35] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -29.189490 | E_var:     0.1056 | E_err:   0.005076
[2025-10-06 23:23:35] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 23:23:39] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -29.186066 | E_var:     0.0702 | E_err:   0.004139
[2025-10-06 23:23:42] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -29.186045 | E_var:     0.0652 | E_err:   0.003989
[2025-10-06 23:23:46] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -29.190003 | E_var:     0.0648 | E_err:   0.003978
[2025-10-06 23:23:49] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -29.189338 | E_var:     0.0552 | E_err:   0.003670
[2025-10-06 23:23:53] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -29.189752 | E_var:     0.0768 | E_err:   0.004331
[2025-10-06 23:23:57] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -29.182389 | E_var:     0.0991 | E_err:   0.004920
[2025-10-06 23:24:00] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -29.189089 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 23:24:04] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -29.182744 | E_var:     0.0770 | E_err:   0.004335
[2025-10-06 23:24:08] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -29.190304 | E_var:     0.0594 | E_err:   0.003809
[2025-10-06 23:24:11] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -29.183379 | E_var:     0.0962 | E_err:   0.004846
[2025-10-06 23:24:15] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -29.191129 | E_var:     0.0793 | E_err:   0.004399
[2025-10-06 23:24:19] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -29.188098 | E_var:     0.0763 | E_err:   0.004317
[2025-10-06 23:24:22] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -29.182680 | E_var:     0.1362 | E_err:   0.005766
[2025-10-06 23:24:26] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -29.188442 | E_var:     0.0655 | E_err:   0.003999
[2025-10-06 23:24:30] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -29.194303 | E_var:     0.0804 | E_err:   0.004431
[2025-10-06 23:24:33] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -29.179885 | E_var:     0.0773 | E_err:   0.004343
[2025-10-06 23:24:37] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -29.190029 | E_var:     0.0643 | E_err:   0.003962
[2025-10-06 23:24:41] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -29.180757 | E_var:     0.0833 | E_err:   0.004510
[2025-10-06 23:24:44] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -29.198811 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 23:24:48] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -29.185291 | E_var:     0.0678 | E_err:   0.004069
[2025-10-06 23:24:52] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -29.187741 | E_var:     0.0935 | E_err:   0.004778
[2025-10-06 23:24:55] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -29.186942 | E_var:     0.1517 | E_err:   0.006085
[2025-10-06 23:24:59] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -29.188883 | E_var:     0.0972 | E_err:   0.004872
[2025-10-06 23:25:03] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -29.192273 | E_var:     0.0776 | E_err:   0.004353
[2025-10-06 23:25:06] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -29.187627 | E_var:     0.0540 | E_err:   0.003630
[2025-10-06 23:25:10] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -29.187471 | E_var:     0.0914 | E_err:   0.004724
[2025-10-06 23:25:14] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -29.185201 | E_var:     0.0729 | E_err:   0.004219
[2025-10-06 23:25:17] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -29.187552 | E_var:     0.0659 | E_err:   0.004012
[2025-10-06 23:25:21] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -29.180101 | E_var:     0.0702 | E_err:   0.004140
[2025-10-06 23:25:25] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -29.187922 | E_var:     0.1098 | E_err:   0.005178
[2025-10-06 23:25:29] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -29.183652 | E_var:     0.0786 | E_err:   0.004380
[2025-10-06 23:25:32] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -29.188704 | E_var:     0.0645 | E_err:   0.003969
[2025-10-06 23:25:36] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -29.188126 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 23:25:40] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -29.184953 | E_var:     0.6446 | E_err:   0.012544
[2025-10-06 23:25:43] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -29.191036 | E_var:     0.0656 | E_err:   0.004003
[2025-10-06 23:25:47] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -29.185771 | E_var:     0.0995 | E_err:   0.004928
[2025-10-06 23:25:51] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -29.186133 | E_var:     0.0712 | E_err:   0.004171
[2025-10-06 23:25:54] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -29.189763 | E_var:     0.0843 | E_err:   0.004537
[2025-10-06 23:25:58] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -29.187148 | E_var:     0.0800 | E_err:   0.004421
[2025-10-06 23:26:02] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -29.187083 | E_var:     0.0864 | E_err:   0.004594
[2025-10-06 23:26:05] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -29.190705 | E_var:     0.0987 | E_err:   0.004908
[2025-10-06 23:26:09] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -29.185744 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 23:26:12] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -29.182136 | E_var:     0.0848 | E_err:   0.004550
[2025-10-06 23:26:16] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -29.192699 | E_var:     0.1039 | E_err:   0.005036
[2025-10-06 23:26:20] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -29.191359 | E_var:     0.0803 | E_err:   0.004428
[2025-10-06 23:26:23] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -29.194070 | E_var:     0.0809 | E_err:   0.004444
[2025-10-06 23:26:27] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -29.190603 | E_var:     0.0762 | E_err:   0.004315
[2025-10-06 23:26:31] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -29.182050 | E_var:     0.0764 | E_err:   0.004319
[2025-10-06 23:26:34] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -29.186600 | E_var:     0.0880 | E_err:   0.004636
[2025-10-06 23:26:38] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -29.190336 | E_var:     0.0646 | E_err:   0.003971
[2025-10-06 23:26:42] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -29.194038 | E_var:     0.0825 | E_err:   0.004489
[2025-10-06 23:26:45] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -29.190458 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 23:26:49] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -29.183175 | E_var:     0.0728 | E_err:   0.004216
[2025-10-06 23:26:53] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -29.190341 | E_var:     0.0772 | E_err:   0.004341
[2025-10-06 23:26:56] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -29.199338 | E_var:     0.0828 | E_err:   0.004497
[2025-10-06 23:27:00] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -29.189043 | E_var:     0.0824 | E_err:   0.004485
[2025-10-06 23:27:04] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -29.180241 | E_var:     0.0831 | E_err:   0.004504
[2025-10-06 23:27:07] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -29.186324 | E_var:     0.0624 | E_err:   0.003904
[2025-10-06 23:27:11] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -29.177341 | E_var:     0.0664 | E_err:   0.004026
[2025-10-06 23:27:15] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -29.178444 | E_var:     0.0795 | E_err:   0.004404
[2025-10-06 23:27:18] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -29.177893 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 23:27:22] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -29.188857 | E_var:     0.0815 | E_err:   0.004460
[2025-10-06 23:27:26] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -29.189753 | E_var:     0.0823 | E_err:   0.004484
[2025-10-06 23:27:29] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -29.188457 | E_var:     0.0792 | E_err:   0.004396
[2025-10-06 23:27:33] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -29.184741 | E_var:     0.0717 | E_err:   0.004183
[2025-10-06 23:27:37] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -29.191133 | E_var:     0.0833 | E_err:   0.004509
[2025-10-06 23:27:40] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -29.183412 | E_var:     0.0791 | E_err:   0.004394
[2025-10-06 23:27:44] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -29.191624 | E_var:     0.0615 | E_err:   0.003876
[2025-10-06 23:27:48] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -29.187822 | E_var:     0.0694 | E_err:   0.004116
[2025-10-06 23:27:53] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -29.193366 | E_var:     0.0816 | E_err:   0.004463
[2025-10-06 23:27:57] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -29.179694 | E_var:     0.0660 | E_err:   0.004014
[2025-10-06 23:28:00] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -29.190508 | E_var:     0.0852 | E_err:   0.004561
[2025-10-06 23:28:04] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -29.191460 | E_var:     0.0866 | E_err:   0.004597
[2025-10-06 23:28:08] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -29.187914 | E_var:     0.0781 | E_err:   0.004367
[2025-10-06 23:28:11] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -29.186463 | E_var:     0.0753 | E_err:   0.004286
[2025-10-06 23:28:15] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -29.184874 | E_var:     0.0702 | E_err:   0.004140
[2025-10-06 23:28:19] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -29.185418 | E_var:     0.0765 | E_err:   0.004322
[2025-10-06 23:28:22] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -29.186253 | E_var:     0.0852 | E_err:   0.004560
[2025-10-06 23:28:26] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -29.187246 | E_var:     0.0653 | E_err:   0.003992
[2025-10-06 23:28:30] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -29.188171 | E_var:     0.0961 | E_err:   0.004844
[2025-10-06 23:28:33] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -29.182784 | E_var:     0.1193 | E_err:   0.005398
[2025-10-06 23:28:37] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -29.180942 | E_var:     0.1071 | E_err:   0.005114
[2025-10-06 23:28:41] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -29.179817 | E_var:     0.0701 | E_err:   0.004138
[2025-10-06 23:28:44] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -29.195990 | E_var:     0.0808 | E_err:   0.004441
[2025-10-06 23:28:48] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -29.187999 | E_var:     0.0716 | E_err:   0.004182
[2025-10-06 23:28:52] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -29.195070 | E_var:     0.1015 | E_err:   0.004978
[2025-10-06 23:28:55] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -29.186501 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 23:28:59] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -29.178156 | E_var:     0.0822 | E_err:   0.004480
[2025-10-06 23:29:03] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -29.183766 | E_var:     0.0708 | E_err:   0.004157
[2025-10-06 23:29:06] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -29.177206 | E_var:     0.0935 | E_err:   0.004778
[2025-10-06 23:29:10] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -29.186873 | E_var:     0.0618 | E_err:   0.003886
[2025-10-06 23:29:14] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -29.190934 | E_var:     0.0784 | E_err:   0.004375
[2025-10-06 23:29:17] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -29.185679 | E_var:     0.0739 | E_err:   0.004247
[2025-10-06 23:29:21] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -29.198809 | E_var:     0.0881 | E_err:   0.004639
[2025-10-06 23:29:25] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -29.193388 | E_var:     0.1294 | E_err:   0.005621
[2025-10-06 23:29:28] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -29.187750 | E_var:     0.1304 | E_err:   0.005643
[2025-10-06 23:29:32] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -29.181629 | E_var:     0.0776 | E_err:   0.004353
[2025-10-06 23:29:36] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -29.185318 | E_var:     0.0766 | E_err:   0.004324
[2025-10-06 23:29:39] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -29.183534 | E_var:     0.0775 | E_err:   0.004350
[2025-10-06 23:29:43] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -29.180860 | E_var:     0.0760 | E_err:   0.004307
[2025-10-06 23:29:43] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 23:29:47] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -29.190081 | E_var:     0.0664 | E_err:   0.004026
[2025-10-06 23:29:50] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -29.187902 | E_var:     0.0640 | E_err:   0.003952
[2025-10-06 23:29:54] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -29.187847 | E_var:     0.0716 | E_err:   0.004181
[2025-10-06 23:29:58] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -29.190148 | E_var:     0.0718 | E_err:   0.004188
[2025-10-06 23:30:01] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -29.184666 | E_var:     0.0869 | E_err:   0.004607
[2025-10-06 23:30:05] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -29.191352 | E_var:     0.0706 | E_err:   0.004152
[2025-10-06 23:30:09] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -29.188633 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 23:30:12] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -29.189624 | E_var:     0.0714 | E_err:   0.004174
[2025-10-06 23:30:16] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -29.187300 | E_var:     0.0836 | E_err:   0.004519
[2025-10-06 23:30:20] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -29.183481 | E_var:     0.0751 | E_err:   0.004282
[2025-10-06 23:30:23] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -29.186693 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 23:30:27] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -29.185224 | E_var:     0.0887 | E_err:   0.004654
[2025-10-06 23:30:31] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -29.184763 | E_var:     0.0837 | E_err:   0.004520
[2025-10-06 23:30:34] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -29.181876 | E_var:     0.0674 | E_err:   0.004057
[2025-10-06 23:30:38] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -29.186299 | E_var:     0.0567 | E_err:   0.003721
[2025-10-06 23:30:42] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -29.184439 | E_var:     0.0779 | E_err:   0.004361
[2025-10-06 23:30:45] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -29.191079 | E_var:     0.0983 | E_err:   0.004898
[2025-10-06 23:30:49] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -29.187260 | E_var:     0.0908 | E_err:   0.004709
[2025-10-06 23:30:53] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -29.196972 | E_var:     0.0718 | E_err:   0.004186
[2025-10-06 23:30:56] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -29.181962 | E_var:     0.0865 | E_err:   0.004596
[2025-10-06 23:31:00] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -29.185693 | E_var:     0.0963 | E_err:   0.004848
[2025-10-06 23:31:04] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -29.190106 | E_var:     0.0765 | E_err:   0.004322
[2025-10-06 23:31:07] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -29.177934 | E_var:     0.1186 | E_err:   0.005382
[2025-10-06 23:31:11] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -29.187489 | E_var:     0.0687 | E_err:   0.004094
[2025-10-06 23:31:15] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -29.183180 | E_var:     0.0754 | E_err:   0.004289
[2025-10-06 23:31:18] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -29.186185 | E_var:     0.0649 | E_err:   0.003982
[2025-10-06 23:31:22] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -29.179712 | E_var:     0.0772 | E_err:   0.004342
[2025-10-06 23:31:26] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -29.190196 | E_var:     0.0641 | E_err:   0.003956
[2025-10-06 23:31:29] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -29.182411 | E_var:     0.0650 | E_err:   0.003984
[2025-10-06 23:31:33] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -29.189371 | E_var:     0.0750 | E_err:   0.004279
[2025-10-06 23:31:37] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -29.188874 | E_var:     0.0683 | E_err:   0.004083
[2025-10-06 23:31:40] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -29.180040 | E_var:     0.0575 | E_err:   0.003748
[2025-10-06 23:31:44] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -29.190191 | E_var:     0.0724 | E_err:   0.004203
[2025-10-06 23:31:48] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -29.185015 | E_var:     0.0668 | E_err:   0.004039
[2025-10-06 23:31:51] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -29.182152 | E_var:     0.0829 | E_err:   0.004500
[2025-10-06 23:31:55] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -29.193701 | E_var:     0.0802 | E_err:   0.004425
[2025-10-06 23:31:59] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -29.186579 | E_var:     0.0730 | E_err:   0.004223
[2025-10-06 23:32:03] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -29.182885 | E_var:     0.0731 | E_err:   0.004223
[2025-10-06 23:32:06] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -29.184389 | E_var:     0.0678 | E_err:   0.004068
[2025-10-06 23:32:10] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -29.195635 | E_var:     0.0705 | E_err:   0.004148
[2025-10-06 23:32:13] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -29.187004 | E_var:     0.0564 | E_err:   0.003709
[2025-10-06 23:32:18] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -29.185525 | E_var:     0.0819 | E_err:   0.004471
[2025-10-06 23:32:22] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -29.188626 | E_var:     0.0635 | E_err:   0.003938
[2025-10-06 23:32:25] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -29.188475 | E_var:     0.0912 | E_err:   0.004719
[2025-10-06 23:32:29] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -29.195510 | E_var:     0.0882 | E_err:   0.004640
[2025-10-06 23:32:33] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -29.186127 | E_var:     0.0942 | E_err:   0.004797
[2025-10-06 23:32:36] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -29.184284 | E_var:     0.0803 | E_err:   0.004427
[2025-10-06 23:32:40] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -29.174824 | E_var:     0.1411 | E_err:   0.005869
[2025-10-06 23:32:44] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -29.185337 | E_var:     0.1084 | E_err:   0.005145
[2025-10-06 23:32:47] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -29.191921 | E_var:     0.0748 | E_err:   0.004273
[2025-10-06 23:32:51] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -29.192408 | E_var:     0.0865 | E_err:   0.004597
[2025-10-06 23:32:55] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -29.200600 | E_var:     0.1046 | E_err:   0.005052
[2025-10-06 23:32:58] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -29.187629 | E_var:     0.0695 | E_err:   0.004119
[2025-10-06 23:33:02] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -29.183061 | E_var:     0.0660 | E_err:   0.004015
[2025-10-06 23:33:06] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -29.189218 | E_var:     0.0706 | E_err:   0.004150
[2025-10-06 23:33:09] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -29.191550 | E_var:     0.0806 | E_err:   0.004435
[2025-10-06 23:33:13] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -29.192190 | E_var:     0.0603 | E_err:   0.003836
[2025-10-06 23:33:17] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -29.189632 | E_var:     0.1453 | E_err:   0.005957
[2025-10-06 23:33:20] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -29.186573 | E_var:     0.0731 | E_err:   0.004224
[2025-10-06 23:33:24] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -29.188592 | E_var:     0.0632 | E_err:   0.003927
[2025-10-06 23:33:28] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -29.182803 | E_var:     0.1249 | E_err:   0.005522
[2025-10-06 23:33:31] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -29.189441 | E_var:     0.0718 | E_err:   0.004187
[2025-10-06 23:33:35] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -29.181041 | E_var:     0.0802 | E_err:   0.004426
[2025-10-06 23:33:39] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -29.190131 | E_var:     0.0721 | E_err:   0.004195
[2025-10-06 23:33:42] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -29.182817 | E_var:     0.0610 | E_err:   0.003859
[2025-10-06 23:33:46] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -29.187210 | E_var:     0.1092 | E_err:   0.005164
[2025-10-06 23:33:50] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -29.197991 | E_var:     0.0707 | E_err:   0.004156
[2025-10-06 23:33:53] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -29.184238 | E_var:     0.0673 | E_err:   0.004054
[2025-10-06 23:33:57] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -29.190751 | E_var:     0.1103 | E_err:   0.005189
[2025-10-06 23:34:01] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -29.190714 | E_var:     0.3222 | E_err:   0.008869
[2025-10-06 23:34:04] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -29.189637 | E_var:     0.0846 | E_err:   0.004544
[2025-10-06 23:34:08] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -29.182795 | E_var:     0.0736 | E_err:   0.004238
[2025-10-06 23:34:12] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -29.191370 | E_var:     0.0599 | E_err:   0.003825
[2025-10-06 23:34:15] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -29.190689 | E_var:     0.0763 | E_err:   0.004316
[2025-10-06 23:34:19] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -29.185449 | E_var:     0.0682 | E_err:   0.004079
[2025-10-06 23:34:23] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -29.188656 | E_var:     0.0711 | E_err:   0.004167
[2025-10-06 23:34:26] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -29.197289 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 23:34:30] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -29.190803 | E_var:     0.1010 | E_err:   0.004964
[2025-10-06 23:34:34] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -29.184193 | E_var:     0.0728 | E_err:   0.004215
[2025-10-06 23:34:37] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -29.177008 | E_var:     0.2788 | E_err:   0.008250
[2025-10-06 23:34:41] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -29.192985 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 23:34:45] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -29.193983 | E_var:     0.0901 | E_err:   0.004690
[2025-10-06 23:34:48] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -29.188907 | E_var:     0.0852 | E_err:   0.004560
[2025-10-06 23:34:52] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -29.184380 | E_var:     0.0860 | E_err:   0.004581
[2025-10-06 23:34:56] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -29.188027 | E_var:     0.0875 | E_err:   0.004623
[2025-10-06 23:34:59] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -29.186878 | E_var:     0.0745 | E_err:   0.004266
[2025-10-06 23:35:03] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -29.184160 | E_var:     0.0810 | E_err:   0.004447
[2025-10-06 23:35:07] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -29.183197 | E_var:     0.0842 | E_err:   0.004535
[2025-10-06 23:35:10] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -29.189299 | E_var:     0.2350 | E_err:   0.007575
[2025-10-06 23:35:14] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -29.190782 | E_var:     0.0761 | E_err:   0.004311
[2025-10-06 23:35:18] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -29.187057 | E_var:     0.0672 | E_err:   0.004050
[2025-10-06 23:35:21] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -29.192076 | E_var:     0.0709 | E_err:   0.004159
[2025-10-06 23:35:25] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -29.189330 | E_var:     0.0936 | E_err:   0.004781
[2025-10-06 23:35:29] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -29.179524 | E_var:     0.0741 | E_err:   0.004252
[2025-10-06 23:35:32] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -29.188783 | E_var:     0.0671 | E_err:   0.004047
[2025-10-06 23:35:36] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -29.194407 | E_var:     0.0858 | E_err:   0.004576
[2025-10-06 23:35:40] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -29.186603 | E_var:     0.1547 | E_err:   0.006146
[2025-10-06 23:35:43] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -29.189228 | E_var:     0.0657 | E_err:   0.004005
[2025-10-06 23:35:47] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -29.187132 | E_var:     0.0841 | E_err:   0.004531
[2025-10-06 23:35:51] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -29.187439 | E_var:     0.0653 | E_err:   0.003994
[2025-10-06 23:35:51] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 23:35:54] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -29.194611 | E_var:     0.0772 | E_err:   0.004340
[2025-10-06 23:35:58] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -29.189933 | E_var:     0.0743 | E_err:   0.004258
[2025-10-06 23:36:02] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -29.184792 | E_var:     0.0554 | E_err:   0.003678
[2025-10-06 23:36:06] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -29.192337 | E_var:     0.0670 | E_err:   0.004046
[2025-10-06 23:36:09] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -29.183361 | E_var:     0.1032 | E_err:   0.005020
[2025-10-06 23:36:13] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -29.184462 | E_var:     0.0819 | E_err:   0.004473
[2025-10-06 23:36:17] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -29.187556 | E_var:     0.0928 | E_err:   0.004760
[2025-10-06 23:36:20] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -29.189847 | E_var:     0.0613 | E_err:   0.003869
[2025-10-06 23:36:24] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -29.188144 | E_var:     0.0639 | E_err:   0.003951
[2025-10-06 23:36:28] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -29.182648 | E_var:     0.0789 | E_err:   0.004389
[2025-10-06 23:36:31] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -29.194186 | E_var:     0.1053 | E_err:   0.005069
[2025-10-06 23:36:35] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -29.192446 | E_var:     0.0713 | E_err:   0.004173
[2025-10-06 23:36:39] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -29.193523 | E_var:     0.0691 | E_err:   0.004106
[2025-10-06 23:36:42] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -29.183270 | E_var:     0.0991 | E_err:   0.004918
[2025-10-06 23:36:46] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -29.189729 | E_var:     0.0702 | E_err:   0.004141
[2025-10-06 23:36:50] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -29.191380 | E_var:     0.0753 | E_err:   0.004289
[2025-10-06 23:36:53] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -29.181541 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 23:36:57] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -29.186093 | E_var:     0.0687 | E_err:   0.004094
[2025-10-06 23:37:00] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -29.180479 | E_var:     0.0721 | E_err:   0.004195
[2025-10-06 23:37:04] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -29.195625 | E_var:     0.0650 | E_err:   0.003983
[2025-10-06 23:37:08] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -29.184152 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 23:37:11] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -29.180123 | E_var:     0.0848 | E_err:   0.004549
[2025-10-06 23:37:15] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -29.186583 | E_var:     0.0693 | E_err:   0.004112
[2025-10-06 23:37:19] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -29.195760 | E_var:     0.0948 | E_err:   0.004812
[2025-10-06 23:37:22] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -29.184453 | E_var:     0.0707 | E_err:   0.004154
[2025-10-06 23:37:26] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -29.187762 | E_var:     0.0700 | E_err:   0.004134
[2025-10-06 23:37:30] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -29.179933 | E_var:     0.0874 | E_err:   0.004620
[2025-10-06 23:37:33] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -29.180146 | E_var:     0.1080 | E_err:   0.005134
[2025-10-06 23:37:37] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -29.184256 | E_var:     0.0695 | E_err:   0.004119
[2025-10-06 23:37:41] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -29.184190 | E_var:     0.0799 | E_err:   0.004418
[2025-10-06 23:37:44] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -29.188986 | E_var:     0.0633 | E_err:   0.003930
[2025-10-06 23:37:48] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -29.189553 | E_var:     0.0659 | E_err:   0.004010
[2025-10-06 23:37:52] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -29.187669 | E_var:     0.0648 | E_err:   0.003978
[2025-10-06 23:37:55] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -29.194166 | E_var:     0.0552 | E_err:   0.003670
[2025-10-06 23:37:59] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -29.189297 | E_var:     0.0793 | E_err:   0.004401
[2025-10-06 23:38:03] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -29.187450 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 23:38:06] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -29.181197 | E_var:     0.0580 | E_err:   0.003764
[2025-10-06 23:38:10] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -29.184570 | E_var:     0.0812 | E_err:   0.004453
[2025-10-06 23:38:14] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -29.187962 | E_var:     0.0573 | E_err:   0.003739
[2025-10-06 23:38:17] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -29.191115 | E_var:     0.0858 | E_err:   0.004578
[2025-10-06 23:38:21] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -29.187005 | E_var:     0.0783 | E_err:   0.004373
[2025-10-06 23:38:25] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -29.190716 | E_var:     0.0756 | E_err:   0.004297
[2025-10-06 23:38:28] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -29.186753 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 23:38:32] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -29.193854 | E_var:     0.0778 | E_err:   0.004358
[2025-10-06 23:38:36] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -29.189023 | E_var:     0.0684 | E_err:   0.004087
[2025-10-06 23:38:39] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -29.192413 | E_var:     0.0642 | E_err:   0.003959
[2025-10-06 23:38:43] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -29.185287 | E_var:     0.0949 | E_err:   0.004813
[2025-10-06 23:38:47] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -29.189007 | E_var:     0.0856 | E_err:   0.004571
[2025-10-06 23:38:50] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -29.190787 | E_var:     0.0678 | E_err:   0.004070
[2025-10-06 23:38:54] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -29.185650 | E_var:     0.1094 | E_err:   0.005168
[2025-10-06 23:38:58] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -29.190366 | E_var:     0.0692 | E_err:   0.004109
[2025-10-06 23:39:01] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -29.184851 | E_var:     0.0732 | E_err:   0.004226
[2025-10-06 23:39:05] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -29.187035 | E_var:     0.0980 | E_err:   0.004892
[2025-10-06 23:39:09] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -29.192085 | E_var:     0.0563 | E_err:   0.003707
[2025-10-06 23:39:12] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -29.188282 | E_var:     0.0891 | E_err:   0.004665
[2025-10-06 23:39:16] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -29.188474 | E_var:     0.0684 | E_err:   0.004087
[2025-10-06 23:39:20] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -29.191678 | E_var:     0.0830 | E_err:   0.004501
[2025-10-06 23:39:23] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -29.184865 | E_var:     0.0868 | E_err:   0.004602
[2025-10-06 23:39:27] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -29.192404 | E_var:     0.0774 | E_err:   0.004347
[2025-10-06 23:39:31] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -29.190975 | E_var:     0.0911 | E_err:   0.004715
[2025-10-06 23:39:34] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -29.184288 | E_var:     0.0694 | E_err:   0.004115
[2025-10-06 23:39:38] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -29.190049 | E_var:     0.0887 | E_err:   0.004653
[2025-10-06 23:39:42] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -29.185042 | E_var:     0.0868 | E_err:   0.004603
[2025-10-06 23:39:45] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -29.193089 | E_var:     0.0834 | E_err:   0.004513
[2025-10-06 23:39:49] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -29.185895 | E_var:     0.0677 | E_err:   0.004066
[2025-10-06 23:39:53] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -29.187634 | E_var:     0.1235 | E_err:   0.005492
[2025-10-06 23:39:56] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -29.179751 | E_var:     0.1105 | E_err:   0.005194
[2025-10-06 23:40:00] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -29.186948 | E_var:     0.0662 | E_err:   0.004019
[2025-10-06 23:40:04] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -29.186943 | E_var:     0.0780 | E_err:   0.004363
[2025-10-06 23:40:08] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -29.185165 | E_var:     0.0873 | E_err:   0.004615
[2025-10-06 23:40:11] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -29.177816 | E_var:     0.2516 | E_err:   0.007838
[2025-10-06 23:40:15] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -29.191500 | E_var:     0.0865 | E_err:   0.004597
[2025-10-06 23:40:19] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -29.189221 | E_var:     0.0679 | E_err:   0.004071
[2025-10-06 23:40:22] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -29.191821 | E_var:     0.0759 | E_err:   0.004305
[2025-10-06 23:40:26] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -29.186375 | E_var:     0.0814 | E_err:   0.004458
[2025-10-06 23:40:30] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -29.183467 | E_var:     0.1027 | E_err:   0.005007
[2025-10-06 23:40:33] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -29.188355 | E_var:     0.0676 | E_err:   0.004062
[2025-10-06 23:40:37] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -29.191246 | E_var:     0.0663 | E_err:   0.004024
[2025-10-06 23:40:41] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -29.186188 | E_var:     0.0696 | E_err:   0.004121
[2025-10-06 23:40:44] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -29.187473 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 23:40:48] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -29.194741 | E_var:     0.0744 | E_err:   0.004263
[2025-10-06 23:40:52] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -29.188194 | E_var:     0.0695 | E_err:   0.004120
[2025-10-06 23:40:55] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -29.183194 | E_var:     0.0892 | E_err:   0.004667
[2025-10-06 23:40:59] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -29.187555 | E_var:     0.0735 | E_err:   0.004237
[2025-10-06 23:41:03] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -29.194231 | E_var:     0.0861 | E_err:   0.004584
[2025-10-06 23:41:06] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -29.188014 | E_var:     0.0730 | E_err:   0.004222
[2025-10-06 23:41:10] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -29.193543 | E_var:     0.0621 | E_err:   0.003892
[2025-10-06 23:41:14] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -29.181175 | E_var:     0.0853 | E_err:   0.004564
[2025-10-06 23:41:17] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -29.184450 | E_var:     0.0993 | E_err:   0.004925
[2025-10-06 23:41:21] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -29.188683 | E_var:     0.0796 | E_err:   0.004407
[2025-10-06 23:41:25] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -29.184225 | E_var:     0.0652 | E_err:   0.003988
[2025-10-06 23:41:28] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -29.179864 | E_var:     0.0754 | E_err:   0.004291
[2025-10-06 23:41:32] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -29.196425 | E_var:     0.0775 | E_err:   0.004350
[2025-10-06 23:41:36] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -29.186200 | E_var:     0.0792 | E_err:   0.004399
[2025-10-06 23:41:39] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -29.182004 | E_var:     0.1099 | E_err:   0.005180
[2025-10-06 23:41:43] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -29.185445 | E_var:     0.1481 | E_err:   0.006014
[2025-10-06 23:41:47] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -29.186727 | E_var:     0.0684 | E_err:   0.004088
[2025-10-06 23:41:50] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -29.185561 | E_var:     0.0896 | E_err:   0.004678
[2025-10-06 23:41:54] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -29.184621 | E_var:     0.0842 | E_err:   0.004533
[2025-10-06 23:41:58] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -29.183900 | E_var:     0.0793 | E_err:   0.004399
[2025-10-06 23:41:58] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 23:42:01] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -29.181562 | E_var:     0.0639 | E_err:   0.003950
[2025-10-06 23:42:05] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -29.187867 | E_var:     0.0724 | E_err:   0.004203
[2025-10-06 23:42:09] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -29.190850 | E_var:     0.3605 | E_err:   0.009382
[2025-10-06 23:42:12] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -29.189186 | E_var:     0.0727 | E_err:   0.004214
[2025-10-06 23:42:16] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -29.194340 | E_var:     0.1039 | E_err:   0.005038
[2025-10-06 23:42:20] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -29.198301 | E_var:     0.0947 | E_err:   0.004808
[2025-10-06 23:42:23] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -29.182181 | E_var:     0.0788 | E_err:   0.004387
[2025-10-06 23:42:27] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -29.189318 | E_var:     0.0898 | E_err:   0.004682
[2025-10-06 23:42:31] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -29.182005 | E_var:     0.0693 | E_err:   0.004113
[2025-10-06 23:42:34] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -29.184215 | E_var:     0.0847 | E_err:   0.004548
[2025-10-06 23:42:38] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -29.182815 | E_var:     0.0756 | E_err:   0.004297
[2025-10-06 23:42:42] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -29.188324 | E_var:     0.0787 | E_err:   0.004385
[2025-10-06 23:42:45] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -29.181800 | E_var:     0.4146 | E_err:   0.010061
[2025-10-06 23:42:49] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -29.192835 | E_var:     0.0577 | E_err:   0.003752
[2025-10-06 23:42:53] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -29.192214 | E_var:     0.1139 | E_err:   0.005274
[2025-10-06 23:42:56] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -29.179789 | E_var:     0.1315 | E_err:   0.005667
[2025-10-06 23:43:00] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -29.191006 | E_var:     0.0676 | E_err:   0.004061
[2025-10-06 23:43:03] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -29.187962 | E_var:     0.0693 | E_err:   0.004114
[2025-10-06 23:43:07] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -29.180316 | E_var:     0.0539 | E_err:   0.003628
[2025-10-06 23:43:11] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -29.192245 | E_var:     0.0857 | E_err:   0.004574
[2025-10-06 23:43:14] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -29.189755 | E_var:     0.0669 | E_err:   0.004043
[2025-10-06 23:43:18] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -29.185937 | E_var:     0.0893 | E_err:   0.004669
[2025-10-06 23:43:22] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -29.201799 | E_var:     0.0844 | E_err:   0.004540
[2025-10-06 23:43:25] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -29.184115 | E_var:     0.2006 | E_err:   0.006998
[2025-10-06 23:43:29] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -29.187324 | E_var:     0.1016 | E_err:   0.004981
[2025-10-06 23:43:33] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -29.186112 | E_var:     0.0633 | E_err:   0.003931
[2025-10-06 23:43:36] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -29.192013 | E_var:     0.1393 | E_err:   0.005833
[2025-10-06 23:43:40] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -29.188500 | E_var:     0.0663 | E_err:   0.004022
[2025-10-06 23:43:44] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -29.185334 | E_var:     0.0860 | E_err:   0.004582
[2025-10-06 23:43:47] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -29.194014 | E_var:     0.0930 | E_err:   0.004764
[2025-10-06 23:43:51] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -29.178226 | E_var:     0.0686 | E_err:   0.004092
[2025-10-06 23:43:55] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -29.187033 | E_var:     0.0809 | E_err:   0.004443
[2025-10-06 23:43:58] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -29.187020 | E_var:     0.0799 | E_err:   0.004415
[2025-10-06 23:44:02] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -29.196048 | E_var:     0.0918 | E_err:   0.004735
[2025-10-06 23:44:06] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -29.184912 | E_var:     0.0691 | E_err:   0.004109
[2025-10-06 23:44:09] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -29.187196 | E_var:     0.0691 | E_err:   0.004106
[2025-10-06 23:44:13] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -29.180283 | E_var:     0.0588 | E_err:   0.003789
[2025-10-06 23:44:17] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -29.184188 | E_var:     0.0857 | E_err:   0.004575
[2025-10-06 23:44:20] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -29.177737 | E_var:     0.0824 | E_err:   0.004484
[2025-10-06 23:44:24] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -29.189548 | E_var:     0.0739 | E_err:   0.004247
[2025-10-06 23:44:28] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -29.185480 | E_var:     0.0679 | E_err:   0.004073
[2025-10-06 23:44:31] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -29.187535 | E_var:     0.0676 | E_err:   0.004062
[2025-10-06 23:44:35] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -29.184516 | E_var:     0.0720 | E_err:   0.004192
[2025-10-06 23:44:39] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -29.195708 | E_var:     0.0578 | E_err:   0.003756
[2025-10-06 23:44:42] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -29.190125 | E_var:     0.0772 | E_err:   0.004341
[2025-10-06 23:44:46] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -29.187852 | E_var:     0.0686 | E_err:   0.004092
[2025-10-06 23:44:50] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -29.186160 | E_var:     0.0698 | E_err:   0.004127
[2025-10-06 23:44:53] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -29.188879 | E_var:     0.0654 | E_err:   0.003994
[2025-10-06 23:44:57] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -29.179009 | E_var:     0.1176 | E_err:   0.005358
[2025-10-06 23:45:01] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -29.188714 | E_var:     0.0858 | E_err:   0.004578
[2025-10-06 23:45:01] ======================================================================================================
[2025-10-06 23:45:01] ✅ Training completed successfully
[2025-10-06 23:45:01] Total restarts: 2
[2025-10-06 23:45:02] Final Energy: -29.18871399 ± 0.00457799
[2025-10-06 23:45:02] Final Variance: 0.085844
[2025-10-06 23:45:02] ======================================================================================================
[2025-10-06 23:45:02] ======================================================================================================
[2025-10-06 23:45:02] Training completed | Runtime: 3916.1s
[2025-10-06 23:45:03] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 23:45:03] ======================================================================================================
