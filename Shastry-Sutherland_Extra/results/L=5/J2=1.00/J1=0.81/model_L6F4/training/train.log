[2025-10-07 12:20:34] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.80/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 12:20:34]   - 迭代次数: final
[2025-10-07 12:20:34]   - 能量: -44.871662-0.000875j ± 0.009491, Var: 0.368960
[2025-10-07 12:20:34]   - 时间戳: 2025-10-07T12:20:11.300990+08:00
[2025-10-07 12:20:56] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 12:20:56] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 12:20:56] ======================================================================================================
[2025-10-07 12:20:56] GCNN for Shastry-Sutherland Model
[2025-10-07 12:20:56] ======================================================================================================
[2025-10-07 12:20:56] System parameters:
[2025-10-07 12:20:56]   - System size: L=5, N=100
[2025-10-07 12:20:56]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-10-07 12:20:56] ------------------------------------------------------------------------------------------------------
[2025-10-07 12:20:56] Model parameters:
[2025-10-07 12:20:56]   - Number of layers = 6
[2025-10-07 12:20:56]   - Number of features = 4
[2025-10-07 12:20:56]   - Total parameters = 32444
[2025-10-07 12:20:56] ------------------------------------------------------------------------------------------------------
[2025-10-07 12:20:56] Training parameters:
[2025-10-07 12:20:56]   - Total iterations: 1050
[2025-10-07 12:20:56]   - Annealing cycles: 3
[2025-10-07 12:20:56]   - Initial period: 150
[2025-10-07 12:20:56]   - Period multiplier: 2.0
[2025-10-07 12:20:56]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 12:20:56]   - Samples: 4096
[2025-10-07 12:20:56]   - Discarded samples: 0
[2025-10-07 12:20:56]   - Chunk size: 4096
[2025-10-07 12:20:56]   - Diagonal shift: 0.15
[2025-10-07 12:20:56]   - Gradient clipping: 1.0
[2025-10-07 12:20:56]   - Checkpoint enabled: interval=100
[2025-10-07 12:20:56]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.81/model_L6F4/training/checkpoints
[2025-10-07 12:20:56] ------------------------------------------------------------------------------------------------------
[2025-10-07 12:20:56] Device status:
[2025-10-07 12:20:56]   - Devices model: NVIDIA H200 NVL
[2025-10-07 12:20:56]   - Number of devices: 1
[2025-10-07 12:20:56]   - Sharding: True
[2025-10-07 12:20:57] ======================================================================================================
[2025-10-07 12:21:37] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -45.517370 | E_var:     0.6454 | E_err:   0.012553
[2025-10-07 12:22:04] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -45.512819 | E_var:     0.4026 | E_err:   0.009915
[2025-10-07 12:22:12] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -45.514744 | E_var:     0.2758 | E_err:   0.008206
[2025-10-07 12:22:20] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -45.523198 | E_var:     0.3502 | E_err:   0.009247
[2025-10-07 12:22:28] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -45.507620 | E_var:     0.2532 | E_err:   0.007862
[2025-10-07 12:22:35] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -45.521282 | E_var:     0.2946 | E_err:   0.008480
[2025-10-07 12:22:43] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -45.544691 | E_var:     0.3438 | E_err:   0.009162
[2025-10-07 12:22:51] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -45.516691 | E_var:     0.2490 | E_err:   0.007797
[2025-10-07 12:22:59] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -45.517007 | E_var:     0.2154 | E_err:   0.007252
[2025-10-07 12:23:07] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -45.512168 | E_var:     0.2878 | E_err:   0.008382
[2025-10-07 12:23:14] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -45.516726 | E_var:     0.4675 | E_err:   0.010684
[2025-10-07 12:23:22] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -45.528324 | E_var:     0.2055 | E_err:   0.007083
[2025-10-07 12:23:30] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -45.511085 | E_var:     0.2318 | E_err:   0.007523
[2025-10-07 12:23:38] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -45.527157 | E_var:     0.2127 | E_err:   0.007206
[2025-10-07 12:23:45] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -45.525616 | E_var:     0.2234 | E_err:   0.007385
[2025-10-07 12:23:53] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -45.515150 | E_var:     0.2241 | E_err:   0.007397
[2025-10-07 12:24:01] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -45.534066 | E_var:     0.2587 | E_err:   0.007947
[2025-10-07 12:24:09] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -45.534173 | E_var:     0.2178 | E_err:   0.007292
[2025-10-07 12:24:17] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -45.532968 | E_var:     0.2015 | E_err:   0.007014
[2025-10-07 12:24:24] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -45.510000 | E_var:     0.3747 | E_err:   0.009564
[2025-10-07 12:24:32] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -45.522559 | E_var:     0.2195 | E_err:   0.007321
[2025-10-07 12:24:40] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -45.505533 | E_var:     0.6193 | E_err:   0.012296
[2025-10-07 12:24:48] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -45.520524 | E_var:     0.2005 | E_err:   0.006996
[2025-10-07 12:24:56] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -45.506892 | E_var:     0.1996 | E_err:   0.006980
[2025-10-07 12:25:03] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -45.528885 | E_var:     0.2158 | E_err:   0.007259
[2025-10-07 12:25:11] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -45.525571 | E_var:     0.2247 | E_err:   0.007407
[2025-10-07 12:25:19] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -45.510457 | E_var:     0.1763 | E_err:   0.006561
[2025-10-07 12:25:27] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -45.515659 | E_var:     0.2260 | E_err:   0.007428
[2025-10-07 12:25:35] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -45.526109 | E_var:     0.2635 | E_err:   0.008021
[2025-10-07 12:25:42] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -45.513094 | E_var:     0.2018 | E_err:   0.007020
[2025-10-07 12:25:50] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -45.522975 | E_var:     0.1915 | E_err:   0.006837
[2025-10-07 12:25:58] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -45.525385 | E_var:     0.1781 | E_err:   0.006593
[2025-10-07 12:26:06] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -45.518474 | E_var:     0.1745 | E_err:   0.006527
[2025-10-07 12:26:13] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -45.529303 | E_var:     0.2007 | E_err:   0.007000
[2025-10-07 12:26:21] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -45.525436 | E_var:     0.3032 | E_err:   0.008604
[2025-10-07 12:26:29] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -45.533269 | E_var:     0.3330 | E_err:   0.009016
[2025-10-07 12:26:37] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -45.522163 | E_var:     0.2358 | E_err:   0.007587
[2025-10-07 12:26:45] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -45.520916 | E_var:     0.1946 | E_err:   0.006892
[2025-10-07 12:26:52] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -45.522421 | E_var:     0.2083 | E_err:   0.007131
[2025-10-07 12:27:00] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -45.517594 | E_var:     0.2139 | E_err:   0.007226
[2025-10-07 12:27:08] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -45.533417 | E_var:     0.2217 | E_err:   0.007356
[2025-10-07 12:27:16] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -45.528353 | E_var:     0.2660 | E_err:   0.008058
[2025-10-07 12:27:24] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -45.519333 | E_var:     0.2431 | E_err:   0.007703
[2025-10-07 12:27:31] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -45.516134 | E_var:     0.1965 | E_err:   0.006926
[2025-10-07 12:27:39] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -45.515982 | E_var:     0.1933 | E_err:   0.006869
[2025-10-07 12:27:47] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -45.521845 | E_var:     0.2405 | E_err:   0.007663
[2025-10-07 12:27:55] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -45.521280 | E_var:     0.2035 | E_err:   0.007049
[2025-10-07 12:28:02] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -45.518239 | E_var:     0.2596 | E_err:   0.007961
[2025-10-07 12:28:10] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -45.534874 | E_var:     0.2158 | E_err:   0.007258
[2025-10-07 12:28:18] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -45.536801 | E_var:     0.1621 | E_err:   0.006291
[2025-10-07 12:28:26] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -45.529757 | E_var:     0.2216 | E_err:   0.007356
[2025-10-07 12:28:34] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -45.527924 | E_var:     0.1535 | E_err:   0.006122
[2025-10-07 12:28:41] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -45.527725 | E_var:     0.2331 | E_err:   0.007545
[2025-10-07 12:28:49] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -45.521603 | E_var:     0.2557 | E_err:   0.007901
[2025-10-07 12:28:57] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -45.517122 | E_var:     0.2106 | E_err:   0.007170
[2025-10-07 12:29:05] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -45.532620 | E_var:     0.2017 | E_err:   0.007017
[2025-10-07 12:29:13] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -45.519615 | E_var:     0.2275 | E_err:   0.007452
[2025-10-07 12:29:20] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -45.533609 | E_var:     0.1888 | E_err:   0.006790
[2025-10-07 12:29:39] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -45.512144 | E_var:     0.2127 | E_err:   0.007206
[2025-10-07 12:29:46] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -45.535177 | E_var:     0.2241 | E_err:   0.007396
[2025-10-07 12:29:54] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -45.506929 | E_var:     0.2095 | E_err:   0.007152
[2025-10-07 12:30:02] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -45.507059 | E_var:     0.2063 | E_err:   0.007098
[2025-10-07 12:30:10] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -45.519847 | E_var:     0.2098 | E_err:   0.007157
[2025-10-07 12:30:18] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -45.522729 | E_var:     0.2192 | E_err:   0.007315
[2025-10-07 12:30:25] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -45.543741 | E_var:     0.3543 | E_err:   0.009300
[2025-10-07 12:30:33] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -45.505158 | E_var:     0.1952 | E_err:   0.006903
[2025-10-07 12:30:41] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -45.520486 | E_var:     0.2234 | E_err:   0.007385
[2025-10-07 12:30:49] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -45.532047 | E_var:     0.1964 | E_err:   0.006924
[2025-10-07 12:30:56] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -45.517585 | E_var:     0.1852 | E_err:   0.006723
[2025-10-07 12:31:04] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -45.520131 | E_var:     0.2252 | E_err:   0.007415
[2025-10-07 12:31:12] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -45.525467 | E_var:     0.2305 | E_err:   0.007502
[2025-10-07 12:31:20] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -45.540054 | E_var:     0.2281 | E_err:   0.007462
[2025-10-07 12:31:28] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -45.512970 | E_var:     0.2776 | E_err:   0.008233
[2025-10-07 12:31:35] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -45.509133 | E_var:     0.3330 | E_err:   0.009017
[2025-10-07 12:31:43] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -45.518553 | E_var:     0.6009 | E_err:   0.012113
[2025-10-07 12:31:51] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -45.541547 | E_var:     0.3995 | E_err:   0.009876
[2025-10-07 12:31:59] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -45.522576 | E_var:     0.1760 | E_err:   0.006554
[2025-10-07 12:32:07] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -45.520601 | E_var:     0.2330 | E_err:   0.007542
[2025-10-07 12:32:14] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -45.511290 | E_var:     0.1870 | E_err:   0.006757
[2025-10-07 12:32:22] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -45.522736 | E_var:     0.2477 | E_err:   0.007776
[2025-10-07 12:32:30] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -45.524593 | E_var:     0.2247 | E_err:   0.007407
[2025-10-07 12:32:38] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -45.523002 | E_var:     0.2669 | E_err:   0.008073
[2025-10-07 12:32:45] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -45.531029 | E_var:     0.1919 | E_err:   0.006844
[2025-10-07 12:32:53] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -45.524383 | E_var:     0.1721 | E_err:   0.006482
[2025-10-07 12:33:01] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -45.506663 | E_var:     0.2466 | E_err:   0.007759
[2025-10-07 12:33:09] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -45.530420 | E_var:     0.2219 | E_err:   0.007360
[2025-10-07 12:33:17] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -45.530181 | E_var:     0.2380 | E_err:   0.007622
[2025-10-07 12:33:24] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -45.536518 | E_var:     0.1677 | E_err:   0.006399
[2025-10-07 12:33:32] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -45.517108 | E_var:     0.2017 | E_err:   0.007017
[2025-10-07 12:33:40] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -45.525007 | E_var:     0.2121 | E_err:   0.007195
[2025-10-07 12:33:48] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -45.520139 | E_var:     0.2058 | E_err:   0.007089
[2025-10-07 12:33:56] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -45.520086 | E_var:     0.2267 | E_err:   0.007439
[2025-10-07 12:34:03] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -45.508490 | E_var:     0.2625 | E_err:   0.008006
[2025-10-07 12:34:11] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -45.527306 | E_var:     0.1708 | E_err:   0.006458
[2025-10-07 12:34:19] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -45.513794 | E_var:     0.2163 | E_err:   0.007267
[2025-10-07 12:34:27] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -45.529103 | E_var:     0.2214 | E_err:   0.007352
[2025-10-07 12:34:34] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -45.498508 | E_var:     0.6852 | E_err:   0.012934
[2025-10-07 12:34:42] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -45.510587 | E_var:     0.2309 | E_err:   0.007508
[2025-10-07 12:34:50] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -45.531763 | E_var:     0.1776 | E_err:   0.006586
[2025-10-07 12:34:58] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -45.522455 | E_var:     0.2546 | E_err:   0.007884
[2025-10-07 12:34:58] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 12:35:06] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -45.527236 | E_var:     0.1997 | E_err:   0.006983
[2025-10-07 12:35:13] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -45.533234 | E_var:     0.1756 | E_err:   0.006547
[2025-10-07 12:35:21] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -45.520605 | E_var:     0.2159 | E_err:   0.007260
[2025-10-07 12:35:29] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -45.512661 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 12:35:37] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -45.521419 | E_var:     0.2502 | E_err:   0.007816
[2025-10-07 12:35:45] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -45.512417 | E_var:     0.2168 | E_err:   0.007276
[2025-10-07 12:35:52] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -45.509780 | E_var:     0.2556 | E_err:   0.007900
[2025-10-07 12:36:00] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -45.530612 | E_var:     0.2084 | E_err:   0.007133
[2025-10-07 12:36:08] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -45.516347 | E_var:     0.4059 | E_err:   0.009955
[2025-10-07 12:36:16] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -45.535262 | E_var:     0.1818 | E_err:   0.006662
[2025-10-07 12:36:24] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -45.528314 | E_var:     0.1949 | E_err:   0.006897
[2025-10-07 12:36:31] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -45.528334 | E_var:     0.2113 | E_err:   0.007182
[2025-10-07 12:36:39] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -45.525990 | E_var:     0.2919 | E_err:   0.008442
[2025-10-07 12:36:47] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -45.515104 | E_var:     0.1662 | E_err:   0.006369
[2025-10-07 12:36:55] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -45.531001 | E_var:     0.2128 | E_err:   0.007208
[2025-10-07 12:37:02] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -45.514689 | E_var:     0.2083 | E_err:   0.007132
[2025-10-07 12:37:10] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -45.516942 | E_var:     0.1877 | E_err:   0.006770
[2025-10-07 12:37:18] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -45.518554 | E_var:     0.2075 | E_err:   0.007118
[2025-10-07 12:37:26] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -45.521437 | E_var:     0.2813 | E_err:   0.008287
[2025-10-07 12:37:34] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -45.531207 | E_var:     0.1700 | E_err:   0.006442
[2025-10-07 12:37:41] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -45.522591 | E_var:     0.2728 | E_err:   0.008160
[2025-10-07 12:37:49] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -45.535511 | E_var:     0.2482 | E_err:   0.007784
[2025-10-07 12:37:57] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -45.521174 | E_var:     0.2731 | E_err:   0.008166
[2025-10-07 12:38:05] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -45.515471 | E_var:     0.1844 | E_err:   0.006709
[2025-10-07 12:38:12] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -45.516858 | E_var:     0.2536 | E_err:   0.007869
[2025-10-07 12:38:20] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -45.522610 | E_var:     0.1680 | E_err:   0.006405
[2025-10-07 12:38:28] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -45.524900 | E_var:     0.2407 | E_err:   0.007666
[2025-10-07 12:38:36] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -45.521829 | E_var:     0.2301 | E_err:   0.007495
[2025-10-07 12:38:44] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -45.520690 | E_var:     0.1676 | E_err:   0.006397
[2025-10-07 12:38:51] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -45.523640 | E_var:     0.2783 | E_err:   0.008243
[2025-10-07 12:38:59] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -45.535598 | E_var:     0.2714 | E_err:   0.008140
[2025-10-07 12:39:07] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -45.505230 | E_var:     0.2033 | E_err:   0.007044
[2025-10-07 12:39:15] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -45.529691 | E_var:     0.2161 | E_err:   0.007263
[2025-10-07 12:39:22] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -45.519289 | E_var:     0.1773 | E_err:   0.006580
[2025-10-07 12:39:30] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -45.518474 | E_var:     0.2122 | E_err:   0.007197
[2025-10-07 12:39:38] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -45.515588 | E_var:     0.3461 | E_err:   0.009192
[2025-10-07 12:39:46] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -45.526162 | E_var:     0.2990 | E_err:   0.008544
[2025-10-07 12:39:54] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -45.526715 | E_var:     0.2045 | E_err:   0.007065
[2025-10-07 12:40:01] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -45.530590 | E_var:     0.1693 | E_err:   0.006429
[2025-10-07 12:40:09] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -45.525213 | E_var:     0.1790 | E_err:   0.006612
[2025-10-07 12:40:17] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -45.528852 | E_var:     0.1934 | E_err:   0.006872
[2025-10-07 12:40:25] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -45.522497 | E_var:     0.2222 | E_err:   0.007365
[2025-10-07 12:40:33] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -45.544852 | E_var:     0.2957 | E_err:   0.008497
[2025-10-07 12:40:40] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -45.528227 | E_var:     0.1997 | E_err:   0.006982
[2025-10-07 12:40:48] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -45.516643 | E_var:     0.2425 | E_err:   0.007695
[2025-10-07 12:40:56] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -45.518668 | E_var:     0.2050 | E_err:   0.007074
[2025-10-07 12:41:04] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -45.517414 | E_var:     0.2143 | E_err:   0.007233
[2025-10-07 12:41:11] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -45.525776 | E_var:     0.1921 | E_err:   0.006848
[2025-10-07 12:41:19] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -45.527112 | E_var:     0.1680 | E_err:   0.006404
[2025-10-07 12:41:27] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -45.525871 | E_var:     0.2207 | E_err:   0.007340
[2025-10-07 12:41:27] 🔄 RESTART #1 | Period: 300
[2025-10-07 12:41:35] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -45.527376 | E_var:     0.2136 | E_err:   0.007221
[2025-10-07 12:41:43] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -45.515740 | E_var:     0.2417 | E_err:   0.007681
[2025-10-07 12:41:50] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -45.516499 | E_var:     0.2086 | E_err:   0.007136
[2025-10-07 12:41:58] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -45.517340 | E_var:     0.3357 | E_err:   0.009053
[2025-10-07 12:42:06] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -45.520594 | E_var:     0.1675 | E_err:   0.006395
[2025-10-07 12:42:14] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -45.534663 | E_var:     0.2455 | E_err:   0.007741
[2025-10-07 12:42:21] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -45.521383 | E_var:     0.1897 | E_err:   0.006805
[2025-10-07 12:42:29] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -45.521643 | E_var:     0.2446 | E_err:   0.007728
[2025-10-07 12:42:37] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -45.516468 | E_var:     0.1947 | E_err:   0.006894
[2025-10-07 12:42:45] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -45.525255 | E_var:     0.2727 | E_err:   0.008159
[2025-10-07 12:42:53] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -45.532016 | E_var:     0.1894 | E_err:   0.006800
[2025-10-07 12:43:00] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -45.528734 | E_var:     0.2585 | E_err:   0.007944
[2025-10-07 12:43:08] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -45.523240 | E_var:     0.2259 | E_err:   0.007426
[2025-10-07 12:43:16] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -45.535180 | E_var:     0.2117 | E_err:   0.007190
[2025-10-07 12:43:24] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -45.535327 | E_var:     0.2513 | E_err:   0.007834
[2025-10-07 12:43:32] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -45.525410 | E_var:     0.3112 | E_err:   0.008717
[2025-10-07 12:43:39] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -45.521062 | E_var:     0.2400 | E_err:   0.007655
[2025-10-07 12:43:47] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -45.519693 | E_var:     0.2163 | E_err:   0.007267
[2025-10-07 12:43:55] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -45.536356 | E_var:     0.1847 | E_err:   0.006715
[2025-10-07 12:44:03] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -45.521941 | E_var:     0.2013 | E_err:   0.007011
[2025-10-07 12:44:10] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -45.519955 | E_var:     0.2075 | E_err:   0.007117
[2025-10-07 12:44:18] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -45.514614 | E_var:     0.2041 | E_err:   0.007059
[2025-10-07 12:44:26] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -45.529741 | E_var:     0.2175 | E_err:   0.007288
[2025-10-07 12:44:34] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -45.511123 | E_var:     0.2076 | E_err:   0.007119
[2025-10-07 12:44:42] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -45.525127 | E_var:     0.1736 | E_err:   0.006509
[2025-10-07 12:44:49] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -45.507403 | E_var:     0.3456 | E_err:   0.009186
[2025-10-07 12:44:57] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -45.527641 | E_var:     0.2854 | E_err:   0.008347
[2025-10-07 12:45:05] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -45.531721 | E_var:     0.2197 | E_err:   0.007325
[2025-10-07 12:45:13] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -45.521845 | E_var:     0.3745 | E_err:   0.009561
[2025-10-07 12:45:20] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -45.514732 | E_var:     0.1751 | E_err:   0.006539
[2025-10-07 12:45:28] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -45.511283 | E_var:     0.2298 | E_err:   0.007490
[2025-10-07 12:45:36] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -45.525305 | E_var:     0.1768 | E_err:   0.006570
[2025-10-07 12:45:44] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -45.528486 | E_var:     0.2069 | E_err:   0.007108
[2025-10-07 12:45:52] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -45.532970 | E_var:     0.3552 | E_err:   0.009313
[2025-10-07 12:45:59] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -45.529390 | E_var:     0.2139 | E_err:   0.007227
[2025-10-07 12:46:07] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -45.516150 | E_var:     0.1876 | E_err:   0.006769
[2025-10-07 12:46:15] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -45.532934 | E_var:     0.2272 | E_err:   0.007447
[2025-10-07 12:46:23] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -45.515746 | E_var:     0.1994 | E_err:   0.006978
[2025-10-07 12:46:30] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -45.525006 | E_var:     0.1904 | E_err:   0.006818
[2025-10-07 12:46:38] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -45.510058 | E_var:     0.2358 | E_err:   0.007588
[2025-10-07 12:46:46] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -45.524653 | E_var:     0.2057 | E_err:   0.007086
[2025-10-07 12:46:54] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -45.519146 | E_var:     0.1992 | E_err:   0.006974
[2025-10-07 12:47:02] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -45.526583 | E_var:     0.2025 | E_err:   0.007031
[2025-10-07 12:47:09] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -45.527789 | E_var:     0.1931 | E_err:   0.006867
[2025-10-07 12:47:17] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -45.534105 | E_var:     0.2137 | E_err:   0.007223
[2025-10-07 12:47:25] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -45.533956 | E_var:     0.2132 | E_err:   0.007215
[2025-10-07 12:47:33] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -45.522670 | E_var:     0.1855 | E_err:   0.006729
[2025-10-07 12:47:41] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -45.513437 | E_var:     0.1969 | E_err:   0.006934
[2025-10-07 12:47:48] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -45.524062 | E_var:     0.2006 | E_err:   0.006998
[2025-10-07 12:47:56] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -45.525725 | E_var:     0.2197 | E_err:   0.007324
[2025-10-07 12:47:56] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 12:48:04] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -45.524510 | E_var:     0.2238 | E_err:   0.007391
[2025-10-07 12:48:12] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -45.524088 | E_var:     0.2294 | E_err:   0.007483
[2025-10-07 12:48:20] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -45.521681 | E_var:     0.2219 | E_err:   0.007360
[2025-10-07 12:48:27] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -45.514491 | E_var:     0.2253 | E_err:   0.007416
[2025-10-07 12:48:35] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -45.522633 | E_var:     0.2808 | E_err:   0.008280
[2025-10-07 12:48:43] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -45.518436 | E_var:     0.2384 | E_err:   0.007629
[2025-10-07 12:48:51] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -45.518362 | E_var:     0.1892 | E_err:   0.006796
[2025-10-07 12:48:58] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -45.525421 | E_var:     0.1890 | E_err:   0.006793
[2025-10-07 12:49:06] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -45.520684 | E_var:     0.1867 | E_err:   0.006752
[2025-10-07 12:49:14] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -45.524406 | E_var:     0.1993 | E_err:   0.006976
[2025-10-07 12:49:22] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -45.526132 | E_var:     0.2051 | E_err:   0.007076
[2025-10-07 12:49:30] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -45.515660 | E_var:     0.1784 | E_err:   0.006600
[2025-10-07 12:49:37] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -45.527972 | E_var:     0.2512 | E_err:   0.007832
[2025-10-07 12:49:45] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -45.525051 | E_var:     0.2614 | E_err:   0.007989
[2025-10-07 12:49:53] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -45.519709 | E_var:     0.3494 | E_err:   0.009236
[2025-10-07 12:50:01] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -45.533792 | E_var:     0.1816 | E_err:   0.006658
[2025-10-07 12:50:08] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -45.517699 | E_var:     0.2683 | E_err:   0.008094
[2025-10-07 12:50:16] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -45.529674 | E_var:     0.1837 | E_err:   0.006697
[2025-10-07 12:50:24] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -45.523846 | E_var:     0.2856 | E_err:   0.008350
[2025-10-07 12:50:32] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -45.535956 | E_var:     0.1966 | E_err:   0.006928
[2025-10-07 12:50:40] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -45.519040 | E_var:     0.2966 | E_err:   0.008509
[2025-10-07 12:50:47] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -45.513044 | E_var:     0.2054 | E_err:   0.007082
[2025-10-07 12:50:55] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -45.499964 | E_var:     0.2162 | E_err:   0.007266
[2025-10-07 12:51:03] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -45.518210 | E_var:     0.2109 | E_err:   0.007175
[2025-10-07 12:51:11] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -45.526209 | E_var:     0.2414 | E_err:   0.007677
[2025-10-07 12:51:19] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -45.531955 | E_var:     0.2116 | E_err:   0.007188
[2025-10-07 12:51:26] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -45.517683 | E_var:     0.1926 | E_err:   0.006857
[2025-10-07 12:51:34] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -45.513956 | E_var:     0.3144 | E_err:   0.008761
[2025-10-07 12:51:42] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -45.521806 | E_var:     0.2031 | E_err:   0.007042
[2025-10-07 12:51:50] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -45.514108 | E_var:     0.2151 | E_err:   0.007247
[2025-10-07 12:51:57] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -45.539988 | E_var:     0.2667 | E_err:   0.008069
[2025-10-07 12:52:05] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -45.532251 | E_var:     0.2342 | E_err:   0.007562
[2025-10-07 12:52:13] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -45.527612 | E_var:     0.1994 | E_err:   0.006977
[2025-10-07 12:52:21] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -45.515839 | E_var:     0.1480 | E_err:   0.006011
[2025-10-07 12:52:29] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -45.522921 | E_var:     0.1860 | E_err:   0.006738
[2025-10-07 12:52:36] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -45.526644 | E_var:     0.1958 | E_err:   0.006913
[2025-10-07 12:52:44] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -45.520762 | E_var:     0.2176 | E_err:   0.007289
[2025-10-07 12:52:52] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -45.521580 | E_var:     0.2442 | E_err:   0.007722
[2025-10-07 12:53:00] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -45.510439 | E_var:     0.2077 | E_err:   0.007122
[2025-10-07 12:53:07] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -45.506943 | E_var:     0.3144 | E_err:   0.008761
[2025-10-07 12:53:15] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -45.526438 | E_var:     0.1754 | E_err:   0.006544
[2025-10-07 12:53:23] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -45.518653 | E_var:     0.1869 | E_err:   0.006755
[2025-10-07 12:53:31] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -45.523072 | E_var:     0.2207 | E_err:   0.007341
[2025-10-07 12:53:39] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -45.513997 | E_var:     0.3480 | E_err:   0.009218
[2025-10-07 12:53:46] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -45.521677 | E_var:     0.2115 | E_err:   0.007185
[2025-10-07 12:53:54] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -45.530837 | E_var:     0.2043 | E_err:   0.007062
[2025-10-07 12:54:02] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -45.512157 | E_var:     0.2098 | E_err:   0.007158
[2025-10-07 12:54:10] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -45.519993 | E_var:     0.2146 | E_err:   0.007239
[2025-10-07 12:54:18] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -45.532101 | E_var:     0.1884 | E_err:   0.006783
[2025-10-07 12:54:25] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -45.515410 | E_var:     0.2256 | E_err:   0.007422
[2025-10-07 12:54:33] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -45.522282 | E_var:     0.2331 | E_err:   0.007544
[2025-10-07 12:54:41] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -45.518239 | E_var:     0.2812 | E_err:   0.008286
[2025-10-07 12:54:49] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -45.519578 | E_var:     0.1788 | E_err:   0.006607
[2025-10-07 12:54:56] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -45.518624 | E_var:     0.1971 | E_err:   0.006937
[2025-10-07 12:55:04] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -45.521451 | E_var:     0.2046 | E_err:   0.007067
[2025-10-07 12:55:12] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -45.531623 | E_var:     0.2205 | E_err:   0.007336
[2025-10-07 12:55:20] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -45.526370 | E_var:     0.1892 | E_err:   0.006796
[2025-10-07 12:55:28] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -45.527167 | E_var:     0.1553 | E_err:   0.006157
[2025-10-07 12:55:35] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -45.520652 | E_var:     0.1708 | E_err:   0.006457
[2025-10-07 12:55:43] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -45.532817 | E_var:     0.1924 | E_err:   0.006854
[2025-10-07 12:55:51] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -45.523498 | E_var:     0.3014 | E_err:   0.008579
[2025-10-07 12:55:59] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -45.531381 | E_var:     0.2682 | E_err:   0.008092
[2025-10-07 12:56:06] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -45.529929 | E_var:     0.2049 | E_err:   0.007073
[2025-10-07 12:56:14] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -45.518127 | E_var:     0.2287 | E_err:   0.007472
[2025-10-07 12:56:22] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -45.506854 | E_var:     0.2286 | E_err:   0.007471
[2025-10-07 12:56:30] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -45.519037 | E_var:     0.2147 | E_err:   0.007239
[2025-10-07 12:56:38] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -45.522489 | E_var:     0.1802 | E_err:   0.006633
[2025-10-07 12:56:45] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -45.519375 | E_var:     0.1675 | E_err:   0.006395
[2025-10-07 12:56:53] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -45.523530 | E_var:     0.2252 | E_err:   0.007415
[2025-10-07 12:57:01] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -45.525515 | E_var:     0.2208 | E_err:   0.007342
[2025-10-07 12:57:09] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -45.521867 | E_var:     0.2347 | E_err:   0.007569
[2025-10-07 12:57:17] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -45.519361 | E_var:     0.1863 | E_err:   0.006744
[2025-10-07 12:57:24] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -45.517329 | E_var:     0.1742 | E_err:   0.006521
[2025-10-07 12:57:32] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -45.528167 | E_var:     0.2409 | E_err:   0.007668
[2025-10-07 12:57:40] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -45.518457 | E_var:     0.2241 | E_err:   0.007397
[2025-10-07 12:57:48] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -45.524293 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 12:57:55] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -45.508518 | E_var:     0.2323 | E_err:   0.007531
[2025-10-07 12:58:03] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -45.511556 | E_var:     0.1907 | E_err:   0.006824
[2025-10-07 12:58:11] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -45.514671 | E_var:     0.2243 | E_err:   0.007401
[2025-10-07 12:58:19] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -45.517949 | E_var:     0.2712 | E_err:   0.008136
[2025-10-07 12:58:27] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -45.525806 | E_var:     0.2564 | E_err:   0.007911
[2025-10-07 12:58:34] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -45.527989 | E_var:     0.1961 | E_err:   0.006919
[2025-10-07 12:58:42] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -45.522983 | E_var:     0.2629 | E_err:   0.008012
[2025-10-07 12:58:50] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -45.514651 | E_var:     0.2058 | E_err:   0.007088
[2025-10-07 12:58:58] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -45.522544 | E_var:     0.2971 | E_err:   0.008517
[2025-10-07 12:59:05] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -45.519102 | E_var:     0.2048 | E_err:   0.007071
[2025-10-07 12:59:13] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -45.522290 | E_var:     0.1621 | E_err:   0.006291
[2025-10-07 12:59:21] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -45.519508 | E_var:     0.1937 | E_err:   0.006877
[2025-10-07 12:59:29] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -45.521392 | E_var:     0.1828 | E_err:   0.006680
[2025-10-07 12:59:37] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -45.531326 | E_var:     0.2270 | E_err:   0.007445
[2025-10-07 12:59:44] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -45.525187 | E_var:     0.2682 | E_err:   0.008092
[2025-10-07 12:59:52] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -45.519957 | E_var:     0.1998 | E_err:   0.006984
[2025-10-07 13:00:00] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -45.537706 | E_var:     0.1819 | E_err:   0.006665
[2025-10-07 13:00:08] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -45.515681 | E_var:     0.1952 | E_err:   0.006903
[2025-10-07 13:00:15] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -45.522487 | E_var:     0.2162 | E_err:   0.007265
[2025-10-07 13:00:23] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -45.515981 | E_var:     0.2093 | E_err:   0.007149
[2025-10-07 13:00:31] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -45.522129 | E_var:     0.2459 | E_err:   0.007749
[2025-10-07 13:00:39] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -45.513184 | E_var:     0.1591 | E_err:   0.006232
[2025-10-07 13:00:47] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -45.519837 | E_var:     0.2084 | E_err:   0.007132
[2025-10-07 13:00:54] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -45.514286 | E_var:     0.1850 | E_err:   0.006721
[2025-10-07 13:00:55] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 13:01:02] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -45.514365 | E_var:     0.2502 | E_err:   0.007816
[2025-10-07 13:01:10] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -45.510467 | E_var:     0.2972 | E_err:   0.008518
[2025-10-07 13:01:18] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -45.521927 | E_var:     0.1923 | E_err:   0.006853
[2025-10-07 13:01:26] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -45.524704 | E_var:     0.2591 | E_err:   0.007953
[2025-10-07 13:01:33] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -45.528371 | E_var:     0.2251 | E_err:   0.007412
[2025-10-07 13:01:41] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -45.517149 | E_var:     0.4384 | E_err:   0.010346
[2025-10-07 13:01:49] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -45.515704 | E_var:     0.2121 | E_err:   0.007196
[2025-10-07 13:01:57] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -45.528087 | E_var:     0.2331 | E_err:   0.007544
[2025-10-07 13:02:05] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -45.505892 | E_var:     0.4628 | E_err:   0.010630
[2025-10-07 13:02:12] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -45.532964 | E_var:     0.1825 | E_err:   0.006676
[2025-10-07 13:02:20] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -45.522639 | E_var:     0.2607 | E_err:   0.007979
[2025-10-07 13:02:28] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -45.534950 | E_var:     0.1745 | E_err:   0.006528
[2025-10-07 13:02:36] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -45.523733 | E_var:     1.3629 | E_err:   0.018241
[2025-10-07 13:02:43] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -45.523835 | E_var:     0.1812 | E_err:   0.006651
[2025-10-07 13:02:51] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -45.531183 | E_var:     0.2211 | E_err:   0.007348
[2025-10-07 13:02:59] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -45.517331 | E_var:     0.3491 | E_err:   0.009232
[2025-10-07 13:03:07] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -45.524567 | E_var:     0.1831 | E_err:   0.006686
[2025-10-07 13:03:15] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -45.521465 | E_var:     0.2142 | E_err:   0.007231
[2025-10-07 13:03:22] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -45.533056 | E_var:     0.2192 | E_err:   0.007316
[2025-10-07 13:03:30] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -45.525426 | E_var:     0.2153 | E_err:   0.007250
[2025-10-07 13:03:38] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -45.526313 | E_var:     0.1598 | E_err:   0.006246
[2025-10-07 13:03:46] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -45.514058 | E_var:     0.2274 | E_err:   0.007450
[2025-10-07 13:03:54] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -45.526595 | E_var:     0.2359 | E_err:   0.007589
[2025-10-07 13:04:01] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -45.521277 | E_var:     0.2099 | E_err:   0.007158
[2025-10-07 13:04:09] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -45.525650 | E_var:     0.2351 | E_err:   0.007576
[2025-10-07 13:04:17] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -45.524165 | E_var:     0.2591 | E_err:   0.007953
[2025-10-07 13:04:25] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -45.511037 | E_var:     0.2282 | E_err:   0.007464
[2025-10-07 13:04:32] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -45.531012 | E_var:     0.2535 | E_err:   0.007867
[2025-10-07 13:04:40] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -45.516894 | E_var:     0.2337 | E_err:   0.007553
[2025-10-07 13:04:48] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -45.529253 | E_var:     0.2458 | E_err:   0.007746
[2025-10-07 13:04:56] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -45.511983 | E_var:     0.1837 | E_err:   0.006696
[2025-10-07 13:05:04] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -45.529975 | E_var:     0.2232 | E_err:   0.007383
[2025-10-07 13:05:11] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -45.522531 | E_var:     0.1966 | E_err:   0.006928
[2025-10-07 13:05:19] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -45.524383 | E_var:     0.2669 | E_err:   0.008072
[2025-10-07 13:05:27] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -45.526051 | E_var:     0.1924 | E_err:   0.006853
[2025-10-07 13:05:35] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -45.524100 | E_var:     0.2349 | E_err:   0.007572
[2025-10-07 13:05:42] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -45.517331 | E_var:     0.1472 | E_err:   0.005995
[2025-10-07 13:05:50] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -45.523271 | E_var:     0.1806 | E_err:   0.006640
[2025-10-07 13:05:58] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -45.523059 | E_var:     0.1872 | E_err:   0.006761
[2025-10-07 13:06:06] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -45.516483 | E_var:     0.1921 | E_err:   0.006849
[2025-10-07 13:06:14] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -45.519757 | E_var:     0.2897 | E_err:   0.008410
[2025-10-07 13:06:21] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -45.532100 | E_var:     0.2308 | E_err:   0.007506
[2025-10-07 13:06:29] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -45.514134 | E_var:     0.1956 | E_err:   0.006910
[2025-10-07 13:06:37] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -45.516698 | E_var:     0.2275 | E_err:   0.007452
[2025-10-07 13:06:45] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -45.518831 | E_var:     0.1637 | E_err:   0.006322
[2025-10-07 13:06:53] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -45.509711 | E_var:     0.1863 | E_err:   0.006744
[2025-10-07 13:07:00] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -45.513774 | E_var:     0.5761 | E_err:   0.011860
[2025-10-07 13:07:08] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -45.523027 | E_var:     0.1898 | E_err:   0.006807
[2025-10-07 13:07:16] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -45.514323 | E_var:     0.2079 | E_err:   0.007124
[2025-10-07 13:07:24] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -45.527883 | E_var:     0.1600 | E_err:   0.006249
[2025-10-07 13:07:31] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -45.522946 | E_var:     0.1854 | E_err:   0.006728
[2025-10-07 13:07:39] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -45.512413 | E_var:     0.1944 | E_err:   0.006889
[2025-10-07 13:07:47] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -45.522472 | E_var:     0.1930 | E_err:   0.006865
[2025-10-07 13:07:55] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -45.529816 | E_var:     0.1693 | E_err:   0.006429
[2025-10-07 13:08:03] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -45.523548 | E_var:     0.3065 | E_err:   0.008651
[2025-10-07 13:08:10] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -45.503277 | E_var:     0.1979 | E_err:   0.006951
[2025-10-07 13:08:18] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -45.511297 | E_var:     0.2650 | E_err:   0.008044
[2025-10-07 13:08:26] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -45.509601 | E_var:     0.2454 | E_err:   0.007741
[2025-10-07 13:08:34] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -45.529443 | E_var:     0.2067 | E_err:   0.007103
[2025-10-07 13:08:42] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -45.528267 | E_var:     0.2247 | E_err:   0.007407
[2025-10-07 13:08:49] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -45.518440 | E_var:     0.1923 | E_err:   0.006852
[2025-10-07 13:08:57] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -45.517010 | E_var:     0.2421 | E_err:   0.007688
[2025-10-07 13:09:05] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -45.519262 | E_var:     0.2225 | E_err:   0.007370
[2025-10-07 13:09:13] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -45.530024 | E_var:     0.2078 | E_err:   0.007123
[2025-10-07 13:09:20] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -45.518968 | E_var:     0.2314 | E_err:   0.007517
[2025-10-07 13:09:28] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -45.522330 | E_var:     0.1971 | E_err:   0.006937
[2025-10-07 13:09:36] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -45.512935 | E_var:     0.2229 | E_err:   0.007377
[2025-10-07 13:09:44] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -45.510164 | E_var:     0.2031 | E_err:   0.007041
[2025-10-07 13:09:52] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -45.530099 | E_var:     0.1982 | E_err:   0.006956
[2025-10-07 13:09:59] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -45.519174 | E_var:     0.2487 | E_err:   0.007792
[2025-10-07 13:10:07] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -45.514576 | E_var:     0.1831 | E_err:   0.006685
[2025-10-07 13:10:15] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -45.536056 | E_var:     0.1851 | E_err:   0.006723
[2025-10-07 13:10:23] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -45.507586 | E_var:     0.2258 | E_err:   0.007425
[2025-10-07 13:10:30] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -45.526264 | E_var:     0.1718 | E_err:   0.006477
[2025-10-07 13:10:38] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -45.526737 | E_var:     0.1908 | E_err:   0.006825
[2025-10-07 13:10:46] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -45.511283 | E_var:     0.3048 | E_err:   0.008626
[2025-10-07 13:10:54] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -45.523814 | E_var:     0.2146 | E_err:   0.007238
[2025-10-07 13:11:02] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -45.521598 | E_var:     0.2905 | E_err:   0.008421
[2025-10-07 13:11:09] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -45.529213 | E_var:     0.1999 | E_err:   0.006986
[2025-10-07 13:11:17] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -45.513733 | E_var:     0.1911 | E_err:   0.006831
[2025-10-07 13:11:25] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -45.532000 | E_var:     0.3166 | E_err:   0.008792
[2025-10-07 13:11:33] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -45.522802 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 13:11:41] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -45.523738 | E_var:     0.2738 | E_err:   0.008175
[2025-10-07 13:11:48] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -45.522079 | E_var:     0.2094 | E_err:   0.007149
[2025-10-07 13:11:56] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -45.526752 | E_var:     0.1515 | E_err:   0.006081
[2025-10-07 13:12:04] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -45.522284 | E_var:     0.2308 | E_err:   0.007506
[2025-10-07 13:12:12] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -45.517005 | E_var:     0.2456 | E_err:   0.007743
[2025-10-07 13:12:19] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -45.524875 | E_var:     0.2037 | E_err:   0.007052
[2025-10-07 13:12:27] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -45.521094 | E_var:     0.1679 | E_err:   0.006403
[2025-10-07 13:12:35] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -45.525386 | E_var:     0.2046 | E_err:   0.007067
[2025-10-07 13:12:43] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -45.524591 | E_var:     0.1961 | E_err:   0.006919
[2025-10-07 13:12:51] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -45.519626 | E_var:     0.2232 | E_err:   0.007382
[2025-10-07 13:12:58] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -45.520256 | E_var:     0.1921 | E_err:   0.006848
[2025-10-07 13:13:06] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -45.512722 | E_var:     0.2321 | E_err:   0.007528
[2025-10-07 13:13:14] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -45.531948 | E_var:     0.1854 | E_err:   0.006729
[2025-10-07 13:13:22] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -45.529556 | E_var:     0.2125 | E_err:   0.007203
[2025-10-07 13:13:29] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -45.528412 | E_var:     0.3020 | E_err:   0.008587
[2025-10-07 13:13:37] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -45.518087 | E_var:     0.2108 | E_err:   0.007174
[2025-10-07 13:13:45] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -45.531764 | E_var:     0.2059 | E_err:   0.007090
[2025-10-07 13:13:53] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -45.525279 | E_var:     0.2892 | E_err:   0.008403
[2025-10-07 13:13:53] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 13:14:01] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -45.524998 | E_var:     0.2059 | E_err:   0.007089
[2025-10-07 13:14:08] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -45.516416 | E_var:     0.1869 | E_err:   0.006755
[2025-10-07 13:14:16] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -45.522362 | E_var:     0.1838 | E_err:   0.006698
[2025-10-07 13:14:24] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -45.523124 | E_var:     0.2077 | E_err:   0.007122
[2025-10-07 13:14:32] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -45.515121 | E_var:     0.2247 | E_err:   0.007407
[2025-10-07 13:14:40] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -45.530970 | E_var:     0.2618 | E_err:   0.007995
[2025-10-07 13:14:47] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -45.519903 | E_var:     0.2285 | E_err:   0.007468
[2025-10-07 13:14:55] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -45.529558 | E_var:     0.1806 | E_err:   0.006640
[2025-10-07 13:15:03] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -45.527485 | E_var:     0.1767 | E_err:   0.006568
[2025-10-07 13:15:11] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -45.522443 | E_var:     0.2430 | E_err:   0.007703
[2025-10-07 13:15:19] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -45.521273 | E_var:     0.2063 | E_err:   0.007097
[2025-10-07 13:15:26] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -45.524640 | E_var:     0.2884 | E_err:   0.008391
[2025-10-07 13:15:34] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -45.521774 | E_var:     0.2862 | E_err:   0.008359
[2025-10-07 13:15:42] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -45.538322 | E_var:     0.2268 | E_err:   0.007440
[2025-10-07 13:15:50] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -45.519479 | E_var:     0.1954 | E_err:   0.006907
[2025-10-07 13:15:57] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -45.514627 | E_var:     0.1855 | E_err:   0.006729
[2025-10-07 13:16:05] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -45.515790 | E_var:     0.1856 | E_err:   0.006732
[2025-10-07 13:16:13] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -45.528408 | E_var:     0.2041 | E_err:   0.007059
[2025-10-07 13:16:21] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -45.508413 | E_var:     0.2555 | E_err:   0.007898
[2025-10-07 13:16:29] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -45.529841 | E_var:     0.2554 | E_err:   0.007897
[2025-10-07 13:16:36] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -45.525895 | E_var:     0.2048 | E_err:   0.007071
[2025-10-07 13:16:44] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -45.519381 | E_var:     0.3039 | E_err:   0.008613
[2025-10-07 13:16:52] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -45.512332 | E_var:     0.1950 | E_err:   0.006901
[2025-10-07 13:17:00] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -45.529124 | E_var:     0.1857 | E_err:   0.006734
[2025-10-07 13:17:07] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -45.531868 | E_var:     0.2401 | E_err:   0.007656
[2025-10-07 13:17:15] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -45.519495 | E_var:     0.2035 | E_err:   0.007049
[2025-10-07 13:17:23] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -45.521591 | E_var:     0.1828 | E_err:   0.006680
[2025-10-07 13:17:31] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -45.525084 | E_var:     0.1809 | E_err:   0.006646
[2025-10-07 13:17:39] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -45.526362 | E_var:     0.2494 | E_err:   0.007804
[2025-10-07 13:17:46] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -45.514920 | E_var:     0.2429 | E_err:   0.007701
[2025-10-07 13:17:54] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -45.522472 | E_var:     0.1893 | E_err:   0.006799
[2025-10-07 13:18:02] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -45.517101 | E_var:     0.2212 | E_err:   0.007349
[2025-10-07 13:18:10] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -45.535185 | E_var:     0.2553 | E_err:   0.007894
[2025-10-07 13:18:18] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -45.542313 | E_var:     0.2720 | E_err:   0.008148
[2025-10-07 13:18:25] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -45.510616 | E_var:     0.2011 | E_err:   0.007006
[2025-10-07 13:18:33] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -45.525464 | E_var:     0.2304 | E_err:   0.007501
[2025-10-07 13:18:41] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -45.514453 | E_var:     0.1903 | E_err:   0.006815
[2025-10-07 13:18:49] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -45.522175 | E_var:     0.1687 | E_err:   0.006418
[2025-10-07 13:18:56] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -45.517529 | E_var:     0.2031 | E_err:   0.007041
[2025-10-07 13:19:04] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -45.511191 | E_var:     0.2775 | E_err:   0.008230
[2025-10-07 13:19:12] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -45.515529 | E_var:     0.2595 | E_err:   0.007959
[2025-10-07 13:19:20] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -45.519443 | E_var:     0.2151 | E_err:   0.007247
[2025-10-07 13:19:28] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -45.522936 | E_var:     0.2093 | E_err:   0.007149
[2025-10-07 13:19:35] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -45.528637 | E_var:     0.2620 | E_err:   0.007998
[2025-10-07 13:19:43] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -45.529295 | E_var:     0.2397 | E_err:   0.007649
[2025-10-07 13:19:51] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -45.529345 | E_var:     0.1892 | E_err:   0.006796
[2025-10-07 13:19:59] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -45.523044 | E_var:     0.2911 | E_err:   0.008430
[2025-10-07 13:20:06] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -45.517652 | E_var:     0.2405 | E_err:   0.007663
[2025-10-07 13:20:14] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -45.528988 | E_var:     0.2392 | E_err:   0.007642
[2025-10-07 13:20:22] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -45.511210 | E_var:     0.1947 | E_err:   0.006894
[2025-10-07 13:20:22] 🔄 RESTART #2 | Period: 600
[2025-10-07 13:20:30] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -45.528475 | E_var:     0.2025 | E_err:   0.007032
[2025-10-07 13:20:38] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -45.526081 | E_var:     0.2344 | E_err:   0.007565
[2025-10-07 13:20:45] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -45.536136 | E_var:     0.2704 | E_err:   0.008125
[2025-10-07 13:20:53] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -45.517715 | E_var:     0.2426 | E_err:   0.007695
[2025-10-07 13:21:01] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -45.525396 | E_var:     0.2761 | E_err:   0.008211
[2025-10-07 13:21:09] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -45.521062 | E_var:     0.1923 | E_err:   0.006852
[2025-10-07 13:21:17] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -45.513748 | E_var:     0.1883 | E_err:   0.006781
[2025-10-07 13:21:24] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -45.519824 | E_var:     0.2154 | E_err:   0.007252
[2025-10-07 13:21:32] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -45.521985 | E_var:     0.2837 | E_err:   0.008322
[2025-10-07 13:21:40] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -45.520299 | E_var:     0.2833 | E_err:   0.008317
[2025-10-07 13:21:48] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -45.517542 | E_var:     0.2256 | E_err:   0.007422
[2025-10-07 13:21:55] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -45.511523 | E_var:     0.1746 | E_err:   0.006530
[2025-10-07 13:22:03] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -45.534879 | E_var:     0.2086 | E_err:   0.007137
[2025-10-07 13:22:11] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -45.512127 | E_var:     0.1616 | E_err:   0.006280
[2025-10-07 13:22:19] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -45.522745 | E_var:     0.1844 | E_err:   0.006710
[2025-10-07 13:22:27] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -45.536394 | E_var:     0.3177 | E_err:   0.008807
[2025-10-07 13:22:34] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -45.517924 | E_var:     0.1872 | E_err:   0.006760
[2025-10-07 13:22:42] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -45.523580 | E_var:     0.1565 | E_err:   0.006180
[2025-10-07 13:22:50] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -45.532506 | E_var:     0.1945 | E_err:   0.006891
[2025-10-07 13:22:58] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -45.525803 | E_var:     0.1922 | E_err:   0.006850
[2025-10-07 13:23:05] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -45.508392 | E_var:     1.9450 | E_err:   0.021791
[2025-10-07 13:23:13] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -45.522399 | E_var:     0.2142 | E_err:   0.007232
[2025-10-07 13:23:21] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -45.519149 | E_var:     0.2103 | E_err:   0.007166
[2025-10-07 13:23:29] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -45.524452 | E_var:     0.1977 | E_err:   0.006948
[2025-10-07 13:23:37] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -45.529672 | E_var:     0.1975 | E_err:   0.006944
[2025-10-07 13:23:44] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -45.526361 | E_var:     0.1940 | E_err:   0.006882
[2025-10-07 13:23:52] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -45.518220 | E_var:     0.1998 | E_err:   0.006985
[2025-10-07 13:24:00] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -45.525309 | E_var:     0.1957 | E_err:   0.006911
[2025-10-07 13:24:08] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -45.527762 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 13:24:16] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -45.512659 | E_var:     0.1952 | E_err:   0.006904
[2025-10-07 13:24:23] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -45.524338 | E_var:     0.2024 | E_err:   0.007029
[2025-10-07 13:24:31] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -45.527106 | E_var:     0.2319 | E_err:   0.007525
[2025-10-07 13:24:39] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -45.516843 | E_var:     0.2512 | E_err:   0.007832
[2025-10-07 13:24:47] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -45.525383 | E_var:     0.1901 | E_err:   0.006813
[2025-10-07 13:24:54] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -45.528565 | E_var:     0.2737 | E_err:   0.008175
[2025-10-07 13:25:02] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -45.528630 | E_var:     0.1704 | E_err:   0.006450
[2025-10-07 13:25:10] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -45.523099 | E_var:     0.2174 | E_err:   0.007285
[2025-10-07 13:25:18] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -45.514244 | E_var:     0.2090 | E_err:   0.007143
[2025-10-07 13:25:26] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -45.524772 | E_var:     0.2083 | E_err:   0.007132
[2025-10-07 13:25:33] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -45.509063 | E_var:     0.2059 | E_err:   0.007090
[2025-10-07 13:25:41] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -45.508031 | E_var:     0.2011 | E_err:   0.007007
[2025-10-07 13:25:49] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -45.519720 | E_var:     0.2125 | E_err:   0.007203
[2025-10-07 13:25:57] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -45.520405 | E_var:     0.2056 | E_err:   0.007085
[2025-10-07 13:26:04] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -45.515934 | E_var:     0.2247 | E_err:   0.007407
[2025-10-07 13:26:12] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -45.527244 | E_var:     0.1860 | E_err:   0.006738
[2025-10-07 13:26:20] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -45.516821 | E_var:     0.3146 | E_err:   0.008764
[2025-10-07 13:26:28] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -45.518602 | E_var:     0.2142 | E_err:   0.007232
[2025-10-07 13:26:36] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -45.520803 | E_var:     0.2157 | E_err:   0.007257
[2025-10-07 13:26:43] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -45.527605 | E_var:     0.1966 | E_err:   0.006928
[2025-10-07 13:26:51] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -45.520722 | E_var:     0.1848 | E_err:   0.006717
[2025-10-07 13:26:51] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 13:26:59] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -45.523886 | E_var:     0.2002 | E_err:   0.006992
[2025-10-07 13:27:07] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -45.518387 | E_var:     0.1837 | E_err:   0.006697
[2025-10-07 13:27:15] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -45.520766 | E_var:     0.1843 | E_err:   0.006707
[2025-10-07 13:27:22] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -45.514296 | E_var:     0.2350 | E_err:   0.007575
[2025-10-07 13:27:30] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -45.535831 | E_var:     0.1715 | E_err:   0.006471
[2025-10-07 13:27:38] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -45.510556 | E_var:     0.2068 | E_err:   0.007105
[2025-10-07 13:27:46] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -45.502033 | E_var:     0.2744 | E_err:   0.008185
[2025-10-07 13:27:54] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -45.522651 | E_var:     0.5060 | E_err:   0.011115
[2025-10-07 13:28:01] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -45.518816 | E_var:     0.2039 | E_err:   0.007056
[2025-10-07 13:28:09] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -45.529958 | E_var:     0.2775 | E_err:   0.008231
[2025-10-07 13:28:17] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -45.524530 | E_var:     0.2345 | E_err:   0.007566
[2025-10-07 13:28:25] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -45.523335 | E_var:     0.2014 | E_err:   0.007012
[2025-10-07 13:28:32] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -45.535044 | E_var:     0.2919 | E_err:   0.008441
[2025-10-07 13:28:40] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -45.524749 | E_var:     0.3588 | E_err:   0.009359
[2025-10-07 13:28:48] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -45.514409 | E_var:     0.1873 | E_err:   0.006763
[2025-10-07 13:28:56] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -45.511508 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 13:29:04] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -45.529138 | E_var:     0.1731 | E_err:   0.006502
[2025-10-07 13:29:11] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -45.531584 | E_var:     0.2433 | E_err:   0.007706
[2025-10-07 13:29:19] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -45.520971 | E_var:     0.2158 | E_err:   0.007259
[2025-10-07 13:29:27] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -45.521043 | E_var:     0.1549 | E_err:   0.006150
[2025-10-07 13:29:35] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -45.518198 | E_var:     0.2020 | E_err:   0.007023
[2025-10-07 13:29:42] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -45.516485 | E_var:     0.2861 | E_err:   0.008358
[2025-10-07 13:29:50] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -45.517812 | E_var:     0.3459 | E_err:   0.009189
[2025-10-07 13:29:58] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -45.516589 | E_var:     0.3566 | E_err:   0.009331
[2025-10-07 13:30:06] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -45.513278 | E_var:     0.2023 | E_err:   0.007028
[2025-10-07 13:30:14] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -45.526932 | E_var:     0.6056 | E_err:   0.012160
[2025-10-07 13:30:21] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -45.521506 | E_var:     0.3684 | E_err:   0.009484
[2025-10-07 13:30:29] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -45.531045 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 13:30:37] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -45.512479 | E_var:     0.2499 | E_err:   0.007811
[2025-10-07 13:30:45] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -45.521935 | E_var:     0.1969 | E_err:   0.006933
[2025-10-07 13:30:53] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -45.518859 | E_var:     0.2416 | E_err:   0.007681
[2025-10-07 13:31:00] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -45.514675 | E_var:     0.2328 | E_err:   0.007540
[2025-10-07 13:31:08] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -45.525252 | E_var:     0.2099 | E_err:   0.007158
[2025-10-07 13:31:16] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -45.548908 | E_var:     0.2648 | E_err:   0.008041
[2025-10-07 13:31:24] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -45.514686 | E_var:     0.2897 | E_err:   0.008411
[2025-10-07 13:31:31] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -45.520850 | E_var:     0.3179 | E_err:   0.008810
[2025-10-07 13:31:39] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -45.523631 | E_var:     0.2478 | E_err:   0.007778
[2025-10-07 13:31:47] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -45.530874 | E_var:     0.2414 | E_err:   0.007677
[2025-10-07 13:31:55] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -45.535862 | E_var:     0.2268 | E_err:   0.007442
[2025-10-07 13:32:03] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -45.523609 | E_var:     0.2171 | E_err:   0.007280
[2025-10-07 13:32:10] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -45.529232 | E_var:     0.2131 | E_err:   0.007213
[2025-10-07 13:32:18] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -45.522349 | E_var:     0.2310 | E_err:   0.007510
[2025-10-07 13:32:26] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -45.527489 | E_var:     0.1757 | E_err:   0.006549
[2025-10-07 13:32:34] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -45.527200 | E_var:     0.1923 | E_err:   0.006853
[2025-10-07 13:32:42] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -45.541342 | E_var:     0.2075 | E_err:   0.007118
[2025-10-07 13:32:49] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -45.528682 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 13:32:57] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -45.521137 | E_var:     0.3077 | E_err:   0.008668
[2025-10-07 13:33:05] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -45.511956 | E_var:     0.2043 | E_err:   0.007063
[2025-10-07 13:33:13] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -45.532745 | E_var:     0.2416 | E_err:   0.007680
[2025-10-07 13:33:20] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -45.520204 | E_var:     0.2413 | E_err:   0.007676
[2025-10-07 13:33:28] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -45.513969 | E_var:     0.2577 | E_err:   0.007932
[2025-10-07 13:33:36] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -45.512136 | E_var:     0.2149 | E_err:   0.007243
[2025-10-07 13:33:44] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -45.516478 | E_var:     0.1612 | E_err:   0.006274
[2025-10-07 13:33:52] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -45.524043 | E_var:     0.1954 | E_err:   0.006907
[2025-10-07 13:33:59] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -45.522179 | E_var:     0.1854 | E_err:   0.006728
[2025-10-07 13:34:07] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -45.522387 | E_var:     0.2410 | E_err:   0.007671
[2025-10-07 13:34:15] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -45.527542 | E_var:     0.1499 | E_err:   0.006050
[2025-10-07 13:34:23] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -45.519088 | E_var:     0.2222 | E_err:   0.007366
[2025-10-07 13:34:30] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -45.528737 | E_var:     0.2583 | E_err:   0.007940
[2025-10-07 13:34:38] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -45.504540 | E_var:     0.2049 | E_err:   0.007073
[2025-10-07 13:34:46] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -45.515695 | E_var:     0.2152 | E_err:   0.007248
[2025-10-07 13:34:54] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -45.517965 | E_var:     0.2561 | E_err:   0.007907
[2025-10-07 13:35:02] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -45.509741 | E_var:     0.2132 | E_err:   0.007214
[2025-10-07 13:35:09] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -45.518837 | E_var:     0.1781 | E_err:   0.006593
[2025-10-07 13:35:17] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -45.519979 | E_var:     0.2019 | E_err:   0.007020
[2025-10-07 13:35:25] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -45.516456 | E_var:     0.2222 | E_err:   0.007366
[2025-10-07 13:35:33] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -45.519275 | E_var:     0.1981 | E_err:   0.006954
[2025-10-07 13:35:41] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -45.517836 | E_var:     0.2211 | E_err:   0.007347
[2025-10-07 13:35:48] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -45.512405 | E_var:     0.2523 | E_err:   0.007849
[2025-10-07 13:35:56] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -45.524643 | E_var:     0.2049 | E_err:   0.007073
[2025-10-07 13:36:04] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -45.521576 | E_var:     0.2227 | E_err:   0.007374
[2025-10-07 13:36:12] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -45.529118 | E_var:     0.1828 | E_err:   0.006680
[2025-10-07 13:36:19] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -45.526758 | E_var:     0.2030 | E_err:   0.007041
[2025-10-07 13:36:27] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -45.518760 | E_var:     0.1656 | E_err:   0.006358
[2025-10-07 13:36:35] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -45.527075 | E_var:     0.2181 | E_err:   0.007298
[2025-10-07 13:36:43] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -45.515593 | E_var:     0.2106 | E_err:   0.007170
[2025-10-07 13:36:51] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -45.525958 | E_var:     0.2497 | E_err:   0.007808
[2025-10-07 13:36:58] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -45.517930 | E_var:     0.2823 | E_err:   0.008301
[2025-10-07 13:37:06] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -45.516194 | E_var:     0.2208 | E_err:   0.007343
[2025-10-07 13:37:14] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -45.537619 | E_var:     0.1901 | E_err:   0.006813
[2025-10-07 13:37:22] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -45.511677 | E_var:     0.2835 | E_err:   0.008320
[2025-10-07 13:37:29] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -45.525444 | E_var:     0.2344 | E_err:   0.007564
[2025-10-07 13:37:37] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -45.525912 | E_var:     0.1984 | E_err:   0.006960
[2025-10-07 13:37:45] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -45.520771 | E_var:     0.2715 | E_err:   0.008141
[2025-10-07 13:37:53] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -45.523590 | E_var:     0.2767 | E_err:   0.008220
[2025-10-07 13:38:01] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -45.522151 | E_var:     0.2239 | E_err:   0.007393
[2025-10-07 13:38:08] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -45.524584 | E_var:     0.3021 | E_err:   0.008588
[2025-10-07 13:38:16] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -45.523049 | E_var:     0.1751 | E_err:   0.006537
[2025-10-07 13:38:24] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -45.524806 | E_var:     0.1654 | E_err:   0.006354
[2025-10-07 13:38:32] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -45.522365 | E_var:     0.1643 | E_err:   0.006334
[2025-10-07 13:38:40] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -45.528952 | E_var:     0.1968 | E_err:   0.006931
[2025-10-07 13:38:47] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -45.525593 | E_var:     0.1678 | E_err:   0.006400
[2025-10-07 13:38:55] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -45.528962 | E_var:     0.2099 | E_err:   0.007159
[2025-10-07 13:39:03] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -45.514632 | E_var:     0.2229 | E_err:   0.007378
[2025-10-07 13:39:11] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -45.522662 | E_var:     0.1914 | E_err:   0.006835
[2025-10-07 13:39:18] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -45.528216 | E_var:     0.2341 | E_err:   0.007559
[2025-10-07 13:39:26] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -45.512224 | E_var:     0.2613 | E_err:   0.007988
[2025-10-07 13:39:34] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -45.524360 | E_var:     0.2304 | E_err:   0.007500
[2025-10-07 13:39:42] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -45.526520 | E_var:     0.1757 | E_err:   0.006550
[2025-10-07 13:39:50] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -45.529203 | E_var:     0.3469 | E_err:   0.009203
[2025-10-07 13:39:50] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 13:39:57] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -45.526870 | E_var:     0.2124 | E_err:   0.007201
[2025-10-07 13:40:05] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -45.507155 | E_var:     0.2435 | E_err:   0.007710
[2025-10-07 13:40:13] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -45.541630 | E_var:     0.3459 | E_err:   0.009189
[2025-10-07 13:40:21] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -45.517900 | E_var:     0.2602 | E_err:   0.007970
[2025-10-07 13:40:29] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -45.536455 | E_var:     0.2158 | E_err:   0.007258
[2025-10-07 13:40:36] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -45.506539 | E_var:     0.1945 | E_err:   0.006891
[2025-10-07 13:40:44] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -45.523243 | E_var:     0.1861 | E_err:   0.006740
[2025-10-07 13:40:52] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -45.518132 | E_var:     0.2838 | E_err:   0.008324
[2025-10-07 13:41:00] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -45.526310 | E_var:     0.1866 | E_err:   0.006750
[2025-10-07 13:41:07] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -45.526713 | E_var:     0.2082 | E_err:   0.007129
[2025-10-07 13:41:15] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -45.526669 | E_var:     0.1766 | E_err:   0.006566
[2025-10-07 13:41:23] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -45.525729 | E_var:     0.2227 | E_err:   0.007374
[2025-10-07 13:41:31] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -45.519903 | E_var:     0.1734 | E_err:   0.006506
[2025-10-07 13:41:39] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -45.537879 | E_var:     0.2151 | E_err:   0.007246
[2025-10-07 13:41:46] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -45.533864 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 13:41:54] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -45.523103 | E_var:     0.1840 | E_err:   0.006702
[2025-10-07 13:42:02] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -45.512552 | E_var:     0.2122 | E_err:   0.007198
[2025-10-07 13:42:10] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -45.528313 | E_var:     0.1750 | E_err:   0.006537
[2025-10-07 13:42:18] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -45.514096 | E_var:     0.1936 | E_err:   0.006875
[2025-10-07 13:42:25] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -45.532165 | E_var:     0.2935 | E_err:   0.008465
[2025-10-07 13:42:33] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -45.522947 | E_var:     0.2554 | E_err:   0.007896
[2025-10-07 13:42:41] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -45.527656 | E_var:     0.2212 | E_err:   0.007349
[2025-10-07 13:42:49] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -45.521597 | E_var:     0.2191 | E_err:   0.007314
[2025-10-07 13:42:56] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -45.517808 | E_var:     0.2168 | E_err:   0.007275
[2025-10-07 13:43:04] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -45.533607 | E_var:     0.1733 | E_err:   0.006505
[2025-10-07 13:43:12] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -45.521314 | E_var:     0.2418 | E_err:   0.007683
[2025-10-07 13:43:20] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -45.517989 | E_var:     0.2184 | E_err:   0.007302
[2025-10-07 13:43:28] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -45.519443 | E_var:     0.4663 | E_err:   0.010670
[2025-10-07 13:43:35] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -45.529358 | E_var:     0.1731 | E_err:   0.006501
[2025-10-07 13:43:43] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -45.514641 | E_var:     0.2214 | E_err:   0.007352
[2025-10-07 13:43:51] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -45.517580 | E_var:     0.2432 | E_err:   0.007705
[2025-10-07 13:43:59] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -45.523171 | E_var:     0.1809 | E_err:   0.006647
[2025-10-07 13:44:06] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -45.504407 | E_var:     0.1875 | E_err:   0.006766
[2025-10-07 13:44:14] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -45.525497 | E_var:     0.1994 | E_err:   0.006977
[2025-10-07 13:44:22] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -45.529654 | E_var:     0.1929 | E_err:   0.006863
[2025-10-07 13:44:30] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -45.540953 | E_var:     0.2438 | E_err:   0.007715
[2025-10-07 13:44:38] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -45.517424 | E_var:     0.1643 | E_err:   0.006334
[2025-10-07 13:44:45] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -45.521723 | E_var:     0.1989 | E_err:   0.006968
[2025-10-07 13:44:53] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -45.519484 | E_var:     0.3160 | E_err:   0.008783
[2025-10-07 13:45:01] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -45.534832 | E_var:     0.1944 | E_err:   0.006890
[2025-10-07 13:45:09] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -45.536208 | E_var:     0.1993 | E_err:   0.006976
[2025-10-07 13:45:16] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -45.522338 | E_var:     0.2136 | E_err:   0.007222
[2025-10-07 13:45:24] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -45.499518 | E_var:     0.1928 | E_err:   0.006860
[2025-10-07 13:45:32] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -45.528108 | E_var:     0.2001 | E_err:   0.006989
[2025-10-07 13:45:40] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -45.509259 | E_var:     0.2383 | E_err:   0.007627
[2025-10-07 13:45:48] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -45.530555 | E_var:     0.2277 | E_err:   0.007455
[2025-10-07 13:45:55] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -45.516654 | E_var:     0.2902 | E_err:   0.008417
[2025-10-07 13:46:03] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -45.514346 | E_var:     0.3930 | E_err:   0.009795
[2025-10-07 13:46:11] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -45.526933 | E_var:     0.2540 | E_err:   0.007874
[2025-10-07 13:46:19] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -45.531187 | E_var:     0.2033 | E_err:   0.007045
[2025-10-07 13:46:27] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -45.521929 | E_var:     0.2433 | E_err:   0.007706
[2025-10-07 13:46:34] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -45.514453 | E_var:     0.1788 | E_err:   0.006607
[2025-10-07 13:46:42] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -45.530001 | E_var:     0.1957 | E_err:   0.006913
[2025-10-07 13:46:50] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -45.516565 | E_var:     0.1854 | E_err:   0.006728
[2025-10-07 13:46:58] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -45.525542 | E_var:     0.1939 | E_err:   0.006880
[2025-10-07 13:47:05] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -45.517331 | E_var:     0.2689 | E_err:   0.008102
[2025-10-07 13:47:13] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -45.534302 | E_var:     0.2330 | E_err:   0.007542
[2025-10-07 13:47:21] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -45.526031 | E_var:     0.2930 | E_err:   0.008457
[2025-10-07 13:47:29] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -45.519223 | E_var:     0.1612 | E_err:   0.006273
[2025-10-07 13:47:37] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -45.518100 | E_var:     0.1946 | E_err:   0.006893
[2025-10-07 13:47:44] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -45.516586 | E_var:     0.1660 | E_err:   0.006366
[2025-10-07 13:47:52] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -45.517851 | E_var:     0.2329 | E_err:   0.007540
[2025-10-07 13:48:00] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -45.530673 | E_var:     0.1857 | E_err:   0.006732
[2025-10-07 13:48:08] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -45.522924 | E_var:     0.2001 | E_err:   0.006989
[2025-10-07 13:48:15] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -45.526207 | E_var:     0.2084 | E_err:   0.007133
[2025-10-07 13:48:23] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -45.521941 | E_var:     0.2302 | E_err:   0.007497
[2025-10-07 13:48:31] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -45.529045 | E_var:     0.2041 | E_err:   0.007059
[2025-10-07 13:48:39] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -45.513090 | E_var:     0.2118 | E_err:   0.007191
[2025-10-07 13:48:47] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -45.531797 | E_var:     0.2198 | E_err:   0.007326
[2025-10-07 13:48:54] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -45.527905 | E_var:     0.1917 | E_err:   0.006841
[2025-10-07 13:49:02] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -45.536825 | E_var:     0.1733 | E_err:   0.006505
[2025-10-07 13:49:10] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -45.533187 | E_var:     0.1973 | E_err:   0.006940
[2025-10-07 13:49:18] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -45.530590 | E_var:     0.2209 | E_err:   0.007343
[2025-10-07 13:49:25] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -45.517964 | E_var:     0.2473 | E_err:   0.007771
[2025-10-07 13:49:33] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -45.521126 | E_var:     0.2207 | E_err:   0.007340
[2025-10-07 13:49:41] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -45.519634 | E_var:     0.2120 | E_err:   0.007193
[2025-10-07 13:49:49] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -45.534129 | E_var:     0.2306 | E_err:   0.007504
[2025-10-07 13:49:57] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -45.515829 | E_var:     0.2293 | E_err:   0.007483
[2025-10-07 13:50:04] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -45.506538 | E_var:     0.2808 | E_err:   0.008280
[2025-10-07 13:50:12] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -45.516993 | E_var:     0.2076 | E_err:   0.007119
[2025-10-07 13:50:20] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -45.523850 | E_var:     0.2632 | E_err:   0.008016
[2025-10-07 13:50:28] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -45.513868 | E_var:     0.1949 | E_err:   0.006898
[2025-10-07 13:50:36] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -45.525640 | E_var:     0.2423 | E_err:   0.007691
[2025-10-07 13:50:43] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -45.533709 | E_var:     0.3066 | E_err:   0.008652
[2025-10-07 13:50:51] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -45.520650 | E_var:     0.4130 | E_err:   0.010042
[2025-10-07 13:50:59] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -45.527216 | E_var:     0.2071 | E_err:   0.007110
[2025-10-07 13:51:07] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -45.530233 | E_var:     0.1567 | E_err:   0.006185
[2025-10-07 13:51:14] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -45.520058 | E_var:     0.1981 | E_err:   0.006954
[2025-10-07 13:51:22] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -45.522873 | E_var:     0.1965 | E_err:   0.006926
[2025-10-07 13:51:30] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -45.538366 | E_var:     0.2352 | E_err:   0.007578
[2025-10-07 13:51:38] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -45.515911 | E_var:     0.1929 | E_err:   0.006862
[2025-10-07 13:51:46] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -45.523892 | E_var:     0.1914 | E_err:   0.006836
[2025-10-07 13:51:53] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -45.529472 | E_var:     0.2115 | E_err:   0.007185
[2025-10-07 13:52:01] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -45.522507 | E_var:     0.2238 | E_err:   0.007392
[2025-10-07 13:52:09] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -45.522315 | E_var:     0.1914 | E_err:   0.006835
[2025-10-07 13:52:17] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -45.525336 | E_var:     0.1914 | E_err:   0.006836
[2025-10-07 13:52:24] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -45.522178 | E_var:     0.2107 | E_err:   0.007172
[2025-10-07 13:52:32] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -45.521796 | E_var:     0.2031 | E_err:   0.007042
[2025-10-07 13:52:40] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -45.518450 | E_var:     0.1908 | E_err:   0.006825
[2025-10-07 13:52:48] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -45.545559 | E_var:     0.3768 | E_err:   0.009591
[2025-10-07 13:52:48] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 13:52:56] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -45.533984 | E_var:     0.1817 | E_err:   0.006661
[2025-10-07 13:53:03] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -45.509987 | E_var:     0.6046 | E_err:   0.012149
[2025-10-07 13:53:11] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -45.522805 | E_var:     0.2231 | E_err:   0.007381
[2025-10-07 13:53:19] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -45.530213 | E_var:     0.2444 | E_err:   0.007724
[2025-10-07 13:53:27] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -45.514592 | E_var:     0.3186 | E_err:   0.008819
[2025-10-07 13:53:35] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -45.519313 | E_var:     0.2149 | E_err:   0.007243
[2025-10-07 13:53:42] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -45.516237 | E_var:     0.1853 | E_err:   0.006727
[2025-10-07 13:53:50] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -45.540843 | E_var:     0.2495 | E_err:   0.007805
[2025-10-07 13:53:58] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -45.531246 | E_var:     0.1902 | E_err:   0.006813
[2025-10-07 13:54:06] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -45.534408 | E_var:     0.2336 | E_err:   0.007552
[2025-10-07 13:54:14] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -45.532994 | E_var:     0.2132 | E_err:   0.007214
[2025-10-07 13:54:21] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -45.527901 | E_var:     0.2009 | E_err:   0.007003
[2025-10-07 13:54:29] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -45.522785 | E_var:     0.2125 | E_err:   0.007203
[2025-10-07 13:54:37] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -45.523316 | E_var:     0.1876 | E_err:   0.006768
[2025-10-07 13:54:45] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -45.540279 | E_var:     0.2270 | E_err:   0.007444
[2025-10-07 13:54:52] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -45.528066 | E_var:     0.1679 | E_err:   0.006402
[2025-10-07 13:55:00] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -45.526540 | E_var:     0.2379 | E_err:   0.007621
[2025-10-07 13:55:08] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -45.513416 | E_var:     0.2019 | E_err:   0.007022
[2025-10-07 13:55:16] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -45.521747 | E_var:     0.2650 | E_err:   0.008043
[2025-10-07 13:55:24] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -45.523517 | E_var:     0.3353 | E_err:   0.009048
[2025-10-07 13:55:31] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -45.523764 | E_var:     0.2127 | E_err:   0.007206
[2025-10-07 13:55:39] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -45.538824 | E_var:     0.2694 | E_err:   0.008109
[2025-10-07 13:55:47] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -45.532603 | E_var:     0.1826 | E_err:   0.006677
[2025-10-07 13:55:55] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -45.528304 | E_var:     0.2339 | E_err:   0.007557
[2025-10-07 13:56:03] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -45.532481 | E_var:     0.1995 | E_err:   0.006979
[2025-10-07 13:56:10] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -45.515265 | E_var:     0.2257 | E_err:   0.007423
[2025-10-07 13:56:18] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -45.519337 | E_var:     0.2545 | E_err:   0.007883
[2025-10-07 13:56:26] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -45.531768 | E_var:     0.2276 | E_err:   0.007455
[2025-10-07 13:56:34] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -45.520741 | E_var:     0.1819 | E_err:   0.006664
[2025-10-07 13:56:41] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -45.522614 | E_var:     0.2029 | E_err:   0.007039
[2025-10-07 13:56:49] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -45.520010 | E_var:     0.2144 | E_err:   0.007235
[2025-10-07 13:56:57] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -45.522087 | E_var:     0.2194 | E_err:   0.007318
[2025-10-07 13:57:05] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -45.527310 | E_var:     0.2688 | E_err:   0.008100
[2025-10-07 13:57:13] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -45.531577 | E_var:     0.3146 | E_err:   0.008763
[2025-10-07 13:57:20] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -45.518376 | E_var:     0.2298 | E_err:   0.007490
[2025-10-07 13:57:28] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -45.536755 | E_var:     0.9588 | E_err:   0.015300
[2025-10-07 13:57:36] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -45.528824 | E_var:     0.2699 | E_err:   0.008118
[2025-10-07 13:57:44] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -45.519662 | E_var:     0.1962 | E_err:   0.006922
[2025-10-07 13:57:51] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -45.521498 | E_var:     0.1907 | E_err:   0.006824
[2025-10-07 13:57:59] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -45.519295 | E_var:     0.1779 | E_err:   0.006589
[2025-10-07 13:58:07] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -45.525610 | E_var:     0.1847 | E_err:   0.006714
[2025-10-07 13:58:15] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -45.532932 | E_var:     0.2332 | E_err:   0.007546
[2025-10-07 13:58:23] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -45.529479 | E_var:     0.1930 | E_err:   0.006864
[2025-10-07 13:58:30] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -45.520732 | E_var:     0.1924 | E_err:   0.006853
[2025-10-07 13:58:38] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -45.517798 | E_var:     0.2188 | E_err:   0.007309
[2025-10-07 13:58:46] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -45.527682 | E_var:     0.2586 | E_err:   0.007946
[2025-10-07 13:58:54] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -45.522634 | E_var:     0.2718 | E_err:   0.008146
[2025-10-07 13:59:01] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -45.522819 | E_var:     0.1935 | E_err:   0.006873
[2025-10-07 13:59:09] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -45.528954 | E_var:     0.1983 | E_err:   0.006958
[2025-10-07 13:59:17] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -45.521093 | E_var:     0.1902 | E_err:   0.006814
[2025-10-07 13:59:25] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -45.529312 | E_var:     0.1840 | E_err:   0.006703
[2025-10-07 13:59:33] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -45.520559 | E_var:     0.2074 | E_err:   0.007116
[2025-10-07 13:59:40] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -45.517925 | E_var:     0.2211 | E_err:   0.007347
[2025-10-07 13:59:48] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -45.515981 | E_var:     0.1968 | E_err:   0.006932
[2025-10-07 13:59:56] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -45.522173 | E_var:     0.2199 | E_err:   0.007328
[2025-10-07 14:00:04] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -45.510046 | E_var:     0.2454 | E_err:   0.007740
[2025-10-07 14:00:12] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -45.518816 | E_var:     0.1799 | E_err:   0.006627
[2025-10-07 14:00:19] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -45.527357 | E_var:     0.2265 | E_err:   0.007435
[2025-10-07 14:00:27] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -45.526305 | E_var:     0.2350 | E_err:   0.007575
[2025-10-07 14:00:35] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -45.526804 | E_var:     0.2087 | E_err:   0.007138
[2025-10-07 14:00:43] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -45.518561 | E_var:     0.2133 | E_err:   0.007216
[2025-10-07 14:00:50] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -45.532581 | E_var:     0.2416 | E_err:   0.007680
[2025-10-07 14:00:58] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -45.522458 | E_var:     0.1970 | E_err:   0.006936
[2025-10-07 14:01:06] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -45.522423 | E_var:     0.1946 | E_err:   0.006892
[2025-10-07 14:01:14] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -45.522720 | E_var:     0.2229 | E_err:   0.007377
[2025-10-07 14:01:22] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -45.535929 | E_var:     0.2811 | E_err:   0.008284
[2025-10-07 14:01:29] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -45.527978 | E_var:     0.1604 | E_err:   0.006258
[2025-10-07 14:01:37] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -45.522447 | E_var:     0.2174 | E_err:   0.007285
[2025-10-07 14:01:45] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -45.515594 | E_var:     0.1947 | E_err:   0.006895
[2025-10-07 14:01:53] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -45.529737 | E_var:     0.1968 | E_err:   0.006932
[2025-10-07 14:02:00] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -45.525108 | E_var:     0.1812 | E_err:   0.006652
[2025-10-07 14:02:08] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -45.526706 | E_var:     0.2232 | E_err:   0.007382
[2025-10-07 14:02:16] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -45.517768 | E_var:     0.2012 | E_err:   0.007009
[2025-10-07 14:02:24] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -45.520555 | E_var:     0.3073 | E_err:   0.008662
[2025-10-07 14:02:32] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -45.528917 | E_var:     0.1779 | E_err:   0.006590
[2025-10-07 14:02:39] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -45.524604 | E_var:     0.3472 | E_err:   0.009207
[2025-10-07 14:02:47] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -45.522733 | E_var:     0.1860 | E_err:   0.006739
[2025-10-07 14:02:55] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -45.521142 | E_var:     0.2715 | E_err:   0.008141
[2025-10-07 14:03:03] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -45.521040 | E_var:     0.1947 | E_err:   0.006894
[2025-10-07 14:03:11] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -45.529691 | E_var:     0.3142 | E_err:   0.008759
[2025-10-07 14:03:18] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -45.524696 | E_var:     0.1724 | E_err:   0.006487
[2025-10-07 14:03:26] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -45.522513 | E_var:     0.2311 | E_err:   0.007511
[2025-10-07 14:03:34] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -45.515522 | E_var:     0.1936 | E_err:   0.006875
[2025-10-07 14:03:42] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -45.538185 | E_var:     0.2751 | E_err:   0.008195
[2025-10-07 14:03:49] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -45.519843 | E_var:     0.1725 | E_err:   0.006490
[2025-10-07 14:03:57] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -45.534205 | E_var:     0.1527 | E_err:   0.006107
[2025-10-07 14:04:05] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -45.514827 | E_var:     0.5439 | E_err:   0.011523
[2025-10-07 14:04:13] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -45.510762 | E_var:     0.1900 | E_err:   0.006810
[2025-10-07 14:04:21] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -45.514053 | E_var:     0.2524 | E_err:   0.007850
[2025-10-07 14:04:28] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -45.535062 | E_var:     0.1845 | E_err:   0.006711
[2025-10-07 14:04:36] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -45.520634 | E_var:     0.2005 | E_err:   0.006996
[2025-10-07 14:04:44] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -45.521600 | E_var:     0.2082 | E_err:   0.007130
[2025-10-07 14:04:52] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -45.527815 | E_var:     0.2441 | E_err:   0.007720
[2025-10-07 14:04:59] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -45.521857 | E_var:     0.1600 | E_err:   0.006250
[2025-10-07 14:05:07] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -45.528016 | E_var:     0.1999 | E_err:   0.006986
[2025-10-07 14:05:15] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -45.531135 | E_var:     0.2206 | E_err:   0.007339
[2025-10-07 14:05:23] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -45.518902 | E_var:     0.1686 | E_err:   0.006417
[2025-10-07 14:05:31] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -45.532052 | E_var:    12.2652 | E_err:   0.054722
[2025-10-07 14:05:38] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -45.532362 | E_var:     0.2299 | E_err:   0.007492
[2025-10-07 14:05:46] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -45.529151 | E_var:     0.2174 | E_err:   0.007285
[2025-10-07 14:05:46] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 14:05:54] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -45.516610 | E_var:     0.2789 | E_err:   0.008252
[2025-10-07 14:06:02] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -45.532179 | E_var:     0.2286 | E_err:   0.007470
[2025-10-07 14:06:10] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -45.524424 | E_var:     0.2949 | E_err:   0.008485
[2025-10-07 14:06:17] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -45.516456 | E_var:     0.2218 | E_err:   0.007359
[2025-10-07 14:06:25] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -45.508031 | E_var:     0.1770 | E_err:   0.006574
[2025-10-07 14:06:33] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -45.522247 | E_var:     0.2125 | E_err:   0.007203
[2025-10-07 14:06:41] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -45.527048 | E_var:     0.1838 | E_err:   0.006698
[2025-10-07 14:06:49] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -45.531526 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 14:06:56] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -45.528178 | E_var:     0.1935 | E_err:   0.006874
[2025-10-07 14:07:04] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -45.526356 | E_var:     0.2211 | E_err:   0.007348
[2025-10-07 14:07:12] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -45.526874 | E_var:     0.1657 | E_err:   0.006361
[2025-10-07 14:07:20] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -45.529581 | E_var:     0.1768 | E_err:   0.006570
[2025-10-07 14:07:27] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -45.519818 | E_var:     0.1674 | E_err:   0.006393
[2025-10-07 14:07:35] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -45.516298 | E_var:     0.2588 | E_err:   0.007949
[2025-10-07 14:07:43] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -45.521927 | E_var:     0.1936 | E_err:   0.006875
[2025-10-07 14:07:51] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -45.533568 | E_var:     0.2185 | E_err:   0.007304
[2025-10-07 14:07:59] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -45.521045 | E_var:     0.3298 | E_err:   0.008973
[2025-10-07 14:08:06] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -45.519180 | E_var:     0.1757 | E_err:   0.006550
[2025-10-07 14:08:14] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -45.531180 | E_var:     0.2116 | E_err:   0.007188
[2025-10-07 14:08:22] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -45.504784 | E_var:     0.2145 | E_err:   0.007237
[2025-10-07 14:08:30] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -45.522954 | E_var:     0.1772 | E_err:   0.006578
[2025-10-07 14:08:37] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -45.518480 | E_var:     0.2063 | E_err:   0.007097
[2025-10-07 14:08:45] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -45.513111 | E_var:     0.3104 | E_err:   0.008705
[2025-10-07 14:08:53] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -45.522528 | E_var:     0.1885 | E_err:   0.006785
[2025-10-07 14:09:01] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -45.529189 | E_var:     0.1500 | E_err:   0.006052
[2025-10-07 14:09:09] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -45.518055 | E_var:     0.2334 | E_err:   0.007548
[2025-10-07 14:09:16] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -45.517395 | E_var:     0.1615 | E_err:   0.006278
[2025-10-07 14:09:24] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -45.520898 | E_var:     0.2752 | E_err:   0.008197
[2025-10-07 14:09:32] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -45.517094 | E_var:     0.1981 | E_err:   0.006955
[2025-10-07 14:09:40] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -45.516214 | E_var:     0.1881 | E_err:   0.006776
[2025-10-07 14:09:48] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -45.527271 | E_var:     0.2781 | E_err:   0.008240
[2025-10-07 14:09:55] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -45.521997 | E_var:     0.2229 | E_err:   0.007377
[2025-10-07 14:10:03] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -45.522085 | E_var:     0.2283 | E_err:   0.007467
[2025-10-07 14:10:11] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -45.515292 | E_var:     0.2021 | E_err:   0.007024
[2025-10-07 14:10:19] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -45.524378 | E_var:     0.1939 | E_err:   0.006881
[2025-10-07 14:10:26] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -45.513383 | E_var:     0.2259 | E_err:   0.007426
[2025-10-07 14:10:34] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -45.524368 | E_var:     0.2518 | E_err:   0.007840
[2025-10-07 14:10:42] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -45.523433 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 14:10:50] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -45.513717 | E_var:     0.1909 | E_err:   0.006827
[2025-10-07 14:10:58] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -45.517927 | E_var:     0.1529 | E_err:   0.006110
[2025-10-07 14:11:05] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -45.517995 | E_var:     0.1976 | E_err:   0.006945
[2025-10-07 14:11:13] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -45.531975 | E_var:     0.2150 | E_err:   0.007245
[2025-10-07 14:11:21] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -45.525109 | E_var:     0.2351 | E_err:   0.007575
[2025-10-07 14:11:29] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -45.527311 | E_var:     0.2155 | E_err:   0.007254
[2025-10-07 14:11:36] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -45.524042 | E_var:     0.2224 | E_err:   0.007368
[2025-10-07 14:11:44] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -45.520699 | E_var:     0.2135 | E_err:   0.007220
[2025-10-07 14:11:52] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -45.522001 | E_var:     0.1884 | E_err:   0.006782
[2025-10-07 14:12:00] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -45.527859 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 14:12:08] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -45.529770 | E_var:     0.1799 | E_err:   0.006626
[2025-10-07 14:12:15] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -45.515992 | E_var:     0.2043 | E_err:   0.007062
[2025-10-07 14:12:23] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -45.516493 | E_var:     0.2020 | E_err:   0.007022
[2025-10-07 14:12:31] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -45.519283 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 14:12:39] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -45.520015 | E_var:     0.2303 | E_err:   0.007498
[2025-10-07 14:12:47] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -45.516091 | E_var:     0.2841 | E_err:   0.008329
[2025-10-07 14:12:54] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -45.524565 | E_var:     0.2363 | E_err:   0.007596
[2025-10-07 14:13:02] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -45.526637 | E_var:     0.2069 | E_err:   0.007107
[2025-10-07 14:13:10] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -45.517747 | E_var:     0.2018 | E_err:   0.007019
[2025-10-07 14:13:18] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -45.518173 | E_var:     0.2422 | E_err:   0.007690
[2025-10-07 14:13:25] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -45.540172 | E_var:     0.1732 | E_err:   0.006502
[2025-10-07 14:13:33] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -45.526817 | E_var:     0.1950 | E_err:   0.006900
[2025-10-07 14:13:41] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -45.528409 | E_var:     0.2306 | E_err:   0.007503
[2025-10-07 14:13:49] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -45.528500 | E_var:     0.3409 | E_err:   0.009123
[2025-10-07 14:13:57] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -45.519546 | E_var:     0.2440 | E_err:   0.007719
[2025-10-07 14:14:04] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -45.525059 | E_var:     0.1751 | E_err:   0.006538
[2025-10-07 14:14:12] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -45.526762 | E_var:     0.2028 | E_err:   0.007036
[2025-10-07 14:14:20] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -45.519799 | E_var:     0.1795 | E_err:   0.006620
[2025-10-07 14:14:28] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -45.515431 | E_var:     0.2152 | E_err:   0.007248
[2025-10-07 14:14:35] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -45.526498 | E_var:     0.2460 | E_err:   0.007750
[2025-10-07 14:14:43] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -45.516336 | E_var:     0.1975 | E_err:   0.006943
[2025-10-07 14:14:51] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -45.524546 | E_var:     0.2289 | E_err:   0.007476
[2025-10-07 14:14:59] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -45.523728 | E_var:     0.3284 | E_err:   0.008954
[2025-10-07 14:15:07] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -45.522558 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 14:15:14] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -45.527325 | E_var:     0.1666 | E_err:   0.006378
[2025-10-07 14:15:22] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -45.524903 | E_var:     0.2140 | E_err:   0.007229
[2025-10-07 14:15:30] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -45.535403 | E_var:     0.2631 | E_err:   0.008014
[2025-10-07 14:15:38] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -45.531467 | E_var:     0.2247 | E_err:   0.007406
[2025-10-07 14:15:45] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -45.529058 | E_var:     0.2293 | E_err:   0.007482
[2025-10-07 14:15:53] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -45.533158 | E_var:     0.5445 | E_err:   0.011530
[2025-10-07 14:16:01] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -45.531166 | E_var:     0.2510 | E_err:   0.007828
[2025-10-07 14:16:09] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -45.522415 | E_var:     0.3171 | E_err:   0.008799
[2025-10-07 14:16:17] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -45.528038 | E_var:     0.1737 | E_err:   0.006512
[2025-10-07 14:16:24] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -45.536514 | E_var:     0.1880 | E_err:   0.006776
[2025-10-07 14:16:32] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -45.529138 | E_var:     0.2029 | E_err:   0.007038
[2025-10-07 14:16:40] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -45.527529 | E_var:     0.2349 | E_err:   0.007572
[2025-10-07 14:16:48] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -45.516987 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 14:16:56] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -45.505789 | E_var:     0.3402 | E_err:   0.009114
[2025-10-07 14:17:03] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -45.525677 | E_var:     0.2239 | E_err:   0.007394
[2025-10-07 14:17:11] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -45.504629 | E_var:     0.2145 | E_err:   0.007236
[2025-10-07 14:17:19] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -45.516247 | E_var:     0.2417 | E_err:   0.007681
[2025-10-07 14:17:27] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -45.534602 | E_var:     0.1984 | E_err:   0.006960
[2025-10-07 14:17:34] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -45.531376 | E_var:     0.2203 | E_err:   0.007334
[2025-10-07 14:17:42] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -45.523311 | E_var:     0.2375 | E_err:   0.007614
[2025-10-07 14:17:50] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -45.518371 | E_var:     0.1981 | E_err:   0.006954
[2025-10-07 14:17:58] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -45.516545 | E_var:     0.2238 | E_err:   0.007391
[2025-10-07 14:18:06] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -45.533067 | E_var:     0.2537 | E_err:   0.007870
[2025-10-07 14:18:13] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -45.540395 | E_var:     0.3132 | E_err:   0.008745
[2025-10-07 14:18:21] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -45.525122 | E_var:     0.1846 | E_err:   0.006714
[2025-10-07 14:18:29] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -45.520418 | E_var:     0.1720 | E_err:   0.006481
[2025-10-07 14:18:37] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -45.522884 | E_var:     0.2994 | E_err:   0.008549
[2025-10-07 14:18:45] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -45.529417 | E_var:     0.2195 | E_err:   0.007321
[2025-10-07 14:18:45] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 14:18:52] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -45.538186 | E_var:     0.2610 | E_err:   0.007983
[2025-10-07 14:19:00] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -45.525239 | E_var:     0.2278 | E_err:   0.007457
[2025-10-07 14:19:08] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -45.520413 | E_var:     0.1763 | E_err:   0.006561
[2025-10-07 14:19:16] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -45.507498 | E_var:     0.2129 | E_err:   0.007209
[2025-10-07 14:19:24] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -45.526661 | E_var:     0.1856 | E_err:   0.006731
[2025-10-07 14:19:31] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -45.518476 | E_var:     0.2837 | E_err:   0.008322
[2025-10-07 14:19:39] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -45.539968 | E_var:     0.2066 | E_err:   0.007102
[2025-10-07 14:19:47] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -45.531381 | E_var:     0.2663 | E_err:   0.008062
[2025-10-07 14:19:55] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -45.503418 | E_var:     0.5499 | E_err:   0.011587
[2025-10-07 14:20:03] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -45.525959 | E_var:     0.2950 | E_err:   0.008487
[2025-10-07 14:20:10] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -45.538314 | E_var:     0.2367 | E_err:   0.007601
[2025-10-07 14:20:18] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -45.523073 | E_var:     0.2152 | E_err:   0.007249
[2025-10-07 14:20:26] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -45.522573 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 14:20:34] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -45.521954 | E_var:     0.1712 | E_err:   0.006465
[2025-10-07 14:20:41] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -45.518403 | E_var:     0.2487 | E_err:   0.007793
[2025-10-07 14:20:49] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -45.523445 | E_var:     0.1865 | E_err:   0.006748
[2025-10-07 14:20:57] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -45.529946 | E_var:     0.1780 | E_err:   0.006592
[2025-10-07 14:21:05] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -45.520608 | E_var:     0.1900 | E_err:   0.006811
[2025-10-07 14:21:13] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -45.517511 | E_var:     0.1907 | E_err:   0.006823
[2025-10-07 14:21:20] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -45.516601 | E_var:     0.1987 | E_err:   0.006966
[2025-10-07 14:21:28] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -45.520431 | E_var:     0.1983 | E_err:   0.006957
[2025-10-07 14:21:36] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -45.530416 | E_var:     0.2304 | E_err:   0.007499
[2025-10-07 14:21:44] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -45.517751 | E_var:     0.2161 | E_err:   0.007263
[2025-10-07 14:21:51] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -45.519791 | E_var:     0.2169 | E_err:   0.007277
[2025-10-07 14:21:59] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -45.532979 | E_var:     0.2610 | E_err:   0.007982
[2025-10-07 14:22:07] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -45.526225 | E_var:     0.2013 | E_err:   0.007011
[2025-10-07 14:22:15] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -45.519303 | E_var:     0.2407 | E_err:   0.007666
[2025-10-07 14:22:23] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -45.511371 | E_var:     0.2150 | E_err:   0.007244
[2025-10-07 14:22:30] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -45.522784 | E_var:     0.2543 | E_err:   0.007879
[2025-10-07 14:22:38] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -45.512807 | E_var:     0.2131 | E_err:   0.007213
[2025-10-07 14:22:46] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -45.516684 | E_var:     0.2573 | E_err:   0.007926
[2025-10-07 14:22:54] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -45.521546 | E_var:     0.1692 | E_err:   0.006427
[2025-10-07 14:23:02] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -45.523096 | E_var:     0.1948 | E_err:   0.006897
[2025-10-07 14:23:09] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -45.525199 | E_var:     0.2626 | E_err:   0.008007
[2025-10-07 14:23:17] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -45.539362 | E_var:     0.1783 | E_err:   0.006597
[2025-10-07 14:23:25] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -45.518771 | E_var:     0.2030 | E_err:   0.007040
[2025-10-07 14:23:33] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -45.532773 | E_var:     0.1814 | E_err:   0.006655
[2025-10-07 14:23:40] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -45.526099 | E_var:     0.1916 | E_err:   0.006840
[2025-10-07 14:23:48] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -45.525411 | E_var:     0.2031 | E_err:   0.007041
[2025-10-07 14:23:56] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -45.533906 | E_var:     0.2170 | E_err:   0.007278
[2025-10-07 14:24:04] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -45.531223 | E_var:     0.4317 | E_err:   0.010266
[2025-10-07 14:24:12] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -45.518544 | E_var:     0.1878 | E_err:   0.006771
[2025-10-07 14:24:19] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -45.527951 | E_var:     0.2471 | E_err:   0.007767
[2025-10-07 14:24:27] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -45.527842 | E_var:     0.2456 | E_err:   0.007743
[2025-10-07 14:24:35] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -45.513568 | E_var:     0.2023 | E_err:   0.007028
[2025-10-07 14:24:43] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -45.530210 | E_var:     0.2102 | E_err:   0.007164
[2025-10-07 14:24:50] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -45.520600 | E_var:     0.2130 | E_err:   0.007211
[2025-10-07 14:24:58] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -45.513022 | E_var:     0.2582 | E_err:   0.007939
[2025-10-07 14:25:06] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -45.521399 | E_var:     0.2196 | E_err:   0.007321
[2025-10-07 14:25:14] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -45.523943 | E_var:     0.2147 | E_err:   0.007239
[2025-10-07 14:25:22] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -45.520278 | E_var:     0.2098 | E_err:   0.007156
[2025-10-07 14:25:29] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -45.518540 | E_var:     0.2233 | E_err:   0.007383
[2025-10-07 14:25:37] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -45.523738 | E_var:     0.2541 | E_err:   0.007876
[2025-10-07 14:25:45] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -45.524486 | E_var:     0.1784 | E_err:   0.006600
[2025-10-07 14:25:53] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -45.524416 | E_var:     0.1830 | E_err:   0.006683
[2025-10-07 14:26:00] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -45.507487 | E_var:     0.1798 | E_err:   0.006625
[2025-10-07 14:26:08] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -45.529089 | E_var:     0.2556 | E_err:   0.007900
[2025-10-07 14:26:16] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -45.519068 | E_var:     0.2034 | E_err:   0.007047
[2025-10-07 14:26:24] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -45.544144 | E_var:     0.3340 | E_err:   0.009030
[2025-10-07 14:26:32] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -45.529446 | E_var:     0.2172 | E_err:   0.007281
[2025-10-07 14:26:39] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -45.508710 | E_var:     0.2047 | E_err:   0.007070
[2025-10-07 14:26:47] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -45.534726 | E_var:     0.1846 | E_err:   0.006713
[2025-10-07 14:26:55] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -45.522916 | E_var:     0.2414 | E_err:   0.007677
[2025-10-07 14:27:03] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -45.540442 | E_var:     0.1923 | E_err:   0.006851
[2025-10-07 14:27:11] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -45.532119 | E_var:     0.2428 | E_err:   0.007700
[2025-10-07 14:27:18] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -45.527509 | E_var:     0.2258 | E_err:   0.007426
[2025-10-07 14:27:26] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -45.535904 | E_var:     0.2704 | E_err:   0.008125
[2025-10-07 14:27:34] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -45.521319 | E_var:     0.1881 | E_err:   0.006777
[2025-10-07 14:27:42] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -45.516135 | E_var:     0.1965 | E_err:   0.006927
[2025-10-07 14:27:49] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -45.529572 | E_var:     0.2237 | E_err:   0.007389
[2025-10-07 14:27:57] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -45.524873 | E_var:     0.2521 | E_err:   0.007845
[2025-10-07 14:28:05] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -45.521783 | E_var:     0.1840 | E_err:   0.006702
[2025-10-07 14:28:13] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -45.528453 | E_var:     0.1638 | E_err:   0.006323
[2025-10-07 14:28:21] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -45.511094 | E_var:     0.2485 | E_err:   0.007789
[2025-10-07 14:28:28] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -45.516247 | E_var:     0.3505 | E_err:   0.009250
[2025-10-07 14:28:36] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -45.500176 | E_var:     0.5154 | E_err:   0.011217
[2025-10-07 14:28:44] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -45.514968 | E_var:     0.2517 | E_err:   0.007840
[2025-10-07 14:28:52] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -45.521573 | E_var:     0.2299 | E_err:   0.007492
[2025-10-07 14:28:59] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -45.533542 | E_var:     0.2116 | E_err:   0.007187
[2025-10-07 14:29:07] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -45.525359 | E_var:     0.1788 | E_err:   0.006607
[2025-10-07 14:29:15] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -45.537741 | E_var:     0.2072 | E_err:   0.007113
[2025-10-07 14:29:23] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -45.529392 | E_var:     0.2469 | E_err:   0.007763
[2025-10-07 14:29:31] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -45.527934 | E_var:     0.1909 | E_err:   0.006828
[2025-10-07 14:29:38] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -45.525048 | E_var:     0.2066 | E_err:   0.007102
[2025-10-07 14:29:46] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -45.533704 | E_var:     0.1983 | E_err:   0.006958
[2025-10-07 14:29:54] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -45.518023 | E_var:     0.1964 | E_err:   0.006924
[2025-10-07 14:30:02] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -45.526329 | E_var:     0.2171 | E_err:   0.007281
[2025-10-07 14:30:09] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -45.530272 | E_var:     0.1779 | E_err:   0.006591
[2025-10-07 14:30:17] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -45.519609 | E_var:     0.2543 | E_err:   0.007880
[2025-10-07 14:30:25] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -45.514451 | E_var:     0.2525 | E_err:   0.007852
[2025-10-07 14:30:33] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -45.515653 | E_var:     0.1746 | E_err:   0.006529
[2025-10-07 14:30:41] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -45.509969 | E_var:     0.2548 | E_err:   0.007887
[2025-10-07 14:30:48] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -45.526240 | E_var:     0.2519 | E_err:   0.007842
[2025-10-07 14:30:56] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -45.511887 | E_var:     0.2653 | E_err:   0.008047
[2025-10-07 14:31:04] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -45.509503 | E_var:     0.1882 | E_err:   0.006779
[2025-10-07 14:31:12] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -45.525025 | E_var:     0.1992 | E_err:   0.006974
[2025-10-07 14:31:20] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -45.521119 | E_var:     0.1568 | E_err:   0.006187
[2025-10-07 14:31:27] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -45.516693 | E_var:     0.1810 | E_err:   0.006648
[2025-10-07 14:31:35] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -45.534615 | E_var:     0.2058 | E_err:   0.007089
[2025-10-07 14:31:43] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -45.520434 | E_var:     0.2416 | E_err:   0.007680
[2025-10-07 14:31:43] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 14:31:51] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -45.525277 | E_var:     0.2190 | E_err:   0.007312
[2025-10-07 14:31:59] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -45.531468 | E_var:     0.2661 | E_err:   0.008060
[2025-10-07 14:32:06] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -45.536433 | E_var:     0.2524 | E_err:   0.007850
[2025-10-07 14:32:14] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -45.521787 | E_var:     0.2284 | E_err:   0.007468
[2025-10-07 14:32:22] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -45.519345 | E_var:     0.2318 | E_err:   0.007522
[2025-10-07 14:32:30] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -45.532314 | E_var:     0.2039 | E_err:   0.007055
[2025-10-07 14:32:37] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -45.521369 | E_var:     0.2293 | E_err:   0.007482
[2025-10-07 14:32:45] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -45.522248 | E_var:     0.1849 | E_err:   0.006719
[2025-10-07 14:32:53] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -45.520194 | E_var:     0.1849 | E_err:   0.006719
[2025-10-07 14:33:01] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -45.524832 | E_var:     0.1822 | E_err:   0.006670
[2025-10-07 14:33:09] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -45.525870 | E_var:     0.1781 | E_err:   0.006595
[2025-10-07 14:33:16] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -45.532596 | E_var:     0.1879 | E_err:   0.006773
[2025-10-07 14:33:24] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -45.518832 | E_var:     0.2304 | E_err:   0.007500
[2025-10-07 14:33:32] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -45.525556 | E_var:     0.2229 | E_err:   0.007377
[2025-10-07 14:33:40] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -45.520202 | E_var:     0.1719 | E_err:   0.006479
[2025-10-07 14:33:47] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -45.523846 | E_var:     0.2027 | E_err:   0.007035
[2025-10-07 14:33:55] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -45.517313 | E_var:     0.1681 | E_err:   0.006406
[2025-10-07 14:34:03] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -45.525943 | E_var:     0.1724 | E_err:   0.006488
[2025-10-07 14:34:11] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -45.516686 | E_var:     0.2234 | E_err:   0.007386
[2025-10-07 14:34:19] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -45.528169 | E_var:     0.2148 | E_err:   0.007242
[2025-10-07 14:34:26] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -45.532606 | E_var:     0.1709 | E_err:   0.006459
[2025-10-07 14:34:34] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -45.510009 | E_var:     0.2591 | E_err:   0.007953
[2025-10-07 14:34:42] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -45.527890 | E_var:     0.2181 | E_err:   0.007297
[2025-10-07 14:34:50] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -45.527382 | E_var:     0.1836 | E_err:   0.006695
[2025-10-07 14:34:58] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -45.521339 | E_var:     0.1898 | E_err:   0.006807
[2025-10-07 14:35:05] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -45.513508 | E_var:     0.2180 | E_err:   0.007295
[2025-10-07 14:35:13] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -45.523816 | E_var:     0.1920 | E_err:   0.006847
[2025-10-07 14:35:21] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -45.526756 | E_var:     0.1793 | E_err:   0.006616
[2025-10-07 14:35:29] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -45.527964 | E_var:     0.2215 | E_err:   0.007353
[2025-10-07 14:35:36] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -45.519709 | E_var:     0.1899 | E_err:   0.006809
[2025-10-07 14:35:44] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -45.521794 | E_var:     0.2490 | E_err:   0.007797
[2025-10-07 14:35:52] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -45.528811 | E_var:     0.1959 | E_err:   0.006916
[2025-10-07 14:36:00] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -45.529623 | E_var:     0.1919 | E_err:   0.006844
[2025-10-07 14:36:08] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -45.530186 | E_var:     0.2058 | E_err:   0.007088
[2025-10-07 14:36:15] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -45.515036 | E_var:     0.1685 | E_err:   0.006414
[2025-10-07 14:36:23] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -45.521159 | E_var:     0.2319 | E_err:   0.007525
[2025-10-07 14:36:31] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -45.536011 | E_var:     0.2862 | E_err:   0.008359
[2025-10-07 14:36:39] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -45.544266 | E_var:     0.1770 | E_err:   0.006574
[2025-10-07 14:36:46] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -45.516039 | E_var:     0.2997 | E_err:   0.008554
[2025-10-07 14:36:54] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -45.532373 | E_var:     0.2067 | E_err:   0.007103
[2025-10-07 14:37:02] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -45.524047 | E_var:     0.2410 | E_err:   0.007670
[2025-10-07 14:37:10] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -45.519937 | E_var:     0.3369 | E_err:   0.009069
[2025-10-07 14:37:18] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -45.513852 | E_var:     0.2270 | E_err:   0.007445
[2025-10-07 14:37:25] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -45.527943 | E_var:     0.2157 | E_err:   0.007256
[2025-10-07 14:37:33] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -45.534867 | E_var:     0.1950 | E_err:   0.006899
[2025-10-07 14:37:41] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -45.533431 | E_var:     0.2721 | E_err:   0.008150
[2025-10-07 14:37:49] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -45.524983 | E_var:     0.1731 | E_err:   0.006501
[2025-10-07 14:37:57] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -45.517977 | E_var:     0.1954 | E_err:   0.006907
[2025-10-07 14:38:04] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -45.525630 | E_var:     0.1702 | E_err:   0.006446
[2025-10-07 14:38:12] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -45.524129 | E_var:     0.1662 | E_err:   0.006370
[2025-10-07 14:38:12] ======================================================================================================
[2025-10-07 14:38:12] ✅ Training completed successfully
[2025-10-07 14:38:12] Total restarts: 2
[2025-10-07 14:38:15] Final Energy: -45.52412908 ± 0.00637047
[2025-10-07 14:38:15] Final Variance: 0.166228
[2025-10-07 14:38:15] ======================================================================================================
[2025-10-07 14:38:15] ======================================================================================================
[2025-10-07 14:38:15] Training completed | Runtime: 8238.8s
[2025-10-07 14:38:17] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 14:38:17] ======================================================================================================
