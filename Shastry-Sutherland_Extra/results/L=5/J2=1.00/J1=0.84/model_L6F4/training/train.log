[2025-10-07 19:15:18] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.83/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 19:15:18]   - 迭代次数: final
[2025-10-07 19:15:18]   - 能量: -46.815041-0.000641j ± 0.006588, Var: 0.177751
[2025-10-07 19:15:18]   - 时间戳: 2025-10-07T19:15:04.117051+08:00
[2025-10-07 19:15:40] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 19:15:40] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 19:15:40] ======================================================================================================
[2025-10-07 19:15:40] GCNN for Shastry-Sutherland Model
[2025-10-07 19:15:40] ======================================================================================================
[2025-10-07 19:15:40] System parameters:
[2025-10-07 19:15:40]   - System size: L=5, N=100
[2025-10-07 19:15:40]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-10-07 19:15:40] ------------------------------------------------------------------------------------------------------
[2025-10-07 19:15:40] Model parameters:
[2025-10-07 19:15:40]   - Number of layers = 6
[2025-10-07 19:15:40]   - Number of features = 4
[2025-10-07 19:15:40]   - Total parameters = 32444
[2025-10-07 19:15:40] ------------------------------------------------------------------------------------------------------
[2025-10-07 19:15:40] Training parameters:
[2025-10-07 19:15:40]   - Total iterations: 1050
[2025-10-07 19:15:40]   - Annealing cycles: 3
[2025-10-07 19:15:40]   - Initial period: 150
[2025-10-07 19:15:40]   - Period multiplier: 2.0
[2025-10-07 19:15:40]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 19:15:40]   - Samples: 4096
[2025-10-07 19:15:40]   - Discarded samples: 0
[2025-10-07 19:15:40]   - Chunk size: 4096
[2025-10-07 19:15:40]   - Diagonal shift: 0.15
[2025-10-07 19:15:40]   - Gradient clipping: 1.0
[2025-10-07 19:15:40]   - Checkpoint enabled: interval=100
[2025-10-07 19:15:40]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.84/model_L6F4/training/checkpoints
[2025-10-07 19:15:40] ------------------------------------------------------------------------------------------------------
[2025-10-07 19:15:40] Device status:
[2025-10-07 19:15:40]   - Devices model: NVIDIA H200 NVL
[2025-10-07 19:15:40]   - Number of devices: 1
[2025-10-07 19:15:40]   - Sharding: True
[2025-10-07 19:15:41] ======================================================================================================
[2025-10-07 19:16:21] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -47.469814 | E_var:     0.4133 | E_err:   0.010045
[2025-10-07 19:16:48] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -47.452753 | E_var:     0.2668 | E_err:   0.008071
[2025-10-07 19:16:55] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -47.450470 | E_var:     0.2896 | E_err:   0.008408
[2025-10-07 19:17:03] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -47.461876 | E_var:     0.2046 | E_err:   0.007068
[2025-10-07 19:17:11] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -47.450939 | E_var:     0.2120 | E_err:   0.007194
[2025-10-07 19:17:19] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -47.449187 | E_var:     0.1878 | E_err:   0.006771
[2025-10-07 19:17:27] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -47.451637 | E_var:     0.1675 | E_err:   0.006394
[2025-10-07 19:17:35] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -47.457530 | E_var:     0.1532 | E_err:   0.006116
[2025-10-07 19:17:42] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -47.453337 | E_var:     0.1549 | E_err:   0.006150
[2025-10-07 19:17:50] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -47.462081 | E_var:     0.1699 | E_err:   0.006440
[2025-10-07 19:17:58] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -47.444964 | E_var:     0.1642 | E_err:   0.006332
[2025-10-07 19:18:06] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -47.456881 | E_var:     0.1407 | E_err:   0.005860
[2025-10-07 19:18:14] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -47.461461 | E_var:     0.1969 | E_err:   0.006933
[2025-10-07 19:18:21] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -47.456743 | E_var:     0.1900 | E_err:   0.006811
[2025-10-07 19:18:29] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -47.451821 | E_var:     0.2231 | E_err:   0.007380
[2025-10-07 19:18:37] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -47.460558 | E_var:     0.1703 | E_err:   0.006448
[2025-10-07 19:18:45] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -47.461938 | E_var:     0.1383 | E_err:   0.005810
[2025-10-07 19:18:53] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -47.459851 | E_var:     0.2198 | E_err:   0.007326
[2025-10-07 19:19:01] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -47.455801 | E_var:     0.1603 | E_err:   0.006257
[2025-10-07 19:19:08] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -47.449974 | E_var:     0.1572 | E_err:   0.006195
[2025-10-07 19:19:16] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -47.460896 | E_var:     0.1438 | E_err:   0.005925
[2025-10-07 19:19:24] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -47.439089 | E_var:     0.1842 | E_err:   0.006706
[2025-10-07 19:19:32] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -47.460407 | E_var:     0.1401 | E_err:   0.005849
[2025-10-07 19:19:40] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -47.469968 | E_var:     0.1415 | E_err:   0.005877
[2025-10-07 19:19:48] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -47.449740 | E_var:     0.1524 | E_err:   0.006100
[2025-10-07 19:19:55] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -47.453374 | E_var:     0.1515 | E_err:   0.006082
[2025-10-07 19:20:03] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -47.458012 | E_var:     0.1805 | E_err:   0.006638
[2025-10-07 19:20:11] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -47.452262 | E_var:     0.1391 | E_err:   0.005827
[2025-10-07 19:20:19] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -47.464595 | E_var:     0.2273 | E_err:   0.007450
[2025-10-07 19:20:27] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -47.455324 | E_var:     0.1407 | E_err:   0.005860
[2025-10-07 19:20:35] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -47.471400 | E_var:     0.2577 | E_err:   0.007931
[2025-10-07 19:20:42] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -47.467516 | E_var:     0.1574 | E_err:   0.006199
[2025-10-07 19:20:50] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -47.449550 | E_var:     0.1647 | E_err:   0.006341
[2025-10-07 19:20:58] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -47.465988 | E_var:     0.1742 | E_err:   0.006521
[2025-10-07 19:21:06] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -47.457859 | E_var:     0.1261 | E_err:   0.005548
[2025-10-07 19:21:14] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -47.445579 | E_var:     0.1553 | E_err:   0.006158
[2025-10-07 19:21:21] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -47.460176 | E_var:     0.2245 | E_err:   0.007404
[2025-10-07 19:21:29] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -47.452117 | E_var:     0.1656 | E_err:   0.006358
[2025-10-07 19:21:37] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -47.448731 | E_var:     0.1814 | E_err:   0.006655
[2025-10-07 19:21:45] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -47.456147 | E_var:     0.1646 | E_err:   0.006340
[2025-10-07 19:21:53] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -47.465295 | E_var:     0.1545 | E_err:   0.006142
[2025-10-07 19:22:01] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -47.458070 | E_var:     0.1601 | E_err:   0.006251
[2025-10-07 19:22:08] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -47.460445 | E_var:     0.1574 | E_err:   0.006199
[2025-10-07 19:22:16] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -47.444189 | E_var:     0.1532 | E_err:   0.006116
[2025-10-07 19:22:24] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -47.455219 | E_var:     0.1609 | E_err:   0.006268
[2025-10-07 19:22:32] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -47.468222 | E_var:     0.1501 | E_err:   0.006053
[2025-10-07 19:22:40] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -47.456590 | E_var:     0.1827 | E_err:   0.006678
[2025-10-07 19:22:47] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -47.454453 | E_var:     0.1527 | E_err:   0.006106
[2025-10-07 19:22:55] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -47.471849 | E_var:     0.1527 | E_err:   0.006105
[2025-10-07 19:23:03] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -47.457547 | E_var:     0.1463 | E_err:   0.005977
[2025-10-07 19:23:11] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -47.456151 | E_var:     0.1593 | E_err:   0.006237
[2025-10-07 19:23:19] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -47.452892 | E_var:     0.1477 | E_err:   0.006006
[2025-10-07 19:23:27] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -47.446983 | E_var:     0.1832 | E_err:   0.006687
[2025-10-07 19:23:34] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -47.465133 | E_var:     0.1627 | E_err:   0.006302
[2025-10-07 19:23:42] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -47.449788 | E_var:     0.2642 | E_err:   0.008031
[2025-10-07 19:23:50] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -47.463760 | E_var:     0.1628 | E_err:   0.006303
[2025-10-07 19:23:58] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -47.454267 | E_var:     0.1606 | E_err:   0.006262
[2025-10-07 19:24:06] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -47.461123 | E_var:     0.1447 | E_err:   0.005943
[2025-10-07 19:24:13] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -47.452922 | E_var:     0.1347 | E_err:   0.005734
[2025-10-07 19:24:21] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -47.455000 | E_var:     0.1715 | E_err:   0.006470
[2025-10-07 19:24:29] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -47.463129 | E_var:     0.1852 | E_err:   0.006724
[2025-10-07 19:24:37] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -47.462763 | E_var:     0.1448 | E_err:   0.005945
[2025-10-07 19:24:45] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -47.448012 | E_var:     0.1742 | E_err:   0.006521
[2025-10-07 19:24:53] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -47.448785 | E_var:     0.1558 | E_err:   0.006167
[2025-10-07 19:25:00] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -47.451098 | E_var:     0.1578 | E_err:   0.006206
[2025-10-07 19:25:08] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -47.455744 | E_var:     0.1329 | E_err:   0.005697
[2025-10-07 19:25:16] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -47.460384 | E_var:     0.1615 | E_err:   0.006278
[2025-10-07 19:25:24] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -47.449285 | E_var:     0.1495 | E_err:   0.006042
[2025-10-07 19:25:32] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -47.451855 | E_var:     0.1846 | E_err:   0.006712
[2025-10-07 19:25:39] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -47.450759 | E_var:     0.1730 | E_err:   0.006499
[2025-10-07 19:25:47] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -47.455917 | E_var:     0.1581 | E_err:   0.006212
[2025-10-07 19:25:55] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -47.455406 | E_var:     0.3034 | E_err:   0.008606
[2025-10-07 19:26:03] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -47.452517 | E_var:     0.1842 | E_err:   0.006705
[2025-10-07 19:26:11] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -47.452670 | E_var:     0.1785 | E_err:   0.006602
[2025-10-07 19:26:19] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -47.456033 | E_var:     0.1508 | E_err:   0.006067
[2025-10-07 19:26:26] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -47.451348 | E_var:     0.1482 | E_err:   0.006016
[2025-10-07 19:26:34] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -47.454601 | E_var:     0.1752 | E_err:   0.006541
[2025-10-07 19:26:42] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -47.454901 | E_var:     0.1414 | E_err:   0.005875
[2025-10-07 19:26:50] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -47.463116 | E_var:     0.1631 | E_err:   0.006310
[2025-10-07 19:26:58] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -47.444131 | E_var:     0.1786 | E_err:   0.006603
[2025-10-07 19:27:05] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -47.467191 | E_var:     0.1212 | E_err:   0.005440
[2025-10-07 19:27:13] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -47.455910 | E_var:     0.1730 | E_err:   0.006498
[2025-10-07 19:27:21] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -47.464424 | E_var:     0.2415 | E_err:   0.007678
[2025-10-07 19:27:29] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -47.458322 | E_var:     0.3716 | E_err:   0.009525
[2025-10-07 19:27:37] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -47.446525 | E_var:     0.1634 | E_err:   0.006315
[2025-10-07 19:27:44] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -47.449325 | E_var:     0.2156 | E_err:   0.007255
[2025-10-07 19:27:52] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -47.461480 | E_var:     0.1493 | E_err:   0.006037
[2025-10-07 19:28:00] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -47.450700 | E_var:     0.1409 | E_err:   0.005865
[2025-10-07 19:28:08] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -47.456689 | E_var:     0.1548 | E_err:   0.006148
[2025-10-07 19:28:16] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -47.457090 | E_var:     0.1441 | E_err:   0.005932
[2025-10-07 19:28:24] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -47.456433 | E_var:     0.1663 | E_err:   0.006371
[2025-10-07 19:28:31] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -47.458365 | E_var:     0.2470 | E_err:   0.007766
[2025-10-07 19:28:39] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -47.464362 | E_var:     0.1524 | E_err:   0.006100
[2025-10-07 19:28:47] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -47.449883 | E_var:     0.1457 | E_err:   0.005964
[2025-10-07 19:28:55] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -47.453751 | E_var:     0.1237 | E_err:   0.005495
[2025-10-07 19:29:03] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -47.466778 | E_var:     0.1512 | E_err:   0.006076
[2025-10-07 19:29:10] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -47.468065 | E_var:     0.3351 | E_err:   0.009045
[2025-10-07 19:29:18] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -47.460060 | E_var:     0.1811 | E_err:   0.006649
[2025-10-07 19:29:26] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -47.452767 | E_var:     0.1724 | E_err:   0.006487
[2025-10-07 19:29:34] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -47.453636 | E_var:     0.1602 | E_err:   0.006253
[2025-10-07 19:29:34] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 19:29:42] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -47.461423 | E_var:     0.1845 | E_err:   0.006712
[2025-10-07 19:29:50] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -47.447841 | E_var:     0.1859 | E_err:   0.006737
[2025-10-07 19:29:58] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -47.449063 | E_var:     0.1527 | E_err:   0.006106
[2025-10-07 19:30:05] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -47.468598 | E_var:     0.1418 | E_err:   0.005885
[2025-10-07 19:30:13] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -47.465340 | E_var:     0.1347 | E_err:   0.005735
[2025-10-07 19:30:21] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -47.457198 | E_var:     0.1440 | E_err:   0.005929
[2025-10-07 19:30:29] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -47.445877 | E_var:     0.2064 | E_err:   0.007099
[2025-10-07 19:30:37] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -47.456215 | E_var:     0.1336 | E_err:   0.005712
[2025-10-07 19:30:45] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -47.458070 | E_var:     0.1761 | E_err:   0.006558
[2025-10-07 19:30:52] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -47.474670 | E_var:     0.1901 | E_err:   0.006813
[2025-10-07 19:31:00] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -47.450585 | E_var:     0.1643 | E_err:   0.006333
[2025-10-07 19:31:08] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -47.465554 | E_var:     0.2089 | E_err:   0.007141
[2025-10-07 19:31:16] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -47.449378 | E_var:     0.1789 | E_err:   0.006609
[2025-10-07 19:31:24] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -47.454861 | E_var:     0.1922 | E_err:   0.006850
[2025-10-07 19:31:31] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -47.458778 | E_var:     0.1680 | E_err:   0.006404
[2025-10-07 19:31:39] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -47.435993 | E_var:     0.1681 | E_err:   0.006407
[2025-10-07 19:31:47] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -47.457235 | E_var:     0.2068 | E_err:   0.007106
[2025-10-07 19:31:55] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -47.465730 | E_var:     0.1914 | E_err:   0.006836
[2025-10-07 19:32:03] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -47.470597 | E_var:     0.1563 | E_err:   0.006177
[2025-10-07 19:32:10] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -47.462293 | E_var:     0.1796 | E_err:   0.006623
[2025-10-07 19:32:18] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -47.460151 | E_var:     0.2261 | E_err:   0.007430
[2025-10-07 19:32:26] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -47.461995 | E_var:     0.1525 | E_err:   0.006101
[2025-10-07 19:32:34] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -47.448874 | E_var:     0.1564 | E_err:   0.006180
[2025-10-07 19:32:42] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -47.451845 | E_var:     0.1953 | E_err:   0.006905
[2025-10-07 19:32:50] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -47.441571 | E_var:     0.1371 | E_err:   0.005785
[2025-10-07 19:32:57] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -47.442323 | E_var:     0.4158 | E_err:   0.010075
[2025-10-07 19:33:05] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -47.471634 | E_var:     0.1558 | E_err:   0.006168
[2025-10-07 19:33:13] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -47.449190 | E_var:     0.2171 | E_err:   0.007280
[2025-10-07 19:33:21] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -47.462950 | E_var:     0.1459 | E_err:   0.005969
[2025-10-07 19:33:29] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -47.452872 | E_var:     0.1623 | E_err:   0.006296
[2025-10-07 19:33:36] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -47.469103 | E_var:     0.1691 | E_err:   0.006425
[2025-10-07 19:33:44] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -47.453681 | E_var:     0.1486 | E_err:   0.006023
[2025-10-07 19:33:52] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -47.460777 | E_var:     0.1258 | E_err:   0.005542
[2025-10-07 19:34:00] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -47.452690 | E_var:     0.2169 | E_err:   0.007277
[2025-10-07 19:34:08] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -47.450262 | E_var:     0.1699 | E_err:   0.006440
[2025-10-07 19:34:16] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -47.463578 | E_var:     0.1492 | E_err:   0.006035
[2025-10-07 19:34:23] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -47.447139 | E_var:     0.1201 | E_err:   0.005414
[2025-10-07 19:34:31] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -47.467326 | E_var:     0.1856 | E_err:   0.006731
[2025-10-07 19:34:39] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -47.452724 | E_var:     0.1312 | E_err:   0.005660
[2025-10-07 19:34:47] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -47.439908 | E_var:     0.1529 | E_err:   0.006110
[2025-10-07 19:34:55] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -47.471092 | E_var:     0.1432 | E_err:   0.005913
[2025-10-07 19:35:02] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -47.465570 | E_var:     0.1645 | E_err:   0.006337
[2025-10-07 19:35:10] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -47.454734 | E_var:     0.1754 | E_err:   0.006545
[2025-10-07 19:35:18] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -47.465409 | E_var:     0.1314 | E_err:   0.005664
[2025-10-07 19:35:26] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -47.457539 | E_var:     0.1604 | E_err:   0.006258
[2025-10-07 19:35:34] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -47.459763 | E_var:     0.2560 | E_err:   0.007905
[2025-10-07 19:35:41] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -47.459222 | E_var:     0.1739 | E_err:   0.006515
[2025-10-07 19:35:49] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -47.458768 | E_var:     0.1514 | E_err:   0.006079
[2025-10-07 19:35:57] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -47.462667 | E_var:     0.1407 | E_err:   0.005860
[2025-10-07 19:36:05] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -47.456584 | E_var:     0.1563 | E_err:   0.006177
[2025-10-07 19:36:05] 🔄 RESTART #1 | Period: 300
[2025-10-07 19:36:13] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -47.466884 | E_var:     0.3407 | E_err:   0.009120
[2025-10-07 19:36:21] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -47.460730 | E_var:     0.1547 | E_err:   0.006145
[2025-10-07 19:36:28] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -47.442552 | E_var:     0.1522 | E_err:   0.006095
[2025-10-07 19:36:36] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -47.453875 | E_var:     0.1301 | E_err:   0.005636
[2025-10-07 19:36:44] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -47.449601 | E_var:     0.2046 | E_err:   0.007068
[2025-10-07 19:36:52] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -47.452504 | E_var:     0.1568 | E_err:   0.006188
[2025-10-07 19:37:00] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -47.446763 | E_var:     0.1681 | E_err:   0.006406
[2025-10-07 19:37:07] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -47.469018 | E_var:     0.2072 | E_err:   0.007113
[2025-10-07 19:37:15] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -47.454913 | E_var:     0.1487 | E_err:   0.006025
[2025-10-07 19:37:23] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -47.448028 | E_var:     0.1735 | E_err:   0.006508
[2025-10-07 19:37:31] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -47.469788 | E_var:     0.1570 | E_err:   0.006191
[2025-10-07 19:37:39] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -47.455534 | E_var:     0.1710 | E_err:   0.006462
[2025-10-07 19:37:47] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -47.443973 | E_var:     0.1402 | E_err:   0.005851
[2025-10-07 19:37:54] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -47.451487 | E_var:     0.1460 | E_err:   0.005971
[2025-10-07 19:38:02] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -47.463777 | E_var:     0.1635 | E_err:   0.006318
[2025-10-07 19:38:10] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -47.480399 | E_var:     0.2087 | E_err:   0.007137
[2025-10-07 19:38:18] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -47.454274 | E_var:     0.1592 | E_err:   0.006234
[2025-10-07 19:38:26] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -47.464950 | E_var:     0.2455 | E_err:   0.007741
[2025-10-07 19:38:33] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -47.460757 | E_var:     0.1631 | E_err:   0.006310
[2025-10-07 19:38:41] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -47.459992 | E_var:     0.1569 | E_err:   0.006189
[2025-10-07 19:38:49] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -47.450902 | E_var:     0.1486 | E_err:   0.006024
[2025-10-07 19:38:57] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -47.453216 | E_var:     0.1416 | E_err:   0.005879
[2025-10-07 19:39:05] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -47.465143 | E_var:     0.1581 | E_err:   0.006212
[2025-10-07 19:39:12] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -47.449665 | E_var:     0.1465 | E_err:   0.005980
[2025-10-07 19:39:20] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -47.458062 | E_var:     0.1662 | E_err:   0.006369
[2025-10-07 19:39:28] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -47.464793 | E_var:     0.1907 | E_err:   0.006823
[2025-10-07 19:39:36] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -47.458713 | E_var:     0.2472 | E_err:   0.007769
[2025-10-07 19:39:44] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -47.454951 | E_var:     0.1718 | E_err:   0.006476
[2025-10-07 19:39:52] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -47.462789 | E_var:     0.1292 | E_err:   0.005616
[2025-10-07 19:39:59] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -47.442197 | E_var:     0.1684 | E_err:   0.006412
[2025-10-07 19:40:07] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -47.465196 | E_var:     0.1316 | E_err:   0.005668
[2025-10-07 19:40:15] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -47.460498 | E_var:     0.1646 | E_err:   0.006340
[2025-10-07 19:40:23] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -47.448175 | E_var:     0.1692 | E_err:   0.006427
[2025-10-07 19:40:31] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -47.457294 | E_var:     0.1422 | E_err:   0.005892
[2025-10-07 19:40:38] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -47.465407 | E_var:     0.1737 | E_err:   0.006511
[2025-10-07 19:40:46] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -47.455268 | E_var:     0.2241 | E_err:   0.007397
[2025-10-07 19:40:54] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -47.459972 | E_var:     0.1504 | E_err:   0.006059
[2025-10-07 19:41:02] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -47.454107 | E_var:     0.1302 | E_err:   0.005637
[2025-10-07 19:41:10] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -47.451462 | E_var:     0.1371 | E_err:   0.005786
[2025-10-07 19:41:18] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -47.451608 | E_var:     0.1710 | E_err:   0.006462
[2025-10-07 19:41:25] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -47.454611 | E_var:     0.2008 | E_err:   0.007001
[2025-10-07 19:41:33] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -47.462718 | E_var:     0.1904 | E_err:   0.006818
[2025-10-07 19:41:41] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -47.454171 | E_var:     0.1719 | E_err:   0.006479
[2025-10-07 19:41:49] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -47.462155 | E_var:     0.1713 | E_err:   0.006467
[2025-10-07 19:41:57] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -47.458510 | E_var:     0.1877 | E_err:   0.006770
[2025-10-07 19:42:04] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -47.450962 | E_var:     0.1619 | E_err:   0.006288
[2025-10-07 19:42:12] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -47.453691 | E_var:     0.1426 | E_err:   0.005901
[2025-10-07 19:42:20] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -47.449277 | E_var:     0.1565 | E_err:   0.006181
[2025-10-07 19:42:28] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -47.464592 | E_var:     0.1662 | E_err:   0.006369
[2025-10-07 19:42:36] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -47.450065 | E_var:     0.1568 | E_err:   0.006187
[2025-10-07 19:42:36] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 19:42:43] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -47.452520 | E_var:     0.1580 | E_err:   0.006211
[2025-10-07 19:42:51] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -47.443201 | E_var:     0.1839 | E_err:   0.006701
[2025-10-07 19:42:59] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -47.454376 | E_var:     0.2354 | E_err:   0.007582
[2025-10-07 19:43:07] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -47.444284 | E_var:     0.2014 | E_err:   0.007012
[2025-10-07 19:43:15] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -47.455542 | E_var:     0.1718 | E_err:   0.006476
[2025-10-07 19:43:23] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -47.458071 | E_var:     0.1620 | E_err:   0.006289
[2025-10-07 19:43:30] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -47.451863 | E_var:     0.1771 | E_err:   0.006576
[2025-10-07 19:43:38] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -47.463473 | E_var:     0.1681 | E_err:   0.006406
[2025-10-07 19:43:46] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -47.447722 | E_var:     0.1420 | E_err:   0.005888
[2025-10-07 19:43:54] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -47.456300 | E_var:     0.2404 | E_err:   0.007661
[2025-10-07 19:44:02] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -47.460892 | E_var:     0.1624 | E_err:   0.006297
[2025-10-07 19:44:09] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -47.450061 | E_var:     0.1509 | E_err:   0.006069
[2025-10-07 19:44:17] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -47.445243 | E_var:     0.1781 | E_err:   0.006595
[2025-10-07 19:44:25] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -47.455159 | E_var:     0.1520 | E_err:   0.006093
[2025-10-07 19:44:33] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -47.456574 | E_var:     0.1912 | E_err:   0.006833
[2025-10-07 19:44:41] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -47.453317 | E_var:     0.1488 | E_err:   0.006028
[2025-10-07 19:44:49] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -47.462363 | E_var:     0.1735 | E_err:   0.006508
[2025-10-07 19:44:56] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -47.446703 | E_var:     0.1578 | E_err:   0.006207
[2025-10-07 19:45:04] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -47.445768 | E_var:     0.1588 | E_err:   0.006227
[2025-10-07 19:45:12] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -47.447872 | E_var:     0.1604 | E_err:   0.006257
[2025-10-07 19:45:20] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -47.449823 | E_var:     0.1852 | E_err:   0.006725
[2025-10-07 19:45:28] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -47.443116 | E_var:     0.1797 | E_err:   0.006624
[2025-10-07 19:45:35] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -47.448104 | E_var:     0.2430 | E_err:   0.007703
[2025-10-07 19:45:43] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -47.452957 | E_var:     0.1642 | E_err:   0.006332
[2025-10-07 19:45:51] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -47.459825 | E_var:     0.1692 | E_err:   0.006428
[2025-10-07 19:45:59] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -47.452054 | E_var:     0.1326 | E_err:   0.005689
[2025-10-07 19:46:07] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -47.437239 | E_var:     0.2200 | E_err:   0.007328
[2025-10-07 19:46:14] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -47.458202 | E_var:     0.1416 | E_err:   0.005880
[2025-10-07 19:46:22] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -47.453383 | E_var:     0.1445 | E_err:   0.005940
[2025-10-07 19:46:30] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -47.453503 | E_var:     0.1564 | E_err:   0.006179
[2025-10-07 19:46:38] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -47.450339 | E_var:     0.1958 | E_err:   0.006914
[2025-10-07 19:46:46] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -47.456296 | E_var:     0.1540 | E_err:   0.006131
[2025-10-07 19:46:54] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -47.458428 | E_var:     0.1780 | E_err:   0.006591
[2025-10-07 19:47:01] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -47.452333 | E_var:     0.1555 | E_err:   0.006162
[2025-10-07 19:47:09] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -47.464537 | E_var:     0.1461 | E_err:   0.005972
[2025-10-07 19:47:17] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -47.444293 | E_var:     0.2218 | E_err:   0.007359
[2025-10-07 19:47:25] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -47.461540 | E_var:     0.1465 | E_err:   0.005981
[2025-10-07 19:47:33] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -47.460976 | E_var:     0.1540 | E_err:   0.006132
[2025-10-07 19:47:40] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -47.462589 | E_var:     0.1718 | E_err:   0.006477
[2025-10-07 19:47:48] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -47.462944 | E_var:     0.1497 | E_err:   0.006046
[2025-10-07 19:47:56] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -47.459365 | E_var:     0.1882 | E_err:   0.006778
[2025-10-07 19:48:04] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -47.458026 | E_var:     0.1501 | E_err:   0.006054
[2025-10-07 19:48:12] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -47.457873 | E_var:     0.1740 | E_err:   0.006517
[2025-10-07 19:48:20] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -47.451107 | E_var:     0.1682 | E_err:   0.006408
[2025-10-07 19:48:28] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -47.458751 | E_var:     0.2108 | E_err:   0.007173
[2025-10-07 19:48:35] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -47.454660 | E_var:     0.1555 | E_err:   0.006161
[2025-10-07 19:48:43] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -47.454850 | E_var:     0.1367 | E_err:   0.005777
[2025-10-07 19:48:51] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -47.446959 | E_var:     0.2315 | E_err:   0.007518
[2025-10-07 19:48:59] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -47.463401 | E_var:     0.1681 | E_err:   0.006406
[2025-10-07 19:49:07] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -47.464164 | E_var:     0.1638 | E_err:   0.006325
[2025-10-07 19:49:14] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -47.467829 | E_var:     0.1598 | E_err:   0.006246
[2025-10-07 19:49:22] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -47.452281 | E_var:     0.1578 | E_err:   0.006206
[2025-10-07 19:49:30] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -47.454120 | E_var:     0.1370 | E_err:   0.005784
[2025-10-07 19:49:38] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -47.453905 | E_var:     0.1727 | E_err:   0.006493
[2025-10-07 19:49:46] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -47.462056 | E_var:     0.2241 | E_err:   0.007396
[2025-10-07 19:49:54] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -47.454323 | E_var:     0.1348 | E_err:   0.005737
[2025-10-07 19:50:01] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -47.445496 | E_var:     0.3331 | E_err:   0.009018
[2025-10-07 19:50:09] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -47.448567 | E_var:     0.1573 | E_err:   0.006197
[2025-10-07 19:50:17] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -47.450726 | E_var:     0.2436 | E_err:   0.007711
[2025-10-07 19:50:25] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -47.470366 | E_var:     0.1524 | E_err:   0.006100
[2025-10-07 19:50:33] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -47.453634 | E_var:     0.1632 | E_err:   0.006312
[2025-10-07 19:50:40] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -47.451218 | E_var:     0.4056 | E_err:   0.009951
[2025-10-07 19:50:48] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -47.458080 | E_var:     0.1447 | E_err:   0.005943
[2025-10-07 19:50:56] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -47.455524 | E_var:     0.2073 | E_err:   0.007114
[2025-10-07 19:51:04] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -47.455079 | E_var:     0.1435 | E_err:   0.005919
[2025-10-07 19:51:12] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -47.462484 | E_var:     0.2402 | E_err:   0.007658
[2025-10-07 19:51:19] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -47.441365 | E_var:     0.1915 | E_err:   0.006838
[2025-10-07 19:51:27] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -47.447042 | E_var:     0.1901 | E_err:   0.006812
[2025-10-07 19:51:35] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -47.453735 | E_var:     0.1500 | E_err:   0.006052
[2025-10-07 19:51:43] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -47.452905 | E_var:     0.1537 | E_err:   0.006126
[2025-10-07 19:51:51] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -47.466042 | E_var:     0.1512 | E_err:   0.006076
[2025-10-07 19:51:59] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -47.457065 | E_var:     0.2165 | E_err:   0.007270
[2025-10-07 19:52:06] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -47.450327 | E_var:     0.1466 | E_err:   0.005982
[2025-10-07 19:52:14] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -47.453185 | E_var:     0.1513 | E_err:   0.006078
[2025-10-07 19:52:22] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -47.457263 | E_var:     0.1851 | E_err:   0.006722
[2025-10-07 19:52:30] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -47.459789 | E_var:     0.1505 | E_err:   0.006062
[2025-10-07 19:52:38] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -47.450987 | E_var:     0.1524 | E_err:   0.006100
[2025-10-07 19:52:45] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -47.455840 | E_var:     0.1703 | E_err:   0.006448
[2025-10-07 19:52:53] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -47.456725 | E_var:     0.2466 | E_err:   0.007759
[2025-10-07 19:53:01] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -47.466770 | E_var:     0.2342 | E_err:   0.007562
[2025-10-07 19:53:09] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -47.461692 | E_var:     0.1599 | E_err:   0.006248
[2025-10-07 19:53:17] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -47.450170 | E_var:     0.1567 | E_err:   0.006186
[2025-10-07 19:53:24] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -47.458248 | E_var:     0.1470 | E_err:   0.005990
[2025-10-07 19:53:32] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -47.469492 | E_var:     0.1772 | E_err:   0.006578
[2025-10-07 19:53:40] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -47.454030 | E_var:     0.1360 | E_err:   0.005761
[2025-10-07 19:53:48] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -47.457391 | E_var:     0.1585 | E_err:   0.006222
[2025-10-07 19:53:56] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -47.460556 | E_var:     0.1732 | E_err:   0.006503
[2025-10-07 19:54:04] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -47.451004 | E_var:     0.1526 | E_err:   0.006104
[2025-10-07 19:54:11] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -47.459010 | E_var:     0.1455 | E_err:   0.005961
[2025-10-07 19:54:19] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -47.463358 | E_var:     0.1994 | E_err:   0.006978
[2025-10-07 19:54:27] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -47.460482 | E_var:     0.1997 | E_err:   0.006983
[2025-10-07 19:54:35] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -47.459773 | E_var:     0.2428 | E_err:   0.007700
[2025-10-07 19:54:43] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -47.453180 | E_var:     0.1396 | E_err:   0.005837
[2025-10-07 19:54:50] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -47.454066 | E_var:     0.3529 | E_err:   0.009282
[2025-10-07 19:54:58] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -47.453772 | E_var:     0.1869 | E_err:   0.006755
[2025-10-07 19:55:06] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -47.447865 | E_var:     0.1219 | E_err:   0.005455
[2025-10-07 19:55:14] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -47.445912 | E_var:     0.1545 | E_err:   0.006142
[2025-10-07 19:55:22] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -47.451042 | E_var:     0.1470 | E_err:   0.005991
[2025-10-07 19:55:29] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -47.463178 | E_var:     0.2002 | E_err:   0.006991
[2025-10-07 19:55:37] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -47.456573 | E_var:     0.1525 | E_err:   0.006102
[2025-10-07 19:55:37] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 19:55:45] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -47.458521 | E_var:     0.1565 | E_err:   0.006181
[2025-10-07 19:55:53] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -47.455319 | E_var:     0.1494 | E_err:   0.006040
[2025-10-07 19:56:01] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -47.459768 | E_var:     0.1817 | E_err:   0.006661
[2025-10-07 19:56:09] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -47.467564 | E_var:     0.2296 | E_err:   0.007487
[2025-10-07 19:56:16] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -47.463284 | E_var:     0.1475 | E_err:   0.006001
[2025-10-07 19:56:24] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -47.461037 | E_var:     0.2550 | E_err:   0.007891
[2025-10-07 19:56:32] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -47.463694 | E_var:     0.1592 | E_err:   0.006234
[2025-10-07 19:56:40] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -47.459641 | E_var:     0.1568 | E_err:   0.006186
[2025-10-07 19:56:48] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -47.461324 | E_var:     0.3745 | E_err:   0.009562
[2025-10-07 19:56:56] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -47.460122 | E_var:     0.1678 | E_err:   0.006401
[2025-10-07 19:57:03] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -47.447988 | E_var:     0.2987 | E_err:   0.008540
[2025-10-07 19:57:11] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -47.480497 | E_var:     0.3440 | E_err:   0.009164
[2025-10-07 19:57:19] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -47.457663 | E_var:     0.2048 | E_err:   0.007071
[2025-10-07 19:57:27] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -47.468559 | E_var:     0.1848 | E_err:   0.006717
[2025-10-07 19:57:35] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -47.454693 | E_var:     0.1522 | E_err:   0.006096
[2025-10-07 19:57:42] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -47.461936 | E_var:     0.1714 | E_err:   0.006469
[2025-10-07 19:57:50] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -47.454629 | E_var:     0.1372 | E_err:   0.005788
[2025-10-07 19:57:58] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -47.453431 | E_var:     0.1497 | E_err:   0.006046
[2025-10-07 19:58:06] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -47.446546 | E_var:     0.2310 | E_err:   0.007510
[2025-10-07 19:58:14] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -47.456385 | E_var:     0.1258 | E_err:   0.005543
[2025-10-07 19:58:21] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -47.443405 | E_var:     0.3066 | E_err:   0.008652
[2025-10-07 19:58:29] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -47.456714 | E_var:     0.1760 | E_err:   0.006555
[2025-10-07 19:58:37] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -47.455099 | E_var:     0.1200 | E_err:   0.005412
[2025-10-07 19:58:45] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -47.459854 | E_var:     0.1721 | E_err:   0.006481
[2025-10-07 19:58:53] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -47.458417 | E_var:     0.1814 | E_err:   0.006656
[2025-10-07 19:59:01] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -47.452129 | E_var:     0.1452 | E_err:   0.005953
[2025-10-07 19:59:08] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -47.457710 | E_var:     0.2032 | E_err:   0.007044
[2025-10-07 19:59:16] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -47.459989 | E_var:     0.1746 | E_err:   0.006529
[2025-10-07 19:59:24] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -47.449720 | E_var:     0.2373 | E_err:   0.007611
[2025-10-07 19:59:32] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -47.456882 | E_var:     0.2201 | E_err:   0.007331
[2025-10-07 19:59:40] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -47.447661 | E_var:     0.1463 | E_err:   0.005976
[2025-10-07 19:59:47] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -47.447488 | E_var:     0.1780 | E_err:   0.006593
[2025-10-07 19:59:55] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -47.458329 | E_var:     0.1728 | E_err:   0.006495
[2025-10-07 20:00:03] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -47.450247 | E_var:     0.1418 | E_err:   0.005884
[2025-10-07 20:00:11] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -47.458854 | E_var:     0.1868 | E_err:   0.006754
[2025-10-07 20:00:19] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -47.452216 | E_var:     0.1438 | E_err:   0.005924
[2025-10-07 20:00:27] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -47.445630 | E_var:     0.1862 | E_err:   0.006742
[2025-10-07 20:00:34] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -47.448756 | E_var:     0.1463 | E_err:   0.005976
[2025-10-07 20:00:42] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -47.447254 | E_var:     0.1315 | E_err:   0.005667
[2025-10-07 20:00:50] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -47.459960 | E_var:     0.1885 | E_err:   0.006784
[2025-10-07 20:00:58] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -47.442571 | E_var:     0.1816 | E_err:   0.006659
[2025-10-07 20:01:06] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -47.455478 | E_var:     0.1537 | E_err:   0.006125
[2025-10-07 20:01:13] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -47.471653 | E_var:     0.1671 | E_err:   0.006388
[2025-10-07 20:01:21] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -47.451604 | E_var:     0.2100 | E_err:   0.007160
[2025-10-07 20:01:29] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -47.456724 | E_var:     0.1344 | E_err:   0.005728
[2025-10-07 20:01:37] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -47.451777 | E_var:     0.2266 | E_err:   0.007438
[2025-10-07 20:01:45] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -47.467196 | E_var:     0.1752 | E_err:   0.006540
[2025-10-07 20:01:52] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -47.456524 | E_var:     0.1766 | E_err:   0.006565
[2025-10-07 20:02:00] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -47.450119 | E_var:     0.1703 | E_err:   0.006449
[2025-10-07 20:02:08] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -47.456640 | E_var:     0.1571 | E_err:   0.006193
[2025-10-07 20:02:16] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -47.440567 | E_var:     0.1747 | E_err:   0.006532
[2025-10-07 20:02:24] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -47.449731 | E_var:     0.1606 | E_err:   0.006262
[2025-10-07 20:02:32] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -47.451727 | E_var:     0.1595 | E_err:   0.006240
[2025-10-07 20:02:39] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -47.458311 | E_var:     0.1647 | E_err:   0.006342
[2025-10-07 20:02:47] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -47.451774 | E_var:     0.2489 | E_err:   0.007795
[2025-10-07 20:02:55] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -47.451767 | E_var:     0.1771 | E_err:   0.006575
[2025-10-07 20:03:03] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -47.457342 | E_var:     0.1583 | E_err:   0.006218
[2025-10-07 20:03:11] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -47.453616 | E_var:     0.1771 | E_err:   0.006575
[2025-10-07 20:03:18] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -47.459997 | E_var:     0.2332 | E_err:   0.007546
[2025-10-07 20:03:26] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -47.453039 | E_var:     0.1550 | E_err:   0.006151
[2025-10-07 20:03:34] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -47.448018 | E_var:     0.1885 | E_err:   0.006783
[2025-10-07 20:03:42] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -47.449806 | E_var:     0.1853 | E_err:   0.006725
[2025-10-07 20:03:50] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -47.455829 | E_var:     0.1758 | E_err:   0.006552
[2025-10-07 20:03:57] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -47.448624 | E_var:     0.1420 | E_err:   0.005888
[2025-10-07 20:04:05] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -47.473638 | E_var:     0.1280 | E_err:   0.005590
[2025-10-07 20:04:13] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -47.456261 | E_var:     0.1534 | E_err:   0.006119
[2025-10-07 20:04:21] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -47.458326 | E_var:     0.1823 | E_err:   0.006671
[2025-10-07 20:04:29] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -47.455687 | E_var:     0.1572 | E_err:   0.006195
[2025-10-07 20:04:37] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -47.456771 | E_var:     0.1486 | E_err:   0.006023
[2025-10-07 20:04:44] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -47.443126 | E_var:     0.1595 | E_err:   0.006241
[2025-10-07 20:04:52] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -47.459739 | E_var:     0.1626 | E_err:   0.006301
[2025-10-07 20:05:00] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -47.464188 | E_var:     0.1775 | E_err:   0.006584
[2025-10-07 20:05:08] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -47.444354 | E_var:     0.1830 | E_err:   0.006685
[2025-10-07 20:05:16] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -47.460638 | E_var:     0.1844 | E_err:   0.006709
[2025-10-07 20:05:23] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -47.453278 | E_var:     0.2652 | E_err:   0.008047
[2025-10-07 20:05:31] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -47.459532 | E_var:     0.2209 | E_err:   0.007343
[2025-10-07 20:05:39] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -47.456474 | E_var:     0.1490 | E_err:   0.006031
[2025-10-07 20:05:47] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -47.468349 | E_var:     0.1533 | E_err:   0.006117
[2025-10-07 20:05:55] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -47.453890 | E_var:     0.1562 | E_err:   0.006176
[2025-10-07 20:06:03] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -47.462487 | E_var:     0.1332 | E_err:   0.005703
[2025-10-07 20:06:10] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -47.452601 | E_var:     0.1322 | E_err:   0.005681
[2025-10-07 20:06:18] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -47.461296 | E_var:     0.1450 | E_err:   0.005951
[2025-10-07 20:06:26] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -47.455230 | E_var:     0.1229 | E_err:   0.005477
[2025-10-07 20:06:34] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -47.441449 | E_var:     0.1866 | E_err:   0.006750
[2025-10-07 20:06:42] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -47.457175 | E_var:     0.1537 | E_err:   0.006125
[2025-10-07 20:06:49] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -47.464226 | E_var:     0.1551 | E_err:   0.006153
[2025-10-07 20:06:57] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -47.457322 | E_var:     0.1707 | E_err:   0.006456
