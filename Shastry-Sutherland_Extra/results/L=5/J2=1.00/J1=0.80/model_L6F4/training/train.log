[2025-10-07 10:02:13] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.79/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 10:02:13]   - 迭代次数: final
[2025-10-07 10:02:13]   - 能量: -44.235225+0.000697j ± 0.008509, Var: 0.296592
[2025-10-07 10:02:13]   - 时间戳: 2025-10-07T10:01:48.890979+08:00
[2025-10-07 10:02:35] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 10:02:35] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 10:02:35] ======================================================================================================
[2025-10-07 10:02:35] GCNN for Shastry-Sutherland Model
[2025-10-07 10:02:35] ======================================================================================================
[2025-10-07 10:02:35] System parameters:
[2025-10-07 10:02:35]   - System size: L=5, N=100
[2025-10-07 10:02:35]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-10-07 10:02:35] ------------------------------------------------------------------------------------------------------
[2025-10-07 10:02:35] Model parameters:
[2025-10-07 10:02:35]   - Number of layers = 6
[2025-10-07 10:02:35]   - Number of features = 4
[2025-10-07 10:02:35]   - Total parameters = 32444
[2025-10-07 10:02:35] ------------------------------------------------------------------------------------------------------
[2025-10-07 10:02:35] Training parameters:
[2025-10-07 10:02:35]   - Total iterations: 1050
[2025-10-07 10:02:35]   - Annealing cycles: 3
[2025-10-07 10:02:35]   - Initial period: 150
[2025-10-07 10:02:35]   - Period multiplier: 2.0
[2025-10-07 10:02:35]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 10:02:35]   - Samples: 4096
[2025-10-07 10:02:35]   - Discarded samples: 0
[2025-10-07 10:02:35]   - Chunk size: 4096
[2025-10-07 10:02:35]   - Diagonal shift: 0.15
[2025-10-07 10:02:35]   - Gradient clipping: 1.0
[2025-10-07 10:02:35]   - Checkpoint enabled: interval=100
[2025-10-07 10:02:35]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.80/model_L6F4/training/checkpoints
[2025-10-07 10:02:35] ------------------------------------------------------------------------------------------------------
[2025-10-07 10:02:35] Device status:
[2025-10-07 10:02:35]   - Devices model: NVIDIA H200 NVL
[2025-10-07 10:02:35]   - Number of devices: 1
[2025-10-07 10:02:35]   - Sharding: True
[2025-10-07 10:02:35] ======================================================================================================
[2025-10-07 10:03:16] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -44.895529 | E_var:     0.6838 | E_err:   0.012921
[2025-10-07 10:03:43] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -44.879556 | E_var:     0.4754 | E_err:   0.010773
[2025-10-07 10:03:50] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -44.876876 | E_var:     0.5016 | E_err:   0.011066
[2025-10-07 10:03:58] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -44.886310 | E_var:     0.3463 | E_err:   0.009195
[2025-10-07 10:04:06] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -44.864946 | E_var:     0.2892 | E_err:   0.008403
[2025-10-07 10:04:14] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -44.874907 | E_var:     0.2968 | E_err:   0.008512
[2025-10-07 10:04:22] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -44.877716 | E_var:     0.2987 | E_err:   0.008539
[2025-10-07 10:04:30] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -44.868875 | E_var:     0.3135 | E_err:   0.008748
[2025-10-07 10:04:37] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -44.879228 | E_var:     0.2309 | E_err:   0.007509
[2025-10-07 10:04:45] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -44.861158 | E_var:     0.3296 | E_err:   0.008971
[2025-10-07 10:04:53] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -44.873792 | E_var:     0.2235 | E_err:   0.007386
[2025-10-07 10:05:01] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -44.871141 | E_var:     0.2268 | E_err:   0.007442
[2025-10-07 10:05:09] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -44.874645 | E_var:     0.2634 | E_err:   0.008020
[2025-10-07 10:05:16] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -44.870172 | E_var:     0.2195 | E_err:   0.007321
[2025-10-07 10:05:24] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -44.865275 | E_var:     0.3259 | E_err:   0.008920
[2025-10-07 10:05:32] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -44.883213 | E_var:     0.3183 | E_err:   0.008816
[2025-10-07 10:05:40] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -44.880611 | E_var:     0.2300 | E_err:   0.007494
[2025-10-07 10:05:48] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -44.869269 | E_var:     0.1997 | E_err:   0.006983
[2025-10-07 10:05:55] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -44.877865 | E_var:     0.3278 | E_err:   0.008946
[2025-10-07 10:06:03] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -44.882947 | E_var:     0.1860 | E_err:   0.006739
[2025-10-07 10:06:11] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -44.875646 | E_var:     0.2331 | E_err:   0.007544
[2025-10-07 10:06:19] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -44.884645 | E_var:     0.2350 | E_err:   0.007575
[2025-10-07 10:06:27] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -44.872042 | E_var:     0.2741 | E_err:   0.008181
[2025-10-07 10:06:34] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -44.877563 | E_var:     0.2612 | E_err:   0.007986
[2025-10-07 10:06:42] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -44.879035 | E_var:     0.2503 | E_err:   0.007816
[2025-10-07 10:06:50] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -44.889946 | E_var:     0.2542 | E_err:   0.007878
[2025-10-07 10:06:58] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -44.869035 | E_var:     0.2311 | E_err:   0.007511
[2025-10-07 10:07:06] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -44.860908 | E_var:     0.3293 | E_err:   0.008967
[2025-10-07 10:07:13] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -44.886459 | E_var:     0.2309 | E_err:   0.007509
[2025-10-07 10:07:21] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -44.876928 | E_var:     0.2379 | E_err:   0.007622
[2025-10-07 10:07:29] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -44.887173 | E_var:     0.1924 | E_err:   0.006854
[2025-10-07 10:07:37] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -44.879578 | E_var:     0.2264 | E_err:   0.007435
[2025-10-07 10:07:45] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -44.878933 | E_var:     0.2513 | E_err:   0.007832
[2025-10-07 10:07:52] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -44.853312 | E_var:     0.7353 | E_err:   0.013399
[2025-10-07 10:08:00] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -44.878461 | E_var:     0.2529 | E_err:   0.007857
[2025-10-07 10:08:08] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -44.873427 | E_var:     0.2779 | E_err:   0.008237
[2025-10-07 10:08:16] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -44.871753 | E_var:     0.3045 | E_err:   0.008622
[2025-10-07 10:08:24] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -44.873230 | E_var:     0.2146 | E_err:   0.007238
[2025-10-07 10:08:31] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -44.863230 | E_var:     0.3540 | E_err:   0.009296
[2025-10-07 10:08:39] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -44.855805 | E_var:     0.2133 | E_err:   0.007216
[2025-10-07 10:08:47] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -44.859190 | E_var:     0.2990 | E_err:   0.008543
[2025-10-07 10:08:55] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -44.887796 | E_var:     0.2173 | E_err:   0.007284
[2025-10-07 10:09:03] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -44.885083 | E_var:     0.2322 | E_err:   0.007530
[2025-10-07 10:09:10] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -44.878406 | E_var:     0.2154 | E_err:   0.007251
[2025-10-07 10:09:18] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -44.875578 | E_var:     0.2573 | E_err:   0.007926
[2025-10-07 10:09:26] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -44.862693 | E_var:     0.2853 | E_err:   0.008346
[2025-10-07 10:09:34] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -44.884800 | E_var:     0.2273 | E_err:   0.007450
[2025-10-07 10:09:42] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -44.876858 | E_var:     0.2155 | E_err:   0.007254
[2025-10-07 10:09:49] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -44.868779 | E_var:     0.2407 | E_err:   0.007665
[2025-10-07 10:09:57] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -44.860107 | E_var:     0.2257 | E_err:   0.007422
[2025-10-07 10:10:05] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -44.888538 | E_var:     0.3382 | E_err:   0.009087
[2025-10-07 10:10:13] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -44.877881 | E_var:     0.2010 | E_err:   0.007006
[2025-10-07 10:10:21] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -44.888603 | E_var:     0.2408 | E_err:   0.007667
[2025-10-07 10:10:28] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -44.870849 | E_var:     0.3212 | E_err:   0.008855
[2025-10-07 10:10:36] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -44.877821 | E_var:     0.2193 | E_err:   0.007317
[2025-10-07 10:10:44] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -44.861123 | E_var:     0.2085 | E_err:   0.007134
[2025-10-07 10:10:52] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -44.867087 | E_var:     0.2254 | E_err:   0.007418
[2025-10-07 10:11:00] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -44.882863 | E_var:     0.2611 | E_err:   0.007983
[2025-10-07 10:11:07] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -44.881560 | E_var:     0.2266 | E_err:   0.007438
[2025-10-07 10:11:15] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -44.891273 | E_var:     0.2363 | E_err:   0.007595
[2025-10-07 10:11:23] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -44.870460 | E_var:     0.2371 | E_err:   0.007608
[2025-10-07 10:11:31] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -44.877416 | E_var:     0.2442 | E_err:   0.007721
[2025-10-07 10:11:39] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -44.869763 | E_var:     0.2532 | E_err:   0.007862
[2025-10-07 10:11:46] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -44.883358 | E_var:     0.2181 | E_err:   0.007297
[2025-10-07 10:11:54] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -44.881101 | E_var:     0.2721 | E_err:   0.008150
[2025-10-07 10:12:02] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -44.855018 | E_var:     0.4968 | E_err:   0.011013
[2025-10-07 10:12:10] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -44.894536 | E_var:     0.1991 | E_err:   0.006973
[2025-10-07 10:12:18] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -44.864711 | E_var:     0.2581 | E_err:   0.007937
[2025-10-07 10:12:25] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -44.875600 | E_var:     0.2290 | E_err:   0.007477
[2025-10-07 10:12:33] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -44.888431 | E_var:     0.1959 | E_err:   0.006917
[2025-10-07 10:12:41] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -44.880487 | E_var:     0.2359 | E_err:   0.007589
[2025-10-07 10:12:49] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -44.872245 | E_var:     0.2702 | E_err:   0.008121
[2025-10-07 10:12:57] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -44.872238 | E_var:     0.2525 | E_err:   0.007851
[2025-10-07 10:13:04] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -44.874457 | E_var:     0.2728 | E_err:   0.008160
[2025-10-07 10:13:12] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -44.880007 | E_var:     0.1926 | E_err:   0.006858
[2025-10-07 10:13:20] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -44.886881 | E_var:     0.2561 | E_err:   0.007907
[2025-10-07 10:13:28] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -44.883689 | E_var:     0.2250 | E_err:   0.007412
[2025-10-07 10:13:36] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -44.876411 | E_var:     0.1954 | E_err:   0.006907
[2025-10-07 10:13:43] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -44.883854 | E_var:     0.2224 | E_err:   0.007369
[2025-10-07 10:13:51] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -44.880587 | E_var:     0.2274 | E_err:   0.007452
[2025-10-07 10:13:59] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -44.872171 | E_var:     0.2121 | E_err:   0.007196
[2025-10-07 10:14:07] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -44.892341 | E_var:     0.3096 | E_err:   0.008694
[2025-10-07 10:14:15] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -44.875003 | E_var:     0.2181 | E_err:   0.007297
[2025-10-07 10:14:22] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -44.884014 | E_var:     0.2375 | E_err:   0.007615
[2025-10-07 10:14:30] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -44.880226 | E_var:     0.2536 | E_err:   0.007869
[2025-10-07 10:14:38] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -44.871062 | E_var:     0.2598 | E_err:   0.007964
[2025-10-07 10:14:46] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -44.884059 | E_var:     0.3908 | E_err:   0.009768
[2025-10-07 10:14:54] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -44.873676 | E_var:     0.2121 | E_err:   0.007195
[2025-10-07 10:15:01] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -44.882127 | E_var:     0.2415 | E_err:   0.007679
[2025-10-07 10:15:09] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -44.879926 | E_var:     0.2011 | E_err:   0.007007
[2025-10-07 10:15:17] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -44.869282 | E_var:     0.2130 | E_err:   0.007212
[2025-10-07 10:15:25] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -44.872561 | E_var:     0.2175 | E_err:   0.007287
[2025-10-07 10:15:33] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -44.867956 | E_var:     0.6743 | E_err:   0.012830
[2025-10-07 10:15:40] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -44.869549 | E_var:     0.2418 | E_err:   0.007683
[2025-10-07 10:15:48] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -44.869265 | E_var:     0.2219 | E_err:   0.007360
[2025-10-07 10:15:56] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -44.878914 | E_var:     0.2734 | E_err:   0.008170
[2025-10-07 10:16:04] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -44.890583 | E_var:     0.2609 | E_err:   0.007980
[2025-10-07 10:16:12] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -44.874201 | E_var:     0.2462 | E_err:   0.007753
[2025-10-07 10:16:19] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -44.876968 | E_var:     0.2419 | E_err:   0.007685
[2025-10-07 10:16:27] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -44.884274 | E_var:     0.2145 | E_err:   0.007236
[2025-10-07 10:16:27] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 10:16:35] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -44.861553 | E_var:     0.2585 | E_err:   0.007943
[2025-10-07 10:16:43] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -44.880939 | E_var:     0.3069 | E_err:   0.008656
[2025-10-07 10:16:51] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -44.880536 | E_var:     0.3061 | E_err:   0.008645
[2025-10-07 10:16:58] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -44.874050 | E_var:     0.1972 | E_err:   0.006939
[2025-10-07 10:17:06] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -44.877350 | E_var:     0.2671 | E_err:   0.008075
[2025-10-07 10:17:14] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -44.878731 | E_var:     0.2357 | E_err:   0.007585
[2025-10-07 10:17:22] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -44.875954 | E_var:     0.1803 | E_err:   0.006634
[2025-10-07 10:17:29] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -44.877495 | E_var:     0.2451 | E_err:   0.007735
[2025-10-07 10:17:37] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -44.871984 | E_var:     0.3719 | E_err:   0.009529
[2025-10-07 10:17:45] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -44.877754 | E_var:     0.2481 | E_err:   0.007783
[2025-10-07 10:17:53] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -44.880549 | E_var:     0.2045 | E_err:   0.007066
[2025-10-07 10:18:01] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -44.892445 | E_var:     0.5036 | E_err:   0.011088
[2025-10-07 10:18:08] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -44.873246 | E_var:     0.2253 | E_err:   0.007416
[2025-10-07 10:18:16] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -44.867884 | E_var:     0.2741 | E_err:   0.008181
[2025-10-07 10:18:24] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -44.878688 | E_var:     0.3091 | E_err:   0.008686
[2025-10-07 10:18:32] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -44.887464 | E_var:     0.2184 | E_err:   0.007303
[2025-10-07 10:18:40] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -44.883540 | E_var:     0.2188 | E_err:   0.007309
[2025-10-07 10:18:47] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -44.887068 | E_var:     0.2706 | E_err:   0.008128
[2025-10-07 10:18:55] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -44.880927 | E_var:     0.2195 | E_err:   0.007321
[2025-10-07 10:19:03] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -44.877221 | E_var:     0.2308 | E_err:   0.007507
[2025-10-07 10:19:11] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -44.891838 | E_var:     0.2987 | E_err:   0.008540
[2025-10-07 10:19:19] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -44.885811 | E_var:     0.2021 | E_err:   0.007025
[2025-10-07 10:19:26] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -44.866240 | E_var:     0.2144 | E_err:   0.007235
[2025-10-07 10:19:34] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -44.878446 | E_var:     0.2376 | E_err:   0.007616
[2025-10-07 10:19:42] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -44.891995 | E_var:     0.2336 | E_err:   0.007552
[2025-10-07 10:19:50] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -44.886391 | E_var:     0.2567 | E_err:   0.007917
[2025-10-07 10:19:58] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -44.872919 | E_var:     0.2000 | E_err:   0.006988
[2025-10-07 10:20:05] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -44.872622 | E_var:     0.2083 | E_err:   0.007131
[2025-10-07 10:20:13] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -44.881981 | E_var:     0.1953 | E_err:   0.006906
[2025-10-07 10:20:21] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -44.863944 | E_var:     0.2347 | E_err:   0.007570
[2025-10-07 10:20:29] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -44.894309 | E_var:     0.2200 | E_err:   0.007330
[2025-10-07 10:20:37] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -44.883686 | E_var:     0.2565 | E_err:   0.007913
[2025-10-07 10:20:44] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -44.889901 | E_var:     0.4164 | E_err:   0.010083
[2025-10-07 10:20:52] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -44.887176 | E_var:     0.2213 | E_err:   0.007350
[2025-10-07 10:21:00] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -44.884351 | E_var:     0.2218 | E_err:   0.007360
[2025-10-07 10:21:08] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -44.876667 | E_var:     0.2302 | E_err:   0.007497
[2025-10-07 10:21:16] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -44.881619 | E_var:     0.2457 | E_err:   0.007744
[2025-10-07 10:21:23] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -44.883687 | E_var:     0.2602 | E_err:   0.007971
[2025-10-07 10:21:31] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -44.887062 | E_var:     0.2354 | E_err:   0.007581
[2025-10-07 10:21:39] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -44.875744 | E_var:     0.4355 | E_err:   0.010311
[2025-10-07 10:21:47] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -44.879616 | E_var:     0.2520 | E_err:   0.007843
[2025-10-07 10:21:55] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -44.886145 | E_var:     0.2181 | E_err:   0.007297
[2025-10-07 10:22:02] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -44.875427 | E_var:     0.4064 | E_err:   0.009961
[2025-10-07 10:22:10] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -44.892826 | E_var:     0.2422 | E_err:   0.007689
[2025-10-07 10:22:18] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -44.877122 | E_var:     0.3312 | E_err:   0.008992
[2025-10-07 10:22:26] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -44.879278 | E_var:     0.2599 | E_err:   0.007965
[2025-10-07 10:22:34] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -44.885004 | E_var:     0.1985 | E_err:   0.006962
[2025-10-07 10:22:41] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -44.886285 | E_var:     0.3986 | E_err:   0.009865
[2025-10-07 10:22:49] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -44.866585 | E_var:     0.3153 | E_err:   0.008773
[2025-10-07 10:22:57] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -44.877827 | E_var:     0.2744 | E_err:   0.008185
[2025-10-07 10:22:57] 🔄 RESTART #1 | Period: 300
[2025-10-07 10:23:05] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -44.870547 | E_var:     0.3267 | E_err:   0.008931
[2025-10-07 10:23:13] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -44.871357 | E_var:     0.2660 | E_err:   0.008059
[2025-10-07 10:23:20] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -44.890342 | E_var:     0.3389 | E_err:   0.009096
[2025-10-07 10:23:28] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -44.881687 | E_var:     0.2459 | E_err:   0.007748
[2025-10-07 10:23:36] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -44.879734 | E_var:     0.1879 | E_err:   0.006773
[2025-10-07 10:23:44] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -44.883474 | E_var:     0.2146 | E_err:   0.007239
[2025-10-07 10:23:52] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -44.888677 | E_var:     0.2218 | E_err:   0.007359
[2025-10-07 10:23:59] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -44.871781 | E_var:     0.2400 | E_err:   0.007654
[2025-10-07 10:24:07] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -44.877805 | E_var:     0.3790 | E_err:   0.009620
[2025-10-07 10:24:15] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -44.881594 | E_var:     0.2109 | E_err:   0.007176
[2025-10-07 10:24:23] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -44.881187 | E_var:     0.1905 | E_err:   0.006820
[2025-10-07 10:24:31] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -44.885778 | E_var:     0.2147 | E_err:   0.007240
[2025-10-07 10:24:38] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -44.884777 | E_var:     0.2321 | E_err:   0.007528
[2025-10-07 10:24:46] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -44.878974 | E_var:     0.2692 | E_err:   0.008107
[2025-10-07 10:24:54] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -44.876680 | E_var:     0.2058 | E_err:   0.007088
[2025-10-07 10:25:02] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -44.878495 | E_var:     0.2001 | E_err:   0.006989
[2025-10-07 10:25:10] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -44.875230 | E_var:     0.1922 | E_err:   0.006851
[2025-10-07 10:25:17] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -44.895803 | E_var:     0.2743 | E_err:   0.008184
[2025-10-07 10:25:25] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -44.867200 | E_var:     0.2322 | E_err:   0.007529
[2025-10-07 10:25:33] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -44.881185 | E_var:     0.2429 | E_err:   0.007701
[2025-10-07 10:25:41] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -44.878064 | E_var:     0.2544 | E_err:   0.007882
[2025-10-07 10:25:48] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -44.881005 | E_var:     0.2406 | E_err:   0.007664
[2025-10-07 10:25:56] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -44.884515 | E_var:     0.2553 | E_err:   0.007896
[2025-10-07 10:26:04] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -44.875783 | E_var:     0.2031 | E_err:   0.007041
[2025-10-07 10:26:12] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -44.872963 | E_var:     0.2536 | E_err:   0.007868
[2025-10-07 10:26:20] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -44.879302 | E_var:     0.2165 | E_err:   0.007270
[2025-10-07 10:26:27] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -44.859659 | E_var:     0.2735 | E_err:   0.008171
[2025-10-07 10:26:35] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -44.882379 | E_var:     0.2407 | E_err:   0.007665
[2025-10-07 10:26:43] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -44.880388 | E_var:     0.2861 | E_err:   0.008358
[2025-10-07 10:26:51] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -44.885219 | E_var:     0.1973 | E_err:   0.006941
[2025-10-07 10:26:59] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -44.872269 | E_var:     0.2861 | E_err:   0.008358
[2025-10-07 10:27:06] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -44.887120 | E_var:     0.3113 | E_err:   0.008719
[2025-10-07 10:27:14] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -44.885079 | E_var:     0.3040 | E_err:   0.008616
[2025-10-07 10:27:22] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -44.876207 | E_var:     0.2497 | E_err:   0.007808
[2025-10-07 10:27:30] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -44.887349 | E_var:     0.2130 | E_err:   0.007211
[2025-10-07 10:27:38] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -44.871122 | E_var:     0.2621 | E_err:   0.007999
[2025-10-07 10:27:45] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -44.866401 | E_var:     0.2147 | E_err:   0.007239
[2025-10-07 10:27:53] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -44.889363 | E_var:     0.2093 | E_err:   0.007148
[2025-10-07 10:28:01] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -44.880220 | E_var:     0.2114 | E_err:   0.007183
[2025-10-07 10:28:09] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -44.878317 | E_var:     0.2109 | E_err:   0.007176
[2025-10-07 10:28:17] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -44.879122 | E_var:     0.2031 | E_err:   0.007041
[2025-10-07 10:28:24] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -44.887987 | E_var:     0.2448 | E_err:   0.007731
[2025-10-07 10:28:32] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -44.890464 | E_var:     0.3192 | E_err:   0.008827
[2025-10-07 10:28:40] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -44.872132 | E_var:     0.1974 | E_err:   0.006943
[2025-10-07 10:28:48] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -44.893438 | E_var:     0.4025 | E_err:   0.009913
[2025-10-07 10:28:56] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -44.881623 | E_var:     0.2249 | E_err:   0.007410
[2025-10-07 10:29:03] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -44.888920 | E_var:     0.2125 | E_err:   0.007203
[2025-10-07 10:29:11] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -44.887551 | E_var:     0.2989 | E_err:   0.008543
[2025-10-07 10:29:19] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -44.891181 | E_var:     0.3173 | E_err:   0.008801
[2025-10-07 10:29:27] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -44.879996 | E_var:     0.2006 | E_err:   0.006998
[2025-10-07 10:29:27] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 10:29:35] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -44.888079 | E_var:     0.2092 | E_err:   0.007146
[2025-10-07 10:29:42] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -44.882800 | E_var:     0.2136 | E_err:   0.007221
[2025-10-07 10:29:50] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -44.863350 | E_var:     0.2532 | E_err:   0.007863
[2025-10-07 10:29:58] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -44.863712 | E_var:     0.2822 | E_err:   0.008300
[2025-10-07 10:30:06] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -44.871305 | E_var:     0.2888 | E_err:   0.008397
[2025-10-07 10:30:14] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -44.879017 | E_var:     0.2327 | E_err:   0.007538
[2025-10-07 10:30:21] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -44.876678 | E_var:     0.2320 | E_err:   0.007526
[2025-10-07 10:30:29] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -44.879045 | E_var:     0.2162 | E_err:   0.007265
[2025-10-07 10:30:37] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -44.881961 | E_var:     0.1963 | E_err:   0.006923
[2025-10-07 10:30:45] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -44.883042 | E_var:     0.1693 | E_err:   0.006429
[2025-10-07 10:30:53] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -44.887711 | E_var:     0.2139 | E_err:   0.007227
[2025-10-07 10:31:00] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -44.890994 | E_var:     0.2481 | E_err:   0.007783
[2025-10-07 10:31:08] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -44.873565 | E_var:     0.1914 | E_err:   0.006835
[2025-10-07 10:31:16] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -44.888763 | E_var:     0.2097 | E_err:   0.007155
[2025-10-07 10:31:24] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -44.874690 | E_var:     0.2835 | E_err:   0.008319
[2025-10-07 10:31:32] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -44.879361 | E_var:     0.2466 | E_err:   0.007759
[2025-10-07 10:31:39] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -44.872387 | E_var:     0.2534 | E_err:   0.007866
[2025-10-07 10:31:47] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -44.864943 | E_var:     0.2169 | E_err:   0.007277
[2025-10-07 10:31:55] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -44.883792 | E_var:     0.2661 | E_err:   0.008059
[2025-10-07 10:32:03] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -44.866964 | E_var:     0.2311 | E_err:   0.007511
[2025-10-07 10:32:10] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -44.879934 | E_var:     0.2729 | E_err:   0.008163
[2025-10-07 10:32:18] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -44.880931 | E_var:     0.2491 | E_err:   0.007799
[2025-10-07 10:32:26] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -44.898435 | E_var:     0.2003 | E_err:   0.006993
[2025-10-07 10:32:34] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -44.880546 | E_var:     0.2290 | E_err:   0.007478
[2025-10-07 10:32:42] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -44.879747 | E_var:     0.1994 | E_err:   0.006977
[2025-10-07 10:32:49] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -44.874366 | E_var:     0.2482 | E_err:   0.007784
[2025-10-07 10:32:57] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -44.860165 | E_var:     1.4891 | E_err:   0.019067
[2025-10-07 10:33:05] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -44.890455 | E_var:     0.2561 | E_err:   0.007907
[2025-10-07 10:33:13] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -44.886647 | E_var:     0.2288 | E_err:   0.007473
[2025-10-07 10:33:21] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -44.890106 | E_var:     0.2654 | E_err:   0.008050
[2025-10-07 10:33:28] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -44.871720 | E_var:     0.3065 | E_err:   0.008650
[2025-10-07 10:33:36] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -44.872469 | E_var:     0.2130 | E_err:   0.007212
[2025-10-07 10:33:44] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -44.871135 | E_var:     0.2659 | E_err:   0.008057
[2025-10-07 10:33:52] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -44.875177 | E_var:     0.2444 | E_err:   0.007724
[2025-10-07 10:34:00] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -44.874443 | E_var:     0.2359 | E_err:   0.007588
[2025-10-07 10:34:07] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -44.880967 | E_var:     0.2216 | E_err:   0.007355
[2025-10-07 10:34:15] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -44.882476 | E_var:     0.3693 | E_err:   0.009496
[2025-10-07 10:34:23] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -44.894866 | E_var:     0.2005 | E_err:   0.006996
[2025-10-07 10:34:31] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -44.881918 | E_var:     0.2980 | E_err:   0.008530
[2025-10-07 10:34:39] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -44.869875 | E_var:     0.2067 | E_err:   0.007104
[2025-10-07 10:34:46] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -44.875699 | E_var:     0.2294 | E_err:   0.007484
[2025-10-07 10:34:54] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -44.888709 | E_var:     0.2340 | E_err:   0.007559
[2025-10-07 10:35:02] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -44.892153 | E_var:     0.2765 | E_err:   0.008217
[2025-10-07 10:35:10] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -44.878171 | E_var:     0.2940 | E_err:   0.008472
[2025-10-07 10:35:18] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -44.887863 | E_var:     0.2650 | E_err:   0.008043
[2025-10-07 10:35:25] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -44.864031 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 10:35:33] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -44.882376 | E_var:     0.2389 | E_err:   0.007637
[2025-10-07 10:35:41] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -44.866702 | E_var:     0.3741 | E_err:   0.009557
[2025-10-07 10:35:49] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -44.867596 | E_var:     0.3134 | E_err:   0.008747
[2025-10-07 10:35:57] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -44.877525 | E_var:     0.2205 | E_err:   0.007337
[2025-10-07 10:36:04] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -44.859659 | E_var:     0.2529 | E_err:   0.007858
[2025-10-07 10:36:12] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -44.882794 | E_var:     0.2531 | E_err:   0.007861
[2025-10-07 10:36:20] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -44.872530 | E_var:     0.2549 | E_err:   0.007889
[2025-10-07 10:36:28] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -44.876829 | E_var:     0.2477 | E_err:   0.007776
[2025-10-07 10:36:36] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -44.866869 | E_var:     0.2035 | E_err:   0.007048
[2025-10-07 10:36:43] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -44.883366 | E_var:     0.2391 | E_err:   0.007640
[2025-10-07 10:36:51] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -44.880964 | E_var:     0.9757 | E_err:   0.015434
[2025-10-07 10:36:59] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -44.885241 | E_var:     0.2299 | E_err:   0.007493
[2025-10-07 10:37:07] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -44.891647 | E_var:     0.2516 | E_err:   0.007837
[2025-10-07 10:37:15] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -44.874622 | E_var:     0.2160 | E_err:   0.007262
[2025-10-07 10:37:22] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -44.870445 | E_var:     0.2141 | E_err:   0.007230
[2025-10-07 10:37:30] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -44.892526 | E_var:     0.2045 | E_err:   0.007065
[2025-10-07 10:37:38] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -44.884020 | E_var:     0.5264 | E_err:   0.011337
[2025-10-07 10:37:46] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -44.873000 | E_var:     0.2010 | E_err:   0.007006
[2025-10-07 10:37:54] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -44.865242 | E_var:     0.4618 | E_err:   0.010618
[2025-10-07 10:38:01] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -44.887703 | E_var:     0.3729 | E_err:   0.009541
[2025-10-07 10:38:09] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -44.877635 | E_var:     0.3013 | E_err:   0.008577
[2025-10-07 10:38:17] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -44.876028 | E_var:     0.2026 | E_err:   0.007032
[2025-10-07 10:38:25] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -44.866241 | E_var:     0.3343 | E_err:   0.009034
[2025-10-07 10:38:33] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -44.885818 | E_var:     0.2578 | E_err:   0.007933
[2025-10-07 10:38:41] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -44.875044 | E_var:     0.2177 | E_err:   0.007290
[2025-10-07 10:38:48] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -44.867819 | E_var:     0.2185 | E_err:   0.007304
[2025-10-07 10:38:56] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -44.883626 | E_var:     0.2322 | E_err:   0.007529
[2025-10-07 10:39:04] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -44.883756 | E_var:     0.3580 | E_err:   0.009349
[2025-10-07 10:39:12] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -44.878413 | E_var:     0.2312 | E_err:   0.007512
[2025-10-07 10:39:20] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -44.865122 | E_var:     0.2787 | E_err:   0.008249
[2025-10-07 10:39:27] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -44.873181 | E_var:     0.7280 | E_err:   0.013332
[2025-10-07 10:39:35] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -44.878291 | E_var:     0.2122 | E_err:   0.007197
[2025-10-07 10:39:43] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -44.886759 | E_var:     0.2045 | E_err:   0.007065
[2025-10-07 10:39:51] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -44.892556 | E_var:     0.1892 | E_err:   0.006796
[2025-10-07 10:39:59] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -44.878294 | E_var:     0.2949 | E_err:   0.008484
[2025-10-07 10:40:06] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -44.882322 | E_var:     0.3185 | E_err:   0.008818
[2025-10-07 10:40:14] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -44.875952 | E_var:     0.2357 | E_err:   0.007585
[2025-10-07 10:40:22] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -44.880328 | E_var:     0.2472 | E_err:   0.007769
[2025-10-07 10:40:30] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -44.879251 | E_var:     0.2221 | E_err:   0.007364
[2025-10-07 10:40:37] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -44.875467 | E_var:     0.2308 | E_err:   0.007507
[2025-10-07 10:40:45] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -44.881030 | E_var:     0.2711 | E_err:   0.008135
[2025-10-07 10:40:53] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -44.871781 | E_var:     0.2355 | E_err:   0.007582
[2025-10-07 10:41:01] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -44.882387 | E_var:     0.3160 | E_err:   0.008783
[2025-10-07 10:41:09] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -44.879237 | E_var:     0.2294 | E_err:   0.007483
[2025-10-07 10:41:16] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -44.887886 | E_var:     0.2422 | E_err:   0.007690
[2025-10-07 10:41:24] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -44.874079 | E_var:     0.1988 | E_err:   0.006967
[2025-10-07 10:41:32] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -44.874043 | E_var:     0.2153 | E_err:   0.007250
[2025-10-07 10:41:40] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -44.875244 | E_var:     0.2030 | E_err:   0.007039
[2025-10-07 10:41:48] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -44.875476 | E_var:     0.2496 | E_err:   0.007806
[2025-10-07 10:41:55] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -44.873462 | E_var:     0.2313 | E_err:   0.007514
[2025-10-07 10:42:03] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -44.888462 | E_var:     0.2400 | E_err:   0.007655
[2025-10-07 10:42:11] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -44.866737 | E_var:     0.3352 | E_err:   0.009046
[2025-10-07 10:42:19] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -44.882745 | E_var:     0.1965 | E_err:   0.006926
[2025-10-07 10:42:27] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -44.866109 | E_var:     0.4840 | E_err:   0.010871
[2025-10-07 10:42:27] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 10:42:34] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -44.878792 | E_var:     0.2682 | E_err:   0.008092
[2025-10-07 10:42:42] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -44.878515 | E_var:     0.2202 | E_err:   0.007333
[2025-10-07 10:42:50] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -44.883438 | E_var:     0.2154 | E_err:   0.007252
[2025-10-07 10:42:58] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -44.867515 | E_var:     0.2290 | E_err:   0.007476
[2025-10-07 10:43:06] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -44.880225 | E_var:     0.1994 | E_err:   0.006977
[2025-10-07 10:43:13] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -44.886564 | E_var:     0.2296 | E_err:   0.007487
[2025-10-07 10:43:21] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -44.874138 | E_var:     0.2710 | E_err:   0.008135
[2025-10-07 10:43:29] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -44.869637 | E_var:     0.2302 | E_err:   0.007497
[2025-10-07 10:43:37] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -44.886131 | E_var:     0.2069 | E_err:   0.007107
[2025-10-07 10:43:45] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -44.889834 | E_var:     0.2536 | E_err:   0.007868
[2025-10-07 10:43:52] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -44.873743 | E_var:     0.2467 | E_err:   0.007760
[2025-10-07 10:44:00] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -44.884235 | E_var:     0.2790 | E_err:   0.008253
[2025-10-07 10:44:08] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -44.899528 | E_var:     0.2095 | E_err:   0.007151
[2025-10-07 10:44:16] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -44.854070 | E_var:     0.2328 | E_err:   0.007539
[2025-10-07 10:44:24] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -44.881893 | E_var:     0.1996 | E_err:   0.006981
[2025-10-07 10:44:31] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -44.862922 | E_var:     0.2699 | E_err:   0.008117
[2025-10-07 10:44:39] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -44.875672 | E_var:     0.3308 | E_err:   0.008987
[2025-10-07 10:44:47] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -44.871218 | E_var:     0.2516 | E_err:   0.007838
[2025-10-07 10:44:55] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -44.867276 | E_var:     0.2514 | E_err:   0.007834
[2025-10-07 10:45:03] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -44.877284 | E_var:     0.2792 | E_err:   0.008257
[2025-10-07 10:45:10] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -44.888401 | E_var:     0.2316 | E_err:   0.007520
[2025-10-07 10:45:18] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -44.881694 | E_var:     0.3247 | E_err:   0.008903
[2025-10-07 10:45:26] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -44.881269 | E_var:     0.2075 | E_err:   0.007118
[2025-10-07 10:45:34] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -44.878168 | E_var:     0.2024 | E_err:   0.007030
[2025-10-07 10:45:42] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -44.887531 | E_var:     0.2886 | E_err:   0.008393
[2025-10-07 10:45:49] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -44.880588 | E_var:     0.2811 | E_err:   0.008285
[2025-10-07 10:45:57] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -44.871502 | E_var:     0.2129 | E_err:   0.007209
[2025-10-07 10:46:05] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -44.892210 | E_var:     0.2166 | E_err:   0.007272
[2025-10-07 10:46:13] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -44.877235 | E_var:     0.2252 | E_err:   0.007415
[2025-10-07 10:46:21] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -44.881274 | E_var:     0.2189 | E_err:   0.007310
[2025-10-07 10:46:28] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -44.870775 | E_var:     0.3873 | E_err:   0.009724
[2025-10-07 10:46:36] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -44.886136 | E_var:     0.2298 | E_err:   0.007491
[2025-10-07 10:46:44] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -44.867126 | E_var:     0.2098 | E_err:   0.007158
[2025-10-07 10:46:52] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -44.873676 | E_var:     0.2765 | E_err:   0.008216
[2025-10-07 10:47:08] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -44.866400 | E_var:     0.1976 | E_err:   0.006946
[2025-10-07 10:47:15] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -44.879934 | E_var:     0.2582 | E_err:   0.007940
[2025-10-07 10:47:23] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -44.876417 | E_var:     0.2238 | E_err:   0.007391
[2025-10-07 10:47:31] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -44.893286 | E_var:     0.4221 | E_err:   0.010151
[2025-10-07 10:47:39] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -44.886014 | E_var:     0.2118 | E_err:   0.007192
[2025-10-07 10:47:47] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -44.882057 | E_var:     0.2270 | E_err:   0.007444
[2025-10-07 10:47:54] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -44.875275 | E_var:     0.2014 | E_err:   0.007012
[2025-10-07 10:48:02] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -44.894540 | E_var:     0.2264 | E_err:   0.007434
[2025-10-07 10:48:10] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -44.877878 | E_var:     0.2983 | E_err:   0.008534
[2025-10-07 10:48:18] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -44.870374 | E_var:     0.2586 | E_err:   0.007946
[2025-10-07 10:48:26] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -44.885684 | E_var:     0.2614 | E_err:   0.007988
[2025-10-07 10:48:33] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -44.889193 | E_var:     0.3339 | E_err:   0.009029
[2025-10-07 10:48:41] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -44.887191 | E_var:     0.3573 | E_err:   0.009340
[2025-10-07 10:48:49] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -44.887703 | E_var:     0.2245 | E_err:   0.007403
[2025-10-07 10:48:57] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -44.877891 | E_var:     0.2199 | E_err:   0.007326
[2025-10-07 10:49:05] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -44.873668 | E_var:     0.2573 | E_err:   0.007925
[2025-10-07 10:49:12] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -44.875410 | E_var:     0.2171 | E_err:   0.007280
[2025-10-07 10:49:20] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -44.867830 | E_var:     0.2898 | E_err:   0.008412
[2025-10-07 10:49:28] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -44.881452 | E_var:     0.2150 | E_err:   0.007244
[2025-10-07 10:49:36] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -44.881683 | E_var:     0.2499 | E_err:   0.007810
[2025-10-07 10:49:44] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -44.872827 | E_var:     0.2259 | E_err:   0.007426
[2025-10-07 10:49:51] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -44.882985 | E_var:     0.2372 | E_err:   0.007610
[2025-10-07 10:49:59] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -44.886993 | E_var:     0.2197 | E_err:   0.007324
[2025-10-07 10:50:07] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -44.872961 | E_var:     0.2283 | E_err:   0.007466
[2025-10-07 10:50:15] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -44.880121 | E_var:     0.2295 | E_err:   0.007485
[2025-10-07 10:50:23] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -44.872071 | E_var:     0.2854 | E_err:   0.008347
[2025-10-07 10:50:30] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -44.894814 | E_var:     0.2684 | E_err:   0.008095
[2025-10-07 10:50:38] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -44.880795 | E_var:     0.2503 | E_err:   0.007818
[2025-10-07 10:50:46] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -44.877867 | E_var:     0.3545 | E_err:   0.009304
[2025-10-07 10:50:54] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -44.887383 | E_var:     0.2142 | E_err:   0.007232
[2025-10-07 10:51:02] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -44.884930 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 10:51:09] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -44.880548 | E_var:     0.3173 | E_err:   0.008802
[2025-10-07 10:51:17] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -44.885138 | E_var:     0.2141 | E_err:   0.007230
[2025-10-07 10:51:25] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -44.890882 | E_var:     0.2877 | E_err:   0.008381
[2025-10-07 10:51:33] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -44.885403 | E_var:     0.1901 | E_err:   0.006813
[2025-10-07 10:51:41] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -44.883829 | E_var:     0.2425 | E_err:   0.007694
[2025-10-07 10:51:48] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -44.887602 | E_var:     0.2505 | E_err:   0.007821
[2025-10-07 10:51:56] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -44.889206 | E_var:     0.2035 | E_err:   0.007049
[2025-10-07 10:52:04] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -44.890634 | E_var:     0.2953 | E_err:   0.008491
[2025-10-07 10:52:12] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -44.884096 | E_var:     0.2408 | E_err:   0.007668
[2025-10-07 10:52:20] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -44.885602 | E_var:     0.1995 | E_err:   0.006979
[2025-10-07 10:52:27] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -44.878136 | E_var:     0.2144 | E_err:   0.007236
[2025-10-07 10:52:35] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -44.861954 | E_var:     0.2103 | E_err:   0.007165
[2025-10-07 10:52:43] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -44.893706 | E_var:     0.3426 | E_err:   0.009146
[2025-10-07 10:52:51] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -44.884717 | E_var:     0.2267 | E_err:   0.007440
[2025-10-07 10:52:59] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -44.890401 | E_var:     0.2983 | E_err:   0.008535
[2025-10-07 10:53:06] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -44.890791 | E_var:     0.3057 | E_err:   0.008639
[2025-10-07 10:53:14] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -44.881239 | E_var:     0.2192 | E_err:   0.007316
[2025-10-07 10:53:22] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -44.880637 | E_var:     0.2216 | E_err:   0.007355
[2025-10-07 10:53:30] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -44.889216 | E_var:     0.2735 | E_err:   0.008171
[2025-10-07 10:53:38] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -44.890927 | E_var:     0.2223 | E_err:   0.007367
[2025-10-07 10:53:45] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -44.884307 | E_var:     0.2295 | E_err:   0.007485
[2025-10-07 10:53:53] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -44.874511 | E_var:     0.2096 | E_err:   0.007154
[2025-10-07 10:54:01] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -44.883616 | E_var:     0.2185 | E_err:   0.007304
[2025-10-07 10:54:09] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -44.869625 | E_var:     0.3138 | E_err:   0.008753
[2025-10-07 10:54:16] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -44.887621 | E_var:     0.2261 | E_err:   0.007430
[2025-10-07 10:54:24] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -44.888433 | E_var:     0.2362 | E_err:   0.007595
[2025-10-07 10:54:32] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -44.873429 | E_var:     0.2106 | E_err:   0.007170
[2025-10-07 10:54:40] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -44.875886 | E_var:     0.2625 | E_err:   0.008006
[2025-10-07 10:54:48] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -44.875006 | E_var:     0.4311 | E_err:   0.010259
[2025-10-07 10:54:56] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -44.873032 | E_var:     0.2282 | E_err:   0.007465
[2025-10-07 10:55:04] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -44.877803 | E_var:     0.2772 | E_err:   0.008226
[2025-10-07 10:55:11] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -44.886180 | E_var:     0.3061 | E_err:   0.008645
[2025-10-07 10:55:19] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -44.879910 | E_var:     0.1956 | E_err:   0.006911
[2025-10-07 10:55:27] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -44.885529 | E_var:     0.2112 | E_err:   0.007180
[2025-10-07 10:55:35] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -44.876524 | E_var:     0.2423 | E_err:   0.007691
[2025-10-07 10:55:35] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 10:55:43] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -44.877772 | E_var:     0.2054 | E_err:   0.007082
[2025-10-07 10:55:50] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -44.875614 | E_var:     0.2540 | E_err:   0.007875
[2025-10-07 10:55:58] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -44.878019 | E_var:     0.3147 | E_err:   0.008765
[2025-10-07 10:56:06] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -44.882248 | E_var:     0.2357 | E_err:   0.007586
[2025-10-07 10:56:14] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -44.880787 | E_var:     0.2377 | E_err:   0.007618
[2025-10-07 10:56:22] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -44.876347 | E_var:     0.2859 | E_err:   0.008354
[2025-10-07 10:56:29] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -44.887905 | E_var:     0.2132 | E_err:   0.007215
[2025-10-07 10:56:37] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -44.874313 | E_var:     0.2136 | E_err:   0.007221
[2025-10-07 10:56:45] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -44.878053 | E_var:     0.2126 | E_err:   0.007204
[2025-10-07 10:56:53] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -44.884044 | E_var:     0.2359 | E_err:   0.007589
[2025-10-07 10:57:01] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -44.881874 | E_var:     0.2814 | E_err:   0.008288
[2025-10-07 10:57:08] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -44.879558 | E_var:     0.3468 | E_err:   0.009201
[2025-10-07 10:57:16] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -44.891756 | E_var:     0.2592 | E_err:   0.007955
[2025-10-07 10:57:24] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -44.885267 | E_var:     0.2174 | E_err:   0.007285
[2025-10-07 10:57:32] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -44.887565 | E_var:     0.2159 | E_err:   0.007259
[2025-10-07 10:57:40] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -44.883369 | E_var:     0.2386 | E_err:   0.007633
[2025-10-07 10:57:47] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -44.886979 | E_var:     0.2155 | E_err:   0.007254
[2025-10-07 10:57:55] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -44.884502 | E_var:     0.2108 | E_err:   0.007173
[2025-10-07 10:58:03] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -44.865710 | E_var:     0.2526 | E_err:   0.007854
[2025-10-07 10:58:11] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -44.867839 | E_var:     0.2575 | E_err:   0.007930
[2025-10-07 10:58:19] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -44.876642 | E_var:     0.3980 | E_err:   0.009857
[2025-10-07 10:58:26] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -44.874725 | E_var:     0.2585 | E_err:   0.007944
[2025-10-07 10:58:34] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -44.876002 | E_var:     0.3136 | E_err:   0.008750
[2025-10-07 10:58:42] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -44.875125 | E_var:     0.2291 | E_err:   0.007478
[2025-10-07 10:58:50] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -44.882374 | E_var:     0.1990 | E_err:   0.006970
[2025-10-07 10:58:58] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -44.873010 | E_var:     0.2113 | E_err:   0.007183
[2025-10-07 10:59:05] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -44.880276 | E_var:     0.3822 | E_err:   0.009660
[2025-10-07 10:59:13] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -44.871563 | E_var:     0.2612 | E_err:   0.007985
[2025-10-07 10:59:21] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -44.878852 | E_var:     0.1886 | E_err:   0.006785
[2025-10-07 10:59:29] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -44.869777 | E_var:     0.2082 | E_err:   0.007129
[2025-10-07 10:59:37] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -44.880420 | E_var:     0.2328 | E_err:   0.007540
[2025-10-07 10:59:44] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -44.882827 | E_var:     0.3284 | E_err:   0.008954
[2025-10-07 10:59:52] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -44.895707 | E_var:     0.2247 | E_err:   0.007406
[2025-10-07 11:00:00] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -44.864460 | E_var:     0.2658 | E_err:   0.008056
[2025-10-07 11:00:08] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -44.867975 | E_var:     0.2380 | E_err:   0.007623
[2025-10-07 11:00:16] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -44.876659 | E_var:     0.2186 | E_err:   0.007306
[2025-10-07 11:00:24] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -44.884739 | E_var:     0.1790 | E_err:   0.006610
[2025-10-07 11:00:31] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -44.883959 | E_var:     0.2722 | E_err:   0.008151
[2025-10-07 11:00:39] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -44.868708 | E_var:     0.1835 | E_err:   0.006692
[2025-10-07 11:00:47] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -44.884307 | E_var:     0.2464 | E_err:   0.007755
[2025-10-07 11:00:55] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -44.879407 | E_var:     0.2304 | E_err:   0.007500
[2025-10-07 11:01:03] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -44.883885 | E_var:     0.1961 | E_err:   0.006919
[2025-10-07 11:01:10] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -44.873437 | E_var:     0.2016 | E_err:   0.007016
[2025-10-07 11:01:18] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -44.879941 | E_var:     0.2601 | E_err:   0.007968
[2025-10-07 11:01:26] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -44.880849 | E_var:     0.2083 | E_err:   0.007132
[2025-10-07 11:01:34] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -44.892191 | E_var:     0.2249 | E_err:   0.007410
[2025-10-07 11:01:42] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -44.870353 | E_var:     0.2215 | E_err:   0.007353
[2025-10-07 11:01:49] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -44.872015 | E_var:     0.1972 | E_err:   0.006939
[2025-10-07 11:01:57] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -44.868383 | E_var:     0.3007 | E_err:   0.008568
[2025-10-07 11:02:05] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -44.879554 | E_var:     0.2768 | E_err:   0.008220
[2025-10-07 11:02:05] 🔄 RESTART #2 | Period: 600
[2025-10-07 11:02:13] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -44.871238 | E_var:     0.1950 | E_err:   0.006900
[2025-10-07 11:02:21] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -44.877491 | E_var:     0.2128 | E_err:   0.007207
[2025-10-07 11:02:28] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -44.870092 | E_var:     0.5240 | E_err:   0.011310
[2025-10-07 11:02:36] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -44.863350 | E_var:     0.2967 | E_err:   0.008511
[2025-10-07 11:02:44] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -44.873742 | E_var:     0.2127 | E_err:   0.007206
[2025-10-07 11:02:52] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -44.876683 | E_var:     0.1966 | E_err:   0.006929
[2025-10-07 11:03:00] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -44.880671 | E_var:     0.2807 | E_err:   0.008278
[2025-10-07 11:03:08] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -44.884806 | E_var:     0.4058 | E_err:   0.009954
[2025-10-07 11:03:15] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -44.882160 | E_var:     0.1933 | E_err:   0.006870
[2025-10-07 11:03:23] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -44.870605 | E_var:     0.2230 | E_err:   0.007379
[2025-10-07 11:03:31] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -44.879994 | E_var:     0.2930 | E_err:   0.008458
[2025-10-07 11:03:39] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -44.882309 | E_var:     0.2206 | E_err:   0.007338
[2025-10-07 11:03:47] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -44.888684 | E_var:     0.2763 | E_err:   0.008213
[2025-10-07 11:03:54] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -44.873652 | E_var:     0.2846 | E_err:   0.008336
[2025-10-07 11:04:02] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -44.879444 | E_var:     0.2354 | E_err:   0.007580
[2025-10-07 11:04:10] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -44.878321 | E_var:     0.2055 | E_err:   0.007082
[2025-10-07 11:04:18] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -44.866617 | E_var:     0.5271 | E_err:   0.011344
[2025-10-07 11:04:26] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -44.881360 | E_var:     0.2343 | E_err:   0.007564
[2025-10-07 11:04:33] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -44.871044 | E_var:     0.2737 | E_err:   0.008175
[2025-10-07 11:04:41] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -44.897343 | E_var:     0.2327 | E_err:   0.007538
[2025-10-07 11:04:49] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -44.886505 | E_var:     0.2122 | E_err:   0.007197
[2025-10-07 11:04:57] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -44.877970 | E_var:     0.2803 | E_err:   0.008272
[2025-10-07 11:05:05] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -44.871840 | E_var:     0.2161 | E_err:   0.007264
[2025-10-07 11:05:13] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -44.893956 | E_var:     0.3222 | E_err:   0.008869
[2025-10-07 11:05:20] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -44.872693 | E_var:     0.2380 | E_err:   0.007623
[2025-10-07 11:05:28] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -44.866524 | E_var:     0.2396 | E_err:   0.007648
[2025-10-07 11:05:36] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -44.876402 | E_var:     0.2575 | E_err:   0.007928
[2025-10-07 11:05:44] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -44.885002 | E_var:     0.2547 | E_err:   0.007885
[2025-10-07 11:05:52] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -44.869981 | E_var:     0.2419 | E_err:   0.007684
[2025-10-07 11:05:59] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -44.876241 | E_var:     0.2453 | E_err:   0.007738
[2025-10-07 11:06:07] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -44.881166 | E_var:     0.2378 | E_err:   0.007620
[2025-10-07 11:06:15] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -44.891154 | E_var:     0.3446 | E_err:   0.009172
[2025-10-07 11:06:23] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -44.888457 | E_var:     0.2224 | E_err:   0.007368
[2025-10-07 11:06:31] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -44.869766 | E_var:     0.2981 | E_err:   0.008532
[2025-10-07 11:06:39] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -44.872413 | E_var:     0.2319 | E_err:   0.007524
[2025-10-07 11:06:46] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -44.884052 | E_var:     0.2535 | E_err:   0.007867
[2025-10-07 11:06:54] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -44.891217 | E_var:     0.2162 | E_err:   0.007265
[2025-10-07 11:07:02] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -44.874456 | E_var:     0.2437 | E_err:   0.007714
[2025-10-07 11:07:10] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -44.881124 | E_var:     0.2093 | E_err:   0.007148
[2025-10-07 11:07:18] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -44.873016 | E_var:     0.5134 | E_err:   0.011196
[2025-10-07 11:07:26] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -44.877285 | E_var:     0.2220 | E_err:   0.007362
[2025-10-07 11:07:34] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -44.871434 | E_var:     0.8770 | E_err:   0.014632
[2025-10-07 11:07:41] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -44.880970 | E_var:     0.3268 | E_err:   0.008932
[2025-10-07 11:07:49] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -44.890676 | E_var:     0.2470 | E_err:   0.007766
[2025-10-07 11:07:57] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -44.882256 | E_var:     0.2024 | E_err:   0.007029
[2025-10-07 11:08:05] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -44.887937 | E_var:     0.2258 | E_err:   0.007424
[2025-10-07 11:08:13] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -44.875543 | E_var:     0.2404 | E_err:   0.007660
[2025-10-07 11:08:20] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -44.875689 | E_var:     0.1769 | E_err:   0.006571
[2025-10-07 11:08:28] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -44.882875 | E_var:     0.2170 | E_err:   0.007279
[2025-10-07 11:08:36] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -44.876046 | E_var:     0.2184 | E_err:   0.007303
[2025-10-07 11:08:36] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 11:08:44] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -44.887116 | E_var:     0.2415 | E_err:   0.007679
[2025-10-07 11:08:52] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -44.891098 | E_var:     0.2459 | E_err:   0.007748
[2025-10-07 11:09:00] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -44.879284 | E_var:     0.2605 | E_err:   0.007975
[2025-10-07 11:09:07] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -44.888297 | E_var:     0.2203 | E_err:   0.007334
[2025-10-07 11:09:15] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -44.877615 | E_var:     0.1923 | E_err:   0.006852
[2025-10-07 11:09:23] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -44.888722 | E_var:     0.2028 | E_err:   0.007037
[2025-10-07 11:09:31] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -44.891420 | E_var:     0.1924 | E_err:   0.006853
[2025-10-07 11:09:39] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -44.879776 | E_var:     0.2144 | E_err:   0.007235
[2025-10-07 11:09:46] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -44.888452 | E_var:     0.2274 | E_err:   0.007450
[2025-10-07 11:09:54] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -44.880400 | E_var:     0.1920 | E_err:   0.006847
[2025-10-07 11:10:02] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -44.873922 | E_var:     0.3011 | E_err:   0.008573
[2025-10-07 11:10:10] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -44.878067 | E_var:     0.2326 | E_err:   0.007536
[2025-10-07 11:10:18] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -44.874095 | E_var:     0.3944 | E_err:   0.009813
[2025-10-07 11:10:25] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -44.878702 | E_var:     0.4511 | E_err:   0.010494
[2025-10-07 11:10:33] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -44.879433 | E_var:     0.2074 | E_err:   0.007116
[2025-10-07 11:10:41] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -44.879817 | E_var:     0.2382 | E_err:   0.007626
[2025-10-07 11:10:49] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -44.898144 | E_var:     0.2787 | E_err:   0.008248
[2025-10-07 11:10:57] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -44.880362 | E_var:     0.2406 | E_err:   0.007665
[2025-10-07 11:11:04] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -44.876440 | E_var:     0.2068 | E_err:   0.007106
[2025-10-07 11:11:12] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -44.878189 | E_var:     0.3055 | E_err:   0.008636
[2025-10-07 11:11:20] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -44.863389 | E_var:     0.2233 | E_err:   0.007384
[2025-10-07 11:11:28] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -44.879628 | E_var:     0.2436 | E_err:   0.007712
[2025-10-07 11:11:36] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -44.884959 | E_var:     0.2573 | E_err:   0.007926
[2025-10-07 11:11:43] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -44.877732 | E_var:     0.2941 | E_err:   0.008474
[2025-10-07 11:11:51] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -44.861481 | E_var:     0.2329 | E_err:   0.007541
[2025-10-07 11:11:59] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -44.889161 | E_var:     0.2247 | E_err:   0.007407
[2025-10-07 11:12:07] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -44.887602 | E_var:     0.2354 | E_err:   0.007581
[2025-10-07 11:12:15] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -44.883236 | E_var:     0.2749 | E_err:   0.008193
[2025-10-07 11:12:22] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -44.897284 | E_var:     1.0207 | E_err:   0.015786
[2025-10-07 11:12:30] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -44.898128 | E_var:     0.2509 | E_err:   0.007826
[2025-10-07 11:12:38] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -44.879950 | E_var:     0.2421 | E_err:   0.007688
[2025-10-07 11:12:46] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -44.882445 | E_var:     0.2290 | E_err:   0.007478
[2025-10-07 11:12:54] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -44.864585 | E_var:     0.2345 | E_err:   0.007566
[2025-10-07 11:13:01] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -44.882296 | E_var:     0.2170 | E_err:   0.007279
[2025-10-07 11:13:09] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -44.870173 | E_var:     0.3036 | E_err:   0.008609
[2025-10-07 11:13:17] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -44.878075 | E_var:     0.2628 | E_err:   0.008009
[2025-10-07 11:13:25] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -44.883464 | E_var:     0.3487 | E_err:   0.009226
[2025-10-07 11:13:33] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -44.883337 | E_var:     0.4887 | E_err:   0.010923
[2025-10-07 11:13:40] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -44.872450 | E_var:     0.2560 | E_err:   0.007905
[2025-10-07 11:13:48] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -44.879547 | E_var:     0.2076 | E_err:   0.007119
[2025-10-07 11:13:56] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -44.887050 | E_var:     0.2472 | E_err:   0.007769
[2025-10-07 11:14:04] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -44.885534 | E_var:     0.2154 | E_err:   0.007252
[2025-10-07 11:14:12] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -44.857157 | E_var:     0.5008 | E_err:   0.011057
[2025-10-07 11:14:19] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -44.869264 | E_var:     0.2369 | E_err:   0.007605
[2025-10-07 11:14:27] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -44.885129 | E_var:     0.2090 | E_err:   0.007143
[2025-10-07 11:14:35] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -44.860294 | E_var:     0.1953 | E_err:   0.006905
[2025-10-07 11:14:43] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -44.868268 | E_var:     0.2575 | E_err:   0.007929
[2025-10-07 11:14:51] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -44.876334 | E_var:     0.2158 | E_err:   0.007259
[2025-10-07 11:14:58] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -44.894581 | E_var:     0.2182 | E_err:   0.007298
[2025-10-07 11:15:06] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -44.872960 | E_var:     0.2029 | E_err:   0.007038
[2025-10-07 11:15:14] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -44.886107 | E_var:     0.2474 | E_err:   0.007771
[2025-10-07 11:15:22] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -44.876576 | E_var:     0.2234 | E_err:   0.007385
[2025-10-07 11:15:30] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -44.879858 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 11:15:37] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -44.887284 | E_var:     0.2989 | E_err:   0.008542
[2025-10-07 11:15:45] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -44.888507 | E_var:     0.2161 | E_err:   0.007264
[2025-10-07 11:15:53] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -44.879639 | E_var:     0.2021 | E_err:   0.007024
[2025-10-07 11:16:01] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -44.878345 | E_var:     0.2731 | E_err:   0.008165
[2025-10-07 11:16:09] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -44.865229 | E_var:     0.7589 | E_err:   0.013612
[2025-10-07 11:16:16] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -44.888107 | E_var:     0.2439 | E_err:   0.007717
[2025-10-07 11:16:24] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -44.877809 | E_var:     0.2185 | E_err:   0.007303
[2025-10-07 11:16:32] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -44.897842 | E_var:     0.2072 | E_err:   0.007112
[2025-10-07 11:16:40] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -44.878748 | E_var:     0.2479 | E_err:   0.007780
[2025-10-07 11:16:48] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -44.889400 | E_var:     0.2285 | E_err:   0.007470
[2025-10-07 11:16:55] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -44.880311 | E_var:     0.3200 | E_err:   0.008839
[2025-10-07 11:17:03] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -44.885249 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 11:17:11] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -44.880096 | E_var:     0.2093 | E_err:   0.007148
[2025-10-07 11:17:19] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -44.885554 | E_var:     0.2068 | E_err:   0.007106
[2025-10-07 11:17:27] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -44.890653 | E_var:     0.2505 | E_err:   0.007820
[2025-10-07 11:17:34] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -44.872987 | E_var:     0.2390 | E_err:   0.007638
[2025-10-07 11:17:42] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -44.862204 | E_var:     0.2032 | E_err:   0.007043
[2025-10-07 11:17:50] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -44.882374 | E_var:     0.2075 | E_err:   0.007118
[2025-10-07 11:17:58] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -44.876738 | E_var:     0.2022 | E_err:   0.007026
[2025-10-07 11:18:05] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -44.869285 | E_var:     0.2836 | E_err:   0.008322
[2025-10-07 11:18:13] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -44.880206 | E_var:     0.2077 | E_err:   0.007121
[2025-10-07 11:18:21] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -44.869750 | E_var:     0.2599 | E_err:   0.007965
[2025-10-07 11:18:29] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -44.870539 | E_var:     0.2494 | E_err:   0.007803
[2025-10-07 11:18:37] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -44.886719 | E_var:     0.1867 | E_err:   0.006752
[2025-10-07 11:18:44] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -44.889110 | E_var:     0.1955 | E_err:   0.006909
[2025-10-07 11:18:52] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -44.891071 | E_var:     0.2277 | E_err:   0.007456
[2025-10-07 11:19:00] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -44.884018 | E_var:     0.2110 | E_err:   0.007177
[2025-10-07 11:19:08] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -44.885567 | E_var:     0.2882 | E_err:   0.008389
[2025-10-07 11:19:16] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -44.868192 | E_var:     0.2134 | E_err:   0.007219
[2025-10-07 11:19:23] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -44.883805 | E_var:     0.2710 | E_err:   0.008135
[2025-10-07 11:19:31] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -44.875216 | E_var:     0.2609 | E_err:   0.007982
[2025-10-07 11:19:39] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -44.883969 | E_var:     0.1965 | E_err:   0.006927
[2025-10-07 11:19:47] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -44.880561 | E_var:     0.1998 | E_err:   0.006985
[2025-10-07 11:19:55] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -44.888317 | E_var:     0.1909 | E_err:   0.006827
[2025-10-07 11:20:02] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -44.872036 | E_var:     0.2483 | E_err:   0.007786
[2025-10-07 11:20:10] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -44.891001 | E_var:     0.2092 | E_err:   0.007147
[2025-10-07 11:20:18] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -44.874093 | E_var:     0.1924 | E_err:   0.006853
[2025-10-07 11:20:26] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -44.870232 | E_var:     0.2290 | E_err:   0.007478
[2025-10-07 11:20:34] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -44.877130 | E_var:     0.1932 | E_err:   0.006867
[2025-10-07 11:20:41] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -44.879125 | E_var:     0.1753 | E_err:   0.006542
[2025-10-07 11:20:49] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -44.873416 | E_var:     0.1996 | E_err:   0.006981
[2025-10-07 11:20:57] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -44.877368 | E_var:     0.2439 | E_err:   0.007717
[2025-10-07 11:21:05] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -44.878494 | E_var:     0.2492 | E_err:   0.007800
[2025-10-07 11:21:13] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -44.885742 | E_var:     0.2447 | E_err:   0.007730
[2025-10-07 11:21:20] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -44.881095 | E_var:     0.1952 | E_err:   0.006904
[2025-10-07 11:21:28] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -44.897149 | E_var:     0.2204 | E_err:   0.007335
[2025-10-07 11:21:36] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -44.868812 | E_var:     0.2019 | E_err:   0.007021
[2025-10-07 11:21:36] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 11:21:44] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -44.881115 | E_var:     0.2239 | E_err:   0.007393
[2025-10-07 11:21:52] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -44.880831 | E_var:     0.2649 | E_err:   0.008041
[2025-10-07 11:22:00] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -44.883960 | E_var:     0.2142 | E_err:   0.007231
[2025-10-07 11:22:07] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -44.875424 | E_var:     0.2162 | E_err:   0.007265
[2025-10-07 11:22:15] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -44.885056 | E_var:     0.2009 | E_err:   0.007003
[2025-10-07 11:22:23] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -44.881237 | E_var:     0.2180 | E_err:   0.007295
[2025-10-07 11:22:31] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -44.868541 | E_var:     0.2188 | E_err:   0.007309
[2025-10-07 11:22:38] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -44.880240 | E_var:     0.3080 | E_err:   0.008672
[2025-10-07 11:22:46] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -44.878242 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 11:22:54] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -44.891828 | E_var:     0.2140 | E_err:   0.007229
[2025-10-07 11:23:02] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -44.895009 | E_var:     0.2305 | E_err:   0.007501
[2025-10-07 11:23:10] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -44.885168 | E_var:     0.3067 | E_err:   0.008653
[2025-10-07 11:23:17] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -44.877320 | E_var:     0.2833 | E_err:   0.008316
[2025-10-07 11:23:25] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -44.880791 | E_var:     0.3036 | E_err:   0.008609
[2025-10-07 11:23:33] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -44.868632 | E_var:     0.2518 | E_err:   0.007841
[2025-10-07 11:23:41] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -44.882012 | E_var:     0.2482 | E_err:   0.007784
[2025-10-07 11:23:49] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -44.875373 | E_var:     0.2632 | E_err:   0.008017
[2025-10-07 11:23:56] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -44.891986 | E_var:     0.2343 | E_err:   0.007564
[2025-10-07 11:24:04] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -44.878374 | E_var:     0.2518 | E_err:   0.007841
[2025-10-07 11:24:12] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -44.881530 | E_var:     0.2220 | E_err:   0.007362
[2025-10-07 11:24:20] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -44.888157 | E_var:     0.2729 | E_err:   0.008163
[2025-10-07 11:24:28] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -44.865357 | E_var:     0.2194 | E_err:   0.007319
[2025-10-07 11:24:35] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -44.882755 | E_var:     0.2044 | E_err:   0.007064
[2025-10-07 11:24:43] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -44.879041 | E_var:     0.3113 | E_err:   0.008717
[2025-10-07 11:24:51] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -44.871490 | E_var:     0.2457 | E_err:   0.007745
[2025-10-07 11:24:59] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -44.874343 | E_var:     0.3631 | E_err:   0.009415
[2025-10-07 11:25:07] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -44.892503 | E_var:     0.2620 | E_err:   0.007998
[2025-10-07 11:25:14] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -44.871898 | E_var:     0.2415 | E_err:   0.007679
[2025-10-07 11:25:22] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -44.871889 | E_var:     0.2844 | E_err:   0.008333
[2025-10-07 11:25:30] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -44.887565 | E_var:     0.2236 | E_err:   0.007388
[2025-10-07 11:25:38] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -44.890490 | E_var:     0.3817 | E_err:   0.009653
[2025-10-07 11:25:46] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -44.890825 | E_var:     0.2585 | E_err:   0.007944
[2025-10-07 11:25:53] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -44.874446 | E_var:     0.2152 | E_err:   0.007249
[2025-10-07 11:26:02] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -44.879746 | E_var:     0.1787 | E_err:   0.006605
[2025-10-07 11:26:10] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -44.872751 | E_var:     0.2537 | E_err:   0.007870
[2025-10-07 11:26:17] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -44.883045 | E_var:     0.2609 | E_err:   0.007982
[2025-10-07 11:26:25] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -44.875338 | E_var:     0.2927 | E_err:   0.008454
[2025-10-07 11:26:33] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -44.877472 | E_var:     0.3295 | E_err:   0.008969
[2025-10-07 11:26:41] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -44.867575 | E_var:     0.2221 | E_err:   0.007364
[2025-10-07 11:26:49] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -44.878920 | E_var:     0.2797 | E_err:   0.008264
[2025-10-07 11:26:56] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -44.885507 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 11:27:04] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -44.883524 | E_var:     0.1923 | E_err:   0.006852
[2025-10-07 11:27:12] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -44.869311 | E_var:     0.3523 | E_err:   0.009274
[2025-10-07 11:27:20] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -44.876435 | E_var:     0.2295 | E_err:   0.007486
[2025-10-07 11:27:28] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -44.869993 | E_var:     0.2306 | E_err:   0.007502
[2025-10-07 11:27:35] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -44.887950 | E_var:     0.2165 | E_err:   0.007270
[2025-10-07 11:27:43] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -44.863434 | E_var:     0.4216 | E_err:   0.010146
[2025-10-07 11:27:51] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -44.893674 | E_var:     0.2336 | E_err:   0.007552
[2025-10-07 11:27:59] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -44.878093 | E_var:     0.2333 | E_err:   0.007548
[2025-10-07 11:28:06] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -44.895252 | E_var:     0.1989 | E_err:   0.006968
[2025-10-07 11:28:14] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -44.879049 | E_var:     0.2367 | E_err:   0.007602
[2025-10-07 11:28:22] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -44.877654 | E_var:     0.2632 | E_err:   0.008016
[2025-10-07 11:28:30] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -44.882151 | E_var:     0.2025 | E_err:   0.007031
[2025-10-07 11:28:38] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -44.873702 | E_var:     0.3325 | E_err:   0.009009
[2025-10-07 11:28:45] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -44.869184 | E_var:     0.2819 | E_err:   0.008296
[2025-10-07 11:28:53] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -44.890203 | E_var:     0.2094 | E_err:   0.007149
[2025-10-07 11:29:01] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -44.871862 | E_var:     0.2335 | E_err:   0.007550
[2025-10-07 11:29:09] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -44.889550 | E_var:     0.2208 | E_err:   0.007343
[2025-10-07 11:29:17] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -44.874457 | E_var:     0.2194 | E_err:   0.007319
[2025-10-07 11:29:24] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -44.869193 | E_var:     0.2221 | E_err:   0.007364
[2025-10-07 11:29:32] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -44.884125 | E_var:     0.2204 | E_err:   0.007336
[2025-10-07 11:29:40] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -44.879977 | E_var:     0.3332 | E_err:   0.009019
[2025-10-07 11:29:48] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -44.887731 | E_var:     0.2465 | E_err:   0.007757
[2025-10-07 11:29:56] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -44.872086 | E_var:     0.2593 | E_err:   0.007956
[2025-10-07 11:30:03] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -44.880419 | E_var:     0.2028 | E_err:   0.007037
[2025-10-07 11:30:11] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -44.873321 | E_var:     0.2958 | E_err:   0.008498
[2025-10-07 11:30:19] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -44.866814 | E_var:     0.2506 | E_err:   0.007822
[2025-10-07 11:30:27] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -44.887734 | E_var:     0.3954 | E_err:   0.009825
[2025-10-07 11:30:35] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -44.885531 | E_var:     0.2933 | E_err:   0.008461
[2025-10-07 11:30:42] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -44.867296 | E_var:     0.4124 | E_err:   0.010034
[2025-10-07 11:30:50] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -44.876798 | E_var:     0.2367 | E_err:   0.007601
[2025-10-07 11:30:58] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -44.882306 | E_var:     0.2171 | E_err:   0.007280
[2025-10-07 11:31:06] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -44.870594 | E_var:     0.2412 | E_err:   0.007673
[2025-10-07 11:31:14] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -44.876568 | E_var:     0.2494 | E_err:   0.007803
[2025-10-07 11:31:22] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -44.889431 | E_var:     0.2106 | E_err:   0.007171
[2025-10-07 11:31:29] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -44.882782 | E_var:     0.2374 | E_err:   0.007613
[2025-10-07 11:31:37] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -44.871324 | E_var:     0.3038 | E_err:   0.008613
[2025-10-07 11:31:45] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -44.880036 | E_var:     0.2465 | E_err:   0.007758
[2025-10-07 11:31:53] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -44.869605 | E_var:     0.8393 | E_err:   0.014315
[2025-10-07 11:32:00] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -44.878788 | E_var:     0.2079 | E_err:   0.007125
[2025-10-07 11:32:08] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -44.879379 | E_var:     0.2104 | E_err:   0.007167
[2025-10-07 11:32:16] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -44.886952 | E_var:     0.3176 | E_err:   0.008806
[2025-10-07 11:32:24] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -44.875063 | E_var:     0.2349 | E_err:   0.007572
[2025-10-07 11:32:32] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -44.882181 | E_var:     0.2400 | E_err:   0.007654
[2025-10-07 11:32:39] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -44.872474 | E_var:     0.2826 | E_err:   0.008307
[2025-10-07 11:32:47] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -44.879604 | E_var:     0.2199 | E_err:   0.007327
[2025-10-07 11:32:55] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -44.882530 | E_var:     0.2309 | E_err:   0.007508
[2025-10-07 11:33:03] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -44.886422 | E_var:     0.2775 | E_err:   0.008231
[2025-10-07 11:33:11] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -44.867927 | E_var:     0.2921 | E_err:   0.008445
[2025-10-07 11:33:18] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -44.881374 | E_var:     0.2691 | E_err:   0.008105
[2025-10-07 11:33:26] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -44.866988 | E_var:     0.2587 | E_err:   0.007948
[2025-10-07 11:33:34] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -44.900673 | E_var:     0.3090 | E_err:   0.008686
[2025-10-07 11:33:42] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -44.880108 | E_var:     0.2162 | E_err:   0.007264
[2025-10-07 11:33:50] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -44.869791 | E_var:     0.5218 | E_err:   0.011287
[2025-10-07 11:33:58] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -44.876706 | E_var:     0.2234 | E_err:   0.007385
[2025-10-07 11:34:06] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -44.894681 | E_var:     0.3487 | E_err:   0.009226
[2025-10-07 11:34:14] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -44.879423 | E_var:     0.2649 | E_err:   0.008042
[2025-10-07 11:34:21] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -44.873772 | E_var:     0.2338 | E_err:   0.007556
[2025-10-07 11:34:29] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -44.888539 | E_var:     0.2604 | E_err:   0.007974
[2025-10-07 11:34:37] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -44.881650 | E_var:     0.2437 | E_err:   0.007713
[2025-10-07 11:34:37] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 11:34:45] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -44.885920 | E_var:     0.2473 | E_err:   0.007771
[2025-10-07 11:34:53] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -44.875487 | E_var:     0.2284 | E_err:   0.007468
[2025-10-07 11:35:01] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -44.882358 | E_var:     0.2607 | E_err:   0.007977
[2025-10-07 11:35:08] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -44.881743 | E_var:     0.3436 | E_err:   0.009158
[2025-10-07 11:35:16] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -44.881445 | E_var:     0.2338 | E_err:   0.007555
[2025-10-07 11:35:24] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -44.874025 | E_var:     0.3042 | E_err:   0.008618
[2025-10-07 11:35:32] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -44.882537 | E_var:     0.2132 | E_err:   0.007214
[2025-10-07 11:35:40] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -44.896289 | E_var:     0.2364 | E_err:   0.007597
[2025-10-07 11:35:47] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -44.885627 | E_var:     0.2643 | E_err:   0.008034
[2025-10-07 11:35:55] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -44.881827 | E_var:     0.2733 | E_err:   0.008169
[2025-10-07 11:36:03] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -44.876933 | E_var:     0.2404 | E_err:   0.007662
[2025-10-07 11:36:11] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -44.871311 | E_var:     0.2971 | E_err:   0.008517
[2025-10-07 11:36:19] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -44.858782 | E_var:     0.3084 | E_err:   0.008677
[2025-10-07 11:36:26] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -44.884267 | E_var:     0.4591 | E_err:   0.010587
[2025-10-07 11:36:34] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -44.871090 | E_var:     0.1953 | E_err:   0.006905
[2025-10-07 11:36:42] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -44.878892 | E_var:     0.2109 | E_err:   0.007175
[2025-10-07 11:36:50] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -44.878238 | E_var:     0.2062 | E_err:   0.007096
[2025-10-07 11:36:57] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -44.875968 | E_var:     0.2632 | E_err:   0.008015
[2025-10-07 11:37:05] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -44.874214 | E_var:     0.2397 | E_err:   0.007650
[2025-10-07 11:37:13] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -44.887359 | E_var:     0.2058 | E_err:   0.007089
[2025-10-07 11:37:21] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -44.863537 | E_var:     0.2390 | E_err:   0.007638
[2025-10-07 11:37:29] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -44.879046 | E_var:     0.2210 | E_err:   0.007346
[2025-10-07 11:37:36] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -44.878415 | E_var:     0.2350 | E_err:   0.007574
[2025-10-07 11:37:44] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -44.885664 | E_var:     0.2185 | E_err:   0.007304
[2025-10-07 11:37:52] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -44.881091 | E_var:     0.2321 | E_err:   0.007528
[2025-10-07 11:38:00] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -44.897663 | E_var:     0.2366 | E_err:   0.007601
[2025-10-07 11:38:08] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -44.878350 | E_var:     0.3051 | E_err:   0.008631
[2025-10-07 11:38:15] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -44.871137 | E_var:     0.2620 | E_err:   0.007997
[2025-10-07 11:38:23] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -44.880435 | E_var:     0.2901 | E_err:   0.008416
[2025-10-07 11:38:31] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -44.865688 | E_var:     0.2193 | E_err:   0.007317
[2025-10-07 11:38:39] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -44.870260 | E_var:     0.4755 | E_err:   0.010774
[2025-10-07 11:38:47] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -44.878720 | E_var:     0.2704 | E_err:   0.008124
[2025-10-07 11:38:54] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -44.885672 | E_var:     0.1967 | E_err:   0.006930
[2025-10-07 11:39:02] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -44.876903 | E_var:     0.2275 | E_err:   0.007452
[2025-10-07 11:39:10] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -44.875735 | E_var:     0.4298 | E_err:   0.010244
[2025-10-07 11:39:18] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -44.888604 | E_var:     0.2008 | E_err:   0.007002
[2025-10-07 11:39:26] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -44.868014 | E_var:     0.2678 | E_err:   0.008085
[2025-10-07 11:39:33] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -44.892170 | E_var:     0.1925 | E_err:   0.006855
[2025-10-07 11:39:41] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -44.878478 | E_var:     0.1989 | E_err:   0.006969
[2025-10-07 11:39:49] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -44.877662 | E_var:     0.2534 | E_err:   0.007866
[2025-10-07 11:39:57] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -44.871170 | E_var:     0.5462 | E_err:   0.011547
[2025-10-07 11:40:05] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -44.892511 | E_var:     0.2052 | E_err:   0.007079
[2025-10-07 11:40:12] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -44.890209 | E_var:     0.2541 | E_err:   0.007876
[2025-10-07 11:40:20] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -44.877857 | E_var:     0.2941 | E_err:   0.008473
[2025-10-07 11:40:28] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -44.880660 | E_var:     0.2410 | E_err:   0.007671
[2025-10-07 11:40:36] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -44.899959 | E_var:     0.3641 | E_err:   0.009428
[2025-10-07 11:40:43] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -44.898712 | E_var:     0.2117 | E_err:   0.007189
[2025-10-07 11:40:51] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -44.876681 | E_var:     0.2365 | E_err:   0.007599
[2025-10-07 11:40:59] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -44.885612 | E_var:     0.2205 | E_err:   0.007337
[2025-10-07 11:41:07] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -44.882192 | E_var:     0.2535 | E_err:   0.007866
[2025-10-07 11:41:15] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -44.892756 | E_var:     0.2352 | E_err:   0.007578
[2025-10-07 11:41:22] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -44.894459 | E_var:     0.3656 | E_err:   0.009448
[2025-10-07 11:41:30] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -44.862798 | E_var:     0.3517 | E_err:   0.009266
[2025-10-07 11:41:38] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -44.885044 | E_var:     0.2319 | E_err:   0.007524
[2025-10-07 11:41:46] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -44.863788 | E_var:     0.2697 | E_err:   0.008114
[2025-10-07 11:41:54] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -44.888935 | E_var:     0.2739 | E_err:   0.008177
[2025-10-07 11:42:01] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -44.880215 | E_var:     0.3174 | E_err:   0.008803
[2025-10-07 11:42:09] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -44.876887 | E_var:     0.3352 | E_err:   0.009046
[2025-10-07 11:42:17] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -44.887015 | E_var:     0.2598 | E_err:   0.007965
[2025-10-07 11:42:25] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -44.873060 | E_var:     0.2529 | E_err:   0.007858
[2025-10-07 11:42:33] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -44.872388 | E_var:     0.2408 | E_err:   0.007667
[2025-10-07 11:42:40] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -44.872265 | E_var:     0.2049 | E_err:   0.007073
[2025-10-07 11:42:48] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -44.877888 | E_var:     0.2993 | E_err:   0.008549
[2025-10-07 11:42:56] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -44.879576 | E_var:     0.3105 | E_err:   0.008707
[2025-10-07 11:43:04] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -44.873185 | E_var:     0.1952 | E_err:   0.006904
[2025-10-07 11:43:12] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -44.893128 | E_var:     0.2862 | E_err:   0.008359
[2025-10-07 11:43:19] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -44.875925 | E_var:     0.2498 | E_err:   0.007810
[2025-10-07 11:43:27] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -44.873900 | E_var:     0.2309 | E_err:   0.007508
[2025-10-07 11:43:35] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -44.870785 | E_var:     0.2515 | E_err:   0.007836
[2025-10-07 11:43:43] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -44.879677 | E_var:     0.1787 | E_err:   0.006606
[2025-10-07 11:43:51] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -44.874100 | E_var:     0.2452 | E_err:   0.007738
[2025-10-07 11:43:58] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -44.880214 | E_var:     0.2064 | E_err:   0.007099
[2025-10-07 11:44:06] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -44.879959 | E_var:     0.2126 | E_err:   0.007204
[2025-10-07 11:44:14] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -44.872152 | E_var:     0.2296 | E_err:   0.007488
[2025-10-07 11:44:22] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -44.885341 | E_var:     0.3758 | E_err:   0.009578
[2025-10-07 11:44:30] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -44.878871 | E_var:     0.2383 | E_err:   0.007628
[2025-10-07 11:44:37] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -44.886642 | E_var:     0.2703 | E_err:   0.008124
[2025-10-07 11:44:45] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -44.879184 | E_var:     0.2150 | E_err:   0.007246
[2025-10-07 11:44:53] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -44.883289 | E_var:     0.2730 | E_err:   0.008164
[2025-10-07 11:45:01] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -44.885164 | E_var:     0.3155 | E_err:   0.008776
[2025-10-07 11:45:08] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -44.876626 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 11:45:16] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -44.876267 | E_var:     0.2138 | E_err:   0.007225
[2025-10-07 11:45:24] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -44.874757 | E_var:     0.3914 | E_err:   0.009775
[2025-10-07 11:45:32] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -44.872394 | E_var:     0.2558 | E_err:   0.007903
[2025-10-07 11:45:40] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -44.884787 | E_var:     0.2467 | E_err:   0.007760
[2025-10-07 11:45:47] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -44.881524 | E_var:     0.2039 | E_err:   0.007055
[2025-10-07 11:45:55] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -44.885146 | E_var:     0.2406 | E_err:   0.007664
[2025-10-07 11:46:03] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -44.879614 | E_var:     0.2591 | E_err:   0.007954
[2025-10-07 11:46:11] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -44.877687 | E_var:     0.3558 | E_err:   0.009320
[2025-10-07 11:46:19] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -44.888278 | E_var:     0.3048 | E_err:   0.008626
[2025-10-07 11:46:26] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -44.865890 | E_var:     0.2538 | E_err:   0.007872
[2025-10-07 11:46:34] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -44.873593 | E_var:     0.1998 | E_err:   0.006985
[2025-10-07 11:46:42] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -44.886463 | E_var:     0.1914 | E_err:   0.006835
[2025-10-07 11:46:50] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -44.886730 | E_var:     0.2166 | E_err:   0.007273
[2025-10-07 11:46:58] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -44.884174 | E_var:     0.1994 | E_err:   0.006976
[2025-10-07 11:47:05] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -44.880829 | E_var:     0.2777 | E_err:   0.008234
[2025-10-07 11:47:13] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -44.877205 | E_var:     0.2365 | E_err:   0.007598
[2025-10-07 11:47:21] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -44.887299 | E_var:     0.2166 | E_err:   0.007272
[2025-10-07 11:47:29] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -44.876829 | E_var:     0.2008 | E_err:   0.007002
[2025-10-07 11:47:37] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -44.876643 | E_var:     0.3539 | E_err:   0.009295
[2025-10-07 11:47:37] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 11:47:45] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -44.883255 | E_var:     0.2091 | E_err:   0.007145
[2025-10-07 11:47:52] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -44.879023 | E_var:     0.2033 | E_err:   0.007046
[2025-10-07 11:48:00] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -44.871377 | E_var:     0.2189 | E_err:   0.007310
[2025-10-07 11:48:08] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -44.876372 | E_var:     0.2160 | E_err:   0.007263
[2025-10-07 11:48:16] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -44.880678 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 11:48:24] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -44.880251 | E_var:     0.2084 | E_err:   0.007133
[2025-10-07 11:48:31] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -44.884657 | E_var:     0.1986 | E_err:   0.006964
[2025-10-07 11:48:39] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -44.886638 | E_var:     0.1731 | E_err:   0.006500
[2025-10-07 11:48:47] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -44.883348 | E_var:     0.3049 | E_err:   0.008628
[2025-10-07 11:48:55] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -44.871724 | E_var:     0.2344 | E_err:   0.007564
[2025-10-07 11:49:03] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -44.876818 | E_var:     0.2046 | E_err:   0.007068
[2025-10-07 11:49:10] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -44.890334 | E_var:     0.2090 | E_err:   0.007143
[2025-10-07 11:49:18] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -44.876031 | E_var:     0.2581 | E_err:   0.007938
[2025-10-07 11:49:26] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -44.882512 | E_var:     0.2533 | E_err:   0.007865
[2025-10-07 11:49:34] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -44.881902 | E_var:     0.2200 | E_err:   0.007329
[2025-10-07 11:49:42] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -44.887327 | E_var:     0.2665 | E_err:   0.008066
[2025-10-07 11:49:49] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -44.887460 | E_var:     0.2375 | E_err:   0.007614
[2025-10-07 11:49:57] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -44.880311 | E_var:     0.3738 | E_err:   0.009552
[2025-10-07 11:50:05] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -44.884767 | E_var:     0.2362 | E_err:   0.007593
[2025-10-07 11:50:13] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -44.880163 | E_var:     0.3365 | E_err:   0.009064
[2025-10-07 11:50:21] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -44.886623 | E_var:     0.2235 | E_err:   0.007386
[2025-10-07 11:50:28] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -44.883121 | E_var:     0.2062 | E_err:   0.007095
[2025-10-07 11:50:36] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -44.882329 | E_var:     0.2232 | E_err:   0.007382
[2025-10-07 11:50:44] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -44.889495 | E_var:     0.2406 | E_err:   0.007664
[2025-10-07 11:50:52] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -44.870123 | E_var:     0.1694 | E_err:   0.006432
[2025-10-07 11:51:00] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -44.885811 | E_var:     0.1533 | E_err:   0.006118
[2025-10-07 11:51:07] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -44.879064 | E_var:     0.1905 | E_err:   0.006820
[2025-10-07 11:51:15] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -44.887939 | E_var:     0.2783 | E_err:   0.008243
[2025-10-07 11:51:23] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -44.887372 | E_var:     0.1894 | E_err:   0.006799
[2025-10-07 11:51:31] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -44.883997 | E_var:     0.1884 | E_err:   0.006783
[2025-10-07 11:51:39] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -44.877133 | E_var:     0.2389 | E_err:   0.007638
[2025-10-07 11:51:46] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -44.875279 | E_var:     0.2491 | E_err:   0.007798
[2025-10-07 11:51:54] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -44.868967 | E_var:     0.2377 | E_err:   0.007619
[2025-10-07 11:52:02] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -44.876807 | E_var:     0.2492 | E_err:   0.007801
[2025-10-07 11:52:10] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -44.892385 | E_var:     0.2474 | E_err:   0.007772
[2025-10-07 11:52:17] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -44.895323 | E_var:     0.2386 | E_err:   0.007632
[2025-10-07 11:52:25] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -44.893454 | E_var:     0.2232 | E_err:   0.007381
[2025-10-07 11:52:33] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -44.876643 | E_var:     0.1933 | E_err:   0.006870
[2025-10-07 11:52:41] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -44.870067 | E_var:     0.2516 | E_err:   0.007838
[2025-10-07 11:52:49] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -44.873183 | E_var:     0.2369 | E_err:   0.007605
[2025-10-07 11:52:56] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -44.869953 | E_var:     0.2030 | E_err:   0.007040
[2025-10-07 11:53:04] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -44.886391 | E_var:     0.3220 | E_err:   0.008866
[2025-10-07 11:53:12] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -44.867408 | E_var:     0.2388 | E_err:   0.007636
[2025-10-07 11:53:20] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -44.893859 | E_var:     0.1993 | E_err:   0.006976
[2025-10-07 11:53:28] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -44.875292 | E_var:     0.1824 | E_err:   0.006673
[2025-10-07 11:53:35] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -44.870920 | E_var:     0.2514 | E_err:   0.007835
[2025-10-07 11:53:43] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -44.891565 | E_var:     0.1950 | E_err:   0.006900
[2025-10-07 11:53:51] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -44.880946 | E_var:     0.1623 | E_err:   0.006294
[2025-10-07 11:53:59] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -44.872918 | E_var:     0.2267 | E_err:   0.007439
[2025-10-07 11:54:07] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -44.873236 | E_var:     0.2156 | E_err:   0.007255
[2025-10-07 11:54:14] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -44.889677 | E_var:     0.2475 | E_err:   0.007773
[2025-10-07 11:54:22] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -44.871326 | E_var:     0.4299 | E_err:   0.010245
[2025-10-07 11:54:30] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -44.883681 | E_var:     0.2059 | E_err:   0.007090
[2025-10-07 11:54:38] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -44.884032 | E_var:     0.1952 | E_err:   0.006903
[2025-10-07 11:54:46] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -44.901761 | E_var:     0.2239 | E_err:   0.007393
[2025-10-07 11:54:53] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -44.883254 | E_var:     0.2398 | E_err:   0.007652
[2025-10-07 11:55:01] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -44.886507 | E_var:     0.2450 | E_err:   0.007734
[2025-10-07 11:55:09] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -44.884823 | E_var:     0.1894 | E_err:   0.006800
[2025-10-07 11:55:17] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -44.873640 | E_var:     0.1900 | E_err:   0.006810
[2025-10-07 11:55:25] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -44.872685 | E_var:     0.2798 | E_err:   0.008264
[2025-10-07 11:55:32] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -44.882740 | E_var:     0.2144 | E_err:   0.007235
[2025-10-07 11:55:40] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -44.873331 | E_var:     0.2223 | E_err:   0.007367
[2025-10-07 11:55:48] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -44.888126 | E_var:     0.2420 | E_err:   0.007686
[2025-10-07 11:55:56] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -44.891306 | E_var:     0.1701 | E_err:   0.006445
[2025-10-07 11:56:04] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -44.899586 | E_var:     0.2210 | E_err:   0.007345
[2025-10-07 11:56:11] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -44.884304 | E_var:     0.1989 | E_err:   0.006968
[2025-10-07 11:56:19] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -44.860621 | E_var:     0.3644 | E_err:   0.009432
[2025-10-07 11:56:27] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -44.873522 | E_var:     0.2435 | E_err:   0.007710
[2025-10-07 11:56:35] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -44.875294 | E_var:     0.2351 | E_err:   0.007576
[2025-10-07 11:56:42] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -44.880856 | E_var:     0.1872 | E_err:   0.006760
[2025-10-07 11:56:50] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -44.889504 | E_var:     0.2042 | E_err:   0.007061
[2025-10-07 11:56:58] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -44.884096 | E_var:     0.2296 | E_err:   0.007487
[2025-10-07 11:57:06] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -44.890422 | E_var:     0.1971 | E_err:   0.006936
[2025-10-07 11:57:14] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -44.883029 | E_var:     0.2518 | E_err:   0.007840
[2025-10-07 11:57:21] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -44.873384 | E_var:     0.2641 | E_err:   0.008030
[2025-10-07 11:57:29] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -44.878355 | E_var:     0.2793 | E_err:   0.008258
[2025-10-07 11:57:37] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -44.870959 | E_var:     0.2296 | E_err:   0.007488
[2025-10-07 11:57:45] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -44.863090 | E_var:     0.2608 | E_err:   0.007980
[2025-10-07 11:57:53] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -44.883139 | E_var:     0.2304 | E_err:   0.007500
[2025-10-07 11:58:00] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -44.881199 | E_var:     0.1988 | E_err:   0.006967
[2025-10-07 11:58:08] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -44.873564 | E_var:     0.2614 | E_err:   0.007989
[2025-10-07 11:58:16] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -44.890720 | E_var:     0.1650 | E_err:   0.006347
[2025-10-07 11:58:24] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -44.866671 | E_var:     0.2489 | E_err:   0.007795
[2025-10-07 11:58:32] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -44.877621 | E_var:     0.2283 | E_err:   0.007466
[2025-10-07 11:58:39] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -44.884789 | E_var:     0.3239 | E_err:   0.008893
[2025-10-07 11:58:47] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -44.883920 | E_var:     0.2164 | E_err:   0.007269
[2025-10-07 11:58:55] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -44.873173 | E_var:     0.2062 | E_err:   0.007094
[2025-10-07 11:59:03] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -44.880838 | E_var:     0.2222 | E_err:   0.007366
[2025-10-07 11:59:11] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -44.880200 | E_var:     0.2598 | E_err:   0.007965
[2025-10-07 11:59:18] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -44.874590 | E_var:     0.2897 | E_err:   0.008411
[2025-10-07 11:59:26] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -44.884627 | E_var:     0.2045 | E_err:   0.007067
[2025-10-07 11:59:34] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -44.876683 | E_var:     0.2244 | E_err:   0.007401
[2025-10-07 11:59:42] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -44.876006 | E_var:     0.3076 | E_err:   0.008666
[2025-10-07 11:59:50] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -44.874159 | E_var:     0.2670 | E_err:   0.008074
[2025-10-07 11:59:57] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -44.876312 | E_var:     0.2057 | E_err:   0.007087
[2025-10-07 12:00:05] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -44.873087 | E_var:     0.2340 | E_err:   0.007558
[2025-10-07 12:00:13] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -44.882572 | E_var:     0.3315 | E_err:   0.008996
[2025-10-07 12:00:21] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -44.884233 | E_var:     0.2190 | E_err:   0.007312
[2025-10-07 12:00:29] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -44.883842 | E_var:     0.2187 | E_err:   0.007307
[2025-10-07 12:00:36] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -44.886379 | E_var:     0.2431 | E_err:   0.007704
[2025-10-07 12:00:36] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 12:00:44] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -44.875536 | E_var:     0.2976 | E_err:   0.008523
[2025-10-07 12:00:52] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -44.883345 | E_var:     0.2938 | E_err:   0.008469
[2025-10-07 12:01:00] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -44.871095 | E_var:     0.2511 | E_err:   0.007830
[2025-10-07 12:01:08] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -44.878507 | E_var:     0.1879 | E_err:   0.006773
[2025-10-07 12:01:15] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -44.884379 | E_var:     0.2265 | E_err:   0.007436
[2025-10-07 12:01:23] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -44.884066 | E_var:     0.2236 | E_err:   0.007388
[2025-10-07 12:01:31] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -44.867603 | E_var:     0.1981 | E_err:   0.006954
[2025-10-07 12:01:39] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -44.885295 | E_var:     0.1908 | E_err:   0.006824
[2025-10-07 12:01:47] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -44.894421 | E_var:     0.2651 | E_err:   0.008045
[2025-10-07 12:01:54] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -44.886238 | E_var:     0.1912 | E_err:   0.006831
[2025-10-07 12:02:02] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -44.880039 | E_var:     0.1772 | E_err:   0.006577
[2025-10-07 12:02:10] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -44.881452 | E_var:     0.2335 | E_err:   0.007550
[2025-10-07 12:02:18] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -44.881311 | E_var:     0.2621 | E_err:   0.007999
[2025-10-07 12:02:26] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -44.874958 | E_var:     0.3226 | E_err:   0.008874
[2025-10-07 12:02:33] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -44.871154 | E_var:     0.1970 | E_err:   0.006935
[2025-10-07 12:02:41] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -44.888003 | E_var:     0.2124 | E_err:   0.007202
[2025-10-07 12:02:49] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -44.867759 | E_var:     0.2437 | E_err:   0.007713
[2025-10-07 12:02:57] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -44.881579 | E_var:     0.2155 | E_err:   0.007254
[2025-10-07 12:03:04] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -44.882161 | E_var:     0.2727 | E_err:   0.008159
[2025-10-07 12:03:12] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -44.882658 | E_var:     0.1965 | E_err:   0.006926
[2025-10-07 12:03:20] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -44.879904 | E_var:     0.2003 | E_err:   0.006993
[2025-10-07 12:03:28] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -44.879760 | E_var:     0.2242 | E_err:   0.007398
[2025-10-07 12:03:36] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -44.875288 | E_var:     0.1975 | E_err:   0.006944
[2025-10-07 12:03:43] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -44.871711 | E_var:     0.2448 | E_err:   0.007731
[2025-10-07 12:03:51] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -44.875263 | E_var:     0.2490 | E_err:   0.007797
[2025-10-07 12:03:59] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -44.870898 | E_var:     0.2099 | E_err:   0.007159
[2025-10-07 12:04:07] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -44.874960 | E_var:     0.2181 | E_err:   0.007297
[2025-10-07 12:04:15] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -44.883074 | E_var:     0.2211 | E_err:   0.007347
[2025-10-07 12:04:22] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -44.881434 | E_var:     0.2349 | E_err:   0.007574
[2025-10-07 12:04:30] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -44.878862 | E_var:     0.2290 | E_err:   0.007477
[2025-10-07 12:04:38] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -44.892077 | E_var:     0.2503 | E_err:   0.007817
[2025-10-07 12:04:46] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -44.883146 | E_var:     0.2182 | E_err:   0.007299
[2025-10-07 12:04:54] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -44.876166 | E_var:     0.1782 | E_err:   0.006597
[2025-10-07 12:05:01] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -44.885925 | E_var:     0.2055 | E_err:   0.007084
[2025-10-07 12:05:09] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -44.880191 | E_var:     0.2074 | E_err:   0.007116
[2025-10-07 12:05:17] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -44.873901 | E_var:     0.2228 | E_err:   0.007375
[2025-10-07 12:05:25] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -44.881666 | E_var:     0.2382 | E_err:   0.007626
[2025-10-07 12:05:33] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -44.890209 | E_var:     0.1842 | E_err:   0.006707
[2025-10-07 12:05:40] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -44.886461 | E_var:     0.1859 | E_err:   0.006737
[2025-10-07 12:05:48] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -44.886768 | E_var:     0.2099 | E_err:   0.007158
[2025-10-07 12:05:56] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -44.882412 | E_var:     0.1800 | E_err:   0.006629
[2025-10-07 12:06:04] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -44.879758 | E_var:     0.2225 | E_err:   0.007370
[2025-10-07 12:06:11] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -44.886493 | E_var:     0.2414 | E_err:   0.007676
[2025-10-07 12:06:19] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -44.884754 | E_var:     0.3093 | E_err:   0.008690
[2025-10-07 12:06:27] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -44.893883 | E_var:     0.2581 | E_err:   0.007938
[2025-10-07 12:06:35] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -44.870046 | E_var:     0.2032 | E_err:   0.007044
[2025-10-07 12:06:43] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -44.869648 | E_var:     0.5055 | E_err:   0.011109
[2025-10-07 12:06:50] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -44.874137 | E_var:     0.3097 | E_err:   0.008695
[2025-10-07 12:06:58] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -44.870946 | E_var:     0.1873 | E_err:   0.006763
[2025-10-07 12:07:06] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -44.894298 | E_var:     0.2401 | E_err:   0.007657
[2025-10-07 12:07:14] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -44.896557 | E_var:     0.2626 | E_err:   0.008007
[2025-10-07 12:07:22] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -44.880200 | E_var:     0.2680 | E_err:   0.008089
[2025-10-07 12:07:29] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -44.887360 | E_var:     0.2081 | E_err:   0.007127
[2025-10-07 12:07:37] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -44.877813 | E_var:     0.2449 | E_err:   0.007733
[2025-10-07 12:07:45] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -44.892456 | E_var:     0.2022 | E_err:   0.007026
[2025-10-07 12:07:53] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -44.875828 | E_var:     0.2211 | E_err:   0.007348
[2025-10-07 12:08:01] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -44.892667 | E_var:     0.2178 | E_err:   0.007292
[2025-10-07 12:08:08] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -44.881687 | E_var:     0.1892 | E_err:   0.006796
[2025-10-07 12:08:16] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -44.875755 | E_var:     0.2321 | E_err:   0.007527
[2025-10-07 12:08:24] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -44.875723 | E_var:     0.2239 | E_err:   0.007394
[2025-10-07 12:08:32] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -44.886965 | E_var:     0.2153 | E_err:   0.007250
[2025-10-07 12:08:40] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -44.890018 | E_var:     0.1900 | E_err:   0.006811
[2025-10-07 12:08:47] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -44.877952 | E_var:     0.2142 | E_err:   0.007232
[2025-10-07 12:08:55] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -44.888418 | E_var:     0.2720 | E_err:   0.008150
[2025-10-07 12:09:03] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -44.876031 | E_var:     0.3009 | E_err:   0.008570
[2025-10-07 12:09:11] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -44.885485 | E_var:     0.2410 | E_err:   0.007671
[2025-10-07 12:09:19] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -44.885702 | E_var:     0.3448 | E_err:   0.009174
[2025-10-07 12:09:26] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -44.870915 | E_var:     0.2390 | E_err:   0.007638
[2025-10-07 12:09:34] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -44.890830 | E_var:     0.4806 | E_err:   0.010832
[2025-10-07 12:09:42] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -44.872864 | E_var:     0.3500 | E_err:   0.009244
[2025-10-07 12:09:50] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -44.890963 | E_var:     0.2178 | E_err:   0.007292
[2025-10-07 12:09:58] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -44.885739 | E_var:     0.2257 | E_err:   0.007422
[2025-10-07 12:10:05] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -44.884960 | E_var:     0.2852 | E_err:   0.008344
[2025-10-07 12:10:13] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -44.878699 | E_var:     0.2219 | E_err:   0.007361
[2025-10-07 12:10:21] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -44.900513 | E_var:     0.2212 | E_err:   0.007348
[2025-10-07 12:10:29] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -44.892295 | E_var:     0.2909 | E_err:   0.008428
[2025-10-07 12:10:37] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -44.886091 | E_var:     0.2360 | E_err:   0.007591
[2025-10-07 12:10:44] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -44.885644 | E_var:     0.2428 | E_err:   0.007699
[2025-10-07 12:10:52] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -44.884312 | E_var:     0.2047 | E_err:   0.007070
[2025-10-07 12:11:00] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -44.882147 | E_var:     0.2228 | E_err:   0.007375
[2025-10-07 12:11:08] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -44.871779 | E_var:     0.2323 | E_err:   0.007530
[2025-10-07 12:11:15] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -44.895139 | E_var:     0.2174 | E_err:   0.007285
[2025-10-07 12:11:23] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -44.900286 | E_var:     0.1871 | E_err:   0.006758
[2025-10-07 12:11:31] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -44.876915 | E_var:     0.1817 | E_err:   0.006660
[2025-10-07 12:11:39] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -44.878487 | E_var:     0.2184 | E_err:   0.007302
[2025-10-07 12:11:47] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -44.888593 | E_var:     0.2572 | E_err:   0.007925
[2025-10-07 12:11:54] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -44.877829 | E_var:     0.2135 | E_err:   0.007219
[2025-10-07 12:12:02] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -44.887503 | E_var:     0.2427 | E_err:   0.007698
[2025-10-07 12:12:10] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -44.871604 | E_var:     0.2478 | E_err:   0.007778
[2025-10-07 12:12:18] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -44.893829 | E_var:     0.2127 | E_err:   0.007207
[2025-10-07 12:12:26] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -44.874458 | E_var:     0.2908 | E_err:   0.008426
[2025-10-07 12:12:33] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -44.874432 | E_var:     0.2476 | E_err:   0.007774
[2025-10-07 12:12:41] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -44.876155 | E_var:     0.2108 | E_err:   0.007175
[2025-10-07 12:12:49] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -44.880670 | E_var:     0.2010 | E_err:   0.007006
[2025-10-07 12:12:57] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -44.869682 | E_var:     0.2270 | E_err:   0.007444
[2025-10-07 12:13:05] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -44.887157 | E_var:     0.2389 | E_err:   0.007636
[2025-10-07 12:13:12] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -44.890667 | E_var:     0.3268 | E_err:   0.008933
[2025-10-07 12:13:20] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -44.881607 | E_var:     0.2748 | E_err:   0.008191
[2025-10-07 12:13:28] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -44.882452 | E_var:     0.2054 | E_err:   0.007081
[2025-10-07 12:13:36] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -44.867234 | E_var:     1.1855 | E_err:   0.017012
[2025-10-07 12:13:36] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 12:13:44] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -44.890154 | E_var:     0.2149 | E_err:   0.007244
[2025-10-07 12:13:51] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -44.892370 | E_var:     0.2698 | E_err:   0.008115
[2025-10-07 12:13:59] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -44.876098 | E_var:     0.3202 | E_err:   0.008841
[2025-10-07 12:14:07] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -44.887370 | E_var:     0.2112 | E_err:   0.007182
[2025-10-07 12:14:15] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -44.884497 | E_var:     0.2058 | E_err:   0.007088
[2025-10-07 12:14:23] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -44.874438 | E_var:     0.2063 | E_err:   0.007097
[2025-10-07 12:14:30] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -44.890308 | E_var:     0.2200 | E_err:   0.007329
[2025-10-07 12:14:38] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -44.888194 | E_var:     0.2070 | E_err:   0.007110
[2025-10-07 12:14:46] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -44.885603 | E_var:     0.1982 | E_err:   0.006956
[2025-10-07 12:14:54] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -44.878003 | E_var:     0.2175 | E_err:   0.007286
[2025-10-07 12:15:02] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -44.874040 | E_var:     0.2570 | E_err:   0.007921
[2025-10-07 12:15:09] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -44.883803 | E_var:     0.2067 | E_err:   0.007104
[2025-10-07 12:15:17] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -44.880703 | E_var:     0.3004 | E_err:   0.008564
[2025-10-07 12:15:25] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -44.882019 | E_var:     0.2132 | E_err:   0.007214
[2025-10-07 12:15:33] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -44.887690 | E_var:     0.2402 | E_err:   0.007658
[2025-10-07 12:15:41] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -44.878787 | E_var:     0.1943 | E_err:   0.006888
[2025-10-07 12:15:48] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -44.865281 | E_var:     0.3549 | E_err:   0.009308
[2025-10-07 12:15:56] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -44.884915 | E_var:     0.2562 | E_err:   0.007909
[2025-10-07 12:16:04] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -44.887702 | E_var:     0.1980 | E_err:   0.006953
[2025-10-07 12:16:12] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -44.870615 | E_var:     0.2395 | E_err:   0.007647
[2025-10-07 12:16:19] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -44.882214 | E_var:     0.2385 | E_err:   0.007631
[2025-10-07 12:16:27] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -44.875944 | E_var:     0.2586 | E_err:   0.007945
[2025-10-07 12:16:35] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -44.877906 | E_var:     0.2483 | E_err:   0.007786
[2025-10-07 12:16:43] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -44.889570 | E_var:     0.2356 | E_err:   0.007584
[2025-10-07 12:16:51] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -44.891120 | E_var:     0.3974 | E_err:   0.009850
[2025-10-07 12:16:58] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -44.876378 | E_var:     0.3056 | E_err:   0.008638
[2025-10-07 12:17:06] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -44.889456 | E_var:     0.2207 | E_err:   0.007340
[2025-10-07 12:17:14] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -44.885067 | E_var:     0.1942 | E_err:   0.006886
[2025-10-07 12:17:22] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -44.883709 | E_var:     0.5027 | E_err:   0.011078
[2025-10-07 12:17:30] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -44.890990 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 12:17:37] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -44.866741 | E_var:     0.2053 | E_err:   0.007080
[2025-10-07 12:17:45] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -44.880861 | E_var:     0.2321 | E_err:   0.007527
[2025-10-07 12:17:53] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -44.891571 | E_var:     0.2286 | E_err:   0.007470
[2025-10-07 12:18:01] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -44.880498 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 12:18:09] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -44.887876 | E_var:     0.2057 | E_err:   0.007087
[2025-10-07 12:18:16] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -44.878842 | E_var:     0.2352 | E_err:   0.007577
[2025-10-07 12:18:24] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -44.886539 | E_var:     0.2033 | E_err:   0.007046
[2025-10-07 12:18:32] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -44.867559 | E_var:     0.1870 | E_err:   0.006757
[2025-10-07 12:18:40] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -44.879213 | E_var:     0.2159 | E_err:   0.007260
[2025-10-07 12:18:48] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -44.886699 | E_var:     0.2152 | E_err:   0.007249
[2025-10-07 12:18:55] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -44.879697 | E_var:     0.1906 | E_err:   0.006822
[2025-10-07 12:19:03] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -44.870473 | E_var:     0.2149 | E_err:   0.007243
[2025-10-07 12:19:11] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -44.880700 | E_var:     0.1988 | E_err:   0.006966
[2025-10-07 12:19:19] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -44.870733 | E_var:     0.2861 | E_err:   0.008358
[2025-10-07 12:19:27] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -44.876424 | E_var:     0.2746 | E_err:   0.008188
[2025-10-07 12:19:34] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -44.897781 | E_var:     0.2116 | E_err:   0.007188
[2025-10-07 12:19:42] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -44.879220 | E_var:     0.3697 | E_err:   0.009500
[2025-10-07 12:19:50] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -44.890866 | E_var:     0.2034 | E_err:   0.007047
[2025-10-07 12:19:58] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -44.879435 | E_var:     0.2366 | E_err:   0.007601
[2025-10-07 12:20:06] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -44.871662 | E_var:     0.3690 | E_err:   0.009491
[2025-10-07 12:20:06] ======================================================================================================
[2025-10-07 12:20:06] ✅ Training completed successfully
[2025-10-07 12:20:06] Total restarts: 2
[2025-10-07 12:20:08] Final Energy: -44.87166235 ± 0.00949095
[2025-10-07 12:20:08] Final Variance: 0.368960
[2025-10-07 12:20:08] ======================================================================================================
[2025-10-07 12:20:08] ======================================================================================================
[2025-10-07 12:20:08] Training completed | Runtime: 8253.4s
[2025-10-07 12:20:11] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 12:20:11] ======================================================================================================
