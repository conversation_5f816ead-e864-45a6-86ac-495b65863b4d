[2025-10-06 10:39:47] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.79/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 10:39:47]   - 迭代次数: final
[2025-10-06 10:39:47]   - 能量: -44.238868-0.002307j ± 0.007938, Var: 0.258126
[2025-10-06 10:39:47]   - 时间戳: 2025-10-06T10:39:30.910961+08:00
[2025-10-06 10:40:06] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 10:40:06] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 10:40:06] ======================================================================================================
[2025-10-06 10:40:06] GCNN for Shastry-Sutherland Model
[2025-10-06 10:40:06] ======================================================================================================
[2025-10-06 10:40:06] System parameters:
[2025-10-06 10:40:06]   - System size: L=5, N=100
[2025-10-06 10:40:06]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-10-06 10:40:06] ------------------------------------------------------------------------------------------------------
[2025-10-06 10:40:06] Model parameters:
[2025-10-06 10:40:06]   - Number of layers = 4
[2025-10-06 10:40:06]   - Number of features = 4
[2025-10-06 10:40:06]   - Total parameters = 19628
[2025-10-06 10:40:06] ------------------------------------------------------------------------------------------------------
[2025-10-06 10:40:06] Training parameters:
[2025-10-06 10:40:06]   - Total iterations: 1050
[2025-10-06 10:40:06]   - Annealing cycles: 3
[2025-10-06 10:40:06]   - Initial period: 150
[2025-10-06 10:40:06]   - Period multiplier: 2.0
[2025-10-06 10:40:06]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 10:40:06]   - Samples: 4096
[2025-10-06 10:40:06]   - Discarded samples: 0
[2025-10-06 10:40:06]   - Chunk size: 4096
[2025-10-06 10:40:06]   - Diagonal shift: 0.15
[2025-10-06 10:40:06]   - Gradient clipping: 1.0
[2025-10-06 10:40:06]   - Checkpoint enabled: interval=100
[2025-10-06 10:40:06]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.80/model_L4F4/training/checkpoints
[2025-10-06 10:40:06] ------------------------------------------------------------------------------------------------------
[2025-10-06 10:40:06] Device status:
[2025-10-06 10:40:06]   - Devices model: NVIDIA H200 NVL
[2025-10-06 10:40:06]   - Number of devices: 1
[2025-10-06 10:40:06]   - Sharding: True
[2025-10-06 10:40:07] ======================================================================================================
[2025-10-06 10:40:37] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -44.869304 | E_var:     0.8919 | E_err:   0.014756
[2025-10-06 10:40:57] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -44.880892 | E_var:     0.4510 | E_err:   0.010493
[2025-10-06 10:41:02] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -44.881239 | E_var:     0.3539 | E_err:   0.009295
[2025-10-06 10:41:07] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -44.871399 | E_var:     0.3230 | E_err:   0.008880
[2025-10-06 10:41:13] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -44.885502 | E_var:     0.2591 | E_err:   0.007953
[2025-10-06 10:41:18] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -44.872119 | E_var:     0.3335 | E_err:   0.009023
[2025-10-06 10:41:23] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -44.891284 | E_var:     0.2739 | E_err:   0.008178
[2025-10-06 10:41:28] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -44.895874 | E_var:     0.2365 | E_err:   0.007598
[2025-10-06 10:41:33] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -44.862173 | E_var:     0.2382 | E_err:   0.007626
[2025-10-06 10:41:38] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -44.881464 | E_var:     0.2588 | E_err:   0.007949
[2025-10-06 10:41:43] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -44.886969 | E_var:     0.2784 | E_err:   0.008245
[2025-10-06 10:41:49] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -44.887394 | E_var:     0.2786 | E_err:   0.008247
[2025-10-06 10:41:54] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -44.868034 | E_var:     0.2826 | E_err:   0.008306
[2025-10-06 10:41:59] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -44.885798 | E_var:     0.2528 | E_err:   0.007856
[2025-10-06 10:42:04] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -44.885753 | E_var:     0.2711 | E_err:   0.008135
[2025-10-06 10:42:09] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -44.885435 | E_var:     0.2370 | E_err:   0.007607
[2025-10-06 10:42:14] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -44.870839 | E_var:     0.3116 | E_err:   0.008722
[2025-10-06 10:42:19] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -44.879522 | E_var:     0.2603 | E_err:   0.007971
[2025-10-06 10:42:25] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -44.874241 | E_var:     0.2009 | E_err:   0.007003
[2025-10-06 10:42:30] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -44.886069 | E_var:     0.2478 | E_err:   0.007779
[2025-10-06 10:42:35] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -44.890111 | E_var:     0.2532 | E_err:   0.007863
[2025-10-06 10:42:40] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -44.892061 | E_var:     0.2348 | E_err:   0.007571
[2025-10-06 10:42:45] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -44.862726 | E_var:     0.3121 | E_err:   0.008729
[2025-10-06 10:42:50] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -44.878999 | E_var:     0.2099 | E_err:   0.007159
[2025-10-06 10:42:56] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -44.880156 | E_var:     0.2896 | E_err:   0.008408
[2025-10-06 10:43:01] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -44.867521 | E_var:     0.4306 | E_err:   0.010253
[2025-10-06 10:43:06] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -44.880942 | E_var:     0.2266 | E_err:   0.007438
[2025-10-06 10:43:11] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -44.893054 | E_var:     0.2231 | E_err:   0.007380
[2025-10-06 10:43:16] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -44.880032 | E_var:     0.2078 | E_err:   0.007122
[2025-10-06 10:43:21] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -44.882802 | E_var:     0.2094 | E_err:   0.007150
[2025-10-06 10:43:26] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -44.875944 | E_var:     0.2008 | E_err:   0.007002
[2025-10-06 10:43:32] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -44.880221 | E_var:     0.2046 | E_err:   0.007067
[2025-10-06 10:43:37] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -44.888218 | E_var:     0.2260 | E_err:   0.007428
[2025-10-06 10:43:42] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -44.875050 | E_var:     0.2314 | E_err:   0.007517
[2025-10-06 10:43:47] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -44.881054 | E_var:     0.2584 | E_err:   0.007943
[2025-10-06 10:43:52] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -44.877072 | E_var:     0.2382 | E_err:   0.007626
[2025-10-06 10:43:57] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -44.895510 | E_var:     0.2590 | E_err:   0.007952
[2025-10-06 10:44:03] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -44.878951 | E_var:     0.2441 | E_err:   0.007720
[2025-10-06 10:44:08] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -44.882085 | E_var:     0.1935 | E_err:   0.006873
[2025-10-06 10:44:13] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -44.884195 | E_var:     0.2478 | E_err:   0.007778
[2025-10-06 10:44:18] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -44.870939 | E_var:     0.9332 | E_err:   0.015094
[2025-10-06 10:44:23] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -44.870267 | E_var:     0.1915 | E_err:   0.006837
[2025-10-06 10:44:28] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -44.887785 | E_var:     0.1909 | E_err:   0.006827
[2025-10-06 10:44:33] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -44.891410 | E_var:     0.2527 | E_err:   0.007854
[2025-10-06 10:44:39] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -44.875164 | E_var:     0.2644 | E_err:   0.008034
[2025-10-06 10:44:44] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -44.878209 | E_var:     0.1989 | E_err:   0.006969
[2025-10-06 10:44:49] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -44.886645 | E_var:     0.2838 | E_err:   0.008324
[2025-10-06 10:44:54] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -44.880971 | E_var:     0.2657 | E_err:   0.008055
[2025-10-06 10:44:59] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -44.875595 | E_var:     0.2456 | E_err:   0.007743
[2025-10-06 10:45:04] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -44.883643 | E_var:     0.2188 | E_err:   0.007308
[2025-10-06 10:45:09] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -44.888229 | E_var:     0.3215 | E_err:   0.008860
[2025-10-06 10:45:15] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -44.885262 | E_var:     0.3018 | E_err:   0.008583
[2025-10-06 10:45:20] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -44.875693 | E_var:     0.2249 | E_err:   0.007410
[2025-10-06 10:45:25] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -44.881249 | E_var:     0.2629 | E_err:   0.008011
[2025-10-06 10:45:30] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -44.873352 | E_var:     0.2024 | E_err:   0.007029
[2025-10-06 10:45:35] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -44.878080 | E_var:     0.2444 | E_err:   0.007725
[2025-10-06 10:45:40] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -44.885848 | E_var:     0.2564 | E_err:   0.007912
[2025-10-06 10:45:46] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -44.882127 | E_var:     0.2473 | E_err:   0.007771
[2025-10-06 10:45:51] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -44.876929 | E_var:     0.2228 | E_err:   0.007375
[2025-10-06 10:45:56] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -44.866114 | E_var:     0.1996 | E_err:   0.006981
[2025-10-06 10:46:01] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -44.875105 | E_var:     0.3390 | E_err:   0.009098
[2025-10-06 10:46:06] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -44.870723 | E_var:     0.5130 | E_err:   0.011192
[2025-10-06 10:46:11] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -44.889715 | E_var:     0.2826 | E_err:   0.008306
[2025-10-06 10:46:16] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -44.889795 | E_var:     0.2396 | E_err:   0.007648
[2025-10-06 10:46:22] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -44.880153 | E_var:     0.2669 | E_err:   0.008073
[2025-10-06 10:46:27] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -44.885418 | E_var:     0.1888 | E_err:   0.006789
[2025-10-06 10:46:32] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -44.883960 | E_var:     0.2637 | E_err:   0.008024
[2025-10-06 10:46:37] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -44.898452 | E_var:     0.3152 | E_err:   0.008773
[2025-10-06 10:46:42] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -44.887584 | E_var:     0.2298 | E_err:   0.007490
[2025-10-06 10:46:47] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -44.888717 | E_var:     0.2358 | E_err:   0.007588
[2025-10-06 10:46:52] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -44.890213 | E_var:     0.2537 | E_err:   0.007870
[2025-10-06 10:46:58] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -44.884520 | E_var:     0.2924 | E_err:   0.008449
[2025-10-06 10:47:03] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -44.871775 | E_var:     0.2349 | E_err:   0.007572
[2025-10-06 10:47:08] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -44.875469 | E_var:     0.2373 | E_err:   0.007612
[2025-10-06 10:47:13] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -44.886027 | E_var:     0.2302 | E_err:   0.007496
[2025-10-06 10:47:19] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -44.879596 | E_var:     0.3135 | E_err:   0.008748
[2025-10-06 10:47:24] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -44.863725 | E_var:     0.3864 | E_err:   0.009713
[2025-10-06 10:47:29] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -44.871903 | E_var:     0.2487 | E_err:   0.007793
[2025-10-06 10:47:34] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -44.881635 | E_var:     0.2383 | E_err:   0.007627
[2025-10-06 10:47:39] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -44.875551 | E_var:     0.2421 | E_err:   0.007689
[2025-10-06 10:47:44] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -44.878874 | E_var:     0.2477 | E_err:   0.007776
[2025-10-06 10:47:49] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -44.891753 | E_var:     0.2079 | E_err:   0.007125
[2025-10-06 10:47:55] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -44.886122 | E_var:     0.1937 | E_err:   0.006877
[2025-10-06 10:48:00] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -44.886755 | E_var:     0.2085 | E_err:   0.007134
[2025-10-06 10:48:05] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -44.889796 | E_var:     0.2345 | E_err:   0.007566
[2025-10-06 10:48:10] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -44.874574 | E_var:     0.2664 | E_err:   0.008065
[2025-10-06 10:48:15] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -44.881134 | E_var:     0.2156 | E_err:   0.007255
[2025-10-06 10:48:20] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -44.883152 | E_var:     0.2033 | E_err:   0.007046
[2025-10-06 10:48:25] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -44.872457 | E_var:     0.3239 | E_err:   0.008892
[2025-10-06 10:48:31] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -44.882096 | E_var:     0.2155 | E_err:   0.007253
[2025-10-06 10:48:36] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -44.879798 | E_var:     0.3000 | E_err:   0.008559
[2025-10-06 10:48:41] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -44.886516 | E_var:     0.2149 | E_err:   0.007243
[2025-10-06 10:48:46] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -44.891419 | E_var:     0.2129 | E_err:   0.007210
[2025-10-06 10:48:51] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -44.869927 | E_var:     0.1937 | E_err:   0.006877
[2025-10-06 10:48:56] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -44.886189 | E_var:     0.2314 | E_err:   0.007516
[2025-10-06 10:49:02] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -44.879124 | E_var:     0.1845 | E_err:   0.006711
[2025-10-06 10:49:07] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -44.877015 | E_var:     0.2231 | E_err:   0.007381
[2025-10-06 10:49:12] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -44.882119 | E_var:     0.2033 | E_err:   0.007045
[2025-10-06 10:49:17] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -44.886681 | E_var:     0.2153 | E_err:   0.007250
[2025-10-06 10:49:22] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -44.885719 | E_var:     0.2338 | E_err:   0.007554
[2025-10-06 10:49:22] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 10:49:27] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -44.888002 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 10:49:32] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -44.886232 | E_var:     0.4649 | E_err:   0.010653
[2025-10-06 10:49:38] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -44.883412 | E_var:     0.2381 | E_err:   0.007624
[2025-10-06 10:49:43] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -44.876264 | E_var:     0.3097 | E_err:   0.008696
[2025-10-06 10:49:48] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -44.881577 | E_var:     0.2424 | E_err:   0.007693
[2025-10-06 10:49:53] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -44.875365 | E_var:     0.2311 | E_err:   0.007512
[2025-10-06 10:49:58] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -44.883675 | E_var:     0.3187 | E_err:   0.008820
[2025-10-06 10:50:03] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -44.885489 | E_var:     0.2107 | E_err:   0.007172
[2025-10-06 10:50:09] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -44.879387 | E_var:     0.2275 | E_err:   0.007452
[2025-10-06 10:50:14] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -44.888348 | E_var:     0.2097 | E_err:   0.007155
[2025-10-06 10:50:19] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -44.877092 | E_var:     0.2351 | E_err:   0.007577
[2025-10-06 10:50:24] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -44.888016 | E_var:     0.2187 | E_err:   0.007308
[2025-10-06 10:50:29] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -44.872299 | E_var:     0.1991 | E_err:   0.006972
[2025-10-06 10:50:35] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -44.869184 | E_var:     0.2198 | E_err:   0.007326
[2025-10-06 10:50:40] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -44.880673 | E_var:     0.2100 | E_err:   0.007160
[2025-10-06 10:50:45] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -44.875607 | E_var:     0.3241 | E_err:   0.008895
[2025-10-06 10:50:50] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -44.868272 | E_var:     0.2608 | E_err:   0.007980
[2025-10-06 10:50:55] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -44.884907 | E_var:     0.3803 | E_err:   0.009636
[2025-10-06 10:51:00] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -44.886898 | E_var:     0.2026 | E_err:   0.007032
[2025-10-06 10:51:05] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -44.882328 | E_var:     0.2031 | E_err:   0.007042
[2025-10-06 10:51:11] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -44.881219 | E_var:     0.2331 | E_err:   0.007544
[2025-10-06 10:51:16] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -44.876569 | E_var:     0.2475 | E_err:   0.007774
[2025-10-06 10:51:21] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -44.875392 | E_var:     0.2104 | E_err:   0.007167
[2025-10-06 10:51:26] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -44.885101 | E_var:     0.2066 | E_err:   0.007103
[2025-10-06 10:51:31] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -44.885789 | E_var:     0.2256 | E_err:   0.007422
[2025-10-06 10:51:36] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -44.877871 | E_var:     0.1846 | E_err:   0.006714
[2025-10-06 10:51:41] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -44.879087 | E_var:     0.3281 | E_err:   0.008949
[2025-10-06 10:51:47] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -44.901674 | E_var:     0.3753 | E_err:   0.009573
[2025-10-06 10:51:52] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -44.884527 | E_var:     0.2002 | E_err:   0.006992
[2025-10-06 10:51:57] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -44.894363 | E_var:     0.2134 | E_err:   0.007218
[2025-10-06 10:52:02] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -44.882951 | E_var:     0.2328 | E_err:   0.007538
[2025-10-06 10:52:07] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -44.885291 | E_var:     0.2413 | E_err:   0.007676
[2025-10-06 10:52:12] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -44.876218 | E_var:     0.1769 | E_err:   0.006572
[2025-10-06 10:52:17] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -44.879117 | E_var:     0.1916 | E_err:   0.006839
[2025-10-06 10:52:23] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -44.883942 | E_var:     0.2311 | E_err:   0.007512
[2025-10-06 10:52:28] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -44.879549 | E_var:     0.2379 | E_err:   0.007622
[2025-10-06 10:52:33] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -44.871765 | E_var:     0.1960 | E_err:   0.006917
[2025-10-06 10:52:38] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -44.880065 | E_var:     0.2267 | E_err:   0.007440
[2025-10-06 10:52:43] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -44.869735 | E_var:     0.2222 | E_err:   0.007365
[2025-10-06 10:52:48] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -44.891057 | E_var:     0.1858 | E_err:   0.006735
[2025-10-06 10:52:54] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -44.882302 | E_var:     0.2085 | E_err:   0.007135
[2025-10-06 10:52:59] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -44.881894 | E_var:     0.1876 | E_err:   0.006768
[2025-10-06 10:53:04] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -44.882398 | E_var:     0.2306 | E_err:   0.007503
[2025-10-06 10:53:09] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -44.874157 | E_var:     0.2354 | E_err:   0.007582
[2025-10-06 10:53:14] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -44.901561 | E_var:     0.5540 | E_err:   0.011630
[2025-10-06 10:53:19] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -44.882640 | E_var:     0.2906 | E_err:   0.008423
[2025-10-06 10:53:24] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -44.876320 | E_var:     0.2869 | E_err:   0.008369
[2025-10-06 10:53:30] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -44.889932 | E_var:     0.2475 | E_err:   0.007773
[2025-10-06 10:53:35] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -44.893514 | E_var:     0.2462 | E_err:   0.007752
[2025-10-06 10:53:40] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -44.899194 | E_var:     0.2263 | E_err:   0.007433
[2025-10-06 10:53:40] 🔄 RESTART #1 | Period: 300
[2025-10-06 10:53:45] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -44.880631 | E_var:     0.2050 | E_err:   0.007074
[2025-10-06 10:53:50] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -44.873701 | E_var:     0.2818 | E_err:   0.008294
[2025-10-06 10:53:55] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -44.887039 | E_var:     0.1822 | E_err:   0.006669
[2025-10-06 10:54:00] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -44.885508 | E_var:     0.2073 | E_err:   0.007115
[2025-10-06 10:54:06] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -44.884656 | E_var:     0.2453 | E_err:   0.007738
[2025-10-06 10:54:11] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -44.875850 | E_var:     0.2151 | E_err:   0.007247
[2025-10-06 10:54:16] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -44.885379 | E_var:     0.1676 | E_err:   0.006396
[2025-10-06 10:54:21] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -44.883057 | E_var:     0.2564 | E_err:   0.007912
[2025-10-06 10:54:26] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -44.872547 | E_var:     0.2051 | E_err:   0.007076
[2025-10-06 10:54:31] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -44.878201 | E_var:     0.2438 | E_err:   0.007714
[2025-10-06 10:54:36] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -44.883079 | E_var:     0.2083 | E_err:   0.007130
[2025-10-06 10:54:42] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -44.891194 | E_var:     0.2260 | E_err:   0.007428
[2025-10-06 10:54:47] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -44.884347 | E_var:     0.2190 | E_err:   0.007311
[2025-10-06 10:54:52] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -44.885692 | E_var:     0.2497 | E_err:   0.007807
[2025-10-06 10:54:57] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -44.884846 | E_var:     0.2413 | E_err:   0.007676
[2025-10-06 10:55:02] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -44.884019 | E_var:     0.2280 | E_err:   0.007461
[2025-10-06 10:55:07] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -44.886175 | E_var:     0.1799 | E_err:   0.006627
[2025-10-06 10:55:12] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -44.885494 | E_var:     0.2293 | E_err:   0.007482
[2025-10-06 10:55:18] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -44.882398 | E_var:     0.2245 | E_err:   0.007404
[2025-10-06 10:55:23] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -44.886490 | E_var:     0.1798 | E_err:   0.006626
[2025-10-06 10:55:28] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -44.879648 | E_var:     0.2409 | E_err:   0.007669
[2025-10-06 10:55:33] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -44.882394 | E_var:     0.3453 | E_err:   0.009182
[2025-10-06 10:55:38] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -44.877736 | E_var:     0.3503 | E_err:   0.009248
[2025-10-06 10:55:43] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -44.874896 | E_var:     0.2175 | E_err:   0.007287
[2025-10-06 10:55:48] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -44.877310 | E_var:     0.2569 | E_err:   0.007920
[2025-10-06 10:55:54] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -44.888697 | E_var:     0.1983 | E_err:   0.006957
[2025-10-06 10:55:59] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -44.882159 | E_var:     0.2982 | E_err:   0.008532
[2025-10-06 10:56:04] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -44.877963 | E_var:     0.2325 | E_err:   0.007534
[2025-10-06 10:56:09] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -44.880606 | E_var:     0.2279 | E_err:   0.007460
[2025-10-06 10:56:14] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -44.879202 | E_var:     0.4986 | E_err:   0.011033
[2025-10-06 10:56:19] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -44.874237 | E_var:     0.2708 | E_err:   0.008131
[2025-10-06 10:56:24] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -44.886067 | E_var:     0.2605 | E_err:   0.007975
[2025-10-06 10:56:30] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -44.878052 | E_var:     0.2116 | E_err:   0.007188
[2025-10-06 10:56:35] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -44.884842 | E_var:     0.2253 | E_err:   0.007417
[2025-10-06 10:56:40] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -44.876751 | E_var:     0.2716 | E_err:   0.008143
[2025-10-06 10:56:45] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -44.881628 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 10:56:50] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -44.884024 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 10:56:55] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -44.888722 | E_var:     0.2522 | E_err:   0.007847
[2025-10-06 10:57:01] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -44.874048 | E_var:     0.2268 | E_err:   0.007440
[2025-10-06 10:57:06] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -44.892590 | E_var:     0.2117 | E_err:   0.007189
[2025-10-06 10:57:11] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -44.887023 | E_var:     0.1977 | E_err:   0.006948
[2025-10-06 10:57:16] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -44.887703 | E_var:     0.2234 | E_err:   0.007385
[2025-10-06 10:57:21] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -44.893148 | E_var:     0.1840 | E_err:   0.006702
[2025-10-06 10:57:26] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -44.893273 | E_var:     0.2589 | E_err:   0.007951
[2025-10-06 10:57:31] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -44.888989 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 10:57:37] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -44.882646 | E_var:     0.2509 | E_err:   0.007827
[2025-10-06 10:57:42] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -44.883143 | E_var:     0.2881 | E_err:   0.008387
[2025-10-06 10:57:47] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -44.894920 | E_var:     0.2025 | E_err:   0.007031
[2025-10-06 10:57:52] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -44.889510 | E_var:     0.1953 | E_err:   0.006905
[2025-10-06 10:57:57] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -44.874077 | E_var:     0.2450 | E_err:   0.007734
[2025-10-06 10:57:57] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 10:58:02] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -44.869420 | E_var:     0.2245 | E_err:   0.007403
[2025-10-06 10:58:07] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -44.877202 | E_var:     0.1975 | E_err:   0.006943
[2025-10-06 10:58:13] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -44.883398 | E_var:     0.2878 | E_err:   0.008382
[2025-10-06 10:58:18] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -44.881620 | E_var:     0.2258 | E_err:   0.007424
[2025-10-06 10:58:23] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -44.877304 | E_var:     0.2692 | E_err:   0.008107
[2025-10-06 10:58:28] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -44.879056 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 10:58:33] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -44.888811 | E_var:     0.2952 | E_err:   0.008489
[2025-10-06 10:58:38] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -44.887783 | E_var:     0.2738 | E_err:   0.008176
[2025-10-06 10:58:43] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -44.879522 | E_var:     0.2732 | E_err:   0.008167
[2025-10-06 10:58:49] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -44.879357 | E_var:     0.2410 | E_err:   0.007670
[2025-10-06 10:58:54] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -44.875945 | E_var:     0.3835 | E_err:   0.009676
[2025-10-06 10:58:59] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -44.874830 | E_var:     0.2194 | E_err:   0.007319
[2025-10-06 10:59:04] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -44.875653 | E_var:     0.2418 | E_err:   0.007684
[2025-10-06 10:59:09] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -44.884585 | E_var:     0.1982 | E_err:   0.006956
[2025-10-06 10:59:14] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -44.871171 | E_var:     0.2176 | E_err:   0.007288
[2025-10-06 10:59:19] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -44.877112 | E_var:     0.2361 | E_err:   0.007592
[2025-10-06 10:59:25] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -44.888699 | E_var:     0.2083 | E_err:   0.007132
[2025-10-06 10:59:30] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -44.871689 | E_var:     0.1721 | E_err:   0.006482
[2025-10-06 10:59:35] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -44.894453 | E_var:     0.4135 | E_err:   0.010048
[2025-10-06 10:59:40] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -44.858672 | E_var:     0.2618 | E_err:   0.007995
[2025-10-06 10:59:45] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -44.877552 | E_var:     0.1942 | E_err:   0.006885
[2025-10-06 10:59:50] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -44.893315 | E_var:     0.1977 | E_err:   0.006948
[2025-10-06 10:59:55] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -44.887840 | E_var:     0.2369 | E_err:   0.007604
[2025-10-06 11:00:01] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -44.876082 | E_var:     0.2900 | E_err:   0.008414
[2025-10-06 11:00:06] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -44.874752 | E_var:     0.2324 | E_err:   0.007532
[2025-10-06 11:00:11] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -44.883744 | E_var:     0.2450 | E_err:   0.007734
[2025-10-06 11:00:16] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -44.885342 | E_var:     0.1856 | E_err:   0.006731
[2025-10-06 11:00:21] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -44.891275 | E_var:     0.1976 | E_err:   0.006946
[2025-10-06 11:00:26] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -44.881047 | E_var:     0.2028 | E_err:   0.007036
[2025-10-06 11:00:31] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -44.883859 | E_var:     0.2627 | E_err:   0.008008
[2025-10-06 11:00:37] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -44.886250 | E_var:     0.1868 | E_err:   0.006753
[2025-10-06 11:00:42] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -44.875050 | E_var:     0.2745 | E_err:   0.008187
[2025-10-06 11:00:47] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -44.885098 | E_var:     0.2270 | E_err:   0.007444
[2025-10-06 11:00:52] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -44.877683 | E_var:     0.2448 | E_err:   0.007732
[2025-10-06 11:00:57] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -44.885506 | E_var:     0.2432 | E_err:   0.007706
[2025-10-06 11:01:02] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -44.896092 | E_var:     0.2386 | E_err:   0.007633
[2025-10-06 11:01:07] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -44.891762 | E_var:     0.2254 | E_err:   0.007418
[2025-10-06 11:01:13] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -44.874658 | E_var:     0.2426 | E_err:   0.007695
[2025-10-06 11:01:18] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -44.877039 | E_var:     0.1731 | E_err:   0.006501
[2025-10-06 11:01:23] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -44.863856 | E_var:     0.2810 | E_err:   0.008283
[2025-10-06 11:01:28] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -44.892768 | E_var:     0.3083 | E_err:   0.008675
[2025-10-06 11:01:33] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -44.890838 | E_var:     0.3234 | E_err:   0.008885
[2025-10-06 11:01:38] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -44.874110 | E_var:     0.2351 | E_err:   0.007577
[2025-10-06 11:01:43] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -44.863692 | E_var:     0.2606 | E_err:   0.007976
[2025-10-06 11:01:49] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -44.887313 | E_var:     0.2395 | E_err:   0.007646
[2025-10-06 11:01:54] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -44.892476 | E_var:     0.2258 | E_err:   0.007425
[2025-10-06 11:01:59] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -44.876984 | E_var:     0.2219 | E_err:   0.007360
[2025-10-06 11:02:04] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -44.886686 | E_var:     0.2080 | E_err:   0.007127
[2025-10-06 11:02:09] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -44.871094 | E_var:     0.2940 | E_err:   0.008472
[2025-10-06 11:02:14] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -44.875890 | E_var:     0.2382 | E_err:   0.007625
[2025-10-06 11:02:20] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -44.871408 | E_var:     0.2553 | E_err:   0.007894
[2025-10-06 11:02:25] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -44.885052 | E_var:     0.2380 | E_err:   0.007623
[2025-10-06 11:02:30] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -44.879986 | E_var:     0.2336 | E_err:   0.007552
[2025-10-06 11:02:35] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -44.874730 | E_var:     0.2354 | E_err:   0.007581
[2025-10-06 11:02:40] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -44.873363 | E_var:     0.1939 | E_err:   0.006880
[2025-10-06 11:02:45] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -44.881277 | E_var:     0.2256 | E_err:   0.007422
[2025-10-06 11:02:50] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -44.888067 | E_var:     0.2233 | E_err:   0.007383
[2025-10-06 11:02:56] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -44.885790 | E_var:     0.2302 | E_err:   0.007497
[2025-10-06 11:03:01] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -44.880835 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 11:03:06] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -44.894386 | E_var:     0.3054 | E_err:   0.008635
[2025-10-06 11:03:11] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -44.880935 | E_var:     0.2538 | E_err:   0.007871
[2025-10-06 11:03:16] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -44.885244 | E_var:     0.2140 | E_err:   0.007229
[2025-10-06 11:03:21] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -44.877044 | E_var:     0.1711 | E_err:   0.006462
[2025-10-06 11:03:26] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -44.869839 | E_var:     0.2375 | E_err:   0.007615
[2025-10-06 11:03:32] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -44.886397 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 11:03:37] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -44.888404 | E_var:     0.2144 | E_err:   0.007235
[2025-10-06 11:03:42] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -44.882014 | E_var:     0.2142 | E_err:   0.007231
[2025-10-06 11:03:47] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -44.882508 | E_var:     0.2257 | E_err:   0.007423
[2025-10-06 11:03:53] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -44.886472 | E_var:     0.2485 | E_err:   0.007789
[2025-10-06 11:03:58] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -44.888185 | E_var:     0.2608 | E_err:   0.007979
[2025-10-06 11:04:03] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -44.880851 | E_var:     0.3329 | E_err:   0.009016
[2025-10-06 11:04:08] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -44.893445 | E_var:     0.2200 | E_err:   0.007329
[2025-10-06 11:04:13] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -44.865378 | E_var:     0.2875 | E_err:   0.008379
[2025-10-06 11:04:18] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -44.874163 | E_var:     0.3410 | E_err:   0.009124
[2025-10-06 11:04:23] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -44.883066 | E_var:     0.2046 | E_err:   0.007067
[2025-10-06 11:04:29] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -44.887012 | E_var:     0.2361 | E_err:   0.007592
[2025-10-06 11:04:34] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -44.883035 | E_var:     0.2498 | E_err:   0.007809
[2025-10-06 11:04:39] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -44.874414 | E_var:     0.3274 | E_err:   0.008941
[2025-10-06 11:04:44] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -44.873620 | E_var:     0.2164 | E_err:   0.007269
[2025-10-06 11:04:49] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -44.885626 | E_var:     0.2219 | E_err:   0.007361
[2025-10-06 11:04:54] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -44.885529 | E_var:     0.2059 | E_err:   0.007090
[2025-10-06 11:04:59] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -44.880797 | E_var:     0.2442 | E_err:   0.007721
[2025-10-06 11:05:05] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -44.884000 | E_var:     0.3993 | E_err:   0.009873
[2025-10-06 11:05:10] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -44.880462 | E_var:     0.2413 | E_err:   0.007675
[2025-10-06 11:05:15] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -44.879606 | E_var:     0.2279 | E_err:   0.007460
[2025-10-06 11:05:20] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -44.871698 | E_var:     0.2514 | E_err:   0.007834
[2025-10-06 11:05:25] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -44.885089 | E_var:     0.2133 | E_err:   0.007215
[2025-10-06 11:05:30] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -44.869170 | E_var:     0.2437 | E_err:   0.007714
[2025-10-06 11:05:35] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -44.876481 | E_var:     0.1973 | E_err:   0.006940
[2025-10-06 11:05:41] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -44.879283 | E_var:     0.2303 | E_err:   0.007498
[2025-10-06 11:05:46] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -44.894245 | E_var:     0.2080 | E_err:   0.007126
[2025-10-06 11:05:51] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -44.876005 | E_var:     0.2522 | E_err:   0.007847
[2025-10-06 11:05:56] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -44.886012 | E_var:     0.2467 | E_err:   0.007760
[2025-10-06 11:06:01] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -44.868321 | E_var:     0.3108 | E_err:   0.008711
[2025-10-06 11:06:06] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -44.884865 | E_var:     0.2103 | E_err:   0.007165
[2025-10-06 11:06:12] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -44.899920 | E_var:     0.2285 | E_err:   0.007468
[2025-10-06 11:06:17] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -44.877371 | E_var:     0.2125 | E_err:   0.007204
[2025-10-06 11:06:22] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -44.872800 | E_var:     0.3830 | E_err:   0.009670
[2025-10-06 11:06:27] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -44.882446 | E_var:     0.2095 | E_err:   0.007152
[2025-10-06 11:06:32] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -44.879237 | E_var:     0.2252 | E_err:   0.007415
[2025-10-06 11:06:32] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 11:06:37] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -44.879915 | E_var:     0.3226 | E_err:   0.008875
[2025-10-06 11:06:42] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -44.877866 | E_var:     0.2009 | E_err:   0.007004
[2025-10-06 11:06:48] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -44.891601 | E_var:     0.2327 | E_err:   0.007537
[2025-10-06 11:06:53] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -44.882203 | E_var:     0.2351 | E_err:   0.007577
[2025-10-06 11:06:58] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -44.881390 | E_var:     0.2005 | E_err:   0.006996
[2025-10-06 11:07:03] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -44.894497 | E_var:     0.2029 | E_err:   0.007038
[2025-10-06 11:07:08] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -44.886257 | E_var:     0.2172 | E_err:   0.007283
[2025-10-06 11:07:13] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -44.890873 | E_var:     0.2387 | E_err:   0.007634
[2025-10-06 11:07:18] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -44.879519 | E_var:     0.2028 | E_err:   0.007036
[2025-10-06 11:07:24] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -44.889623 | E_var:     0.2371 | E_err:   0.007609
[2025-10-06 11:07:29] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -44.887916 | E_var:     0.2407 | E_err:   0.007666
[2025-10-06 11:07:34] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -44.885928 | E_var:     0.2082 | E_err:   0.007130
[2025-10-06 11:07:39] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -44.881013 | E_var:     0.3132 | E_err:   0.008744
[2025-10-06 11:07:44] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -44.875338 | E_var:     0.2645 | E_err:   0.008036
[2025-10-06 11:07:49] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -44.879349 | E_var:     0.2508 | E_err:   0.007824
[2025-10-06 11:07:54] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -44.881452 | E_var:     0.1882 | E_err:   0.006779
[2025-10-06 11:08:00] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -44.885512 | E_var:     0.2329 | E_err:   0.007541
[2025-10-06 11:08:05] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -44.887262 | E_var:     0.2795 | E_err:   0.008260
[2025-10-06 11:08:10] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -44.881512 | E_var:     0.4814 | E_err:   0.010841
[2025-10-06 11:08:15] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -44.883593 | E_var:     0.2366 | E_err:   0.007601
[2025-10-06 11:08:20] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -44.881257 | E_var:     0.2343 | E_err:   0.007563
[2025-10-06 11:08:25] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -44.884412 | E_var:     0.1726 | E_err:   0.006491
[2025-10-06 11:08:31] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -44.884992 | E_var:     0.3500 | E_err:   0.009244
[2025-10-06 11:08:36] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -44.883376 | E_var:     0.2563 | E_err:   0.007910
[2025-10-06 11:08:41] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -44.882813 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 11:08:46] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -44.886485 | E_var:     0.2453 | E_err:   0.007738
[2025-10-06 11:08:51] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -44.880494 | E_var:     0.2423 | E_err:   0.007692
[2025-10-06 11:08:56] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -44.887415 | E_var:     0.2561 | E_err:   0.007907
[2025-10-06 11:09:01] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -44.885902 | E_var:     0.2392 | E_err:   0.007642
[2025-10-06 11:09:07] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -44.896468 | E_var:     0.2982 | E_err:   0.008533
[2025-10-06 11:09:12] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -44.880414 | E_var:     0.2238 | E_err:   0.007392
[2025-10-06 11:09:17] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -44.867679 | E_var:     0.1951 | E_err:   0.006902
[2025-10-06 11:09:22] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -44.887233 | E_var:     0.2227 | E_err:   0.007374
[2025-10-06 11:09:27] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -44.895079 | E_var:     0.2030 | E_err:   0.007040
[2025-10-06 11:09:32] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -44.889961 | E_var:     0.2366 | E_err:   0.007600
[2025-10-06 11:09:37] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -44.878823 | E_var:     0.1837 | E_err:   0.006696
[2025-10-06 11:09:43] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -44.889055 | E_var:     0.2529 | E_err:   0.007858
[2025-10-06 11:09:48] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -44.885867 | E_var:     0.2155 | E_err:   0.007253
[2025-10-06 11:09:53] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -44.887889 | E_var:     0.2919 | E_err:   0.008441
[2025-10-06 11:09:58] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -44.880771 | E_var:     0.2901 | E_err:   0.008415
[2025-10-06 11:10:03] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -44.880741 | E_var:     0.2690 | E_err:   0.008103
[2025-10-06 11:10:08] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -44.894682 | E_var:     0.2435 | E_err:   0.007711
[2025-10-06 11:10:13] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -44.879214 | E_var:     0.2021 | E_err:   0.007024
[2025-10-06 11:10:19] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -44.895766 | E_var:     0.2132 | E_err:   0.007214
[2025-10-06 11:10:24] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -44.879558 | E_var:     0.2440 | E_err:   0.007717
[2025-10-06 11:10:29] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -44.879496 | E_var:     0.2550 | E_err:   0.007891
[2025-10-06 11:10:34] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -44.863141 | E_var:     0.2409 | E_err:   0.007668
[2025-10-06 11:10:39] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -44.874121 | E_var:     0.2253 | E_err:   0.007416
[2025-10-06 11:10:44] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -44.876920 | E_var:     0.2346 | E_err:   0.007568
[2025-10-06 11:10:49] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -44.890653 | E_var:     0.2580 | E_err:   0.007937
[2025-10-06 11:10:55] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -44.892845 | E_var:     0.1990 | E_err:   0.006970
[2025-10-06 11:11:00] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -44.867538 | E_var:     0.4003 | E_err:   0.009886
[2025-10-06 11:11:05] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -44.880835 | E_var:     0.2210 | E_err:   0.007346
[2025-10-06 11:11:10] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -44.886286 | E_var:     0.2133 | E_err:   0.007216
[2025-10-06 11:11:15] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -44.881998 | E_var:     0.1963 | E_err:   0.006924
[2025-10-06 11:11:20] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -44.872446 | E_var:     0.2642 | E_err:   0.008032
[2025-10-06 11:11:25] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -44.890249 | E_var:     0.2259 | E_err:   0.007426
[2025-10-06 11:11:31] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -44.878985 | E_var:     0.2000 | E_err:   0.006988
[2025-10-06 11:11:36] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -44.889505 | E_var:     0.2451 | E_err:   0.007736
[2025-10-06 11:11:41] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -44.882342 | E_var:     0.2757 | E_err:   0.008205
[2025-10-06 11:11:46] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -44.889304 | E_var:     0.3299 | E_err:   0.008975
[2025-10-06 11:11:51] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -44.890477 | E_var:     0.1852 | E_err:   0.006724
[2025-10-06 11:11:56] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -44.878522 | E_var:     0.2139 | E_err:   0.007226
[2025-10-06 11:12:01] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -44.870236 | E_var:     0.2315 | E_err:   0.007518
[2025-10-06 11:12:07] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -44.879349 | E_var:     0.2505 | E_err:   0.007820
[2025-10-06 11:12:12] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -44.888122 | E_var:     0.2557 | E_err:   0.007900
[2025-10-06 11:12:17] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -44.880313 | E_var:     0.2131 | E_err:   0.007213
[2025-10-06 11:12:22] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -44.891910 | E_var:     0.2490 | E_err:   0.007796
[2025-10-06 11:12:27] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -44.886933 | E_var:     0.2328 | E_err:   0.007539
[2025-10-06 11:12:32] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -44.894754 | E_var:     0.1894 | E_err:   0.006799
[2025-10-06 11:12:37] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -44.873619 | E_var:     0.2657 | E_err:   0.008055
[2025-10-06 11:12:43] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -44.872538 | E_var:     0.2766 | E_err:   0.008218
[2025-10-06 11:12:48] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -44.885646 | E_var:     0.2814 | E_err:   0.008288
[2025-10-06 11:12:53] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -44.876614 | E_var:     0.4200 | E_err:   0.010127
[2025-10-06 11:12:58] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -44.891218 | E_var:     0.2438 | E_err:   0.007715
[2025-10-06 11:13:03] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -44.883270 | E_var:     0.2207 | E_err:   0.007340
[2025-10-06 11:13:08] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -44.893066 | E_var:     0.7848 | E_err:   0.013842
[2025-10-06 11:13:13] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -44.882821 | E_var:     0.2003 | E_err:   0.006993
[2025-10-06 11:13:19] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -44.882341 | E_var:     0.3024 | E_err:   0.008593
[2025-10-06 11:13:24] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -44.887925 | E_var:     0.2227 | E_err:   0.007373
[2025-10-06 11:13:29] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -44.876230 | E_var:     0.2128 | E_err:   0.007209
[2025-10-06 11:13:34] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -44.888240 | E_var:     0.2934 | E_err:   0.008463
[2025-10-06 11:13:39] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -44.884615 | E_var:     0.2277 | E_err:   0.007456
[2025-10-06 11:13:44] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -44.890509 | E_var:     0.1889 | E_err:   0.006791
[2025-10-06 11:13:49] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -44.891388 | E_var:     0.2258 | E_err:   0.007424
[2025-10-06 11:13:55] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -44.881976 | E_var:     0.2769 | E_err:   0.008223
[2025-10-06 11:14:00] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -44.884504 | E_var:     0.2005 | E_err:   0.006997
[2025-10-06 11:14:05] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -44.870368 | E_var:     0.2704 | E_err:   0.008125
[2025-10-06 11:14:10] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -44.881052 | E_var:     0.2661 | E_err:   0.008060
[2025-10-06 11:14:15] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -44.897735 | E_var:     0.2592 | E_err:   0.007956
[2025-10-06 11:14:20] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -44.875892 | E_var:     0.2593 | E_err:   0.007956
[2025-10-06 11:14:25] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -44.876446 | E_var:     0.2783 | E_err:   0.008243
[2025-10-06 11:14:31] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -44.884148 | E_var:     0.2360 | E_err:   0.007591
[2025-10-06 11:14:36] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -44.882796 | E_var:     0.2075 | E_err:   0.007118
[2025-10-06 11:14:41] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -44.897085 | E_var:     0.3368 | E_err:   0.009068
[2025-10-06 11:14:46] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -44.886152 | E_var:     0.2304 | E_err:   0.007500
[2025-10-06 11:14:51] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -44.888003 | E_var:     0.2570 | E_err:   0.007921
[2025-10-06 11:14:56] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -44.872385 | E_var:     0.1997 | E_err:   0.006983
[2025-10-06 11:15:02] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -44.870572 | E_var:     0.2942 | E_err:   0.008476
[2025-10-06 11:15:07] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -44.881555 | E_var:     0.2517 | E_err:   0.007838
[2025-10-06 11:15:07] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 11:15:12] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -44.888045 | E_var:     0.2433 | E_err:   0.007707
[2025-10-06 11:15:17] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -44.892159 | E_var:     0.3585 | E_err:   0.009355
[2025-10-06 11:15:22] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -44.875366 | E_var:     0.2617 | E_err:   0.007992
[2025-10-06 11:15:27] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -44.872532 | E_var:     0.2354 | E_err:   0.007581
[2025-10-06 11:15:32] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -44.878475 | E_var:     0.2525 | E_err:   0.007852
[2025-10-06 11:15:38] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -44.889999 | E_var:     0.1918 | E_err:   0.006843
[2025-10-06 11:15:43] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -44.879297 | E_var:     0.2482 | E_err:   0.007784
[2025-10-06 11:15:48] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -44.872935 | E_var:     0.1982 | E_err:   0.006955
[2025-10-06 11:15:53] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -44.873640 | E_var:     0.2019 | E_err:   0.007020
[2025-10-06 11:15:58] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -44.870587 | E_var:     0.2216 | E_err:   0.007355
[2025-10-06 11:16:04] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -44.889030 | E_var:     0.3295 | E_err:   0.008969
[2025-10-06 11:16:09] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -44.887644 | E_var:     0.2000 | E_err:   0.006988
[2025-10-06 11:16:14] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -44.877144 | E_var:     0.2185 | E_err:   0.007303
[2025-10-06 11:16:19] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -44.891095 | E_var:     0.2058 | E_err:   0.007088
[2025-10-06 11:16:24] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -44.888909 | E_var:     0.1919 | E_err:   0.006845
[2025-10-06 11:16:29] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -44.875001 | E_var:     0.2307 | E_err:   0.007505
[2025-10-06 11:16:34] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -44.876381 | E_var:     0.3215 | E_err:   0.008860
[2025-10-06 11:16:40] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -44.886335 | E_var:     0.1956 | E_err:   0.006910
[2025-10-06 11:16:45] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -44.883740 | E_var:     0.2282 | E_err:   0.007465
[2025-10-06 11:16:50] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -44.870965 | E_var:     0.3089 | E_err:   0.008684
[2025-10-06 11:16:55] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -44.873046 | E_var:     0.2496 | E_err:   0.007805
[2025-10-06 11:17:00] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -44.882941 | E_var:     0.2740 | E_err:   0.008179
[2025-10-06 11:17:05] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -44.889399 | E_var:     0.2963 | E_err:   0.008505
[2025-10-06 11:17:11] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -44.883588 | E_var:     0.1935 | E_err:   0.006874
[2025-10-06 11:17:16] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -44.882522 | E_var:     0.2220 | E_err:   0.007362
[2025-10-06 11:17:21] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -44.890239 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 11:17:26] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -44.887763 | E_var:     0.1886 | E_err:   0.006785
[2025-10-06 11:17:31] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -44.878492 | E_var:     0.2519 | E_err:   0.007842
[2025-10-06 11:17:36] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -44.885974 | E_var:     0.2143 | E_err:   0.007233
[2025-10-06 11:17:42] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -44.873635 | E_var:     0.2778 | E_err:   0.008236
[2025-10-06 11:17:47] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -44.869570 | E_var:     0.2207 | E_err:   0.007341
[2025-10-06 11:17:52] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -44.889308 | E_var:     0.2241 | E_err:   0.007396
[2025-10-06 11:17:57] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -44.882841 | E_var:     0.2276 | E_err:   0.007454
[2025-10-06 11:18:02] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -44.876157 | E_var:     0.2031 | E_err:   0.007042
[2025-10-06 11:18:07] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -44.891315 | E_var:     0.3191 | E_err:   0.008826
[2025-10-06 11:18:12] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -44.882355 | E_var:     0.1903 | E_err:   0.006817
[2025-10-06 11:18:18] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -44.893225 | E_var:     0.2217 | E_err:   0.007357
[2025-10-06 11:18:23] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -44.867983 | E_var:     0.2757 | E_err:   0.008204
[2025-10-06 11:18:28] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -44.884160 | E_var:     0.2274 | E_err:   0.007451
[2025-10-06 11:18:33] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -44.881645 | E_var:     0.2030 | E_err:   0.007039
[2025-10-06 11:18:38] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -44.873809 | E_var:     0.2294 | E_err:   0.007484
[2025-10-06 11:18:43] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -44.883775 | E_var:     0.2065 | E_err:   0.007101
[2025-10-06 11:18:48] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -44.882735 | E_var:     0.1969 | E_err:   0.006934
[2025-10-06 11:18:54] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -44.875063 | E_var:     0.1838 | E_err:   0.006699
[2025-10-06 11:18:59] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -44.882786 | E_var:     0.2662 | E_err:   0.008062
[2025-10-06 11:19:04] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -44.892876 | E_var:     0.2279 | E_err:   0.007460
[2025-10-06 11:19:09] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -44.875057 | E_var:     0.2586 | E_err:   0.007945
[2025-10-06 11:19:14] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -44.889175 | E_var:     0.2176 | E_err:   0.007289
[2025-10-06 11:19:19] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -44.887240 | E_var:     0.3101 | E_err:   0.008701
[2025-10-06 11:19:25] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -44.893377 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 11:19:25] 🔄 RESTART #2 | Period: 600
[2025-10-06 11:19:30] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -44.880589 | E_var:     0.2265 | E_err:   0.007437
[2025-10-06 11:19:35] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -44.895041 | E_var:     0.2488 | E_err:   0.007794
[2025-10-06 11:19:40] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -44.893341 | E_var:     0.2887 | E_err:   0.008395
[2025-10-06 11:19:45] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -44.885892 | E_var:     1.7250 | E_err:   0.020522
[2025-10-06 11:19:50] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -44.890853 | E_var:     0.1693 | E_err:   0.006428
[2025-10-06 11:19:55] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -44.876826 | E_var:     0.1993 | E_err:   0.006976
[2025-10-06 11:20:01] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -44.889727 | E_var:     0.2005 | E_err:   0.006996
[2025-10-06 11:20:06] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -44.876764 | E_var:     0.2286 | E_err:   0.007471
[2025-10-06 11:20:11] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -44.889156 | E_var:     0.1613 | E_err:   0.006275
[2025-10-06 11:20:16] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -44.886089 | E_var:     0.3013 | E_err:   0.008577
[2025-10-06 11:20:21] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -44.878991 | E_var:     0.2148 | E_err:   0.007242
[2025-10-06 11:20:26] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -44.884970 | E_var:     0.2165 | E_err:   0.007271
[2025-10-06 11:20:32] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -44.884062 | E_var:     0.2267 | E_err:   0.007440
[2025-10-06 11:20:37] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -44.876142 | E_var:     0.1905 | E_err:   0.006819
[2025-10-06 11:20:42] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -44.889579 | E_var:     0.2468 | E_err:   0.007763
[2025-10-06 11:20:47] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -44.886805 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 11:20:52] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -44.892354 | E_var:     0.2062 | E_err:   0.007095
[2025-10-06 11:20:57] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -44.872905 | E_var:     0.7743 | E_err:   0.013750
[2025-10-06 11:21:02] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -44.869358 | E_var:     0.8533 | E_err:   0.014433
[2025-10-06 11:21:08] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -44.866654 | E_var:     0.8634 | E_err:   0.014519
[2025-10-06 11:21:13] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -44.883839 | E_var:     0.2226 | E_err:   0.007371
[2025-10-06 11:21:18] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -44.860479 | E_var:     0.2699 | E_err:   0.008118
[2025-10-06 11:21:23] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -44.895760 | E_var:     0.2136 | E_err:   0.007222
[2025-10-06 11:21:28] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -44.887695 | E_var:     0.2205 | E_err:   0.007338
[2025-10-06 11:21:33] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -44.886825 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 11:21:38] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -44.879151 | E_var:     0.2160 | E_err:   0.007263
[2025-10-06 11:21:44] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -44.896556 | E_var:     0.4172 | E_err:   0.010092
[2025-10-06 11:21:49] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -44.876624 | E_var:     0.2611 | E_err:   0.007985
[2025-10-06 11:21:54] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -44.882422 | E_var:     0.2464 | E_err:   0.007756
[2025-10-06 11:21:59] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -44.880407 | E_var:     0.3010 | E_err:   0.008572
[2025-10-06 11:22:04] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -44.892628 | E_var:     0.4298 | E_err:   0.010244
[2025-10-06 11:22:09] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -44.902433 | E_var:     0.2275 | E_err:   0.007453
[2025-10-06 11:22:14] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -44.874500 | E_var:     0.2371 | E_err:   0.007609
[2025-10-06 11:22:20] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -44.896212 | E_var:     0.1894 | E_err:   0.006799
[2025-10-06 11:22:25] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -44.873303 | E_var:     0.2567 | E_err:   0.007917
[2025-10-06 11:22:30] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -44.889440 | E_var:     0.2207 | E_err:   0.007341
[2025-10-06 11:22:35] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -44.891264 | E_var:     0.2371 | E_err:   0.007608
[2025-10-06 11:22:40] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -44.883644 | E_var:     0.2577 | E_err:   0.007932
[2025-10-06 11:22:45] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -44.892992 | E_var:     0.2334 | E_err:   0.007549
[2025-10-06 11:22:51] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -44.886841 | E_var:     0.2343 | E_err:   0.007564
[2025-10-06 11:22:56] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -44.876242 | E_var:     0.3562 | E_err:   0.009326
[2025-10-06 11:23:01] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -44.901507 | E_var:     0.2934 | E_err:   0.008463
[2025-10-06 11:23:06] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -44.881289 | E_var:     0.2231 | E_err:   0.007381
[2025-10-06 11:23:11] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -44.885510 | E_var:     0.2403 | E_err:   0.007660
[2025-10-06 11:23:16] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -44.884395 | E_var:     0.2022 | E_err:   0.007026
[2025-10-06 11:23:21] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -44.882822 | E_var:     0.1999 | E_err:   0.006986
[2025-10-06 11:23:27] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -44.883276 | E_var:     0.4994 | E_err:   0.011042
[2025-10-06 11:23:32] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -44.883332 | E_var:     0.3816 | E_err:   0.009653
[2025-10-06 11:23:37] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -44.871387 | E_var:     0.2256 | E_err:   0.007421
[2025-10-06 11:23:42] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -44.872715 | E_var:     0.2063 | E_err:   0.007096
[2025-10-06 11:23:42] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 11:23:47] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -44.880178 | E_var:     0.2285 | E_err:   0.007469
[2025-10-06 11:23:52] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -44.885791 | E_var:     0.1864 | E_err:   0.006747
[2025-10-06 11:23:58] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -44.888259 | E_var:     0.2530 | E_err:   0.007859
[2025-10-06 11:24:03] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -44.881346 | E_var:     0.2339 | E_err:   0.007557
[2025-10-06 11:24:08] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -44.881206 | E_var:     0.2038 | E_err:   0.007053
[2025-10-06 11:24:13] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -44.880302 | E_var:     0.2291 | E_err:   0.007479
[2025-10-06 11:24:18] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -44.890117 | E_var:     0.2047 | E_err:   0.007069
[2025-10-06 11:24:23] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -44.884440 | E_var:     0.2739 | E_err:   0.008178
[2025-10-06 11:24:28] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -44.883009 | E_var:     0.2627 | E_err:   0.008009
[2025-10-06 11:24:34] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -44.869810 | E_var:     0.2880 | E_err:   0.008386
[2025-10-06 11:24:39] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -44.884690 | E_var:     0.2925 | E_err:   0.008451
[2025-10-06 11:24:44] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -44.890111 | E_var:     0.3402 | E_err:   0.009114
[2025-10-06 11:24:49] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -44.892603 | E_var:     0.2652 | E_err:   0.008047
[2025-10-06 11:24:54] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -44.882868 | E_var:     0.2923 | E_err:   0.008448
[2025-10-06 11:24:59] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -44.886224 | E_var:     0.3751 | E_err:   0.009569
[2025-10-06 11:25:04] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -44.886058 | E_var:     0.2365 | E_err:   0.007599
[2025-10-06 11:25:10] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -44.880143 | E_var:     0.3050 | E_err:   0.008630
[2025-10-06 11:25:15] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -44.895508 | E_var:     0.2548 | E_err:   0.007888
[2025-10-06 11:25:20] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -44.880515 | E_var:     0.2060 | E_err:   0.007092
[2025-10-06 11:25:25] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -44.879845 | E_var:     0.2558 | E_err:   0.007902
[2025-10-06 11:25:30] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -44.863185 | E_var:     0.2625 | E_err:   0.008005
[2025-10-06 11:25:35] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -44.887816 | E_var:     0.2860 | E_err:   0.008356
[2025-10-06 11:25:40] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -44.876966 | E_var:     0.2263 | E_err:   0.007433
[2025-10-06 11:25:46] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -44.887987 | E_var:     0.2250 | E_err:   0.007411
[2025-10-06 11:25:51] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -44.886388 | E_var:     0.2112 | E_err:   0.007181
[2025-10-06 11:25:56] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -44.889054 | E_var:     0.2557 | E_err:   0.007901
[2025-10-06 11:26:01] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -44.896129 | E_var:     0.2199 | E_err:   0.007326
[2025-10-06 11:26:06] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -44.868153 | E_var:     0.2242 | E_err:   0.007398
[2025-10-06 11:26:11] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -44.883515 | E_var:     0.2389 | E_err:   0.007637
[2025-10-06 11:26:16] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -44.888429 | E_var:     0.2483 | E_err:   0.007786
[2025-10-06 11:26:22] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -44.896944 | E_var:     0.2489 | E_err:   0.007796
[2025-10-06 11:26:27] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -44.892920 | E_var:     0.2103 | E_err:   0.007165
[2025-10-06 11:26:32] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -44.879132 | E_var:     0.2344 | E_err:   0.007565
[2025-10-06 11:26:37] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -44.888693 | E_var:     0.2217 | E_err:   0.007357
[2025-10-06 11:26:42] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -44.883072 | E_var:     0.1859 | E_err:   0.006738
[2025-10-06 11:26:47] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -44.881086 | E_var:     0.1648 | E_err:   0.006343
[2025-10-06 11:26:52] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -44.896285 | E_var:     0.2995 | E_err:   0.008551
[2025-10-06 11:26:58] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -44.875898 | E_var:     0.2000 | E_err:   0.006988
[2025-10-06 11:27:03] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -44.883789 | E_var:     0.2079 | E_err:   0.007124
[2025-10-06 11:27:08] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -44.876696 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 11:27:13] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -44.881270 | E_var:     0.1867 | E_err:   0.006751
[2025-10-06 11:27:18] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -44.874651 | E_var:     0.2119 | E_err:   0.007193
[2025-10-06 11:27:23] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -44.904204 | E_var:     0.2423 | E_err:   0.007692
[2025-10-06 11:27:28] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -44.904720 | E_var:     0.2038 | E_err:   0.007054
[2025-10-06 11:27:34] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -44.869889 | E_var:     0.2230 | E_err:   0.007379
[2025-10-06 11:27:39] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -44.882541 | E_var:     0.2070 | E_err:   0.007108
[2025-10-06 11:27:44] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -44.883936 | E_var:     0.2105 | E_err:   0.007169
[2025-10-06 11:27:49] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -44.879561 | E_var:     0.2084 | E_err:   0.007133
[2025-10-06 11:27:54] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -44.878642 | E_var:     0.1958 | E_err:   0.006913
[2025-10-06 11:27:59] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -44.889028 | E_var:     0.2246 | E_err:   0.007405
[2025-10-06 11:28:05] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -44.885066 | E_var:     0.2341 | E_err:   0.007560
[2025-10-06 11:28:10] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -44.889895 | E_var:     0.2448 | E_err:   0.007730
[2025-10-06 11:28:15] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -44.891006 | E_var:     0.2446 | E_err:   0.007728
[2025-10-06 11:28:20] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -44.883749 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 11:28:25] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -44.896547 | E_var:     0.2874 | E_err:   0.008377
[2025-10-06 11:28:30] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -44.875721 | E_var:     0.2261 | E_err:   0.007430
[2025-10-06 11:28:35] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -44.885748 | E_var:     0.2066 | E_err:   0.007102
[2025-10-06 11:28:41] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -44.889191 | E_var:     0.2227 | E_err:   0.007374
[2025-10-06 11:28:46] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -44.868816 | E_var:     0.1857 | E_err:   0.006734
[2025-10-06 11:28:51] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -44.896051 | E_var:     0.2218 | E_err:   0.007358
[2025-10-06 11:28:56] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -44.876249 | E_var:     0.2578 | E_err:   0.007933
[2025-10-06 11:29:01] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -44.891930 | E_var:     0.2241 | E_err:   0.007398
[2025-10-06 11:29:06] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -44.873103 | E_var:     0.1951 | E_err:   0.006902
[2025-10-06 11:29:12] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -44.887540 | E_var:     0.4081 | E_err:   0.009982
[2025-10-06 11:29:17] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -44.872027 | E_var:     0.2208 | E_err:   0.007342
[2025-10-06 11:29:22] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -44.874621 | E_var:     0.4533 | E_err:   0.010519
[2025-10-06 11:29:27] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -44.880134 | E_var:     0.2880 | E_err:   0.008386
[2025-10-06 11:29:32] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -44.886959 | E_var:     0.2223 | E_err:   0.007367
[2025-10-06 11:29:37] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -44.879495 | E_var:     0.2806 | E_err:   0.008277
[2025-10-06 11:29:42] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -44.880195 | E_var:     0.2181 | E_err:   0.007298
[2025-10-06 11:29:48] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -44.879659 | E_var:     0.2188 | E_err:   0.007309
[2025-10-06 11:29:53] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -44.884964 | E_var:     0.2864 | E_err:   0.008362
[2025-10-06 11:29:58] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -44.882465 | E_var:     0.2069 | E_err:   0.007107
[2025-10-06 11:30:03] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -44.877903 | E_var:     0.2323 | E_err:   0.007531
[2025-10-06 11:30:08] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -44.871632 | E_var:     0.2465 | E_err:   0.007758
[2025-10-06 11:30:13] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -44.875378 | E_var:     0.2217 | E_err:   0.007357
[2025-10-06 11:30:18] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -44.875056 | E_var:     0.2223 | E_err:   0.007368
[2025-10-06 11:30:24] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -44.881569 | E_var:     0.2274 | E_err:   0.007452
[2025-10-06 11:30:29] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -44.888989 | E_var:     0.2335 | E_err:   0.007550
[2025-10-06 11:30:34] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -44.883227 | E_var:     0.3245 | E_err:   0.008901
[2025-10-06 11:30:39] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -44.891443 | E_var:     0.2354 | E_err:   0.007581
[2025-10-06 11:30:44] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -44.883879 | E_var:     0.2304 | E_err:   0.007500
[2025-10-06 11:30:49] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -44.891989 | E_var:     0.2128 | E_err:   0.007209
[2025-10-06 11:30:54] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -44.860917 | E_var:     0.5783 | E_err:   0.011882
[2025-10-06 11:31:00] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -44.880528 | E_var:     0.2137 | E_err:   0.007223
[2025-10-06 11:31:05] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -44.873579 | E_var:     0.2232 | E_err:   0.007382
[2025-10-06 11:31:10] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -44.882299 | E_var:     0.3262 | E_err:   0.008924
[2025-10-06 11:31:15] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -44.886147 | E_var:     0.2024 | E_err:   0.007029
[2025-10-06 11:31:20] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -44.876953 | E_var:     0.2397 | E_err:   0.007650
[2025-10-06 11:31:25] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -44.877525 | E_var:     0.2249 | E_err:   0.007410
[2025-10-06 11:31:30] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -44.899173 | E_var:     0.2198 | E_err:   0.007325
[2025-10-06 11:31:36] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -44.895365 | E_var:     0.2648 | E_err:   0.008040
[2025-10-06 11:31:41] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -44.886649 | E_var:     0.2140 | E_err:   0.007228
[2025-10-06 11:31:46] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -44.882395 | E_var:     0.1911 | E_err:   0.006831
[2025-10-06 11:31:51] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -44.877256 | E_var:     0.1972 | E_err:   0.006939
[2025-10-06 11:31:56] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -44.879898 | E_var:     0.2096 | E_err:   0.007154
[2025-10-06 11:32:01] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -44.882709 | E_var:     0.1810 | E_err:   0.006647
[2025-10-06 11:32:07] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -44.878092 | E_var:     0.2252 | E_err:   0.007415
[2025-10-06 11:32:12] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -44.874029 | E_var:     0.2226 | E_err:   0.007372
[2025-10-06 11:32:17] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -44.887013 | E_var:     0.2236 | E_err:   0.007388
[2025-10-06 11:32:17] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 11:32:22] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -44.898955 | E_var:     0.2059 | E_err:   0.007090
[2025-10-06 11:32:27] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -44.884908 | E_var:     0.2302 | E_err:   0.007497
[2025-10-06 11:32:32] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -44.889754 | E_var:     0.2932 | E_err:   0.008461
[2025-10-06 11:32:37] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -44.899863 | E_var:     0.2260 | E_err:   0.007429
[2025-10-06 11:32:43] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -44.888019 | E_var:     0.2285 | E_err:   0.007468
[2025-10-06 11:32:48] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -44.893950 | E_var:     0.2234 | E_err:   0.007385
[2025-10-06 11:32:53] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -44.885440 | E_var:     0.2359 | E_err:   0.007589
[2025-10-06 11:32:58] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -44.877990 | E_var:     0.2554 | E_err:   0.007896
[2025-10-06 11:33:03] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -44.894133 | E_var:     0.2356 | E_err:   0.007584
[2025-10-06 11:33:08] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -44.891836 | E_var:     0.1868 | E_err:   0.006754
[2025-10-06 11:33:13] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -44.874505 | E_var:     0.2134 | E_err:   0.007218
[2025-10-06 11:33:19] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -44.895492 | E_var:     0.2543 | E_err:   0.007880
[2025-10-06 11:33:24] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -44.888234 | E_var:     0.2219 | E_err:   0.007361
[2025-10-06 11:33:29] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -44.888466 | E_var:     0.2122 | E_err:   0.007198
[2025-10-06 11:33:34] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -44.898076 | E_var:     0.1982 | E_err:   0.006956
[2025-10-06 11:33:39] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -44.900028 | E_var:     0.2173 | E_err:   0.007283
[2025-10-06 11:33:44] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -44.878407 | E_var:     0.3113 | E_err:   0.008718
[2025-10-06 11:33:49] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -44.879054 | E_var:     0.2305 | E_err:   0.007502
[2025-10-06 11:33:55] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -44.882089 | E_var:     0.1814 | E_err:   0.006655
[2025-10-06 11:34:00] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -44.896236 | E_var:     0.2028 | E_err:   0.007036
[2025-10-06 11:34:05] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -44.892740 | E_var:     0.2154 | E_err:   0.007252
[2025-10-06 11:34:10] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -44.873998 | E_var:     0.3017 | E_err:   0.008582
[2025-10-06 11:34:15] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -44.871170 | E_var:     0.2103 | E_err:   0.007166
[2025-10-06 11:34:20] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -44.877159 | E_var:     0.2790 | E_err:   0.008253
[2025-10-06 11:34:25] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -44.875075 | E_var:     0.2133 | E_err:   0.007216
[2025-10-06 11:34:31] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -44.883847 | E_var:     0.2082 | E_err:   0.007129
[2025-10-06 11:34:36] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -44.874513 | E_var:     0.2755 | E_err:   0.008201
[2025-10-06 11:34:41] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -44.889175 | E_var:     0.2519 | E_err:   0.007842
[2025-10-06 11:34:46] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -44.880909 | E_var:     0.2409 | E_err:   0.007669
[2025-10-06 11:34:51] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -44.879157 | E_var:     0.2216 | E_err:   0.007355
[2025-10-06 11:34:56] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -44.872658 | E_var:     0.3177 | E_err:   0.008806
[2025-10-06 11:35:02] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -44.883145 | E_var:     0.2342 | E_err:   0.007562
[2025-10-06 11:35:07] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -44.891494 | E_var:     0.2671 | E_err:   0.008075
[2025-10-06 11:35:12] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -44.877519 | E_var:     1.0476 | E_err:   0.015993
[2025-10-06 11:35:17] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -44.886376 | E_var:     0.1975 | E_err:   0.006944
[2025-10-06 11:35:22] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -44.871556 | E_var:     0.2246 | E_err:   0.007405
[2025-10-06 11:35:27] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -44.878902 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 11:35:32] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -44.890576 | E_var:     0.2351 | E_err:   0.007576
[2025-10-06 11:35:38] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -44.870488 | E_var:     0.2172 | E_err:   0.007281
[2025-10-06 11:35:43] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -44.883548 | E_var:     0.2214 | E_err:   0.007353
[2025-10-06 11:35:48] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -44.896351 | E_var:     0.2209 | E_err:   0.007344
[2025-10-06 11:35:53] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -44.895618 | E_var:     0.1854 | E_err:   0.006728
[2025-10-06 11:35:58] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -44.868726 | E_var:     0.2803 | E_err:   0.008272
[2025-10-06 11:36:03] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -44.878181 | E_var:     0.1838 | E_err:   0.006700
[2025-10-06 11:36:08] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -44.881459 | E_var:     0.3426 | E_err:   0.009145
[2025-10-06 11:36:14] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -44.875409 | E_var:     0.1952 | E_err:   0.006903
[2025-10-06 11:36:19] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -44.891882 | E_var:     0.1902 | E_err:   0.006814
[2025-10-06 11:36:24] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -44.878024 | E_var:     0.2053 | E_err:   0.007079
[2025-10-06 11:36:29] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -44.891816 | E_var:     0.2317 | E_err:   0.007522
[2025-10-06 11:36:34] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -44.874155 | E_var:     0.2453 | E_err:   0.007738
[2025-10-06 11:36:39] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -44.888801 | E_var:     0.2289 | E_err:   0.007476
[2025-10-06 11:36:44] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -44.887506 | E_var:     0.2020 | E_err:   0.007023
[2025-10-06 11:36:50] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -44.885221 | E_var:     0.1846 | E_err:   0.006713
[2025-10-06 11:36:55] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -44.875967 | E_var:     0.1770 | E_err:   0.006573
[2025-10-06 11:37:00] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -44.877968 | E_var:     0.2309 | E_err:   0.007508
[2025-10-06 11:37:05] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -44.872986 | E_var:     0.2043 | E_err:   0.007062
[2025-10-06 11:37:10] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -44.889249 | E_var:     0.1925 | E_err:   0.006855
[2025-10-06 11:37:15] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -44.897620 | E_var:     0.2568 | E_err:   0.007918
[2025-10-06 11:37:20] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -44.885044 | E_var:     0.5538 | E_err:   0.011628
[2025-10-06 11:37:26] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -44.894155 | E_var:     0.2723 | E_err:   0.008154
[2025-10-06 11:37:31] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -44.878293 | E_var:     0.1950 | E_err:   0.006901
[2025-10-06 11:37:36] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -44.888695 | E_var:     0.1994 | E_err:   0.006978
[2025-10-06 11:37:41] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -44.887038 | E_var:     0.2286 | E_err:   0.007470
[2025-10-06 11:37:46] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -44.877434 | E_var:     0.2244 | E_err:   0.007401
[2025-10-06 11:37:51] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -44.881861 | E_var:     0.2344 | E_err:   0.007565
[2025-10-06 11:37:56] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -44.891170 | E_var:     0.2287 | E_err:   0.007472
[2025-10-06 11:38:02] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -44.866463 | E_var:     0.2330 | E_err:   0.007542
[2025-10-06 11:38:07] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -44.876487 | E_var:     0.2535 | E_err:   0.007866
[2025-10-06 11:38:12] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -44.884655 | E_var:     0.1848 | E_err:   0.006718
[2025-10-06 11:38:17] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -44.889570 | E_var:     0.2692 | E_err:   0.008107
[2025-10-06 11:38:22] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -44.890159 | E_var:     0.2321 | E_err:   0.007528
[2025-10-06 11:38:27] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -44.886551 | E_var:     0.3099 | E_err:   0.008698
[2025-10-06 11:38:33] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -44.873995 | E_var:     0.2243 | E_err:   0.007400
[2025-10-06 11:38:38] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -44.887201 | E_var:     0.2040 | E_err:   0.007058
[2025-10-06 11:38:43] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -44.888670 | E_var:     0.2504 | E_err:   0.007819
[2025-10-06 11:38:48] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -44.886101 | E_var:     0.2136 | E_err:   0.007221
[2025-10-06 11:38:53] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -44.889672 | E_var:     0.2873 | E_err:   0.008374
[2025-10-06 11:38:58] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -44.875788 | E_var:     0.2341 | E_err:   0.007561
[2025-10-06 11:39:03] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -44.876408 | E_var:     0.2138 | E_err:   0.007225
[2025-10-06 11:39:09] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -44.886990 | E_var:     0.1987 | E_err:   0.006965
[2025-10-06 11:39:14] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -44.885499 | E_var:     0.1880 | E_err:   0.006774
[2025-10-06 11:39:19] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -44.883311 | E_var:     0.2198 | E_err:   0.007326
[2025-10-06 11:39:24] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -44.875020 | E_var:     0.2182 | E_err:   0.007299
[2025-10-06 11:39:29] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -44.887666 | E_var:     0.2603 | E_err:   0.007972
[2025-10-06 11:39:34] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -44.889268 | E_var:     0.2668 | E_err:   0.008071
[2025-10-06 11:39:39] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -44.884460 | E_var:     0.2677 | E_err:   0.008084
[2025-10-06 11:39:45] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -44.876283 | E_var:     0.2332 | E_err:   0.007545
[2025-10-06 11:39:50] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -44.894055 | E_var:     0.1924 | E_err:   0.006854
[2025-10-06 11:39:55] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -44.885215 | E_var:     0.2299 | E_err:   0.007491
[2025-10-06 11:40:00] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -44.873741 | E_var:     0.2376 | E_err:   0.007617
[2025-10-06 11:40:05] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -44.877434 | E_var:     0.1767 | E_err:   0.006568
[2025-10-06 11:40:10] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -44.880810 | E_var:     0.2113 | E_err:   0.007182
[2025-10-06 11:40:15] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -44.877873 | E_var:     0.1909 | E_err:   0.006827
[2025-10-06 11:40:21] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -44.877538 | E_var:     0.2422 | E_err:   0.007690
[2025-10-06 11:40:26] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -44.882030 | E_var:     0.2825 | E_err:   0.008305
[2025-10-06 11:40:31] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -44.874807 | E_var:     0.2443 | E_err:   0.007724
[2025-10-06 11:40:36] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -44.889749 | E_var:     0.2317 | E_err:   0.007521
[2025-10-06 11:40:41] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -44.883365 | E_var:     0.2095 | E_err:   0.007152
[2025-10-06 11:40:46] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -44.894007 | E_var:     0.2937 | E_err:   0.008468
[2025-10-06 11:40:52] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -44.883961 | E_var:     0.2256 | E_err:   0.007422
[2025-10-06 11:40:52] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 11:40:57] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -44.876463 | E_var:     0.2686 | E_err:   0.008098
[2025-10-06 11:41:02] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -44.898155 | E_var:     0.2299 | E_err:   0.007492
[2025-10-06 11:41:07] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -44.891907 | E_var:     0.2334 | E_err:   0.007549
[2025-10-06 11:41:12] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -44.874071 | E_var:     0.2822 | E_err:   0.008301
[2025-10-06 11:41:17] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -44.892761 | E_var:     0.2433 | E_err:   0.007708
[2025-10-06 11:41:22] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -44.887105 | E_var:     0.1790 | E_err:   0.006611
[2025-10-06 11:41:28] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -44.874755 | E_var:     0.2258 | E_err:   0.007424
[2025-10-06 11:41:33] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -44.880164 | E_var:     0.1946 | E_err:   0.006893
[2025-10-06 11:41:38] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -44.875389 | E_var:     0.2014 | E_err:   0.007012
[2025-10-06 11:41:43] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -44.874524 | E_var:     0.2427 | E_err:   0.007697
[2025-10-06 11:41:48] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -44.896098 | E_var:     0.2279 | E_err:   0.007459
[2025-10-06 11:41:53] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -44.877811 | E_var:     0.2026 | E_err:   0.007034
[2025-10-06 11:41:59] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -44.881632 | E_var:     0.2801 | E_err:   0.008270
[2025-10-06 11:42:04] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -44.884245 | E_var:     0.3316 | E_err:   0.008998
[2025-10-06 11:42:09] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -44.885674 | E_var:     0.2578 | E_err:   0.007933
[2025-10-06 11:42:14] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -44.888510 | E_var:     0.2214 | E_err:   0.007352
[2025-10-06 11:42:19] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -44.874993 | E_var:     0.2565 | E_err:   0.007913
[2025-10-06 11:42:24] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -44.890288 | E_var:     0.4505 | E_err:   0.010488
[2025-10-06 11:42:29] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -44.869560 | E_var:     0.7173 | E_err:   0.013234
[2025-10-06 11:42:35] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -44.882985 | E_var:     0.2782 | E_err:   0.008242
[2025-10-06 11:42:40] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -44.882935 | E_var:     0.2117 | E_err:   0.007190
[2025-10-06 11:42:45] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -44.891933 | E_var:     0.2124 | E_err:   0.007201
[2025-10-06 11:42:50] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -44.888965 | E_var:     0.2423 | E_err:   0.007691
[2025-10-06 11:42:55] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -44.879533 | E_var:     0.2162 | E_err:   0.007265
[2025-10-06 11:43:00] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -44.890945 | E_var:     0.2116 | E_err:   0.007188
[2025-10-06 11:43:06] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -44.884467 | E_var:     0.2806 | E_err:   0.008277
[2025-10-06 11:43:11] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -44.880334 | E_var:     0.2088 | E_err:   0.007140
[2025-10-06 11:43:16] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -44.888976 | E_var:     0.2748 | E_err:   0.008191
[2025-10-06 11:43:21] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -44.877105 | E_var:     0.2040 | E_err:   0.007057
[2025-10-06 11:43:26] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -44.885629 | E_var:     0.3225 | E_err:   0.008873
[2025-10-06 11:43:31] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -44.885651 | E_var:     0.2577 | E_err:   0.007931
[2025-10-06 11:43:36] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -44.877666 | E_var:     0.2220 | E_err:   0.007363
[2025-10-06 11:43:42] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -44.896748 | E_var:     0.2484 | E_err:   0.007788
[2025-10-06 11:43:47] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -44.883137 | E_var:     0.1912 | E_err:   0.006832
[2025-10-06 11:43:52] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -44.885781 | E_var:     0.2318 | E_err:   0.007522
[2025-10-06 11:43:57] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -44.896060 | E_var:     0.2201 | E_err:   0.007330
[2025-10-06 11:44:02] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -44.894783 | E_var:     0.2256 | E_err:   0.007421
[2025-10-06 11:44:07] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -44.887245 | E_var:     0.1787 | E_err:   0.006604
[2025-10-06 11:44:13] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -44.884464 | E_var:     0.2386 | E_err:   0.007632
[2025-10-06 11:44:18] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -44.879129 | E_var:     0.1827 | E_err:   0.006679
[2025-10-06 11:44:23] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -44.871433 | E_var:     0.2482 | E_err:   0.007784
[2025-10-06 11:44:28] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -44.873812 | E_var:     0.3232 | E_err:   0.008883
[2025-10-06 11:44:33] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -44.881945 | E_var:     0.3007 | E_err:   0.008568
[2025-10-06 11:44:38] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -44.883681 | E_var:     0.2199 | E_err:   0.007327
[2025-10-06 11:44:44] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -44.878428 | E_var:     0.1562 | E_err:   0.006175
[2025-10-06 11:44:49] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -44.878753 | E_var:     0.2676 | E_err:   0.008083
[2025-10-06 11:44:54] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -44.884311 | E_var:     0.3460 | E_err:   0.009190
[2025-10-06 11:44:59] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -44.888428 | E_var:     0.1910 | E_err:   0.006828
[2025-10-06 11:45:04] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -44.886119 | E_var:     0.2171 | E_err:   0.007280
[2025-10-06 11:45:09] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -44.888770 | E_var:     0.2016 | E_err:   0.007015
[2025-10-06 11:45:14] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -44.873574 | E_var:     0.2677 | E_err:   0.008084
[2025-10-06 11:45:20] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -44.884164 | E_var:     0.2854 | E_err:   0.008348
[2025-10-06 11:45:25] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -44.872443 | E_var:     0.1995 | E_err:   0.006978
[2025-10-06 11:45:30] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -44.872956 | E_var:     0.2362 | E_err:   0.007594
[2025-10-06 11:45:35] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -44.879829 | E_var:     0.2123 | E_err:   0.007200
[2025-10-06 11:45:40] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -44.873455 | E_var:     0.2290 | E_err:   0.007477
[2025-10-06 11:45:45] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -44.889124 | E_var:     0.1954 | E_err:   0.006906
[2025-10-06 11:45:51] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -44.881195 | E_var:     0.1964 | E_err:   0.006925
[2025-10-06 11:45:56] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -44.885601 | E_var:     0.2139 | E_err:   0.007227
[2025-10-06 11:46:01] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -44.883729 | E_var:     0.2119 | E_err:   0.007192
[2025-10-06 11:46:06] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -44.894390 | E_var:     0.1934 | E_err:   0.006871
[2025-10-06 11:46:11] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -44.885587 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 11:46:16] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -44.873979 | E_var:     0.2223 | E_err:   0.007368
[2025-10-06 11:46:21] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -44.868920 | E_var:     0.2996 | E_err:   0.008553
[2025-10-06 11:46:27] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -44.871469 | E_var:     0.2592 | E_err:   0.007955
[2025-10-06 11:46:32] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -44.865139 | E_var:     1.4812 | E_err:   0.019017
[2025-10-06 11:46:37] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -44.887171 | E_var:     0.2207 | E_err:   0.007340
[2025-10-06 11:46:42] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -44.888640 | E_var:     0.2858 | E_err:   0.008353
[2025-10-06 11:46:47] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -44.882255 | E_var:     0.2460 | E_err:   0.007750
[2025-10-06 11:46:52] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -44.891618 | E_var:     0.2270 | E_err:   0.007445
[2025-10-06 11:46:58] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -44.884234 | E_var:     0.2116 | E_err:   0.007187
[2025-10-06 11:47:03] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -44.891777 | E_var:     0.3160 | E_err:   0.008784
[2025-10-06 11:47:08] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -44.894814 | E_var:     0.2161 | E_err:   0.007264
[2025-10-06 11:47:13] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -44.880906 | E_var:     0.2115 | E_err:   0.007186
[2025-10-06 11:47:18] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -44.879426 | E_var:     0.2349 | E_err:   0.007573
[2025-10-06 11:47:23] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -44.857591 | E_var:     0.2160 | E_err:   0.007262
[2025-10-06 11:47:29] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -44.884753 | E_var:     0.2439 | E_err:   0.007716
[2025-10-06 11:47:34] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -44.864835 | E_var:     0.2403 | E_err:   0.007659
[2025-10-06 11:47:39] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -44.888986 | E_var:     0.2393 | E_err:   0.007643
[2025-10-06 11:47:44] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -44.888851 | E_var:     0.3086 | E_err:   0.008680
[2025-10-06 11:47:49] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -44.879274 | E_var:     0.2030 | E_err:   0.007040
[2025-10-06 11:47:54] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -44.866844 | E_var:     0.2381 | E_err:   0.007624
[2025-10-06 11:47:59] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -44.880454 | E_var:     0.2516 | E_err:   0.007838
[2025-10-06 11:48:05] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -44.873142 | E_var:     0.2198 | E_err:   0.007325
[2025-10-06 11:48:10] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -44.885589 | E_var:     0.2317 | E_err:   0.007521
[2025-10-06 11:48:15] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -44.893843 | E_var:     0.2517 | E_err:   0.007840
[2025-10-06 11:48:20] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -44.878795 | E_var:     0.1920 | E_err:   0.006847
[2025-10-06 11:48:25] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -44.884744 | E_var:     0.1938 | E_err:   0.006878
[2025-10-06 11:48:30] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -44.889327 | E_var:     0.2081 | E_err:   0.007127
[2025-10-06 11:48:36] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -44.884868 | E_var:     0.2672 | E_err:   0.008076
[2025-10-06 11:48:41] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -44.876893 | E_var:     0.1979 | E_err:   0.006951
[2025-10-06 11:48:46] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -44.890982 | E_var:     0.2086 | E_err:   0.007136
[2025-10-06 11:48:51] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -44.875073 | E_var:     0.2390 | E_err:   0.007638
[2025-10-06 11:48:56] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -44.892450 | E_var:     0.1625 | E_err:   0.006298
[2025-10-06 11:49:01] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -44.876090 | E_var:     0.2285 | E_err:   0.007470
[2025-10-06 11:49:06] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -44.894563 | E_var:     0.1946 | E_err:   0.006892
[2025-10-06 11:49:12] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -44.889184 | E_var:     0.3175 | E_err:   0.008804
[2025-10-06 11:49:17] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -44.883243 | E_var:     0.2173 | E_err:   0.007284
[2025-10-06 11:49:22] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -44.883654 | E_var:     0.2012 | E_err:   0.007008
[2025-10-06 11:49:27] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -44.882358 | E_var:     0.2287 | E_err:   0.007473
[2025-10-06 11:49:27] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 11:49:32] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -44.884912 | E_var:     0.2384 | E_err:   0.007629
[2025-10-06 11:49:38] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -44.879746 | E_var:     0.2429 | E_err:   0.007700
[2025-10-06 11:49:43] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -44.873208 | E_var:     0.1888 | E_err:   0.006789
[2025-10-06 11:49:48] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -44.885396 | E_var:     0.2193 | E_err:   0.007317
[2025-10-06 11:49:53] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -44.878989 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 11:49:58] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -44.879848 | E_var:     0.1947 | E_err:   0.006895
[2025-10-06 11:50:03] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -44.891804 | E_var:     0.2440 | E_err:   0.007718
[2025-10-06 11:50:08] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -44.898425 | E_var:     0.5183 | E_err:   0.011249
[2025-10-06 11:50:14] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -44.890404 | E_var:     0.2009 | E_err:   0.007004
[2025-10-06 11:50:19] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -44.877249 | E_var:     0.2605 | E_err:   0.007976
[2025-10-06 11:50:24] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -44.875442 | E_var:     0.2151 | E_err:   0.007246
[2025-10-06 11:50:29] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -44.878896 | E_var:     0.2739 | E_err:   0.008178
[2025-10-06 11:50:34] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -44.883786 | E_var:     0.2096 | E_err:   0.007153
[2025-10-06 11:50:39] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -44.874106 | E_var:     0.2802 | E_err:   0.008272
[2025-10-06 11:50:45] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -44.895297 | E_var:     0.2541 | E_err:   0.007876
[2025-10-06 11:50:50] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -44.877200 | E_var:     0.3650 | E_err:   0.009440
[2025-10-06 11:50:55] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -44.899118 | E_var:     0.4330 | E_err:   0.010281
[2025-10-06 11:51:00] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -44.887118 | E_var:     0.1993 | E_err:   0.006976
[2025-10-06 11:51:05] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -44.891059 | E_var:     0.2081 | E_err:   0.007128
[2025-10-06 11:51:10] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -44.883035 | E_var:     0.2948 | E_err:   0.008484
[2025-10-06 11:51:16] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -44.882932 | E_var:     0.2217 | E_err:   0.007358
[2025-10-06 11:51:21] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -44.900092 | E_var:     0.2057 | E_err:   0.007087
[2025-10-06 11:51:26] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -44.888431 | E_var:     0.2576 | E_err:   0.007930
[2025-10-06 11:51:31] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -44.885921 | E_var:     0.2264 | E_err:   0.007434
[2025-10-06 11:51:36] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -44.889427 | E_var:     0.1778 | E_err:   0.006588
[2025-10-06 11:51:41] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -44.898594 | E_var:     0.3063 | E_err:   0.008648
[2025-10-06 11:51:46] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -44.886302 | E_var:     0.2681 | E_err:   0.008091
[2025-10-06 11:51:52] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -44.878667 | E_var:     0.2358 | E_err:   0.007587
[2025-10-06 11:51:57] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -44.892832 | E_var:     0.2349 | E_err:   0.007574
[2025-10-06 11:52:02] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -44.893504 | E_var:     0.2387 | E_err:   0.007633
[2025-10-06 11:52:07] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -44.882811 | E_var:     0.2214 | E_err:   0.007351
[2025-10-06 11:52:12] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -44.873337 | E_var:     0.2002 | E_err:   0.006992
[2025-10-06 11:52:17] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -44.893984 | E_var:     0.2658 | E_err:   0.008055
[2025-10-06 11:52:23] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -44.872140 | E_var:     0.2700 | E_err:   0.008119
[2025-10-06 11:52:28] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -44.895170 | E_var:     0.2241 | E_err:   0.007396
[2025-10-06 11:52:33] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -44.886505 | E_var:     0.2130 | E_err:   0.007211
[2025-10-06 11:52:38] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -44.876031 | E_var:     0.2901 | E_err:   0.008416
[2025-10-06 11:52:43] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -44.880717 | E_var:     0.1689 | E_err:   0.006421
[2025-10-06 11:52:48] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -44.868413 | E_var:     0.2372 | E_err:   0.007609
[2025-10-06 11:52:53] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -44.892050 | E_var:     0.2314 | E_err:   0.007516
[2025-10-06 11:52:59] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -44.885662 | E_var:     0.5475 | E_err:   0.011562
[2025-10-06 11:53:04] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -44.885227 | E_var:     0.3760 | E_err:   0.009581
[2025-10-06 11:53:09] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -44.875700 | E_var:     0.2418 | E_err:   0.007683
[2025-10-06 11:53:14] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -44.883346 | E_var:     0.2121 | E_err:   0.007196
[2025-10-06 11:53:19] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -44.885062 | E_var:     0.1871 | E_err:   0.006758
[2025-10-06 11:53:24] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -44.871611 | E_var:     0.2630 | E_err:   0.008013
[2025-10-06 11:53:30] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -44.877876 | E_var:     0.1952 | E_err:   0.006903
[2025-10-06 11:53:35] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -44.896408 | E_var:     0.2327 | E_err:   0.007537
[2025-10-06 11:53:40] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -44.881282 | E_var:     0.2095 | E_err:   0.007151
[2025-10-06 11:53:45] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -44.886605 | E_var:     0.1872 | E_err:   0.006761
[2025-10-06 11:53:50] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -44.890356 | E_var:     0.2302 | E_err:   0.007496
[2025-10-06 11:53:55] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -44.881942 | E_var:     0.2081 | E_err:   0.007128
[2025-10-06 11:54:00] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -44.890572 | E_var:     0.2292 | E_err:   0.007480
[2025-10-06 11:54:06] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -44.896434 | E_var:     0.2194 | E_err:   0.007319
[2025-10-06 11:54:11] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -44.878082 | E_var:     0.2138 | E_err:   0.007225
[2025-10-06 11:54:16] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -44.890854 | E_var:     0.1912 | E_err:   0.006831
[2025-10-06 11:54:21] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -44.901963 | E_var:     0.6223 | E_err:   0.012326
[2025-10-06 11:54:26] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -44.892594 | E_var:     0.2648 | E_err:   0.008040
[2025-10-06 11:54:31] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -44.883465 | E_var:     0.1993 | E_err:   0.006975
[2025-10-06 11:54:37] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -44.891559 | E_var:     0.2104 | E_err:   0.007166
[2025-10-06 11:54:42] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -44.877241 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 11:54:47] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -44.874387 | E_var:     0.4790 | E_err:   0.010814
[2025-10-06 11:54:52] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -44.886129 | E_var:     0.1992 | E_err:   0.006973
[2025-10-06 11:54:57] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -44.884832 | E_var:     0.2133 | E_err:   0.007216
[2025-10-06 11:55:02] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -44.879157 | E_var:     0.2673 | E_err:   0.008078
[2025-10-06 11:55:07] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -44.884019 | E_var:     0.2358 | E_err:   0.007587
[2025-10-06 11:55:13] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -44.871690 | E_var:     0.2637 | E_err:   0.008024
[2025-10-06 11:55:18] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -44.884970 | E_var:     0.1916 | E_err:   0.006839
[2025-10-06 11:55:23] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -44.881867 | E_var:     0.1843 | E_err:   0.006708
[2025-10-06 11:55:28] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -44.875663 | E_var:     0.2740 | E_err:   0.008178
[2025-10-06 11:55:33] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -44.873607 | E_var:     0.2422 | E_err:   0.007689
[2025-10-06 11:55:38] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -44.867919 | E_var:     0.2124 | E_err:   0.007202
[2025-10-06 11:55:44] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -44.877434 | E_var:     0.2375 | E_err:   0.007615
[2025-10-06 11:55:49] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -44.891199 | E_var:     0.2408 | E_err:   0.007668
[2025-10-06 11:55:54] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -44.880726 | E_var:     0.1932 | E_err:   0.006868
[2025-10-06 11:55:59] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -44.880232 | E_var:     0.1916 | E_err:   0.006840
[2025-10-06 11:56:04] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -44.885342 | E_var:     0.2184 | E_err:   0.007302
[2025-10-06 11:56:09] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -44.882699 | E_var:     0.2452 | E_err:   0.007737
[2025-10-06 11:56:15] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -44.887465 | E_var:     0.1857 | E_err:   0.006734
[2025-10-06 11:56:20] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -44.875507 | E_var:     0.3139 | E_err:   0.008754
[2025-10-06 11:56:25] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -44.865001 | E_var:     0.3327 | E_err:   0.009012
[2025-10-06 11:56:30] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -44.878996 | E_var:     0.2744 | E_err:   0.008185
[2025-10-06 11:56:35] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -44.870306 | E_var:     0.2473 | E_err:   0.007770
[2025-10-06 11:56:40] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -44.880764 | E_var:     0.2820 | E_err:   0.008298
[2025-10-06 11:56:45] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -44.873226 | E_var:     0.2882 | E_err:   0.008388
[2025-10-06 11:56:51] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -44.901566 | E_var:     0.1936 | E_err:   0.006876
[2025-10-06 11:56:56] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -44.882323 | E_var:     0.1960 | E_err:   0.006918
[2025-10-06 11:57:01] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -44.879287 | E_var:     0.2766 | E_err:   0.008218
[2025-10-06 11:57:06] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -44.893123 | E_var:     0.2173 | E_err:   0.007283
[2025-10-06 11:57:11] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -44.880047 | E_var:     0.2188 | E_err:   0.007309
[2025-10-06 11:57:16] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -44.891329 | E_var:     0.2109 | E_err:   0.007176
[2025-10-06 11:57:22] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -44.880427 | E_var:     0.2229 | E_err:   0.007377
[2025-10-06 11:57:27] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -44.887749 | E_var:     0.1817 | E_err:   0.006660
[2025-10-06 11:57:32] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -44.884669 | E_var:     0.2106 | E_err:   0.007171
[2025-10-06 11:57:37] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -44.879538 | E_var:     0.2059 | E_err:   0.007090
[2025-10-06 11:57:42] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -44.896705 | E_var:     0.2033 | E_err:   0.007044
[2025-10-06 11:57:47] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -44.872979 | E_var:     0.3174 | E_err:   0.008803
[2025-10-06 11:57:52] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -44.879971 | E_var:     0.2857 | E_err:   0.008351
[2025-10-06 11:57:58] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -44.881633 | E_var:     0.1985 | E_err:   0.006962
[2025-10-06 11:58:03] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -44.883998 | E_var:     0.2745 | E_err:   0.008187
[2025-10-06 11:58:03] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 11:58:08] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -44.892337 | E_var:     0.2969 | E_err:   0.008514
[2025-10-06 11:58:13] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -44.885309 | E_var:     0.3122 | E_err:   0.008731
[2025-10-06 11:58:19] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -44.888947 | E_var:     0.2183 | E_err:   0.007300
[2025-10-06 11:58:24] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -44.883252 | E_var:     0.2518 | E_err:   0.007841
[2025-10-06 11:58:29] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -44.894358 | E_var:     0.2891 | E_err:   0.008402
[2025-10-06 11:58:34] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -44.887117 | E_var:     0.2413 | E_err:   0.007675
[2025-10-06 11:58:39] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -44.887223 | E_var:     0.2625 | E_err:   0.008005
[2025-10-06 11:58:44] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -44.888535 | E_var:     0.1940 | E_err:   0.006882
[2025-10-06 11:58:49] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -44.882323 | E_var:     0.2119 | E_err:   0.007192
[2025-10-06 11:58:55] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -44.886932 | E_var:     0.2437 | E_err:   0.007714
[2025-10-06 11:59:00] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -44.889288 | E_var:     0.3435 | E_err:   0.009157
[2025-10-06 11:59:05] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -44.882298 | E_var:     0.2238 | E_err:   0.007392
[2025-10-06 11:59:10] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -44.891239 | E_var:     0.2026 | E_err:   0.007033
[2025-10-06 11:59:15] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -44.885751 | E_var:     0.1899 | E_err:   0.006809
[2025-10-06 11:59:20] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -44.877754 | E_var:     0.1897 | E_err:   0.006806
[2025-10-06 11:59:26] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -44.891066 | E_var:     0.2103 | E_err:   0.007166
[2025-10-06 11:59:31] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -44.888882 | E_var:     0.1956 | E_err:   0.006911
[2025-10-06 11:59:36] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -44.866443 | E_var:     0.3890 | E_err:   0.009745
[2025-10-06 11:59:41] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -44.881703 | E_var:     0.2347 | E_err:   0.007570
[2025-10-06 11:59:46] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -44.887529 | E_var:     0.3033 | E_err:   0.008606
[2025-10-06 11:59:51] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -44.895665 | E_var:     0.2073 | E_err:   0.007115
[2025-10-06 11:59:56] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -44.880815 | E_var:     0.2028 | E_err:   0.007036
[2025-10-06 12:00:02] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -44.895731 | E_var:     0.2114 | E_err:   0.007184
[2025-10-06 12:00:07] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -44.887313 | E_var:     0.1992 | E_err:   0.006973
[2025-10-06 12:00:12] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -44.891575 | E_var:     0.2722 | E_err:   0.008152
[2025-10-06 12:00:17] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -44.898506 | E_var:     0.2477 | E_err:   0.007777
[2025-10-06 12:00:22] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -44.894970 | E_var:     0.2134 | E_err:   0.007219
[2025-10-06 12:00:27] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -44.896446 | E_var:     0.1542 | E_err:   0.006135
[2025-10-06 12:00:33] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -44.892935 | E_var:     0.1961 | E_err:   0.006920
[2025-10-06 12:00:38] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -44.875897 | E_var:     0.2673 | E_err:   0.008079
[2025-10-06 12:00:43] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -44.872987 | E_var:     0.1892 | E_err:   0.006797
[2025-10-06 12:00:48] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -44.880431 | E_var:     0.2079 | E_err:   0.007124
[2025-10-06 12:00:53] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -44.889631 | E_var:     0.2121 | E_err:   0.007196
[2025-10-06 12:00:58] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -44.883996 | E_var:     0.1841 | E_err:   0.006704
[2025-10-06 12:01:03] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -44.896185 | E_var:     0.1614 | E_err:   0.006278
[2025-10-06 12:01:09] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -44.880407 | E_var:     0.2184 | E_err:   0.007302
[2025-10-06 12:01:14] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -44.882779 | E_var:     0.2182 | E_err:   0.007299
[2025-10-06 12:01:19] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -44.880034 | E_var:     0.2385 | E_err:   0.007631
[2025-10-06 12:01:24] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -44.880420 | E_var:     0.1896 | E_err:   0.006804
[2025-10-06 12:01:29] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -44.887924 | E_var:     0.2169 | E_err:   0.007276
[2025-10-06 12:01:34] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -44.877413 | E_var:     0.2238 | E_err:   0.007392
[2025-10-06 12:01:40] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -44.883938 | E_var:     0.2330 | E_err:   0.007542
[2025-10-06 12:01:45] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -44.876408 | E_var:     0.2414 | E_err:   0.007677
[2025-10-06 12:01:50] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -44.883262 | E_var:     0.2377 | E_err:   0.007618
[2025-10-06 12:01:55] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -44.893110 | E_var:     0.2137 | E_err:   0.007223
[2025-10-06 12:02:00] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -44.888216 | E_var:     0.2079 | E_err:   0.007125
[2025-10-06 12:02:05] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -44.884560 | E_var:     0.2052 | E_err:   0.007078
[2025-10-06 12:02:10] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -44.870679 | E_var:     0.2388 | E_err:   0.007636
[2025-10-06 12:02:16] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -44.891157 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 12:02:21] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -44.894973 | E_var:     0.2063 | E_err:   0.007097
[2025-10-06 12:02:26] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -44.901215 | E_var:     0.2263 | E_err:   0.007433
[2025-10-06 12:02:31] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -44.897988 | E_var:     0.2833 | E_err:   0.008317
[2025-10-06 12:02:36] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -44.877207 | E_var:     0.2190 | E_err:   0.007313
[2025-10-06 12:02:41] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -44.885209 | E_var:     0.2327 | E_err:   0.007537
[2025-10-06 12:02:47] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -44.888523 | E_var:     0.2000 | E_err:   0.006987
[2025-10-06 12:02:52] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -44.893556 | E_var:     0.2149 | E_err:   0.007244
[2025-10-06 12:02:57] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -44.895958 | E_var:     0.1925 | E_err:   0.006855
[2025-10-06 12:03:02] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -44.892268 | E_var:     0.2024 | E_err:   0.007029
[2025-10-06 12:03:07] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -44.883923 | E_var:     0.2723 | E_err:   0.008153
[2025-10-06 12:03:12] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -44.876103 | E_var:     0.2290 | E_err:   0.007477
[2025-10-06 12:03:17] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -44.887419 | E_var:     0.2389 | E_err:   0.007637
[2025-10-06 12:03:23] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -44.870563 | E_var:     0.6440 | E_err:   0.012539
[2025-10-06 12:03:28] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -44.867234 | E_var:     0.2516 | E_err:   0.007838
[2025-10-06 12:03:33] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -44.881570 | E_var:     0.2571 | E_err:   0.007923
[2025-10-06 12:03:38] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -44.881749 | E_var:     0.2039 | E_err:   0.007056
[2025-10-06 12:03:43] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -44.884279 | E_var:     0.2753 | E_err:   0.008198
[2025-10-06 12:03:48] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -44.883158 | E_var:     0.2300 | E_err:   0.007494
[2025-10-06 12:03:54] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -44.897372 | E_var:     0.2635 | E_err:   0.008021
[2025-10-06 12:03:59] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -44.889497 | E_var:     0.2409 | E_err:   0.007670
[2025-10-06 12:04:04] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -44.883149 | E_var:     0.2132 | E_err:   0.007215
[2025-10-06 12:04:09] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -44.892668 | E_var:     0.2256 | E_err:   0.007421
[2025-10-06 12:04:14] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -44.889494 | E_var:     0.2379 | E_err:   0.007622
[2025-10-06 12:04:19] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -44.900481 | E_var:     0.2780 | E_err:   0.008239
[2025-10-06 12:04:24] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -44.896883 | E_var:     0.3750 | E_err:   0.009568
[2025-10-06 12:04:30] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -44.878533 | E_var:     0.1892 | E_err:   0.006796
[2025-10-06 12:04:35] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -44.871151 | E_var:     0.2770 | E_err:   0.008224
[2025-10-06 12:04:40] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -44.884924 | E_var:     0.2579 | E_err:   0.007934
[2025-10-06 12:04:45] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -44.886765 | E_var:     0.2741 | E_err:   0.008181
[2025-10-06 12:04:50] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -44.889285 | E_var:     0.2634 | E_err:   0.008018
[2025-10-06 12:04:55] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -44.892002 | E_var:     0.2417 | E_err:   0.007682
[2025-10-06 12:05:01] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -44.875126 | E_var:     0.2320 | E_err:   0.007526
[2025-10-06 12:05:06] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -44.905334 | E_var:     0.1820 | E_err:   0.006666
[2025-10-06 12:05:11] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -44.888782 | E_var:     0.2545 | E_err:   0.007882
[2025-10-06 12:05:16] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -44.881959 | E_var:     0.4715 | E_err:   0.010729
[2025-10-06 12:05:21] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -44.891404 | E_var:     0.2220 | E_err:   0.007362
[2025-10-06 12:05:26] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -44.900330 | E_var:     0.1981 | E_err:   0.006954
[2025-10-06 12:05:31] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -44.888888 | E_var:     0.2433 | E_err:   0.007707
[2025-10-06 12:05:37] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -44.889850 | E_var:     0.2337 | E_err:   0.007553
[2025-10-06 12:05:42] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -44.886990 | E_var:     0.1788 | E_err:   0.006608
[2025-10-06 12:05:47] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -44.879729 | E_var:     0.1850 | E_err:   0.006721
[2025-10-06 12:05:52] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -44.878839 | E_var:     0.1739 | E_err:   0.006516
[2025-10-06 12:05:57] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -44.889534 | E_var:     0.2052 | E_err:   0.007077
[2025-10-06 12:06:02] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -44.889637 | E_var:     0.2278 | E_err:   0.007458
[2025-10-06 12:06:08] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -44.890652 | E_var:     0.1664 | E_err:   0.006373
[2025-10-06 12:06:13] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -44.884021 | E_var:     0.2486 | E_err:   0.007790
[2025-10-06 12:06:18] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -44.891507 | E_var:     0.2440 | E_err:   0.007719
[2025-10-06 12:06:23] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -44.871507 | E_var:     0.3455 | E_err:   0.009184
[2025-10-06 12:06:28] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -44.893190 | E_var:     0.1680 | E_err:   0.006404
[2025-10-06 12:06:33] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -44.880291 | E_var:     0.2456 | E_err:   0.007743
[2025-10-06 12:06:38] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -44.897195 | E_var:     0.2038 | E_err:   0.007054
[2025-10-06 12:06:38] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 12:06:44] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -44.881475 | E_var:     0.2518 | E_err:   0.007841
[2025-10-06 12:06:49] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -44.884485 | E_var:     0.1988 | E_err:   0.006966
[2025-10-06 12:06:54] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -44.876364 | E_var:     0.2463 | E_err:   0.007754
[2025-10-06 12:06:59] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -44.888141 | E_var:     0.2323 | E_err:   0.007531
[2025-10-06 12:07:04] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -44.880787 | E_var:     0.2826 | E_err:   0.008307
[2025-10-06 12:07:09] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -44.886662 | E_var:     0.2257 | E_err:   0.007424
[2025-10-06 12:07:15] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -44.873490 | E_var:     0.2253 | E_err:   0.007416
[2025-10-06 12:07:20] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -44.884245 | E_var:     0.1881 | E_err:   0.006776
[2025-10-06 12:07:25] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -44.886274 | E_var:     0.2987 | E_err:   0.008539
[2025-10-06 12:07:30] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -44.886197 | E_var:     0.2246 | E_err:   0.007405
[2025-10-06 12:07:35] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -44.885766 | E_var:     0.2109 | E_err:   0.007176
[2025-10-06 12:07:40] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -44.877798 | E_var:     0.2146 | E_err:   0.007238
[2025-10-06 12:07:45] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -44.884068 | E_var:     0.2196 | E_err:   0.007322
[2025-10-06 12:07:51] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -44.889836 | E_var:     0.1971 | E_err:   0.006936
[2025-10-06 12:07:56] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -44.881043 | E_var:     0.2845 | E_err:   0.008334
[2025-10-06 12:08:01] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -44.888083 | E_var:     0.2197 | E_err:   0.007324
[2025-10-06 12:08:06] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -44.882333 | E_var:     0.2245 | E_err:   0.007404
[2025-10-06 12:08:11] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -44.885380 | E_var:     0.2355 | E_err:   0.007582
[2025-10-06 12:08:16] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -44.891010 | E_var:     0.2315 | E_err:   0.007518
[2025-10-06 12:08:21] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -44.884439 | E_var:     0.2157 | E_err:   0.007257
[2025-10-06 12:08:27] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -44.865424 | E_var:     0.2297 | E_err:   0.007488
[2025-10-06 12:08:32] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -44.889798 | E_var:     0.2350 | E_err:   0.007574
[2025-10-06 12:08:37] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -44.889504 | E_var:     0.2542 | E_err:   0.007878
[2025-10-06 12:08:42] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -44.879813 | E_var:     0.4627 | E_err:   0.010629
[2025-10-06 12:08:47] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -44.882200 | E_var:     0.2142 | E_err:   0.007231
[2025-10-06 12:08:52] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -44.878771 | E_var:     0.1942 | E_err:   0.006886
[2025-10-06 12:08:58] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -44.863669 | E_var:     0.2387 | E_err:   0.007635
[2025-10-06 12:09:03] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -44.881074 | E_var:     0.2010 | E_err:   0.007005
[2025-10-06 12:09:08] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -44.882745 | E_var:     0.2004 | E_err:   0.006994
[2025-10-06 12:09:13] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -44.896823 | E_var:     0.2403 | E_err:   0.007659
[2025-10-06 12:09:18] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -44.884790 | E_var:     0.2658 | E_err:   0.008056
[2025-10-06 12:09:23] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -44.886780 | E_var:     0.2114 | E_err:   0.007185
[2025-10-06 12:09:28] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -44.880838 | E_var:     0.1678 | E_err:   0.006400
[2025-10-06 12:09:34] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -44.881528 | E_var:     0.2389 | E_err:   0.007637
[2025-10-06 12:09:39] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -44.886832 | E_var:     0.2105 | E_err:   0.007169
[2025-10-06 12:09:44] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -44.884915 | E_var:     0.2242 | E_err:   0.007399
[2025-10-06 12:09:49] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -44.896495 | E_var:     0.2166 | E_err:   0.007273
[2025-10-06 12:09:54] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -44.889473 | E_var:     0.2028 | E_err:   0.007036
[2025-10-06 12:09:59] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -44.890093 | E_var:     0.2077 | E_err:   0.007121
[2025-10-06 12:10:04] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -44.880257 | E_var:     0.2817 | E_err:   0.008293
[2025-10-06 12:10:10] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -44.880304 | E_var:     0.2312 | E_err:   0.007513
[2025-10-06 12:10:15] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -44.883153 | E_var:     0.2377 | E_err:   0.007618
[2025-10-06 12:10:20] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -44.888441 | E_var:     0.3981 | E_err:   0.009859
[2025-10-06 12:10:25] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -44.891445 | E_var:     0.2037 | E_err:   0.007053
[2025-10-06 12:10:30] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -44.884531 | E_var:     0.2103 | E_err:   0.007166
[2025-10-06 12:10:35] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -44.887289 | E_var:     0.2199 | E_err:   0.007328
[2025-10-06 12:10:41] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -44.887308 | E_var:     0.1948 | E_err:   0.006896
[2025-10-06 12:10:46] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -44.877784 | E_var:     0.2364 | E_err:   0.007597
[2025-10-06 12:10:51] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -44.894383 | E_var:     0.2789 | E_err:   0.008251
[2025-10-06 12:10:56] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -44.875588 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 12:10:56] ======================================================================================================
[2025-10-06 12:10:56] ✅ Training completed successfully
[2025-10-06 12:10:56] Total restarts: 2
[2025-10-06 12:10:58] Final Energy: -44.87558780 ± 0.00728502
[2025-10-06 12:10:58] Final Variance: 0.217381
[2025-10-06 12:10:58] ======================================================================================================
[2025-10-06 12:10:58] ======================================================================================================
[2025-10-06 12:10:58] Training completed | Runtime: 5451.7s
[2025-10-06 12:10:59] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 12:10:59] ======================================================================================================
