[2025-10-07 03:06:58] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.76/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 03:06:58]   - 迭代次数: final
[2025-10-07 03:06:58]   - 能量: -42.304412-0.000007j ± 0.009998, Var: 0.409427
[2025-10-07 03:06:58]   - 时间戳: 2025-10-03T02:53:25.449126+08:00
[2025-10-07 03:07:19] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 03:07:19] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 03:07:19] ======================================================================================================
[2025-10-07 03:07:19] GCNN for Shastry-Sutherland Model
[2025-10-07 03:07:19] ======================================================================================================
[2025-10-07 03:07:19] System parameters:
[2025-10-07 03:07:19]   - System size: L=5, N=100
[2025-10-07 03:07:19]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-10-07 03:07:19] ------------------------------------------------------------------------------------------------------
[2025-10-07 03:07:19] Model parameters:
[2025-10-07 03:07:19]   - Number of layers = 6
[2025-10-07 03:07:19]   - Number of features = 4
[2025-10-07 03:07:19]   - Total parameters = 32444
[2025-10-07 03:07:19] ------------------------------------------------------------------------------------------------------
[2025-10-07 03:07:19] Training parameters:
[2025-10-07 03:07:19]   - Total iterations: 1050
[2025-10-07 03:07:19]   - Annealing cycles: 3
[2025-10-07 03:07:20]   - Initial period: 150
[2025-10-07 03:07:20]   - Period multiplier: 2.0
[2025-10-07 03:07:20]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 03:07:20]   - Samples: 4096
[2025-10-07 03:07:20]   - Discarded samples: 0
[2025-10-07 03:07:20]   - Chunk size: 4096
[2025-10-07 03:07:20]   - Diagonal shift: 0.15
[2025-10-07 03:07:20]   - Gradient clipping: 1.0
[2025-10-07 03:07:20]   - Checkpoint enabled: interval=100
[2025-10-07 03:07:20]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.77/model_L6F4/training/checkpoints
[2025-10-07 03:07:20] ------------------------------------------------------------------------------------------------------
[2025-10-07 03:07:20] Device status:
[2025-10-07 03:07:20]   - Devices model: NVIDIA H200 NVL
[2025-10-07 03:07:20]   - Number of devices: 1
[2025-10-07 03:07:20]   - Sharding: True
[2025-10-07 03:07:20] ======================================================================================================
[2025-10-07 03:08:01] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -42.890697 | E_var:     3.6230 | E_err:   0.029741
[2025-10-07 03:08:28] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -42.916776 | E_var:     3.5930 | E_err:   0.029617
[2025-10-07 03:08:36] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -42.883716 | E_var:     2.7409 | E_err:   0.025868
[2025-10-07 03:08:43] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -42.877770 | E_var:     3.3376 | E_err:   0.028545
[2025-10-07 03:08:51] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -42.887816 | E_var:     2.2168 | E_err:   0.023264
[2025-10-07 03:08:59] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -42.941195 | E_var:     0.5104 | E_err:   0.011162
[2025-10-07 03:09:07] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -42.948735 | E_var:     0.5511 | E_err:   0.011599
[2025-10-07 03:09:15] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -42.931202 | E_var:     0.3503 | E_err:   0.009248
[2025-10-07 03:09:23] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -42.941909 | E_var:     0.3799 | E_err:   0.009631
[2025-10-07 03:09:30] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -42.955926 | E_var:     0.3714 | E_err:   0.009523
[2025-10-07 03:09:38] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -42.931739 | E_var:     0.3856 | E_err:   0.009703
[2025-10-07 03:09:46] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -42.937913 | E_var:     0.3846 | E_err:   0.009690
[2025-10-07 03:09:54] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -42.932584 | E_var:     0.4154 | E_err:   0.010070
[2025-10-07 03:10:02] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -42.961109 | E_var:     0.3608 | E_err:   0.009386
[2025-10-07 03:10:09] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -42.941696 | E_var:     0.4270 | E_err:   0.010211
[2025-10-07 03:10:17] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -42.944870 | E_var:     0.4177 | E_err:   0.010098
[2025-10-07 03:10:25] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -42.951542 | E_var:     0.3572 | E_err:   0.009339
[2025-10-07 03:10:33] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -42.955322 | E_var:     0.3591 | E_err:   0.009363
[2025-10-07 03:10:41] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -42.929199 | E_var:     0.4155 | E_err:   0.010072
[2025-10-07 03:10:49] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -42.939632 | E_var:     0.3951 | E_err:   0.009821
[2025-10-07 03:10:56] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -42.960942 | E_var:     0.4301 | E_err:   0.010247
[2025-10-07 03:11:04] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -42.947217 | E_var:     0.4063 | E_err:   0.009959
[2025-10-07 03:11:12] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -42.939687 | E_var:     0.4467 | E_err:   0.010443
[2025-10-07 03:11:20] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -42.958302 | E_var:     0.3767 | E_err:   0.009590
[2025-10-07 03:11:28] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -42.944607 | E_var:     0.3245 | E_err:   0.008901
[2025-10-07 03:11:36] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -42.950691 | E_var:     0.5301 | E_err:   0.011376
[2025-10-07 03:11:43] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -42.939589 | E_var:     0.3710 | E_err:   0.009517
[2025-10-07 03:11:51] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -42.928203 | E_var:     0.3158 | E_err:   0.008780
[2025-10-07 03:11:59] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -42.927991 | E_var:     0.4231 | E_err:   0.010164
[2025-10-07 03:12:07] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -42.963810 | E_var:     0.3898 | E_err:   0.009756
[2025-10-07 03:12:15] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -42.940632 | E_var:     0.3832 | E_err:   0.009673
[2025-10-07 03:12:23] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -42.939354 | E_var:     0.4083 | E_err:   0.009984
[2025-10-07 03:12:30] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -42.934346 | E_var:     0.3055 | E_err:   0.008637
[2025-10-07 03:12:38] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -42.970239 | E_var:     0.4193 | E_err:   0.010118
[2025-10-07 03:12:46] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -42.926014 | E_var:     0.3954 | E_err:   0.009826
[2025-10-07 03:12:54] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -42.930264 | E_var:     0.3325 | E_err:   0.009010
[2025-10-07 03:13:02] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -42.923504 | E_var:     0.3331 | E_err:   0.009018
[2025-10-07 03:13:10] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -42.923554 | E_var:     0.4125 | E_err:   0.010036
[2025-10-07 03:13:17] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -42.957549 | E_var:     0.3626 | E_err:   0.009409
[2025-10-07 03:13:25] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -42.943525 | E_var:     0.3699 | E_err:   0.009503
[2025-10-07 03:13:33] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -42.926917 | E_var:     0.3375 | E_err:   0.009078
[2025-10-07 03:13:41] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -42.934743 | E_var:     0.3727 | E_err:   0.009538
[2025-10-07 03:13:49] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -42.931972 | E_var:     0.3947 | E_err:   0.009816
[2025-10-07 03:13:56] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -42.946506 | E_var:     0.3321 | E_err:   0.009005
[2025-10-07 03:14:04] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -42.957795 | E_var:     0.4172 | E_err:   0.010092
[2025-10-07 03:14:12] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -42.935959 | E_var:     0.3805 | E_err:   0.009639
[2025-10-07 03:14:20] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -42.942726 | E_var:     0.4572 | E_err:   0.010565
[2025-10-07 03:14:28] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -42.945787 | E_var:     0.5296 | E_err:   0.011371
[2025-10-07 03:14:36] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -42.933965 | E_var:     0.6288 | E_err:   0.012390
[2025-10-07 03:14:43] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -42.956531 | E_var:     0.3752 | E_err:   0.009570
[2025-10-07 03:14:51] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -42.951687 | E_var:     0.3681 | E_err:   0.009480
[2025-10-07 03:14:59] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -42.944297 | E_var:     0.3742 | E_err:   0.009558
[2025-10-07 03:15:07] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -42.959969 | E_var:     0.3272 | E_err:   0.008937
[2025-10-07 03:15:15] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -42.949430 | E_var:     0.3298 | E_err:   0.008973
[2025-10-07 03:15:23] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -42.942424 | E_var:     0.3945 | E_err:   0.009814
[2025-10-07 03:15:30] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -42.927078 | E_var:     0.4064 | E_err:   0.009961
[2025-10-07 03:15:38] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -42.931626 | E_var:     0.3585 | E_err:   0.009355
[2025-10-07 03:15:46] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -42.956683 | E_var:     0.3898 | E_err:   0.009755
[2025-10-07 03:15:54] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -42.939681 | E_var:     0.4054 | E_err:   0.009948
[2025-10-07 03:16:02] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -42.963570 | E_var:     0.4290 | E_err:   0.010234
[2025-10-07 03:16:09] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -42.954139 | E_var:     0.4580 | E_err:   0.010575
[2025-10-07 03:16:17] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -42.946134 | E_var:     0.3486 | E_err:   0.009225
[2025-10-07 03:16:25] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -42.940027 | E_var:     0.3360 | E_err:   0.009057
[2025-10-07 03:16:33] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -42.948218 | E_var:     0.3965 | E_err:   0.009839
[2025-10-07 03:16:41] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -42.977945 | E_var:     1.3692 | E_err:   0.018284
[2025-10-07 03:16:49] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -42.947817 | E_var:     0.4164 | E_err:   0.010083
[2025-10-07 03:16:56] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -42.947530 | E_var:     0.3794 | E_err:   0.009624
[2025-10-07 03:17:04] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -42.951845 | E_var:     0.3541 | E_err:   0.009297
[2025-10-07 03:17:12] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -42.949627 | E_var:     0.3785 | E_err:   0.009613
[2025-10-07 03:17:20] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -42.943158 | E_var:     0.3297 | E_err:   0.008971
[2025-10-07 03:17:28] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -42.929232 | E_var:     0.4193 | E_err:   0.010117
[2025-10-07 03:17:36] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -42.936128 | E_var:     0.3068 | E_err:   0.008654
[2025-10-07 03:17:43] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -42.945708 | E_var:     0.3739 | E_err:   0.009554
[2025-10-07 03:17:51] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -42.948475 | E_var:     0.4858 | E_err:   0.010891
[2025-10-07 03:17:59] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -42.947870 | E_var:     0.2926 | E_err:   0.008452
[2025-10-07 03:18:07] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -42.934859 | E_var:     0.3501 | E_err:   0.009245
[2025-10-07 03:18:15] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -42.950451 | E_var:     0.3893 | E_err:   0.009750
[2025-10-07 03:18:22] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -42.947723 | E_var:     0.3353 | E_err:   0.009047
[2025-10-07 03:18:30] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -42.954540 | E_var:     0.4515 | E_err:   0.010499
[2025-10-07 03:18:38] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -42.923749 | E_var:     0.3169 | E_err:   0.008796
[2025-10-07 03:18:46] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -42.940965 | E_var:     0.3873 | E_err:   0.009724
[2025-10-07 03:18:54] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -42.936850 | E_var:     0.3301 | E_err:   0.008977
[2025-10-07 03:19:02] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -42.930009 | E_var:     0.5661 | E_err:   0.011756
[2025-10-07 03:19:09] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -42.942229 | E_var:     0.3542 | E_err:   0.009299
[2025-10-07 03:19:17] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -42.939204 | E_var:     0.3533 | E_err:   0.009287
[2025-10-07 03:19:25] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -42.935599 | E_var:     0.5105 | E_err:   0.011163
[2025-10-07 03:19:33] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -42.939064 | E_var:     0.3427 | E_err:   0.009147
[2025-10-07 03:19:41] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -42.944230 | E_var:     0.4336 | E_err:   0.010289
[2025-10-07 03:19:48] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -42.935911 | E_var:     0.3649 | E_err:   0.009439
[2025-10-07 03:19:56] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -42.935289 | E_var:     0.3735 | E_err:   0.009549
[2025-10-07 03:20:04] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -42.949200 | E_var:     0.3899 | E_err:   0.009757
[2025-10-07 03:20:12] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -42.931913 | E_var:     0.4275 | E_err:   0.010216
[2025-10-07 03:20:20] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -42.933208 | E_var:     0.3965 | E_err:   0.009839
[2025-10-07 03:20:28] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -42.938208 | E_var:     0.4023 | E_err:   0.009910
[2025-10-07 03:20:35] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -42.948565 | E_var:     0.4121 | E_err:   0.010030
[2025-10-07 03:20:43] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -42.935166 | E_var:     0.3282 | E_err:   0.008951
[2025-10-07 03:20:51] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -42.940132 | E_var:     0.3216 | E_err:   0.008860
[2025-10-07 03:20:59] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -42.943403 | E_var:     0.6301 | E_err:   0.012403
[2025-10-07 03:21:07] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -42.941110 | E_var:     0.3277 | E_err:   0.008944
[2025-10-07 03:21:14] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -42.949377 | E_var:     0.4261 | E_err:   0.010199
[2025-10-07 03:21:14] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 03:21:22] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -42.944813 | E_var:     0.5388 | E_err:   0.011470
[2025-10-07 03:21:30] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -42.956947 | E_var:     0.3495 | E_err:   0.009237
[2025-10-07 03:21:38] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -42.930186 | E_var:     0.3678 | E_err:   0.009476
[2025-10-07 03:21:46] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -42.943109 | E_var:     0.3148 | E_err:   0.008767
[2025-10-07 03:21:54] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -42.941271 | E_var:     0.3071 | E_err:   0.008658
[2025-10-07 03:22:01] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -42.928184 | E_var:     0.3778 | E_err:   0.009604
[2025-10-07 03:22:09] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -42.933505 | E_var:     0.3110 | E_err:   0.008714
[2025-10-07 03:22:17] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -42.940882 | E_var:     0.4235 | E_err:   0.010169
[2025-10-07 03:22:25] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -42.936648 | E_var:     0.3388 | E_err:   0.009094
[2025-10-07 03:22:33] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -42.943506 | E_var:     0.4103 | E_err:   0.010008
[2025-10-07 03:22:41] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -42.948581 | E_var:     0.3199 | E_err:   0.008838
[2025-10-07 03:22:48] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -42.943308 | E_var:     0.2946 | E_err:   0.008481
[2025-10-07 03:22:56] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -42.945961 | E_var:     0.3167 | E_err:   0.008793
[2025-10-07 03:23:04] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -42.933852 | E_var:     0.4294 | E_err:   0.010238
[2025-10-07 03:23:12] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -42.938274 | E_var:     0.3613 | E_err:   0.009392
[2025-10-07 03:23:20] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -42.937927 | E_var:     0.4007 | E_err:   0.009891
[2025-10-07 03:23:27] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -42.937470 | E_var:     0.4513 | E_err:   0.010497
[2025-10-07 03:23:35] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -42.953160 | E_var:     0.3886 | E_err:   0.009740
[2025-10-07 03:23:43] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -42.952388 | E_var:     0.3139 | E_err:   0.008754
[2025-10-07 03:23:51] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -42.934253 | E_var:     0.3340 | E_err:   0.009030
[2025-10-07 03:23:59] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -42.940368 | E_var:     0.3463 | E_err:   0.009195
[2025-10-07 03:24:07] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -42.958821 | E_var:     0.3361 | E_err:   0.009058
[2025-10-07 03:24:14] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -42.951712 | E_var:     0.4331 | E_err:   0.010283
[2025-10-07 03:24:22] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -42.930140 | E_var:     0.3523 | E_err:   0.009274
[2025-10-07 03:24:30] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -42.944936 | E_var:     0.3539 | E_err:   0.009296
[2025-10-07 03:24:38] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -42.951954 | E_var:     0.3468 | E_err:   0.009202
[2025-10-07 03:24:46] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -42.924612 | E_var:     0.4237 | E_err:   0.010170
[2025-10-07 03:24:53] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -42.937328 | E_var:     0.4249 | E_err:   0.010185
[2025-10-07 03:25:01] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -42.930980 | E_var:     0.4328 | E_err:   0.010280
[2025-10-07 03:25:09] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -42.934630 | E_var:     0.3245 | E_err:   0.008901
[2025-10-07 03:25:17] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -42.948026 | E_var:     0.3952 | E_err:   0.009822
[2025-10-07 03:25:25] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -42.943899 | E_var:     0.3356 | E_err:   0.009051
[2025-10-07 03:25:33] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -42.943518 | E_var:     0.3825 | E_err:   0.009664
[2025-10-07 03:25:40] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -42.952569 | E_var:     0.3971 | E_err:   0.009846
[2025-10-07 03:25:48] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -42.955700 | E_var:     0.3394 | E_err:   0.009103
[2025-10-07 03:25:56] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -42.932785 | E_var:     0.3781 | E_err:   0.009607
[2025-10-07 03:26:04] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -42.945534 | E_var:     0.3142 | E_err:   0.008759
[2025-10-07 03:26:12] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -42.934833 | E_var:     0.3873 | E_err:   0.009724
[2025-10-07 03:26:20] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -42.949617 | E_var:     0.6112 | E_err:   0.012215
[2025-10-07 03:26:27] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -42.943592 | E_var:     0.3836 | E_err:   0.009677
[2025-10-07 03:26:35] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -42.950618 | E_var:     0.3267 | E_err:   0.008931
[2025-10-07 03:26:43] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -42.943808 | E_var:     0.3800 | E_err:   0.009632
[2025-10-07 03:26:51] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -42.942536 | E_var:     0.4176 | E_err:   0.010097
[2025-10-07 03:26:59] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -42.961511 | E_var:     0.3328 | E_err:   0.009014
[2025-10-07 03:27:06] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -42.958412 | E_var:     0.5785 | E_err:   0.011884
[2025-10-07 03:27:14] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -42.953757 | E_var:     0.2828 | E_err:   0.008309
[2025-10-07 03:27:22] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -42.952761 | E_var:     0.3213 | E_err:   0.008856
[2025-10-07 03:27:30] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -42.948070 | E_var:     0.3595 | E_err:   0.009369
[2025-10-07 03:27:38] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -42.939840 | E_var:     0.3623 | E_err:   0.009404
[2025-10-07 03:27:46] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -42.929562 | E_var:     0.4738 | E_err:   0.010756
[2025-10-07 03:27:46] 🔄 RESTART #1 | Period: 300
[2025-10-07 03:27:53] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -42.940803 | E_var:     0.3455 | E_err:   0.009184
[2025-10-07 03:28:01] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -42.938502 | E_var:     0.4095 | E_err:   0.009999
[2025-10-07 03:28:09] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -42.950215 | E_var:     0.4447 | E_err:   0.010420
[2025-10-07 03:28:17] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -42.930501 | E_var:     0.3514 | E_err:   0.009263
[2025-10-07 03:28:25] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -42.942707 | E_var:     0.3185 | E_err:   0.008818
[2025-10-07 03:28:32] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -42.946795 | E_var:     0.3530 | E_err:   0.009283
[2025-10-07 03:28:40] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -42.935400 | E_var:     0.4662 | E_err:   0.010669
[2025-10-07 03:28:48] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -42.948327 | E_var:     0.3432 | E_err:   0.009153
[2025-10-07 03:28:56] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -42.945287 | E_var:     0.3750 | E_err:   0.009568
[2025-10-07 03:29:04] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -42.946536 | E_var:     0.4711 | E_err:   0.010724
[2025-10-07 03:29:12] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -42.923620 | E_var:     0.4173 | E_err:   0.010094
[2025-10-07 03:29:19] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -42.937276 | E_var:     0.3048 | E_err:   0.008626
[2025-10-07 03:29:27] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -42.950638 | E_var:     0.3832 | E_err:   0.009672
[2025-10-07 03:29:35] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -42.955340 | E_var:     0.4413 | E_err:   0.010379
[2025-10-07 03:29:43] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -42.932681 | E_var:     0.4312 | E_err:   0.010260
[2025-10-07 03:29:51] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -42.945855 | E_var:     0.3194 | E_err:   0.008831
[2025-10-07 03:29:59] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -42.942425 | E_var:     0.4273 | E_err:   0.010214
[2025-10-07 03:30:06] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -42.955975 | E_var:     0.3794 | E_err:   0.009624
[2025-10-07 03:30:14] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -42.950658 | E_var:     0.4006 | E_err:   0.009889
[2025-10-07 03:30:22] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -42.928627 | E_var:     0.5079 | E_err:   0.011135
[2025-10-07 03:30:30] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -42.944366 | E_var:     0.3769 | E_err:   0.009593
[2025-10-07 03:30:38] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -42.938358 | E_var:     0.4783 | E_err:   0.010807
[2025-10-07 03:30:45] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -42.935124 | E_var:     0.3228 | E_err:   0.008877
[2025-10-07 03:30:53] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -42.964382 | E_var:     0.4098 | E_err:   0.010002
[2025-10-07 03:31:01] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -42.937047 | E_var:     0.4561 | E_err:   0.010552
[2025-10-07 03:31:09] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -42.952933 | E_var:     0.3050 | E_err:   0.008630
[2025-10-07 03:31:17] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -42.950438 | E_var:     0.3561 | E_err:   0.009324
[2025-10-07 03:31:25] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -42.932960 | E_var:     0.3258 | E_err:   0.008919
[2025-10-07 03:31:32] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -42.951380 | E_var:     0.3148 | E_err:   0.008766
[2025-10-07 03:31:40] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -42.957602 | E_var:     0.4013 | E_err:   0.009898
[2025-10-07 03:31:48] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -42.947683 | E_var:     0.3404 | E_err:   0.009116
[2025-10-07 03:31:56] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -42.951841 | E_var:     0.3470 | E_err:   0.009204
[2025-10-07 03:32:04] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -42.934740 | E_var:     0.4701 | E_err:   0.010713
[2025-10-07 03:32:11] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -42.960670 | E_var:     0.7950 | E_err:   0.013932
[2025-10-07 03:32:19] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -42.937100 | E_var:     0.4067 | E_err:   0.009965
[2025-10-07 03:32:27] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -42.957510 | E_var:     0.3687 | E_err:   0.009488
[2025-10-07 03:32:35] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -42.954658 | E_var:     0.2831 | E_err:   0.008314
[2025-10-07 03:32:43] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -42.949792 | E_var:     0.3281 | E_err:   0.008950
[2025-10-07 03:32:51] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -42.944704 | E_var:     0.3897 | E_err:   0.009754
[2025-10-07 03:32:58] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -42.935546 | E_var:     0.3516 | E_err:   0.009265
[2025-10-07 03:33:06] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -42.939290 | E_var:     0.3384 | E_err:   0.009089
[2025-10-07 03:33:14] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -42.939448 | E_var:     0.4177 | E_err:   0.010098
[2025-10-07 03:33:22] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -42.951351 | E_var:     0.3078 | E_err:   0.008668
[2025-10-07 03:33:30] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -42.934089 | E_var:     0.3595 | E_err:   0.009369
[2025-10-07 03:33:37] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -42.930796 | E_var:     0.3004 | E_err:   0.008563
[2025-10-07 03:33:45] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -42.952441 | E_var:     0.4217 | E_err:   0.010147
[2025-10-07 03:33:53] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -42.946394 | E_var:     0.3449 | E_err:   0.009177
[2025-10-07 03:34:01] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -42.956141 | E_var:     0.4706 | E_err:   0.010719
[2025-10-07 03:34:09] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -42.942320 | E_var:     0.3602 | E_err:   0.009378
[2025-10-07 03:34:17] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -42.946282 | E_var:     0.3779 | E_err:   0.009606
[2025-10-07 03:34:17] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 03:34:24] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -42.950289 | E_var:     0.3438 | E_err:   0.009161
[2025-10-07 03:34:32] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -42.931238 | E_var:     0.3001 | E_err:   0.008560
[2025-10-07 03:34:40] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -42.937329 | E_var:     0.3402 | E_err:   0.009114
[2025-10-07 03:34:48] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -42.939081 | E_var:     0.3858 | E_err:   0.009706
[2025-10-07 03:34:56] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -42.952684 | E_var:     0.2971 | E_err:   0.008517
[2025-10-07 03:35:04] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -42.930187 | E_var:     0.3924 | E_err:   0.009788
[2025-10-07 03:35:11] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -42.936443 | E_var:     0.4376 | E_err:   0.010336
[2025-10-07 03:35:19] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -42.941590 | E_var:     0.3042 | E_err:   0.008618
[2025-10-07 03:35:27] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -42.962937 | E_var:     0.3722 | E_err:   0.009532
[2025-10-07 03:35:35] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -42.944139 | E_var:     0.3240 | E_err:   0.008894
[2025-10-07 03:35:43] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -42.951291 | E_var:     0.3725 | E_err:   0.009537
[2025-10-07 03:35:50] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -42.955314 | E_var:     0.5081 | E_err:   0.011138
[2025-10-07 03:35:58] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -42.949895 | E_var:     0.4122 | E_err:   0.010031
[2025-10-07 03:36:06] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -42.956328 | E_var:     0.3806 | E_err:   0.009640
[2025-10-07 03:36:14] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -42.946004 | E_var:     0.3544 | E_err:   0.009302
[2025-10-07 03:36:22] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -42.940797 | E_var:     0.6310 | E_err:   0.012412
[2025-10-07 03:36:30] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -42.951409 | E_var:     0.5288 | E_err:   0.011362
[2025-10-07 03:36:37] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -42.942343 | E_var:     0.3503 | E_err:   0.009248
[2025-10-07 03:36:45] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -42.944660 | E_var:     0.3997 | E_err:   0.009878
[2025-10-07 03:36:53] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -42.939121 | E_var:     0.3745 | E_err:   0.009562
[2025-10-07 03:37:01] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -42.941127 | E_var:     0.4503 | E_err:   0.010485
[2025-10-07 03:37:09] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -42.933944 | E_var:     0.4490 | E_err:   0.010470
[2025-10-07 03:37:16] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -42.943748 | E_var:     0.3084 | E_err:   0.008677
[2025-10-07 03:37:24] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -42.941584 | E_var:     0.5036 | E_err:   0.011089
[2025-10-07 03:37:32] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -42.957897 | E_var:     0.5264 | E_err:   0.011336
[2025-10-07 03:37:40] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -42.949997 | E_var:     0.3867 | E_err:   0.009716
[2025-10-07 03:37:48] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -42.952990 | E_var:     0.3221 | E_err:   0.008868
[2025-10-07 03:37:56] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -42.957362 | E_var:     0.3257 | E_err:   0.008917
[2025-10-07 03:38:03] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -42.938131 | E_var:     0.3405 | E_err:   0.009117
[2025-10-07 03:38:11] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -42.948208 | E_var:     0.3107 | E_err:   0.008710
[2025-10-07 03:38:19] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -42.957713 | E_var:     0.3693 | E_err:   0.009496
[2025-10-07 03:38:27] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -42.938358 | E_var:     0.5147 | E_err:   0.011210
[2025-10-07 03:38:35] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -42.947237 | E_var:     0.4338 | E_err:   0.010291
[2025-10-07 03:38:43] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -42.945472 | E_var:     0.3728 | E_err:   0.009540
[2025-10-07 03:38:50] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -42.939802 | E_var:     0.3424 | E_err:   0.009143
[2025-10-07 03:38:58] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -42.966383 | E_var:     0.3483 | E_err:   0.009222
[2025-10-07 03:39:06] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -42.947266 | E_var:     0.3800 | E_err:   0.009632
[2025-10-07 03:39:14] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -42.937881 | E_var:     0.3395 | E_err:   0.009104
[2025-10-07 03:39:22] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -42.934492 | E_var:     0.4303 | E_err:   0.010250
[2025-10-07 03:39:29] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -42.936497 | E_var:     0.3539 | E_err:   0.009295
[2025-10-07 03:39:37] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -42.940450 | E_var:     0.3882 | E_err:   0.009735
[2025-10-07 03:39:45] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -42.951558 | E_var:     0.3607 | E_err:   0.009384
[2025-10-07 03:39:53] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -42.944939 | E_var:     0.3725 | E_err:   0.009537
[2025-10-07 03:40:01] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -42.958694 | E_var:     0.3225 | E_err:   0.008873
[2025-10-07 03:40:09] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -42.942228 | E_var:     0.3408 | E_err:   0.009121
[2025-10-07 03:40:16] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -42.961075 | E_var:     0.4619 | E_err:   0.010619
[2025-10-07 03:40:24] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -42.948421 | E_var:     0.3241 | E_err:   0.008895
[2025-10-07 03:40:32] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -42.956847 | E_var:     0.4132 | E_err:   0.010044
[2025-10-07 03:40:40] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -42.958910 | E_var:     0.3534 | E_err:   0.009289
[2025-10-07 03:40:48] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -42.937547 | E_var:     0.3957 | E_err:   0.009828
[2025-10-07 03:40:55] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -42.952588 | E_var:     0.3244 | E_err:   0.008899
[2025-10-07 03:41:03] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -42.939967 | E_var:     0.3448 | E_err:   0.009175
[2025-10-07 03:41:11] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -42.945738 | E_var:     0.3329 | E_err:   0.009015
[2025-10-07 03:41:19] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -42.945038 | E_var:     0.3690 | E_err:   0.009491
[2025-10-07 03:41:27] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -42.943786 | E_var:     0.3116 | E_err:   0.008723
[2025-10-07 03:41:35] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -42.942021 | E_var:     0.3730 | E_err:   0.009543
[2025-10-07 03:41:42] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -42.928539 | E_var:     0.3771 | E_err:   0.009596
[2025-10-07 03:41:50] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -42.941218 | E_var:     0.3999 | E_err:   0.009881
[2025-10-07 03:41:58] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -42.947081 | E_var:     0.3114 | E_err:   0.008719
[2025-10-07 03:42:06] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -42.945306 | E_var:     0.3480 | E_err:   0.009217
[2025-10-07 03:42:14] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -42.921857 | E_var:     0.3748 | E_err:   0.009566
[2025-10-07 03:42:21] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -42.958028 | E_var:     0.5177 | E_err:   0.011242
[2025-10-07 03:42:29] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -42.964302 | E_var:     0.5738 | E_err:   0.011836
[2025-10-07 03:42:37] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -42.940133 | E_var:     0.3674 | E_err:   0.009471
[2025-10-07 03:42:45] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -42.931393 | E_var:     0.3467 | E_err:   0.009200
[2025-10-07 03:42:53] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -42.953344 | E_var:     0.2936 | E_err:   0.008466
[2025-10-07 03:43:01] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -42.946175 | E_var:     0.3368 | E_err:   0.009068
[2025-10-07 03:43:08] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -42.950200 | E_var:     0.4797 | E_err:   0.010821
[2025-10-07 03:43:16] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -42.950988 | E_var:     0.3467 | E_err:   0.009200
[2025-10-07 03:43:24] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -42.958330 | E_var:     0.3530 | E_err:   0.009284
[2025-10-07 03:43:32] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -42.950755 | E_var:     0.3301 | E_err:   0.008977
[2025-10-07 03:43:40] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -42.958552 | E_var:     0.3439 | E_err:   0.009163
[2025-10-07 03:43:47] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -42.952956 | E_var:     0.4911 | E_err:   0.010950
[2025-10-07 03:43:55] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -42.953927 | E_var:     0.3221 | E_err:   0.008868
[2025-10-07 03:44:03] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -42.942008 | E_var:     0.3048 | E_err:   0.008626
[2025-10-07 03:44:11] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -42.948897 | E_var:     0.3046 | E_err:   0.008624
[2025-10-07 03:44:19] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -42.973347 | E_var:     0.3059 | E_err:   0.008641
[2025-10-07 03:44:27] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -42.939390 | E_var:     0.3506 | E_err:   0.009252
[2025-10-07 03:44:34] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -42.948067 | E_var:     0.3971 | E_err:   0.009846
[2025-10-07 03:44:42] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -42.938556 | E_var:     0.3542 | E_err:   0.009299
[2025-10-07 03:44:50] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -42.940229 | E_var:     0.3005 | E_err:   0.008565
[2025-10-07 03:44:58] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -42.944402 | E_var:     0.3861 | E_err:   0.009709
[2025-10-07 03:45:06] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -42.945811 | E_var:     0.3628 | E_err:   0.009412
[2025-10-07 03:45:13] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -42.940136 | E_var:     0.3103 | E_err:   0.008704
[2025-10-07 03:45:21] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -42.929216 | E_var:     0.4108 | E_err:   0.010014
[2025-10-07 03:45:29] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -42.951711 | E_var:     0.3627 | E_err:   0.009411
[2025-10-07 03:45:37] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -42.947009 | E_var:     0.3910 | E_err:   0.009771
[2025-10-07 03:45:45] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -42.958381 | E_var:     0.4581 | E_err:   0.010575
[2025-10-07 03:45:53] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -42.941591 | E_var:     0.3503 | E_err:   0.009248
[2025-10-07 03:46:00] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -42.949016 | E_var:     0.3733 | E_err:   0.009547
[2025-10-07 03:46:08] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -42.929060 | E_var:     0.4851 | E_err:   0.010883
[2025-10-07 03:46:16] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -42.938174 | E_var:     0.6814 | E_err:   0.012898
[2025-10-07 03:46:24] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -42.948254 | E_var:     0.3193 | E_err:   0.008829
[2025-10-07 03:46:32] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -42.948625 | E_var:     0.3956 | E_err:   0.009827
[2025-10-07 03:46:39] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -42.942273 | E_var:     0.3931 | E_err:   0.009796
[2025-10-07 03:46:47] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -42.955062 | E_var:     0.3993 | E_err:   0.009873
[2025-10-07 03:46:55] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -42.955554 | E_var:     0.3515 | E_err:   0.009264
[2025-10-07 03:47:03] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -42.954131 | E_var:     0.3216 | E_err:   0.008861
[2025-10-07 03:47:11] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -42.938245 | E_var:     0.5416 | E_err:   0.011499
[2025-10-07 03:47:19] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -42.937648 | E_var:     0.4100 | E_err:   0.010004
[2025-10-07 03:47:19] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 03:47:26] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -42.947668 | E_var:     0.3702 | E_err:   0.009507
[2025-10-07 03:47:34] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -42.948982 | E_var:     0.3622 | E_err:   0.009404
[2025-10-07 03:47:42] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -42.949793 | E_var:     0.5472 | E_err:   0.011558
[2025-10-07 03:47:50] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -42.955868 | E_var:     0.3179 | E_err:   0.008809
[2025-10-07 03:47:58] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -42.949087 | E_var:     0.3334 | E_err:   0.009023
[2025-10-07 03:48:06] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -42.937047 | E_var:     0.3538 | E_err:   0.009294
[2025-10-07 03:48:13] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -42.937029 | E_var:     0.4778 | E_err:   0.010801
[2025-10-07 03:48:21] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -42.936740 | E_var:     0.3788 | E_err:   0.009616
[2025-10-07 03:48:29] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -42.960083 | E_var:     0.4374 | E_err:   0.010334
[2025-10-07 03:48:37] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -42.941500 | E_var:     0.3470 | E_err:   0.009205
[2025-10-07 03:48:45] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -42.954464 | E_var:     0.4000 | E_err:   0.009882
[2025-10-07 03:48:52] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -42.941731 | E_var:     0.3577 | E_err:   0.009345
[2025-10-07 03:49:00] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -42.937964 | E_var:     0.4374 | E_err:   0.010334
[2025-10-07 03:49:08] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -42.941303 | E_var:     0.3303 | E_err:   0.008980
[2025-10-07 03:49:16] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -42.941983 | E_var:     0.5061 | E_err:   0.011116
[2025-10-07 03:49:24] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -42.946628 | E_var:     0.3369 | E_err:   0.009070
[2025-10-07 03:49:32] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -42.955207 | E_var:     0.4132 | E_err:   0.010043
[2025-10-07 03:49:39] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -42.942517 | E_var:     0.3586 | E_err:   0.009357
[2025-10-07 03:49:47] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -42.956682 | E_var:     0.3730 | E_err:   0.009542
[2025-10-07 03:49:55] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -42.940582 | E_var:     0.3201 | E_err:   0.008840
[2025-10-07 03:50:03] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -42.943081 | E_var:     0.3801 | E_err:   0.009634
[2025-10-07 03:50:11] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -42.959633 | E_var:     0.3217 | E_err:   0.008862
[2025-10-07 03:50:18] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -42.950476 | E_var:     0.4100 | E_err:   0.010005
[2025-10-07 03:50:26] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -42.948624 | E_var:     0.3579 | E_err:   0.009347
[2025-10-07 03:50:34] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -42.927258 | E_var:     0.2958 | E_err:   0.008499
[2025-10-07 03:50:42] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -42.932841 | E_var:     0.5180 | E_err:   0.011246
[2025-10-07 03:50:50] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -42.947631 | E_var:     0.3981 | E_err:   0.009858
[2025-10-07 03:50:58] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -42.939894 | E_var:     0.3618 | E_err:   0.009398
[2025-10-07 03:51:05] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -42.941136 | E_var:     0.3795 | E_err:   0.009626
[2025-10-07 03:51:13] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -42.953142 | E_var:     0.4093 | E_err:   0.009997
[2025-10-07 03:51:21] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -42.950389 | E_var:     0.4071 | E_err:   0.009970
[2025-10-07 03:51:29] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -42.950678 | E_var:     0.3361 | E_err:   0.009058
[2025-10-07 03:51:37] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -42.940821 | E_var:     0.4123 | E_err:   0.010033
[2025-10-07 03:51:45] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -42.955258 | E_var:     0.4128 | E_err:   0.010039
[2025-10-07 03:51:52] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -42.946992 | E_var:     0.3412 | E_err:   0.009127
[2025-10-07 03:52:00] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -42.938150 | E_var:     0.3334 | E_err:   0.009022
[2025-10-07 03:52:08] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -42.945880 | E_var:     0.3645 | E_err:   0.009434
[2025-10-07 03:52:16] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -42.953655 | E_var:     0.3321 | E_err:   0.009005
[2025-10-07 03:52:24] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -42.942016 | E_var:     0.2774 | E_err:   0.008230
[2025-10-07 03:52:31] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -42.944595 | E_var:     0.3165 | E_err:   0.008791
[2025-10-07 03:52:39] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -42.973093 | E_var:     0.8958 | E_err:   0.014789
[2025-10-07 03:52:47] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -42.950904 | E_var:     0.4153 | E_err:   0.010069
[2025-10-07 03:52:55] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -42.946071 | E_var:     0.3353 | E_err:   0.009047
[2025-10-07 03:53:03] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -42.946029 | E_var:     0.4231 | E_err:   0.010163
[2025-10-07 03:53:11] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -42.940005 | E_var:     0.2908 | E_err:   0.008426
[2025-10-07 03:53:18] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -42.932537 | E_var:     0.4163 | E_err:   0.010081
[2025-10-07 03:53:26] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -42.946285 | E_var:     0.4763 | E_err:   0.010784
[2025-10-07 03:53:34] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -42.941822 | E_var:     0.4216 | E_err:   0.010145
[2025-10-07 03:53:42] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -42.948283 | E_var:     0.3683 | E_err:   0.009483
[2025-10-07 03:53:50] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -42.958769 | E_var:     0.3417 | E_err:   0.009133
[2025-10-07 03:53:57] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -42.936279 | E_var:     0.3401 | E_err:   0.009112
[2025-10-07 03:54:05] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -42.932952 | E_var:     0.4023 | E_err:   0.009911
[2025-10-07 03:54:13] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -42.942522 | E_var:     0.3445 | E_err:   0.009171
[2025-10-07 03:54:21] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -42.964539 | E_var:     0.3408 | E_err:   0.009122
[2025-10-07 03:54:29] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -42.930862 | E_var:     0.3435 | E_err:   0.009158
[2025-10-07 03:54:37] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -42.933530 | E_var:     0.3301 | E_err:   0.008977
[2025-10-07 03:54:44] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -42.939434 | E_var:     0.2917 | E_err:   0.008439
[2025-10-07 03:54:52] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -42.953209 | E_var:     0.4215 | E_err:   0.010144
[2025-10-07 03:55:00] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -42.954548 | E_var:     0.3488 | E_err:   0.009228
[2025-10-07 03:55:08] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -42.926465 | E_var:     0.3923 | E_err:   0.009786
[2025-10-07 03:55:16] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -42.930043 | E_var:     0.3105 | E_err:   0.008707
[2025-10-07 03:55:24] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -42.960221 | E_var:     0.3459 | E_err:   0.009190
[2025-10-07 03:55:31] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -42.952123 | E_var:     0.3657 | E_err:   0.009449
[2025-10-07 03:55:39] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -42.944549 | E_var:     0.3827 | E_err:   0.009665
[2025-10-07 03:55:47] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -42.958762 | E_var:     0.4194 | E_err:   0.010119
[2025-10-07 03:55:55] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -42.958838 | E_var:     0.3571 | E_err:   0.009337
[2025-10-07 03:56:03] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -42.936462 | E_var:     0.5161 | E_err:   0.011225
[2025-10-07 03:56:11] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -42.939822 | E_var:     0.3458 | E_err:   0.009188
[2025-10-07 03:56:18] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -42.942831 | E_var:     0.3175 | E_err:   0.008804
[2025-10-07 03:56:26] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -42.959754 | E_var:     0.3409 | E_err:   0.009123
[2025-10-07 03:56:34] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -42.951575 | E_var:     0.3602 | E_err:   0.009378
[2025-10-07 03:56:42] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -42.957646 | E_var:     0.3491 | E_err:   0.009232
[2025-10-07 03:56:50] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -42.935984 | E_var:     0.3929 | E_err:   0.009794
[2025-10-07 03:56:57] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -42.950032 | E_var:     0.3845 | E_err:   0.009688
[2025-10-07 03:57:05] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -42.944995 | E_var:     0.4390 | E_err:   0.010353
[2025-10-07 03:57:13] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -42.959335 | E_var:     0.4034 | E_err:   0.009924
[2025-10-07 03:57:21] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -42.926000 | E_var:     0.3831 | E_err:   0.009671
[2025-10-07 03:57:29] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -42.943117 | E_var:     0.3827 | E_err:   0.009667
[2025-10-07 03:57:37] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -42.963388 | E_var:     0.4039 | E_err:   0.009930
[2025-10-07 03:57:44] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -42.940138 | E_var:     0.3413 | E_err:   0.009129
[2025-10-07 03:57:52] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -42.945175 | E_var:     0.3740 | E_err:   0.009555
[2025-10-07 03:58:00] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -42.950655 | E_var:     0.5096 | E_err:   0.011154
[2025-10-07 03:58:08] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -42.949588 | E_var:     0.2920 | E_err:   0.008443
[2025-10-07 03:58:16] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -42.938692 | E_var:     0.3740 | E_err:   0.009555
[2025-10-07 03:58:23] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -42.944446 | E_var:     0.3394 | E_err:   0.009103
[2025-10-07 03:58:31] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -42.945888 | E_var:     0.3521 | E_err:   0.009272
[2025-10-07 03:58:39] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -42.938346 | E_var:     0.3848 | E_err:   0.009693
[2025-10-07 03:58:47] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -42.939542 | E_var:     0.3694 | E_err:   0.009497
[2025-10-07 03:58:55] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -42.937152 | E_var:     0.3935 | E_err:   0.009802
[2025-10-07 03:59:03] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -42.931432 | E_var:     0.3266 | E_err:   0.008930
[2025-10-07 03:59:10] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -42.958294 | E_var:     0.5258 | E_err:   0.011331
[2025-10-07 03:59:18] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -42.948608 | E_var:     0.3638 | E_err:   0.009425
[2025-10-07 03:59:26] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -42.943181 | E_var:     0.4745 | E_err:   0.010763
[2025-10-07 03:59:34] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -42.957043 | E_var:     0.3728 | E_err:   0.009540
[2025-10-07 03:59:42] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -42.958884 | E_var:     0.3009 | E_err:   0.008571
[2025-10-07 03:59:50] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -42.944263 | E_var:     0.3556 | E_err:   0.009318
[2025-10-07 03:59:57] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -42.954935 | E_var:     0.3569 | E_err:   0.009334
[2025-10-07 04:00:05] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -42.929631 | E_var:     0.3089 | E_err:   0.008684
[2025-10-07 04:00:13] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -42.943643 | E_var:     0.4375 | E_err:   0.010335
[2025-10-07 04:00:21] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -42.927859 | E_var:     0.3568 | E_err:   0.009333
[2025-10-07 04:00:21] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 04:00:29] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -42.961251 | E_var:     0.3955 | E_err:   0.009826
[2025-10-07 04:00:36] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -42.953648 | E_var:     0.3520 | E_err:   0.009270
[2025-10-07 04:00:44] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -42.950759 | E_var:     0.3984 | E_err:   0.009862
[2025-10-07 04:00:52] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -42.958023 | E_var:     0.3883 | E_err:   0.009736
[2025-10-07 04:01:00] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -42.948814 | E_var:     0.3815 | E_err:   0.009651
[2025-10-07 04:01:08] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -42.931071 | E_var:     0.4216 | E_err:   0.010146
[2025-10-07 04:01:16] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -42.940474 | E_var:     0.3661 | E_err:   0.009454
[2025-10-07 04:01:23] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -42.955281 | E_var:     0.5769 | E_err:   0.011868
[2025-10-07 04:01:31] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -42.945467 | E_var:     0.3126 | E_err:   0.008735
[2025-10-07 04:01:39] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -42.917861 | E_var:     0.4021 | E_err:   0.009909
[2025-10-07 04:01:47] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -42.935573 | E_var:     0.3856 | E_err:   0.009703
[2025-10-07 04:01:55] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -42.958330 | E_var:     0.3934 | E_err:   0.009800
[2025-10-07 04:02:03] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -42.944561 | E_var:     0.3688 | E_err:   0.009489
[2025-10-07 04:02:10] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -42.959192 | E_var:     0.4476 | E_err:   0.010453
[2025-10-07 04:02:18] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -42.931394 | E_var:     0.3169 | E_err:   0.008796
[2025-10-07 04:02:26] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -42.964318 | E_var:     0.4073 | E_err:   0.009972
[2025-10-07 04:02:34] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -42.955405 | E_var:     0.3412 | E_err:   0.009126
[2025-10-07 04:02:42] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -42.946354 | E_var:     0.3863 | E_err:   0.009711
[2025-10-07 04:02:49] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -42.968089 | E_var:     0.3322 | E_err:   0.009006
[2025-10-07 04:02:57] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -42.944649 | E_var:     0.3898 | E_err:   0.009755
[2025-10-07 04:03:05] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -42.951643 | E_var:     0.4121 | E_err:   0.010030
[2025-10-07 04:03:13] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -42.954682 | E_var:     0.3480 | E_err:   0.009218
[2025-10-07 04:03:21] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -42.968534 | E_var:     0.3490 | E_err:   0.009230
[2025-10-07 04:03:29] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -42.951887 | E_var:     0.3607 | E_err:   0.009384
[2025-10-07 04:03:36] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -42.952298 | E_var:     0.3208 | E_err:   0.008850
[2025-10-07 04:03:44] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -42.947998 | E_var:     0.3967 | E_err:   0.009842
[2025-10-07 04:03:52] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -42.953476 | E_var:     0.3840 | E_err:   0.009682
[2025-10-07 04:04:00] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -42.940610 | E_var:     0.3695 | E_err:   0.009498
[2025-10-07 04:04:08] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -42.941137 | E_var:     0.3539 | E_err:   0.009295
[2025-10-07 04:04:15] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -42.956541 | E_var:     0.3144 | E_err:   0.008762
[2025-10-07 04:04:23] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -42.944234 | E_var:     0.3596 | E_err:   0.009370
[2025-10-07 04:04:31] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -42.939350 | E_var:     0.3284 | E_err:   0.008953
[2025-10-07 04:04:39] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -42.935800 | E_var:     0.3075 | E_err:   0.008664
[2025-10-07 04:04:47] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -42.952812 | E_var:     0.4446 | E_err:   0.010419
[2025-10-07 04:04:55] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -42.952600 | E_var:     0.4477 | E_err:   0.010455
[2025-10-07 04:05:02] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -42.942836 | E_var:     0.3126 | E_err:   0.008736
[2025-10-07 04:05:10] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -42.948831 | E_var:     0.4298 | E_err:   0.010244
[2025-10-07 04:05:18] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -42.957481 | E_var:     0.3246 | E_err:   0.008903
[2025-10-07 04:05:26] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -42.934924 | E_var:     0.3438 | E_err:   0.009162
[2025-10-07 04:05:34] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -42.947898 | E_var:     0.3517 | E_err:   0.009267
[2025-10-07 04:05:41] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -42.946668 | E_var:     0.3654 | E_err:   0.009445
[2025-10-07 04:05:49] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -42.927282 | E_var:     0.3573 | E_err:   0.009340
[2025-10-07 04:05:57] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -42.936619 | E_var:     0.4815 | E_err:   0.010842
[2025-10-07 04:06:05] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -42.948804 | E_var:     0.3730 | E_err:   0.009543
[2025-10-07 04:06:13] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -42.962960 | E_var:     0.3221 | E_err:   0.008868
[2025-10-07 04:06:21] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -42.956897 | E_var:     0.2935 | E_err:   0.008465
[2025-10-07 04:06:28] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -42.947265 | E_var:     0.3951 | E_err:   0.009821
[2025-10-07 04:06:36] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -42.956581 | E_var:     0.4169 | E_err:   0.010088
[2025-10-07 04:06:44] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -42.958351 | E_var:     0.3056 | E_err:   0.008637
[2025-10-07 04:06:52] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -42.952380 | E_var:     0.4027 | E_err:   0.009916
[2025-10-07 04:06:52] 🔄 RESTART #2 | Period: 600
[2025-10-07 04:07:00] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -42.940274 | E_var:     0.3178 | E_err:   0.008808
[2025-10-07 04:07:07] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -42.944553 | E_var:     0.5087 | E_err:   0.011145
[2025-10-07 04:07:15] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -42.949755 | E_var:     0.3310 | E_err:   0.008989
[2025-10-07 04:07:23] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -42.959905 | E_var:     0.4002 | E_err:   0.009885
[2025-10-07 04:07:31] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -42.958578 | E_var:     0.3585 | E_err:   0.009356
[2025-10-07 04:07:39] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -42.945989 | E_var:     0.3487 | E_err:   0.009227
[2025-10-07 04:07:47] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -42.943811 | E_var:     0.3633 | E_err:   0.009417
[2025-10-07 04:07:54] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -42.939047 | E_var:     0.3222 | E_err:   0.008869
[2025-10-07 04:08:02] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -42.931234 | E_var:     0.6664 | E_err:   0.012755
[2025-10-07 04:08:10] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -42.949961 | E_var:     0.3543 | E_err:   0.009300
[2025-10-07 04:08:18] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -42.953154 | E_var:     0.3348 | E_err:   0.009041
[2025-10-07 04:08:26] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -42.964461 | E_var:     0.6051 | E_err:   0.012154
[2025-10-07 04:08:33] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -42.963482 | E_var:     0.3834 | E_err:   0.009675
[2025-10-07 04:08:41] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -42.939307 | E_var:     0.5212 | E_err:   0.011281
[2025-10-07 04:08:49] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -42.957692 | E_var:     0.3254 | E_err:   0.008913
[2025-10-07 04:08:57] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -42.931705 | E_var:     0.3483 | E_err:   0.009221
[2025-10-07 04:09:05] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -42.940471 | E_var:     0.3575 | E_err:   0.009342
[2025-10-07 04:09:13] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -42.961558 | E_var:     0.3885 | E_err:   0.009740
[2025-10-07 04:09:20] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -42.933461 | E_var:     0.3938 | E_err:   0.009805
[2025-10-07 04:09:28] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -42.956308 | E_var:     0.3453 | E_err:   0.009182
[2025-10-07 04:09:36] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -42.966499 | E_var:     0.3342 | E_err:   0.009032
[2025-10-07 04:09:44] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -42.959363 | E_var:     0.3554 | E_err:   0.009316
[2025-10-07 04:09:52] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -42.971293 | E_var:     0.3531 | E_err:   0.009285
[2025-10-07 04:09:59] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -42.955970 | E_var:     0.2840 | E_err:   0.008327
[2025-10-07 04:10:07] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -42.952722 | E_var:     0.3558 | E_err:   0.009321
[2025-10-07 04:10:15] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -42.960206 | E_var:     0.4100 | E_err:   0.010005
[2025-10-07 04:10:23] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -42.942685 | E_var:     0.3675 | E_err:   0.009472
[2025-10-07 04:10:31] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -42.960722 | E_var:     0.3686 | E_err:   0.009486
[2025-10-07 04:10:39] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -42.941142 | E_var:     0.4171 | E_err:   0.010092
[2025-10-07 04:10:46] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -42.958068 | E_var:     0.3837 | E_err:   0.009678
[2025-10-07 04:10:54] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -42.955277 | E_var:     0.3580 | E_err:   0.009349
[2025-10-07 04:11:02] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -42.941919 | E_var:     0.4375 | E_err:   0.010335
[2025-10-07 04:11:10] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -42.947193 | E_var:     0.3917 | E_err:   0.009779
[2025-10-07 04:11:18] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -42.953719 | E_var:     0.3909 | E_err:   0.009769
[2025-10-07 04:11:25] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -42.931948 | E_var:     0.4314 | E_err:   0.010263
[2025-10-07 04:11:33] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -42.948016 | E_var:     0.3902 | E_err:   0.009760
[2025-10-07 04:11:41] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -42.938118 | E_var:     0.3659 | E_err:   0.009452
[2025-10-07 04:11:49] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -42.928948 | E_var:     0.6682 | E_err:   0.012773
[2025-10-07 04:11:57] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -42.939416 | E_var:     0.3319 | E_err:   0.009002
[2025-10-07 04:12:05] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -42.947193 | E_var:     0.2918 | E_err:   0.008441
[2025-10-07 04:12:12] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -42.925579 | E_var:     1.3111 | E_err:   0.017891
[2025-10-07 04:12:20] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -42.934749 | E_var:     0.6014 | E_err:   0.012118
[2025-10-07 04:12:28] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -42.941894 | E_var:     0.3209 | E_err:   0.008851
[2025-10-07 04:12:36] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -42.952284 | E_var:     0.3338 | E_err:   0.009027
[2025-10-07 04:12:44] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -42.935648 | E_var:     0.3556 | E_err:   0.009318
[2025-10-07 04:12:51] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -42.933881 | E_var:     0.3515 | E_err:   0.009264
[2025-10-07 04:12:59] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -42.955274 | E_var:     0.3425 | E_err:   0.009144
[2025-10-07 04:13:07] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -42.943148 | E_var:     0.3367 | E_err:   0.009067
[2025-10-07 04:13:15] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -42.929993 | E_var:     0.5087 | E_err:   0.011145
[2025-10-07 04:13:23] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -42.949120 | E_var:     0.3349 | E_err:   0.009043
[2025-10-07 04:13:23] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 04:13:31] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -42.946094 | E_var:     0.3640 | E_err:   0.009427
[2025-10-07 04:13:39] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -42.933588 | E_var:     0.4048 | E_err:   0.009941
[2025-10-07 04:13:47] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -42.955484 | E_var:     0.2960 | E_err:   0.008501
[2025-10-07 04:13:54] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -42.943054 | E_var:     0.4685 | E_err:   0.010694
[2025-10-07 04:14:02] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -42.963303 | E_var:     0.4327 | E_err:   0.010278
[2025-10-07 04:14:10] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -42.937504 | E_var:     0.3431 | E_err:   0.009152
[2025-10-07 04:14:18] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -42.953461 | E_var:     0.3769 | E_err:   0.009592
[2025-10-07 04:14:26] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -42.931506 | E_var:     0.3818 | E_err:   0.009655
[2025-10-07 04:14:34] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -42.953164 | E_var:     0.3604 | E_err:   0.009380
[2025-10-07 04:14:41] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -42.955305 | E_var:     0.3524 | E_err:   0.009275
[2025-10-07 04:14:50] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -42.946415 | E_var:     0.3893 | E_err:   0.009748
[2025-10-07 04:14:57] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -42.946861 | E_var:     0.3377 | E_err:   0.009079
[2025-10-07 04:15:05] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -42.942406 | E_var:     0.3609 | E_err:   0.009386
[2025-10-07 04:15:13] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -42.958942 | E_var:     0.4505 | E_err:   0.010488
[2025-10-07 04:15:21] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -42.946257 | E_var:     0.3102 | E_err:   0.008703
[2025-10-07 04:15:29] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -42.950552 | E_var:     0.4116 | E_err:   0.010025
[2025-10-07 04:15:37] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -42.952453 | E_var:     0.3002 | E_err:   0.008561
[2025-10-07 04:15:44] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -42.958020 | E_var:     0.4242 | E_err:   0.010176
[2025-10-07 04:15:52] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -42.949208 | E_var:     0.4064 | E_err:   0.009961
[2025-10-07 04:16:00] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -42.941017 | E_var:     0.3203 | E_err:   0.008843
[2025-10-07 04:16:08] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -42.963730 | E_var:     0.3287 | E_err:   0.008958
[2025-10-07 04:16:16] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -42.935714 | E_var:     0.5636 | E_err:   0.011730
[2025-10-07 04:16:24] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -42.937380 | E_var:     0.3800 | E_err:   0.009632
[2025-10-07 04:16:31] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -42.953221 | E_var:     0.3360 | E_err:   0.009058
[2025-10-07 04:16:39] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -42.949166 | E_var:     0.3207 | E_err:   0.008848
[2025-10-07 04:16:47] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -42.931219 | E_var:     0.4537 | E_err:   0.010525
[2025-10-07 04:16:55] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -42.942316 | E_var:     0.2807 | E_err:   0.008278
[2025-10-07 04:17:03] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -42.935071 | E_var:     0.4648 | E_err:   0.010652
[2025-10-07 04:17:11] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -42.959936 | E_var:     0.3008 | E_err:   0.008570
[2025-10-07 04:17:18] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -42.954730 | E_var:     0.4763 | E_err:   0.010783
[2025-10-07 04:17:26] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -42.946400 | E_var:     0.4226 | E_err:   0.010157
[2025-10-07 04:17:34] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -42.941674 | E_var:     0.4248 | E_err:   0.010184
[2025-10-07 04:17:42] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -42.957281 | E_var:     0.3160 | E_err:   0.008784
[2025-10-07 04:17:50] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -42.932411 | E_var:     0.5054 | E_err:   0.011108
[2025-10-07 04:17:57] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -42.933383 | E_var:     0.3945 | E_err:   0.009814
[2025-10-07 04:18:05] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -42.949700 | E_var:     0.3198 | E_err:   0.008837
[2025-10-07 04:18:13] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -42.948720 | E_var:     0.4092 | E_err:   0.009995
[2025-10-07 04:18:21] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -42.939572 | E_var:     0.3865 | E_err:   0.009714
[2025-10-07 04:18:29] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -42.942507 | E_var:     0.3799 | E_err:   0.009631
[2025-10-07 04:18:37] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -42.949790 | E_var:     0.4009 | E_err:   0.009893
[2025-10-07 04:18:44] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -42.960868 | E_var:     0.4382 | E_err:   0.010344
[2025-10-07 04:18:52] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -42.951893 | E_var:     0.3200 | E_err:   0.008839
[2025-10-07 04:19:00] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -42.942835 | E_var:     0.3349 | E_err:   0.009043
[2025-10-07 04:19:08] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -42.949245 | E_var:     0.3931 | E_err:   0.009796
[2025-10-07 04:19:16] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -42.953688 | E_var:     0.3457 | E_err:   0.009186
[2025-10-07 04:19:23] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -42.970284 | E_var:     0.4319 | E_err:   0.010269
[2025-10-07 04:19:31] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -42.946793 | E_var:     0.2992 | E_err:   0.008547
[2025-10-07 04:19:39] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -42.963923 | E_var:     0.2966 | E_err:   0.008510
[2025-10-07 04:19:47] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -42.952308 | E_var:     0.3749 | E_err:   0.009567
[2025-10-07 04:19:55] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -42.951390 | E_var:     0.5007 | E_err:   0.011056
[2025-10-07 04:20:03] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -42.948517 | E_var:     0.3538 | E_err:   0.009294
[2025-10-07 04:20:10] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -42.936019 | E_var:     0.4061 | E_err:   0.009957
[2025-10-07 04:20:18] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -42.962507 | E_var:     0.3794 | E_err:   0.009625
[2025-10-07 04:20:26] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -42.938842 | E_var:     0.3615 | E_err:   0.009394
[2025-10-07 04:20:34] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -42.952242 | E_var:     0.4073 | E_err:   0.009971
[2025-10-07 04:20:42] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -42.935892 | E_var:     0.3366 | E_err:   0.009066
[2025-10-07 04:20:49] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -42.929684 | E_var:     0.3600 | E_err:   0.009374
[2025-10-07 04:20:57] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -42.946667 | E_var:     0.4707 | E_err:   0.010720
[2025-10-07 04:21:05] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -42.932847 | E_var:     0.3524 | E_err:   0.009276
[2025-10-07 04:21:13] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -42.933960 | E_var:     0.3231 | E_err:   0.008882
[2025-10-07 04:21:21] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -42.939858 | E_var:     0.3179 | E_err:   0.008809
[2025-10-07 04:21:29] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -42.950959 | E_var:     0.3279 | E_err:   0.008947
[2025-10-07 04:21:36] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -42.942085 | E_var:     0.3133 | E_err:   0.008745
[2025-10-07 04:21:44] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -42.926444 | E_var:     0.3460 | E_err:   0.009191
[2025-10-07 04:21:52] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -42.948174 | E_var:     0.4367 | E_err:   0.010325
[2025-10-07 04:22:00] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -42.936212 | E_var:     0.9453 | E_err:   0.015192
[2025-10-07 04:22:08] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -42.936651 | E_var:     0.3244 | E_err:   0.008899
[2025-10-07 04:22:16] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -42.916124 | E_var:     0.3937 | E_err:   0.009804
[2025-10-07 04:22:23] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -42.955819 | E_var:     0.3757 | E_err:   0.009577
[2025-10-07 04:22:31] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -42.948321 | E_var:     0.3265 | E_err:   0.008929
[2025-10-07 04:22:39] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -42.942634 | E_var:     0.3431 | E_err:   0.009153
[2025-10-07 04:22:47] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -42.958942 | E_var:     0.3497 | E_err:   0.009240
[2025-10-07 04:22:55] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -42.948407 | E_var:     0.3275 | E_err:   0.008942
[2025-10-07 04:23:02] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -42.966414 | E_var:     0.6919 | E_err:   0.012997
[2025-10-07 04:23:10] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -42.956842 | E_var:     0.3981 | E_err:   0.009858
[2025-10-07 04:23:18] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -42.944712 | E_var:     0.3892 | E_err:   0.009748
[2025-10-07 04:23:26] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -42.957236 | E_var:     0.3080 | E_err:   0.008671
[2025-10-07 04:23:34] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -42.947707 | E_var:     0.4401 | E_err:   0.010366
[2025-10-07 04:23:42] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -42.946546 | E_var:     0.4192 | E_err:   0.010116
[2025-10-07 04:23:49] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -42.952973 | E_var:     0.5609 | E_err:   0.011702
[2025-10-07 04:23:57] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -42.963752 | E_var:     0.3246 | E_err:   0.008902
[2025-10-07 04:24:05] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -42.939314 | E_var:     0.3213 | E_err:   0.008856
[2025-10-07 04:24:13] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -42.935429 | E_var:     0.3410 | E_err:   0.009124
[2025-10-07 04:24:21] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -42.941204 | E_var:     0.2943 | E_err:   0.008477
[2025-10-07 04:24:28] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -42.932821 | E_var:     0.3170 | E_err:   0.008797
[2025-10-07 04:24:36] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -42.941447 | E_var:     0.9857 | E_err:   0.015513
[2025-10-07 04:24:44] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -42.957035 | E_var:     0.3670 | E_err:   0.009465
[2025-10-07 04:24:52] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -42.949562 | E_var:     0.4025 | E_err:   0.009913
[2025-10-07 04:25:00] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -42.954713 | E_var:     0.3395 | E_err:   0.009105
[2025-10-07 04:25:08] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -42.943971 | E_var:     0.3517 | E_err:   0.009266
[2025-10-07 04:25:15] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -42.952973 | E_var:     0.3576 | E_err:   0.009344
[2025-10-07 04:25:23] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -42.944689 | E_var:     0.3047 | E_err:   0.008624
[2025-10-07 04:25:31] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -42.939686 | E_var:     0.4292 | E_err:   0.010236
[2025-10-07 04:25:39] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -42.951467 | E_var:     0.3565 | E_err:   0.009329
[2025-10-07 04:25:47] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -42.951217 | E_var:     0.4362 | E_err:   0.010320
[2025-10-07 04:25:54] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -42.954068 | E_var:     0.3288 | E_err:   0.008959
[2025-10-07 04:26:02] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -42.946466 | E_var:     0.3987 | E_err:   0.009865
[2025-10-07 04:26:10] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -42.947197 | E_var:     0.4961 | E_err:   0.011005
[2025-10-07 04:26:18] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -42.956219 | E_var:     0.3659 | E_err:   0.009451
[2025-10-07 04:26:26] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -42.936904 | E_var:     0.3369 | E_err:   0.009069
[2025-10-07 04:26:26] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 04:26:34] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -42.932877 | E_var:     0.3025 | E_err:   0.008594
[2025-10-07 04:26:41] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -42.952552 | E_var:     0.4019 | E_err:   0.009906
[2025-10-07 04:26:49] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -42.939062 | E_var:     0.3340 | E_err:   0.009031
[2025-10-07 04:26:57] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -42.957685 | E_var:     0.3574 | E_err:   0.009342
[2025-10-07 04:27:05] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -42.937784 | E_var:     0.4165 | E_err:   0.010084
[2025-10-07 04:27:13] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -42.945186 | E_var:     0.2738 | E_err:   0.008176
[2025-10-07 04:27:21] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -42.938350 | E_var:     0.4060 | E_err:   0.009955
[2025-10-07 04:27:28] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -42.947314 | E_var:     0.4047 | E_err:   0.009941
[2025-10-07 04:27:36] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -42.948926 | E_var:     0.3017 | E_err:   0.008583
[2025-10-07 04:27:44] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -42.956223 | E_var:     0.3977 | E_err:   0.009854
[2025-10-07 04:27:52] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -42.937911 | E_var:     0.4061 | E_err:   0.009957
[2025-10-07 04:28:00] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -42.968181 | E_var:     0.3247 | E_err:   0.008904
[2025-10-07 04:28:07] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -42.947542 | E_var:     0.7222 | E_err:   0.013278
[2025-10-07 04:28:15] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -42.952301 | E_var:     0.6129 | E_err:   0.012232
[2025-10-07 04:28:23] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -42.965288 | E_var:     0.4322 | E_err:   0.010272
[2025-10-07 04:28:31] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -42.940298 | E_var:     0.3490 | E_err:   0.009231
[2025-10-07 04:28:39] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -42.940640 | E_var:     0.3139 | E_err:   0.008754
[2025-10-07 04:28:47] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -42.939413 | E_var:     0.3120 | E_err:   0.008728
[2025-10-07 04:28:54] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -42.952805 | E_var:     0.3403 | E_err:   0.009115
[2025-10-07 04:29:02] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -42.954085 | E_var:     0.3052 | E_err:   0.008631
[2025-10-07 04:29:10] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -42.954365 | E_var:     0.2969 | E_err:   0.008514
[2025-10-07 04:29:18] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -42.933625 | E_var:     0.3824 | E_err:   0.009662
[2025-10-07 04:29:26] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -42.943336 | E_var:     0.3316 | E_err:   0.008997
[2025-10-07 04:29:33] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -42.977048 | E_var:     0.3595 | E_err:   0.009369
[2025-10-07 04:29:41] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -42.959500 | E_var:     0.2672 | E_err:   0.008076
[2025-10-07 04:29:49] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -42.950164 | E_var:     0.3962 | E_err:   0.009836
[2025-10-07 04:29:57] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -42.945806 | E_var:     0.3497 | E_err:   0.009240
[2025-10-07 04:30:05] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -42.947283 | E_var:     0.3229 | E_err:   0.008878
[2025-10-07 04:30:13] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -42.947925 | E_var:     0.3305 | E_err:   0.008983
[2025-10-07 04:30:20] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -42.941923 | E_var:     0.3320 | E_err:   0.009003
[2025-10-07 04:30:28] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -42.949004 | E_var:     0.4358 | E_err:   0.010315
[2025-10-07 04:30:36] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -42.956707 | E_var:     0.4391 | E_err:   0.010354
[2025-10-07 04:30:44] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -42.945237 | E_var:     0.5153 | E_err:   0.011216
[2025-10-07 04:30:52] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -42.942054 | E_var:     0.4266 | E_err:   0.010206
[2025-10-07 04:30:59] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -42.968376 | E_var:     0.5352 | E_err:   0.011431
[2025-10-07 04:31:07] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -42.965204 | E_var:     0.3700 | E_err:   0.009504
[2025-10-07 04:31:15] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -42.946877 | E_var:     0.3410 | E_err:   0.009124
[2025-10-07 04:31:23] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -42.955080 | E_var:     0.2910 | E_err:   0.008429
[2025-10-07 04:31:31] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -42.938182 | E_var:     0.3875 | E_err:   0.009727
[2025-10-07 04:31:39] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -42.953481 | E_var:     0.2870 | E_err:   0.008370
[2025-10-07 04:31:46] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -42.958630 | E_var:     0.2918 | E_err:   0.008440
[2025-10-07 04:31:54] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -42.929897 | E_var:     0.3446 | E_err:   0.009172
[2025-10-07 04:32:02] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -42.961043 | E_var:     0.3069 | E_err:   0.008656
[2025-10-07 04:32:10] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -42.952842 | E_var:     0.3181 | E_err:   0.008812
[2025-10-07 04:32:18] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -42.958160 | E_var:     0.4248 | E_err:   0.010184
[2025-10-07 04:32:25] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -42.953344 | E_var:     0.3351 | E_err:   0.009044
[2025-10-07 04:32:33] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -42.948270 | E_var:     0.3583 | E_err:   0.009353
[2025-10-07 04:32:41] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -42.947764 | E_var:     0.3286 | E_err:   0.008957
[2025-10-07 04:32:49] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -42.955180 | E_var:     0.3922 | E_err:   0.009785
[2025-10-07 04:32:57] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -42.937962 | E_var:     0.3733 | E_err:   0.009547
[2025-10-07 04:33:05] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -42.948465 | E_var:     0.3361 | E_err:   0.009059
[2025-10-07 04:33:12] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -42.960132 | E_var:     0.2972 | E_err:   0.008519
[2025-10-07 04:33:20] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -42.948901 | E_var:     0.4501 | E_err:   0.010483
[2025-10-07 04:33:28] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -42.943489 | E_var:     0.3673 | E_err:   0.009469
[2025-10-07 04:33:36] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -42.947867 | E_var:     0.2974 | E_err:   0.008521
[2025-10-07 04:33:44] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -42.951024 | E_var:     0.3328 | E_err:   0.009014
[2025-10-07 04:33:52] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -42.949397 | E_var:     0.4028 | E_err:   0.009917
[2025-10-07 04:33:59] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -42.938764 | E_var:     0.3025 | E_err:   0.008594
[2025-10-07 04:34:07] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -42.943453 | E_var:     0.3391 | E_err:   0.009099
[2025-10-07 04:34:15] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -42.957350 | E_var:     0.2909 | E_err:   0.008428
[2025-10-07 04:34:23] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -42.936022 | E_var:     0.3703 | E_err:   0.009508
[2025-10-07 04:34:31] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -42.958810 | E_var:     0.3832 | E_err:   0.009672
[2025-10-07 04:34:38] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -42.951076 | E_var:     0.3258 | E_err:   0.008919
[2025-10-07 04:34:46] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -42.967026 | E_var:     0.4770 | E_err:   0.010791
[2025-10-07 04:34:54] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -42.937359 | E_var:     0.3742 | E_err:   0.009558
[2025-10-07 04:35:02] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -42.947502 | E_var:     0.4314 | E_err:   0.010263
[2025-10-07 04:35:10] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -42.941839 | E_var:     0.3676 | E_err:   0.009474
[2025-10-07 04:35:18] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -42.957303 | E_var:     0.3341 | E_err:   0.009032
[2025-10-07 04:35:25] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -42.947833 | E_var:     0.3413 | E_err:   0.009128
[2025-10-07 04:35:33] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -42.963149 | E_var:     0.3511 | E_err:   0.009258
[2025-10-07 04:35:41] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -42.961453 | E_var:     0.3146 | E_err:   0.008764
[2025-10-07 04:35:49] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -42.937601 | E_var:     0.3767 | E_err:   0.009590
[2025-10-07 04:35:57] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -42.955941 | E_var:     0.2832 | E_err:   0.008315
[2025-10-07 04:36:04] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -42.948455 | E_var:     0.3400 | E_err:   0.009111
[2025-10-07 04:36:12] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -42.949087 | E_var:     0.3551 | E_err:   0.009310
[2025-10-07 04:36:20] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -42.941362 | E_var:     0.2803 | E_err:   0.008272
[2025-10-07 04:36:28] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -42.945005 | E_var:     0.3528 | E_err:   0.009281
[2025-10-07 04:36:36] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -42.945807 | E_var:     0.3125 | E_err:   0.008734
[2025-10-07 04:36:44] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -42.964092 | E_var:     0.3524 | E_err:   0.009276
[2025-10-07 04:36:51] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -42.957568 | E_var:     0.5434 | E_err:   0.011518
[2025-10-07 04:36:59] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -42.941297 | E_var:     0.5559 | E_err:   0.011650
[2025-10-07 04:37:07] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -42.946310 | E_var:     0.3312 | E_err:   0.008993
[2025-10-07 04:37:15] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -42.955081 | E_var:     0.4196 | E_err:   0.010121
[2025-10-07 04:37:23] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -42.944105 | E_var:     0.3697 | E_err:   0.009500
[2025-10-07 04:37:31] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -42.949424 | E_var:     0.3579 | E_err:   0.009347
[2025-10-07 04:37:38] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -42.940030 | E_var:     0.4312 | E_err:   0.010260
[2025-10-07 04:37:46] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -42.951413 | E_var:     0.3855 | E_err:   0.009701
[2025-10-07 04:37:54] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -42.952641 | E_var:     0.2964 | E_err:   0.008507
[2025-10-07 04:38:02] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -42.951868 | E_var:     0.3097 | E_err:   0.008695
[2025-10-07 04:38:10] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -42.949475 | E_var:     0.2837 | E_err:   0.008322
[2025-10-07 04:38:17] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -42.953738 | E_var:     0.3556 | E_err:   0.009318
[2025-10-07 04:38:25] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -42.945688 | E_var:     0.3326 | E_err:   0.009011
[2025-10-07 04:38:33] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -42.952967 | E_var:     0.3425 | E_err:   0.009144
[2025-10-07 04:38:41] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -42.956119 | E_var:     0.3208 | E_err:   0.008850
[2025-10-07 04:38:49] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -42.937545 | E_var:     0.4305 | E_err:   0.010252
[2025-10-07 04:38:57] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -42.954850 | E_var:     0.4237 | E_err:   0.010171
[2025-10-07 04:39:04] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -42.955863 | E_var:     0.4503 | E_err:   0.010486
[2025-10-07 04:39:12] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -42.940033 | E_var:     0.4231 | E_err:   0.010163
[2025-10-07 04:39:20] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -42.950277 | E_var:     0.3333 | E_err:   0.009021
[2025-10-07 04:39:28] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -42.962329 | E_var:     0.5441 | E_err:   0.011526
[2025-10-07 04:39:28] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 04:39:36] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -42.949698 | E_var:     0.6739 | E_err:   0.012827
[2025-10-07 04:39:43] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -42.971969 | E_var:     0.3380 | E_err:   0.009084
[2025-10-07 04:39:51] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -42.961847 | E_var:     0.3887 | E_err:   0.009742
[2025-10-07 04:39:59] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -42.946452 | E_var:     0.3565 | E_err:   0.009330
[2025-10-07 04:40:07] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -42.959639 | E_var:     0.4108 | E_err:   0.010014
[2025-10-07 04:40:15] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -42.942710 | E_var:     0.3548 | E_err:   0.009307
[2025-10-07 04:40:23] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -42.970216 | E_var:     0.3783 | E_err:   0.009611
[2025-10-07 04:40:30] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -42.932740 | E_var:     0.5047 | E_err:   0.011100
[2025-10-07 04:40:38] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -42.942777 | E_var:     0.5783 | E_err:   0.011882
[2025-10-07 04:40:46] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -42.949583 | E_var:     0.3387 | E_err:   0.009093
[2025-10-07 04:40:54] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -42.949669 | E_var:     0.2900 | E_err:   0.008415
[2025-10-07 04:41:02] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -42.949296 | E_var:     0.3180 | E_err:   0.008812
[2025-10-07 04:41:10] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -42.955586 | E_var:     0.3254 | E_err:   0.008913
[2025-10-07 04:41:17] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -42.944421 | E_var:     0.3307 | E_err:   0.008985
[2025-10-07 04:41:25] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -42.958843 | E_var:     0.3669 | E_err:   0.009465
[2025-10-07 04:41:33] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -42.932153 | E_var:     0.3818 | E_err:   0.009655
[2025-10-07 04:41:41] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -42.940481 | E_var:     0.4272 | E_err:   0.010212
[2025-10-07 04:41:49] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -42.946696 | E_var:     0.3054 | E_err:   0.008635
[2025-10-07 04:41:56] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -42.950499 | E_var:     0.2958 | E_err:   0.008497
[2025-10-07 04:42:04] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -42.942855 | E_var:     0.3151 | E_err:   0.008771
[2025-10-07 04:42:12] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -42.939127 | E_var:     0.3827 | E_err:   0.009666
[2025-10-07 04:42:20] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -42.958823 | E_var:     0.3446 | E_err:   0.009172
[2025-10-07 04:42:28] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -42.939233 | E_var:     0.3634 | E_err:   0.009419
[2025-10-07 04:42:36] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -42.963854 | E_var:     0.3197 | E_err:   0.008835
[2025-10-07 04:42:43] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -42.932002 | E_var:     0.3855 | E_err:   0.009701
[2025-10-07 04:42:51] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -42.940143 | E_var:     0.4470 | E_err:   0.010447
[2025-10-07 04:42:59] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -42.953230 | E_var:     0.3207 | E_err:   0.008848
[2025-10-07 04:43:07] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -42.942859 | E_var:     0.3376 | E_err:   0.009079
[2025-10-07 04:43:15] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -42.940627 | E_var:     0.2967 | E_err:   0.008512
[2025-10-07 04:43:22] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -42.944011 | E_var:     0.4219 | E_err:   0.010149
[2025-10-07 04:43:30] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -42.937684 | E_var:     0.3353 | E_err:   0.009048
[2025-10-07 04:43:38] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -42.937476 | E_var:     0.3286 | E_err:   0.008957
[2025-10-07 04:43:46] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -42.944195 | E_var:     0.4671 | E_err:   0.010679
[2025-10-07 04:43:54] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -42.941288 | E_var:     0.3432 | E_err:   0.009154
[2025-10-07 04:44:02] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -42.954503 | E_var:     0.3204 | E_err:   0.008845
[2025-10-07 04:44:09] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -42.951094 | E_var:     0.4163 | E_err:   0.010082
[2025-10-07 04:44:17] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -42.956646 | E_var:     0.3361 | E_err:   0.009059
[2025-10-07 04:44:25] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -42.937176 | E_var:     0.3133 | E_err:   0.008746
[2025-10-07 04:44:33] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -42.955879 | E_var:     0.3587 | E_err:   0.009359
[2025-10-07 04:44:41] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -42.955434 | E_var:     0.2882 | E_err:   0.008388
[2025-10-07 04:44:48] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -42.924760 | E_var:     0.3607 | E_err:   0.009384
[2025-10-07 04:44:56] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -42.940914 | E_var:     0.4226 | E_err:   0.010158
[2025-10-07 04:45:04] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -42.947675 | E_var:     0.3263 | E_err:   0.008925
[2025-10-07 04:45:12] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -42.958207 | E_var:     0.3643 | E_err:   0.009431
[2025-10-07 04:45:20] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -42.952753 | E_var:     0.3291 | E_err:   0.008963
[2025-10-07 04:45:28] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -42.965182 | E_var:     0.3908 | E_err:   0.009768
[2025-10-07 04:45:35] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -42.952703 | E_var:     0.3078 | E_err:   0.008669
[2025-10-07 04:45:43] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -42.964140 | E_var:     0.3261 | E_err:   0.008923
[2025-10-07 04:45:51] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -42.954869 | E_var:     0.3585 | E_err:   0.009355
[2025-10-07 04:45:59] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -42.922046 | E_var:     0.4536 | E_err:   0.010524
[2025-10-07 04:46:07] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -42.952887 | E_var:     0.3762 | E_err:   0.009584
[2025-10-07 04:46:14] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -42.956547 | E_var:     0.4497 | E_err:   0.010478
[2025-10-07 04:46:22] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -42.946781 | E_var:     0.3260 | E_err:   0.008921
[2025-10-07 04:46:30] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -42.948007 | E_var:     0.2998 | E_err:   0.008555
[2025-10-07 04:46:38] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -42.924469 | E_var:     0.5053 | E_err:   0.011107
[2025-10-07 04:46:46] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -42.962298 | E_var:     0.3193 | E_err:   0.008829
[2025-10-07 04:46:54] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -42.942135 | E_var:     0.3201 | E_err:   0.008840
[2025-10-07 04:47:01] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -42.951196 | E_var:     0.3514 | E_err:   0.009262
[2025-10-07 04:47:09] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -42.955871 | E_var:     0.3421 | E_err:   0.009139
[2025-10-07 04:47:17] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -42.956241 | E_var:     0.3893 | E_err:   0.009748
[2025-10-07 04:47:25] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -42.943480 | E_var:     0.3336 | E_err:   0.009025
[2025-10-07 04:47:33] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -42.955270 | E_var:     0.4022 | E_err:   0.009909
[2025-10-07 04:47:40] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -42.935843 | E_var:     0.4507 | E_err:   0.010490
[2025-10-07 04:47:48] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -42.944056 | E_var:     0.3762 | E_err:   0.009584
[2025-10-07 04:47:56] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -42.959407 | E_var:     0.3667 | E_err:   0.009462
[2025-10-07 04:48:04] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -42.955758 | E_var:     0.3512 | E_err:   0.009259
[2025-10-07 04:48:12] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -42.945180 | E_var:     0.3323 | E_err:   0.009008
[2025-10-07 04:48:20] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -42.950844 | E_var:     0.2757 | E_err:   0.008204
[2025-10-07 04:48:27] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -42.934196 | E_var:     0.4349 | E_err:   0.010304
[2025-10-07 04:48:35] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -42.943996 | E_var:     0.3404 | E_err:   0.009116
[2025-10-07 04:48:43] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -42.955808 | E_var:     0.2790 | E_err:   0.008253
[2025-10-07 04:48:51] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -42.946660 | E_var:     0.4407 | E_err:   0.010372
[2025-10-07 04:48:59] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -42.947352 | E_var:     0.2634 | E_err:   0.008020
[2025-10-07 04:49:06] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -42.966227 | E_var:     0.3340 | E_err:   0.009031
[2025-10-07 04:49:14] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -42.961377 | E_var:     0.9125 | E_err:   0.014926
[2025-10-07 04:49:22] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -42.954594 | E_var:     0.3350 | E_err:   0.009043
[2025-10-07 04:49:30] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -42.956033 | E_var:     0.3050 | E_err:   0.008629
[2025-10-07 04:49:38] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -42.944043 | E_var:     0.3009 | E_err:   0.008571
[2025-10-07 04:49:46] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -42.957454 | E_var:     0.3187 | E_err:   0.008821
[2025-10-07 04:49:53] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -42.941605 | E_var:     0.3287 | E_err:   0.008958
[2025-10-07 04:50:01] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -42.938554 | E_var:     0.3424 | E_err:   0.009142
[2025-10-07 04:50:09] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -42.952530 | E_var:     0.3094 | E_err:   0.008692
[2025-10-07 04:50:17] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -42.975223 | E_var:     1.3704 | E_err:   0.018291
[2025-10-07 04:50:25] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -42.946538 | E_var:     0.3860 | E_err:   0.009707
[2025-10-07 04:50:32] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -42.949224 | E_var:     0.3475 | E_err:   0.009211
[2025-10-07 04:50:40] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -42.952455 | E_var:     0.3519 | E_err:   0.009269
[2025-10-07 04:50:48] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -42.950508 | E_var:     0.3855 | E_err:   0.009701
[2025-10-07 04:50:56] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -42.951538 | E_var:     0.3793 | E_err:   0.009623
[2025-10-07 04:51:04] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -42.961911 | E_var:     0.3036 | E_err:   0.008609
[2025-10-07 04:51:12] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -42.949803 | E_var:     0.3157 | E_err:   0.008779
[2025-10-07 04:51:19] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -42.956429 | E_var:     0.3888 | E_err:   0.009743
[2025-10-07 04:51:27] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -42.947512 | E_var:     0.3921 | E_err:   0.009785
[2025-10-07 04:51:35] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -42.955153 | E_var:     0.3236 | E_err:   0.008888
[2025-10-07 04:51:43] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -42.961209 | E_var:     0.4207 | E_err:   0.010135
[2025-10-07 04:51:51] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -42.961771 | E_var:     0.3065 | E_err:   0.008651
[2025-10-07 04:51:59] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -42.946369 | E_var:     0.5213 | E_err:   0.011281
[2025-10-07 04:52:06] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -42.946252 | E_var:     0.3469 | E_err:   0.009203
[2025-10-07 04:52:14] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -42.956836 | E_var:     0.3119 | E_err:   0.008726
[2025-10-07 04:52:22] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -42.949391 | E_var:     0.4186 | E_err:   0.010109
[2025-10-07 04:52:30] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -42.955766 | E_var:     0.3180 | E_err:   0.008811
[2025-10-07 04:52:30] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 04:52:38] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -42.964698 | E_var:     0.3856 | E_err:   0.009702
[2025-10-07 04:52:45] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -42.937234 | E_var:     0.3286 | E_err:   0.008957
[2025-10-07 04:52:53] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -42.958816 | E_var:     0.4521 | E_err:   0.010506
[2025-10-07 04:53:01] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -42.954517 | E_var:     0.3428 | E_err:   0.009148
[2025-10-07 04:53:09] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -42.964256 | E_var:     0.2782 | E_err:   0.008241
[2025-10-07 04:53:17] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -42.948432 | E_var:     0.4168 | E_err:   0.010087
[2025-10-07 04:53:25] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -42.937520 | E_var:     0.3026 | E_err:   0.008595
[2025-10-07 04:53:32] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -42.965029 | E_var:     0.3030 | E_err:   0.008601
[2025-10-07 04:53:40] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -42.953427 | E_var:     0.3411 | E_err:   0.009126
[2025-10-07 04:53:48] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -42.935661 | E_var:     0.4419 | E_err:   0.010387
[2025-10-07 04:53:56] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -42.956845 | E_var:     0.3144 | E_err:   0.008761
[2025-10-07 04:54:04] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -42.944173 | E_var:     0.4267 | E_err:   0.010207
[2025-10-07 04:54:12] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -42.949474 | E_var:     0.3781 | E_err:   0.009607
[2025-10-07 04:54:19] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -42.944296 | E_var:     0.3470 | E_err:   0.009205
[2025-10-07 04:54:27] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -42.941753 | E_var:     0.3362 | E_err:   0.009059
[2025-10-07 04:54:35] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -42.944796 | E_var:     0.3067 | E_err:   0.008653
[2025-10-07 04:54:43] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -42.958065 | E_var:     0.2821 | E_err:   0.008300
[2025-10-07 04:54:51] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -42.952928 | E_var:     0.3733 | E_err:   0.009547
[2025-10-07 04:54:58] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -42.956258 | E_var:     0.3422 | E_err:   0.009141
[2025-10-07 04:55:06] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -42.946438 | E_var:     0.3388 | E_err:   0.009095
[2025-10-07 04:55:14] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -42.963316 | E_var:     0.3796 | E_err:   0.009626
[2025-10-07 04:55:22] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -42.963531 | E_var:     0.3675 | E_err:   0.009473
[2025-10-07 04:55:30] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -42.941448 | E_var:     0.4857 | E_err:   0.010889
[2025-10-07 04:55:38] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -42.939373 | E_var:     0.4052 | E_err:   0.009947
[2025-10-07 04:55:45] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -42.955354 | E_var:     0.3817 | E_err:   0.009654
[2025-10-07 04:55:53] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -42.953607 | E_var:     0.3685 | E_err:   0.009485
[2025-10-07 04:56:01] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -42.953410 | E_var:     0.3678 | E_err:   0.009476
[2025-10-07 04:56:09] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -42.934610 | E_var:     0.3542 | E_err:   0.009299
[2025-10-07 04:56:17] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -42.957353 | E_var:     0.3597 | E_err:   0.009371
[2025-10-07 04:56:25] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -42.953162 | E_var:     0.3365 | E_err:   0.009064
[2025-10-07 04:56:32] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -42.932130 | E_var:     0.3589 | E_err:   0.009360
[2025-10-07 04:56:40] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -42.959240 | E_var:     0.3361 | E_err:   0.009059
[2025-10-07 04:56:48] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -42.952385 | E_var:     0.5444 | E_err:   0.011529
[2025-10-07 04:56:56] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -42.945864 | E_var:     0.4023 | E_err:   0.009911
[2025-10-07 04:57:04] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -42.955094 | E_var:     0.3416 | E_err:   0.009132
[2025-10-07 04:57:11] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -42.946909 | E_var:     0.3193 | E_err:   0.008829
[2025-10-07 04:57:19] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -42.952829 | E_var:     0.3091 | E_err:   0.008687
[2025-10-07 04:57:27] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -42.923157 | E_var:     0.4506 | E_err:   0.010488
[2025-10-07 04:57:35] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -42.970538 | E_var:     0.6220 | E_err:   0.012323
[2025-10-07 04:57:43] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -42.954358 | E_var:     0.3680 | E_err:   0.009479
[2025-10-07 04:57:51] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -42.952527 | E_var:     0.3012 | E_err:   0.008575
[2025-10-07 04:57:58] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -42.951607 | E_var:     0.3335 | E_err:   0.009024
[2025-10-07 04:58:06] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -42.953267 | E_var:     0.4097 | E_err:   0.010001
[2025-10-07 04:58:14] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -42.941030 | E_var:     0.3188 | E_err:   0.008822
[2025-10-07 04:58:22] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -42.950580 | E_var:     0.3261 | E_err:   0.008922
[2025-10-07 04:58:30] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -42.935530 | E_var:     0.5096 | E_err:   0.011154
[2025-10-07 04:58:38] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -42.957844 | E_var:     0.3466 | E_err:   0.009199
[2025-10-07 04:58:45] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -42.953194 | E_var:     0.3523 | E_err:   0.009274
[2025-10-07 04:58:53] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -42.960527 | E_var:     0.3645 | E_err:   0.009433
[2025-10-07 04:59:01] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -42.947982 | E_var:     0.3297 | E_err:   0.008971
[2025-10-07 04:59:09] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -42.955957 | E_var:     0.4089 | E_err:   0.009991
[2025-10-07 04:59:17] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -42.945301 | E_var:     0.4411 | E_err:   0.010377
[2025-10-07 04:59:24] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -42.949205 | E_var:     0.3897 | E_err:   0.009754
[2025-10-07 04:59:32] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -42.954187 | E_var:     0.3364 | E_err:   0.009062
[2025-10-07 04:59:40] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -42.945981 | E_var:     0.4531 | E_err:   0.010518
[2025-10-07 04:59:48] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -42.938833 | E_var:     0.3895 | E_err:   0.009752
[2025-10-07 04:59:56] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -42.938223 | E_var:     0.3013 | E_err:   0.008577
[2025-10-07 05:00:04] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -42.953054 | E_var:     0.2989 | E_err:   0.008542
[2025-10-07 05:00:11] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -42.955430 | E_var:     0.3120 | E_err:   0.008727
[2025-10-07 05:00:19] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -42.952027 | E_var:     0.3130 | E_err:   0.008742
[2025-10-07 05:00:27] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -42.954316 | E_var:     0.3146 | E_err:   0.008763
[2025-10-07 05:00:35] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -42.954506 | E_var:     0.3646 | E_err:   0.009434
[2025-10-07 05:00:43] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -42.944009 | E_var:     0.5213 | E_err:   0.011281
[2025-10-07 05:00:51] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -42.950643 | E_var:     0.2922 | E_err:   0.008446
[2025-10-07 05:00:58] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -42.954696 | E_var:     0.3859 | E_err:   0.009707
[2025-10-07 05:01:06] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -42.961214 | E_var:     0.3429 | E_err:   0.009149
[2025-10-07 05:01:14] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -42.939429 | E_var:     0.3787 | E_err:   0.009615
[2025-10-07 05:01:22] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -42.962507 | E_var:     0.3454 | E_err:   0.009183
[2025-10-07 05:01:30] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -42.950014 | E_var:     0.3295 | E_err:   0.008969
[2025-10-07 05:01:37] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -42.935907 | E_var:     0.3557 | E_err:   0.009319
[2025-10-07 05:01:45] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -42.950968 | E_var:     0.3171 | E_err:   0.008799
[2025-10-07 05:01:53] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -42.951049 | E_var:     0.3730 | E_err:   0.009542
[2025-10-07 05:02:01] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -42.961090 | E_var:     0.3056 | E_err:   0.008638
[2025-10-07 05:02:09] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -42.945986 | E_var:     0.3496 | E_err:   0.009239
[2025-10-07 05:02:17] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -42.944958 | E_var:     0.2725 | E_err:   0.008157
[2025-10-07 05:02:24] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -42.936256 | E_var:     0.3317 | E_err:   0.008999
[2025-10-07 05:02:32] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -42.955410 | E_var:     0.2620 | E_err:   0.007998
[2025-10-07 05:02:40] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -42.946784 | E_var:     0.3491 | E_err:   0.009232
[2025-10-07 05:02:48] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -42.967013 | E_var:     0.4440 | E_err:   0.010411
[2025-10-07 05:02:56] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -42.961384 | E_var:     0.3194 | E_err:   0.008831
[2025-10-07 05:03:03] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -42.951882 | E_var:     0.4059 | E_err:   0.009955
[2025-10-07 05:03:11] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -42.948551 | E_var:     0.2852 | E_err:   0.008344
[2025-10-07 05:03:19] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -42.936166 | E_var:     0.3342 | E_err:   0.009033
[2025-10-07 05:03:27] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -42.936740 | E_var:     0.3453 | E_err:   0.009182
[2025-10-07 05:03:35] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -42.949344 | E_var:     0.3413 | E_err:   0.009128
[2025-10-07 05:03:43] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -42.946703 | E_var:     0.3657 | E_err:   0.009449
[2025-10-07 05:03:50] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -42.946111 | E_var:     0.3179 | E_err:   0.008810
[2025-10-07 05:03:58] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -42.935046 | E_var:     0.3972 | E_err:   0.009848
[2025-10-07 05:04:06] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -42.949382 | E_var:     0.3269 | E_err:   0.008934
[2025-10-07 05:04:14] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -42.952194 | E_var:     0.4291 | E_err:   0.010235
[2025-10-07 05:04:22] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -42.969242 | E_var:     0.3769 | E_err:   0.009592
[2025-10-07 05:04:29] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -42.948311 | E_var:     0.3129 | E_err:   0.008740
[2025-10-07 05:04:37] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -42.957659 | E_var:     0.3129 | E_err:   0.008740
[2025-10-07 05:04:45] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -42.962237 | E_var:     0.3720 | E_err:   0.009530
[2025-10-07 05:04:53] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -42.937068 | E_var:     0.3240 | E_err:   0.008893
[2025-10-07 05:05:01] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -42.964536 | E_var:     0.3359 | E_err:   0.009056
[2025-10-07 05:05:09] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -42.965999 | E_var:     0.3870 | E_err:   0.009720
[2025-10-07 05:05:16] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -42.957158 | E_var:     0.3239 | E_err:   0.008893
[2025-10-07 05:05:24] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -42.956676 | E_var:     0.4928 | E_err:   0.010969
[2025-10-07 05:05:32] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -42.946001 | E_var:     0.3225 | E_err:   0.008874
[2025-10-07 05:05:32] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 05:05:40] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -42.944793 | E_var:     0.3192 | E_err:   0.008828
[2025-10-07 05:05:48] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -42.969282 | E_var:     0.3383 | E_err:   0.009088
[2025-10-07 05:05:56] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -42.956589 | E_var:     0.3456 | E_err:   0.009185
[2025-10-07 05:06:03] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -42.945104 | E_var:     0.2876 | E_err:   0.008380
[2025-10-07 05:06:11] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -42.943227 | E_var:     0.3022 | E_err:   0.008589
[2025-10-07 05:06:19] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -42.935799 | E_var:     0.3189 | E_err:   0.008824
[2025-10-07 05:06:27] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -42.946212 | E_var:     0.3304 | E_err:   0.008981
[2025-10-07 05:06:35] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -42.954355 | E_var:     0.3097 | E_err:   0.008695
[2025-10-07 05:06:42] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -42.963980 | E_var:     0.3647 | E_err:   0.009436
[2025-10-07 05:06:50] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -42.951003 | E_var:     0.2552 | E_err:   0.007893
[2025-10-07 05:06:58] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -42.940386 | E_var:     0.3306 | E_err:   0.008984
[2025-10-07 05:07:06] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -42.945645 | E_var:     0.3382 | E_err:   0.009087
[2025-10-07 05:07:14] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -42.964779 | E_var:     0.7448 | E_err:   0.013484
[2025-10-07 05:07:22] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -42.951979 | E_var:     0.3030 | E_err:   0.008601
[2025-10-07 05:07:29] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -42.950817 | E_var:     0.3000 | E_err:   0.008558
[2025-10-07 05:07:37] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -42.944623 | E_var:     0.4001 | E_err:   0.009884
[2025-10-07 05:07:45] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -42.960182 | E_var:     0.4387 | E_err:   0.010349
[2025-10-07 05:07:53] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -42.947778 | E_var:     0.3671 | E_err:   0.009466
[2025-10-07 05:08:01] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -42.984751 | E_var:     1.7282 | E_err:   0.020541
[2025-10-07 05:08:08] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -42.944997 | E_var:     0.3630 | E_err:   0.009414
[2025-10-07 05:08:16] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -42.960157 | E_var:     0.4460 | E_err:   0.010435
[2025-10-07 05:08:24] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -42.960438 | E_var:     0.3784 | E_err:   0.009612
[2025-10-07 05:08:32] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -42.957637 | E_var:     0.2974 | E_err:   0.008520
[2025-10-07 05:08:40] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -42.946911 | E_var:     0.3679 | E_err:   0.009478
[2025-10-07 05:08:48] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -42.934173 | E_var:     0.4877 | E_err:   0.010911
[2025-10-07 05:08:55] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -42.948951 | E_var:     0.3516 | E_err:   0.009265
[2025-10-07 05:09:03] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -42.958628 | E_var:     0.3292 | E_err:   0.008965
[2025-10-07 05:09:11] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -42.926388 | E_var:     0.4338 | E_err:   0.010291
[2025-10-07 05:09:19] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -42.957304 | E_var:     0.3080 | E_err:   0.008672
[2025-10-07 05:09:27] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -42.961466 | E_var:     0.4504 | E_err:   0.010487
[2025-10-07 05:09:34] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -42.961580 | E_var:     0.3447 | E_err:   0.009173
[2025-10-07 05:09:42] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -42.944023 | E_var:     0.3463 | E_err:   0.009195
[2025-10-07 05:09:50] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -42.935551 | E_var:     0.2999 | E_err:   0.008557
[2025-10-07 05:09:58] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -42.943981 | E_var:     0.3688 | E_err:   0.009488
[2025-10-07 05:10:06] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -42.952610 | E_var:     0.3131 | E_err:   0.008743
[2025-10-07 05:10:14] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -42.950289 | E_var:     0.3345 | E_err:   0.009037
[2025-10-07 05:10:21] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -42.949097 | E_var:     0.3170 | E_err:   0.008798
[2025-10-07 05:10:29] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -42.948463 | E_var:     0.2873 | E_err:   0.008374
[2025-10-07 05:10:37] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -42.952930 | E_var:     0.2817 | E_err:   0.008292
[2025-10-07 05:10:45] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -42.945246 | E_var:     0.4453 | E_err:   0.010427
[2025-10-07 05:10:53] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -42.956532 | E_var:     0.2867 | E_err:   0.008366
[2025-10-07 05:11:00] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -42.941704 | E_var:     0.3611 | E_err:   0.009389
[2025-10-07 05:11:08] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -42.967791 | E_var:     0.3102 | E_err:   0.008702
[2025-10-07 05:11:16] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -42.950749 | E_var:     0.3334 | E_err:   0.009022
[2025-10-07 05:11:24] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -42.952873 | E_var:     0.3515 | E_err:   0.009264
[2025-10-07 05:11:32] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -42.961641 | E_var:     0.3989 | E_err:   0.009869
[2025-10-07 05:11:40] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -42.946847 | E_var:     0.6138 | E_err:   0.012241
[2025-10-07 05:11:47] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -42.969456 | E_var:     0.3478 | E_err:   0.009215
[2025-10-07 05:11:55] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -42.942853 | E_var:     0.3666 | E_err:   0.009461
[2025-10-07 05:12:03] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -42.951880 | E_var:     0.3500 | E_err:   0.009244
[2025-10-07 05:12:11] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -42.943024 | E_var:     0.3470 | E_err:   0.009205
[2025-10-07 05:12:19] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -42.952757 | E_var:     0.3155 | E_err:   0.008777
[2025-10-07 05:12:26] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -42.951268 | E_var:     0.3691 | E_err:   0.009492
[2025-10-07 05:12:34] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -42.950226 | E_var:     0.2768 | E_err:   0.008220
[2025-10-07 05:12:42] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -42.942545 | E_var:     0.3287 | E_err:   0.008958
[2025-10-07 05:12:50] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -42.954675 | E_var:     0.3021 | E_err:   0.008588
[2025-10-07 05:12:58] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -42.969184 | E_var:     0.4834 | E_err:   0.010864
[2025-10-07 05:13:06] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -42.950584 | E_var:     0.3625 | E_err:   0.009407
[2025-10-07 05:13:13] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -42.951459 | E_var:     0.4005 | E_err:   0.009889
[2025-10-07 05:13:21] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -42.936570 | E_var:     0.4164 | E_err:   0.010082
[2025-10-07 05:13:29] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -42.945048 | E_var:     0.3563 | E_err:   0.009327
[2025-10-07 05:13:37] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -42.978795 | E_var:     0.2993 | E_err:   0.008548
[2025-10-07 05:13:45] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -42.948304 | E_var:     0.4917 | E_err:   0.010957
[2025-10-07 05:13:52] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -42.951836 | E_var:     0.3509 | E_err:   0.009256
[2025-10-07 05:14:00] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -42.967846 | E_var:     0.3463 | E_err:   0.009195
[2025-10-07 05:14:08] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -42.950601 | E_var:     0.2710 | E_err:   0.008134
[2025-10-07 05:14:16] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -42.942000 | E_var:     0.4880 | E_err:   0.010915
[2025-10-07 05:14:24] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -42.961183 | E_var:     0.2882 | E_err:   0.008388
[2025-10-07 05:14:32] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -42.949296 | E_var:     0.3858 | E_err:   0.009706
[2025-10-07 05:14:39] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -42.936442 | E_var:     0.4144 | E_err:   0.010059
[2025-10-07 05:14:47] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -42.946609 | E_var:     0.3438 | E_err:   0.009162
[2025-10-07 05:14:55] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -42.944364 | E_var:     0.3535 | E_err:   0.009290
[2025-10-07 05:15:03] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -42.967366 | E_var:     0.3172 | E_err:   0.008800
[2025-10-07 05:15:11] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -42.941395 | E_var:     0.3583 | E_err:   0.009352
[2025-10-07 05:15:18] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -42.953454 | E_var:     0.3921 | E_err:   0.009784
[2025-10-07 05:15:26] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -42.945027 | E_var:     0.3782 | E_err:   0.009609
[2025-10-07 05:15:34] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -42.961171 | E_var:     0.3963 | E_err:   0.009836
[2025-10-07 05:15:42] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -42.962455 | E_var:     0.3450 | E_err:   0.009178
[2025-10-07 05:15:50] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -42.957500 | E_var:     0.2846 | E_err:   0.008336
[2025-10-07 05:15:58] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -42.935064 | E_var:     0.4614 | E_err:   0.010614
[2025-10-07 05:16:05] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -42.945033 | E_var:     0.2910 | E_err:   0.008429
[2025-10-07 05:16:13] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -42.958477 | E_var:     0.2658 | E_err:   0.008056
[2025-10-07 05:16:21] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -42.953534 | E_var:     0.3723 | E_err:   0.009533
[2025-10-07 05:16:29] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -42.962165 | E_var:     0.2954 | E_err:   0.008493
[2025-10-07 05:16:37] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -42.955017 | E_var:     0.2900 | E_err:   0.008414
[2025-10-07 05:16:44] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -42.944461 | E_var:     0.4027 | E_err:   0.009915
[2025-10-07 05:16:52] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -42.946562 | E_var:     0.2672 | E_err:   0.008077
[2025-10-07 05:17:00] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -42.971759 | E_var:     0.3816 | E_err:   0.009652
[2025-10-07 05:17:08] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -42.953510 | E_var:     0.3118 | E_err:   0.008725
[2025-10-07 05:17:16] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -42.970971 | E_var:     0.3379 | E_err:   0.009083
[2025-10-07 05:17:24] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -42.952121 | E_var:     0.3337 | E_err:   0.009026
[2025-10-07 05:17:31] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -42.955019 | E_var:     0.3392 | E_err:   0.009101
[2025-10-07 05:17:39] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -42.965733 | E_var:     0.3214 | E_err:   0.008858
[2025-10-07 05:17:47] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -42.955005 | E_var:     0.3430 | E_err:   0.009151
[2025-10-07 05:17:55] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -42.948675 | E_var:     0.4498 | E_err:   0.010479
[2025-10-07 05:18:03] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -42.957871 | E_var:     0.2775 | E_err:   0.008231
[2025-10-07 05:18:10] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -42.947988 | E_var:     0.4291 | E_err:   0.010235
[2025-10-07 05:18:18] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -42.940580 | E_var:     0.2944 | E_err:   0.008479
[2025-10-07 05:18:26] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -42.950815 | E_var:     0.3774 | E_err:   0.009599
[2025-10-07 05:18:34] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -42.959301 | E_var:     0.4545 | E_err:   0.010534
[2025-10-07 05:18:34] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 05:18:42] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -42.948105 | E_var:     0.3130 | E_err:   0.008741
[2025-10-07 05:18:50] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -42.959020 | E_var:     0.4167 | E_err:   0.010086
[2025-10-07 05:18:57] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -42.965005 | E_var:     0.3691 | E_err:   0.009492
[2025-10-07 05:19:05] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -42.942990 | E_var:     0.5257 | E_err:   0.011329
[2025-10-07 05:19:13] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -42.955751 | E_var:     0.3173 | E_err:   0.008801
[2025-10-07 05:19:21] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -42.961412 | E_var:     0.2999 | E_err:   0.008556
[2025-10-07 05:19:29] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -42.958931 | E_var:     0.2531 | E_err:   0.007861
[2025-10-07 05:19:36] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -42.947642 | E_var:     0.3527 | E_err:   0.009279
[2025-10-07 05:19:44] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -42.966040 | E_var:     0.2901 | E_err:   0.008416
[2025-10-07 05:19:52] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -42.947109 | E_var:     0.4783 | E_err:   0.010806
[2025-10-07 05:20:00] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -42.960824 | E_var:     0.3052 | E_err:   0.008632
[2025-10-07 05:20:08] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -42.959042 | E_var:     0.3261 | E_err:   0.008922
[2025-10-07 05:20:16] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -42.944237 | E_var:     0.3220 | E_err:   0.008867
[2025-10-07 05:20:23] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -42.944660 | E_var:     0.2927 | E_err:   0.008453
[2025-10-07 05:20:31] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -42.929472 | E_var:     0.3260 | E_err:   0.008921
[2025-10-07 05:20:39] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -42.947793 | E_var:     0.2835 | E_err:   0.008320
[2025-10-07 05:20:47] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -42.954880 | E_var:     0.2831 | E_err:   0.008314
[2025-10-07 05:20:55] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -42.967322 | E_var:     0.2739 | E_err:   0.008177
[2025-10-07 05:21:03] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -42.951550 | E_var:     0.2874 | E_err:   0.008376
[2025-10-07 05:21:10] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -42.957264 | E_var:     0.3416 | E_err:   0.009132
[2025-10-07 05:21:18] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -42.942632 | E_var:     0.2955 | E_err:   0.008493
[2025-10-07 05:21:26] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -42.965148 | E_var:     0.4041 | E_err:   0.009933
[2025-10-07 05:21:34] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -42.956105 | E_var:     0.3310 | E_err:   0.008990
[2025-10-07 05:21:42] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -42.946774 | E_var:     0.3266 | E_err:   0.008929
[2025-10-07 05:21:49] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -42.958217 | E_var:     0.3020 | E_err:   0.008587
[2025-10-07 05:21:57] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -42.949189 | E_var:     0.4564 | E_err:   0.010556
[2025-10-07 05:22:05] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -42.963811 | E_var:     0.4491 | E_err:   0.010471
[2025-10-07 05:22:13] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -42.959028 | E_var:     0.3596 | E_err:   0.009370
[2025-10-07 05:22:21] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -42.963591 | E_var:     0.7294 | E_err:   0.013345
[2025-10-07 05:22:29] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -42.956803 | E_var:     0.3400 | E_err:   0.009110
[2025-10-07 05:22:36] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -42.947314 | E_var:     0.4754 | E_err:   0.010774
[2025-10-07 05:22:44] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -42.965229 | E_var:     0.3120 | E_err:   0.008728
[2025-10-07 05:22:52] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -42.965269 | E_var:     0.3381 | E_err:   0.009086
[2025-10-07 05:23:00] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -42.942069 | E_var:     0.2951 | E_err:   0.008488
[2025-10-07 05:23:08] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -42.955079 | E_var:     0.3419 | E_err:   0.009137
[2025-10-07 05:23:15] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -42.953613 | E_var:     0.3764 | E_err:   0.009586
[2025-10-07 05:23:23] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -42.950799 | E_var:     0.3557 | E_err:   0.009319
[2025-10-07 05:23:31] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -42.954500 | E_var:     0.3674 | E_err:   0.009471
[2025-10-07 05:23:39] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -42.937447 | E_var:     0.3253 | E_err:   0.008912
[2025-10-07 05:23:47] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -42.935749 | E_var:     0.3316 | E_err:   0.008998
[2025-10-07 05:23:55] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -42.967875 | E_var:     0.4490 | E_err:   0.010470
[2025-10-07 05:24:02] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -42.953131 | E_var:     0.3444 | E_err:   0.009169
[2025-10-07 05:24:10] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -42.949031 | E_var:     0.3357 | E_err:   0.009053
[2025-10-07 05:24:18] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -42.950152 | E_var:     0.3110 | E_err:   0.008714
[2025-10-07 05:24:26] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -42.968981 | E_var:     0.3467 | E_err:   0.009200
[2025-10-07 05:24:34] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -42.941024 | E_var:     0.4577 | E_err:   0.010570
[2025-10-07 05:24:41] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -42.963046 | E_var:     0.6179 | E_err:   0.012282
[2025-10-07 05:24:49] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -42.950573 | E_var:     0.3425 | E_err:   0.009144
[2025-10-07 05:24:57] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -42.956538 | E_var:     0.3544 | E_err:   0.009302
[2025-10-07 05:25:05] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -42.945952 | E_var:     0.2914 | E_err:   0.008435
[2025-10-07 05:25:05] ======================================================================================================
[2025-10-07 05:25:05] ✅ Training completed successfully
[2025-10-07 05:25:05] Total restarts: 2
[2025-10-07 05:25:08] Final Energy: -42.94595245 ± 0.00843460
[2025-10-07 05:25:08] Final Variance: 0.291399
[2025-10-07 05:25:08] ======================================================================================================
[2025-10-07 05:25:08] ======================================================================================================
[2025-10-07 05:25:08] Training completed | Runtime: 8268.0s
[2025-10-07 05:25:10] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 05:25:10] ======================================================================================================
