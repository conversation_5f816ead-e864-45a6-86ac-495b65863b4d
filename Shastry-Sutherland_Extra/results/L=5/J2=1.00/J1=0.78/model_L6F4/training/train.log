[2025-10-07 05:25:32] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.77/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 05:25:32]   - 迭代次数: final
[2025-10-07 05:25:32]   - 能量: -42.945952-0.000766j ± 0.008435, Var: 0.291399
[2025-10-07 05:25:32]   - 时间戳: 2025-10-07T05:25:10.645031+08:00
[2025-10-07 05:25:53] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 05:25:53] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 05:25:53] ======================================================================================================
[2025-10-07 05:25:53] GCNN for Shastry-Sutherland Model
[2025-10-07 05:25:53] ======================================================================================================
[2025-10-07 05:25:53] System parameters:
[2025-10-07 05:25:53]   - System size: L=5, N=100
[2025-10-07 05:25:53]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-10-07 05:25:53] ------------------------------------------------------------------------------------------------------
[2025-10-07 05:25:53] Model parameters:
[2025-10-07 05:25:53]   - Number of layers = 6
[2025-10-07 05:25:53]   - Number of features = 4
[2025-10-07 05:25:53]   - Total parameters = 32444
[2025-10-07 05:25:53] ------------------------------------------------------------------------------------------------------
[2025-10-07 05:25:53] Training parameters:
[2025-10-07 05:25:53]   - Total iterations: 1050
[2025-10-07 05:25:53]   - Annealing cycles: 3
[2025-10-07 05:25:53]   - Initial period: 150
[2025-10-07 05:25:53]   - Period multiplier: 2.0
[2025-10-07 05:25:53]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 05:25:53]   - Samples: 4096
[2025-10-07 05:25:53]   - Discarded samples: 0
[2025-10-07 05:25:53]   - Chunk size: 4096
[2025-10-07 05:25:53]   - Diagonal shift: 0.15
[2025-10-07 05:25:53]   - Gradient clipping: 1.0
[2025-10-07 05:25:53]   - Checkpoint enabled: interval=100
[2025-10-07 05:25:53]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.78/model_L6F4/training/checkpoints
[2025-10-07 05:25:53] ------------------------------------------------------------------------------------------------------
[2025-10-07 05:25:53] Device status:
[2025-10-07 05:25:53]   - Devices model: NVIDIA H200 NVL
[2025-10-07 05:25:53]   - Number of devices: 1
[2025-10-07 05:25:53]   - Sharding: True
[2025-10-07 05:25:54] ======================================================================================================
[2025-10-07 05:26:35] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -43.555633 | E_var:     1.1705 | E_err:   0.016905
[2025-10-07 05:27:01] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -43.599204 | E_var:     0.5277 | E_err:   0.011351
[2025-10-07 05:27:09] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -43.577592 | E_var:     0.5217 | E_err:   0.011286
[2025-10-07 05:27:17] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -43.598981 | E_var:     0.4529 | E_err:   0.010515
[2025-10-07 05:27:25] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -43.590634 | E_var:     0.4309 | E_err:   0.010256
[2025-10-07 05:27:32] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -43.591227 | E_var:     0.3847 | E_err:   0.009691
[2025-10-07 05:27:40] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -43.589940 | E_var:     0.3687 | E_err:   0.009488
[2025-10-07 05:27:48] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -43.580186 | E_var:     0.9042 | E_err:   0.014858
[2025-10-07 05:27:56] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -43.585729 | E_var:     0.2903 | E_err:   0.008418
[2025-10-07 05:28:04] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -43.590029 | E_var:     0.3534 | E_err:   0.009289
[2025-10-07 05:28:11] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -43.616307 | E_var:     0.4399 | E_err:   0.010363
[2025-10-07 05:28:19] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -43.595745 | E_var:     0.3364 | E_err:   0.009062
[2025-10-07 05:28:27] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -43.596397 | E_var:     0.2914 | E_err:   0.008434
[2025-10-07 05:28:35] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -43.575949 | E_var:     0.4795 | E_err:   0.010820
[2025-10-07 05:28:43] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -43.580803 | E_var:     0.3242 | E_err:   0.008897
[2025-10-07 05:28:50] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -43.592078 | E_var:     0.3948 | E_err:   0.009817
[2025-10-07 05:28:58] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -43.589029 | E_var:     0.3262 | E_err:   0.008924
[2025-10-07 05:29:06] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -43.598893 | E_var:     0.3599 | E_err:   0.009374
[2025-10-07 05:29:14] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -43.580207 | E_var:     0.5799 | E_err:   0.011899
[2025-10-07 05:29:22] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -43.605395 | E_var:     0.3987 | E_err:   0.009867
[2025-10-07 05:29:29] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -43.597639 | E_var:     0.2591 | E_err:   0.007953
[2025-10-07 05:29:37] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -43.605239 | E_var:     0.2980 | E_err:   0.008529
[2025-10-07 05:29:45] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -43.587313 | E_var:     0.3745 | E_err:   0.009562
[2025-10-07 05:29:53] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -43.586510 | E_var:     0.2778 | E_err:   0.008235
[2025-10-07 05:30:01] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -43.586103 | E_var:     0.2827 | E_err:   0.008308
[2025-10-07 05:30:08] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -43.581540 | E_var:     0.3789 | E_err:   0.009618
[2025-10-07 05:30:16] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -43.580706 | E_var:     0.3392 | E_err:   0.009100
[2025-10-07 05:30:24] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -43.612397 | E_var:     0.3677 | E_err:   0.009475
[2025-10-07 05:30:32] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -43.606375 | E_var:     0.3268 | E_err:   0.008933
[2025-10-07 05:30:40] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -43.587649 | E_var:     0.2866 | E_err:   0.008365
[2025-10-07 05:30:47] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -43.577087 | E_var:     0.3438 | E_err:   0.009161
[2025-10-07 05:30:55] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -43.579411 | E_var:     0.3162 | E_err:   0.008786
[2025-10-07 05:31:03] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -43.586664 | E_var:     0.3273 | E_err:   0.008939
[2025-10-07 05:31:11] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -43.580809 | E_var:     0.2826 | E_err:   0.008306
[2025-10-07 05:31:19] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -43.577320 | E_var:     0.5570 | E_err:   0.011661
[2025-10-07 05:31:26] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -43.584188 | E_var:     0.3984 | E_err:   0.009862
[2025-10-07 05:31:34] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -43.586659 | E_var:     0.3043 | E_err:   0.008619
[2025-10-07 05:31:42] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -43.580373 | E_var:     0.3092 | E_err:   0.008689
[2025-10-07 05:31:50] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -43.580030 | E_var:     0.3160 | E_err:   0.008784
[2025-10-07 05:31:58] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -43.579922 | E_var:     0.3149 | E_err:   0.008769
[2025-10-07 05:32:05] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -43.591410 | E_var:     0.3057 | E_err:   0.008639
[2025-10-07 05:32:13] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -43.593148 | E_var:     0.2776 | E_err:   0.008233
[2025-10-07 05:32:21] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -43.583607 | E_var:     0.3370 | E_err:   0.009071
[2025-10-07 05:32:29] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -43.576646 | E_var:     0.3248 | E_err:   0.008905
[2025-10-07 05:32:37] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -43.588018 | E_var:     0.2714 | E_err:   0.008140
[2025-10-07 05:32:45] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -43.590845 | E_var:     0.2372 | E_err:   0.007611
[2025-10-07 05:32:52] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -43.581954 | E_var:     0.3178 | E_err:   0.008808
[2025-10-07 05:33:00] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -43.602909 | E_var:     0.3171 | E_err:   0.008799
[2025-10-07 05:33:08] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -43.585398 | E_var:     0.2978 | E_err:   0.008526
[2025-10-07 05:33:16] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -43.559009 | E_var:     0.6692 | E_err:   0.012782
[2025-10-07 05:33:24] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -43.599510 | E_var:     0.2641 | E_err:   0.008029
[2025-10-07 05:33:31] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -43.589370 | E_var:     0.3409 | E_err:   0.009122
[2025-10-07 05:33:39] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -43.595635 | E_var:     0.3593 | E_err:   0.009366
[2025-10-07 05:33:47] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -43.580651 | E_var:     0.2895 | E_err:   0.008407
[2025-10-07 05:33:55] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -43.592818 | E_var:     0.3154 | E_err:   0.008775
[2025-10-07 05:34:03] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -43.591580 | E_var:     0.2929 | E_err:   0.008456
[2025-10-07 05:34:10] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -43.595522 | E_var:     0.3427 | E_err:   0.009148
[2025-10-07 05:34:18] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -43.587766 | E_var:     0.3209 | E_err:   0.008851
[2025-10-07 05:34:26] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -43.585515 | E_var:     0.3126 | E_err:   0.008736
[2025-10-07 05:34:34] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -43.584924 | E_var:     0.3936 | E_err:   0.009803
[2025-10-07 05:34:42] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -43.599249 | E_var:     0.2776 | E_err:   0.008233
[2025-10-07 05:34:49] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -43.580400 | E_var:     0.2462 | E_err:   0.007753
[2025-10-07 05:34:57] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -43.590598 | E_var:     0.3323 | E_err:   0.009007
[2025-10-07 05:35:05] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -43.604944 | E_var:     0.2541 | E_err:   0.007876
[2025-10-07 05:35:13] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -43.587395 | E_var:     0.2719 | E_err:   0.008147
[2025-10-07 05:35:21] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -43.586268 | E_var:     0.3118 | E_err:   0.008725
[2025-10-07 05:35:28] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -43.573762 | E_var:     0.3147 | E_err:   0.008766
[2025-10-07 05:35:36] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -43.584274 | E_var:     0.2729 | E_err:   0.008162
[2025-10-07 05:35:44] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -43.585774 | E_var:     0.3120 | E_err:   0.008727
[2025-10-07 05:35:52] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -43.601076 | E_var:     0.2800 | E_err:   0.008269
[2025-10-07 05:35:59] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -43.585953 | E_var:     0.3757 | E_err:   0.009577
[2025-10-07 05:36:07] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -43.585349 | E_var:     0.3116 | E_err:   0.008722
[2025-10-07 05:36:15] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -43.588655 | E_var:     0.2822 | E_err:   0.008301
[2025-10-07 05:36:23] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -43.595778 | E_var:     0.3084 | E_err:   0.008677
[2025-10-07 05:36:31] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -43.607337 | E_var:     0.3391 | E_err:   0.009098
[2025-10-07 05:36:39] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -43.586497 | E_var:     0.3014 | E_err:   0.008579
[2025-10-07 05:36:46] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -43.584280 | E_var:     0.3502 | E_err:   0.009247
[2025-10-07 05:36:54] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -43.590845 | E_var:     0.5617 | E_err:   0.011710
[2025-10-07 05:37:02] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -43.598764 | E_var:     0.2268 | E_err:   0.007441
[2025-10-07 05:37:10] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -43.591324 | E_var:     0.2975 | E_err:   0.008523
[2025-10-07 05:37:18] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -43.596643 | E_var:     0.2543 | E_err:   0.007879
[2025-10-07 05:37:25] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -43.583287 | E_var:     0.3337 | E_err:   0.009025
[2025-10-07 05:37:33] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -43.587041 | E_var:     0.2863 | E_err:   0.008360
[2025-10-07 05:37:41] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -43.582535 | E_var:     0.3058 | E_err:   0.008640
[2025-10-07 05:37:49] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -43.585399 | E_var:     0.3554 | E_err:   0.009314
[2025-10-07 05:37:57] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -43.598650 | E_var:     0.3344 | E_err:   0.009036
[2025-10-07 05:38:04] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -43.591700 | E_var:     0.2651 | E_err:   0.008044
[2025-10-07 05:38:12] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -43.593325 | E_var:     0.3733 | E_err:   0.009546
[2025-10-07 05:38:20] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -43.581234 | E_var:     0.3489 | E_err:   0.009230
[2025-10-07 05:38:28] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -43.598470 | E_var:     0.2774 | E_err:   0.008229
[2025-10-07 05:38:35] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -43.586281 | E_var:     0.3116 | E_err:   0.008723
[2025-10-07 05:38:43] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -43.589447 | E_var:     0.2882 | E_err:   0.008388
[2025-10-07 05:38:51] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -43.599337 | E_var:     0.3038 | E_err:   0.008612
[2025-10-07 05:38:59] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -43.590030 | E_var:     0.2788 | E_err:   0.008250
[2025-10-07 05:39:07] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -43.590477 | E_var:     0.3173 | E_err:   0.008802
[2025-10-07 05:39:14] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -43.580272 | E_var:     0.4880 | E_err:   0.010915
[2025-10-07 05:39:22] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -43.589614 | E_var:     0.3440 | E_err:   0.009165
[2025-10-07 05:39:30] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -43.595440 | E_var:     0.3050 | E_err:   0.008629
[2025-10-07 05:39:38] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -43.598421 | E_var:     0.3771 | E_err:   0.009595
[2025-10-07 05:39:46] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -43.596949 | E_var:     0.2894 | E_err:   0.008405
[2025-10-07 05:39:46] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 05:39:53] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -43.609159 | E_var:     0.3214 | E_err:   0.008857
[2025-10-07 05:40:01] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -43.564177 | E_var:     0.9756 | E_err:   0.015433
[2025-10-07 05:40:09] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -43.580466 | E_var:     0.2963 | E_err:   0.008506
[2025-10-07 05:40:17] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -43.599877 | E_var:     0.3869 | E_err:   0.009719
[2025-10-07 05:40:25] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -43.584377 | E_var:     0.3023 | E_err:   0.008590
[2025-10-07 05:40:32] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -43.607602 | E_var:     0.3700 | E_err:   0.009504
[2025-10-07 05:40:40] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -43.590704 | E_var:     0.3047 | E_err:   0.008625
[2025-10-07 05:40:48] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -43.603910 | E_var:     0.2926 | E_err:   0.008452
[2025-10-07 05:40:56] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -43.583634 | E_var:     0.5135 | E_err:   0.011197
[2025-10-07 05:41:04] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -43.585894 | E_var:     0.2631 | E_err:   0.008014
[2025-10-07 05:41:11] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -43.603684 | E_var:     0.3030 | E_err:   0.008601
[2025-10-07 05:41:19] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -43.601657 | E_var:     0.3273 | E_err:   0.008939
[2025-10-07 05:41:27] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -43.582759 | E_var:     0.2960 | E_err:   0.008502
[2025-10-07 05:41:35] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -43.578031 | E_var:     0.3667 | E_err:   0.009461
[2025-10-07 05:41:43] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -43.589482 | E_var:     0.2921 | E_err:   0.008445
[2025-10-07 05:41:50] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -43.595136 | E_var:     0.3094 | E_err:   0.008692
[2025-10-07 05:41:58] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -43.591085 | E_var:     0.3451 | E_err:   0.009179
[2025-10-07 05:42:06] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -43.604392 | E_var:     0.2719 | E_err:   0.008148
[2025-10-07 05:42:14] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -43.570945 | E_var:     0.3590 | E_err:   0.009362
[2025-10-07 05:42:22] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -43.573381 | E_var:     0.4796 | E_err:   0.010820
[2025-10-07 05:42:29] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -43.588518 | E_var:     0.3772 | E_err:   0.009596
[2025-10-07 05:42:37] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -43.587972 | E_var:     0.3326 | E_err:   0.009011
[2025-10-07 05:42:45] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -43.602291 | E_var:     0.3464 | E_err:   0.009197
[2025-10-07 05:42:53] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -43.587268 | E_var:     0.3231 | E_err:   0.008882
[2025-10-07 05:43:01] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -43.577724 | E_var:     0.3333 | E_err:   0.009021
[2025-10-07 05:43:08] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -43.585138 | E_var:     0.2853 | E_err:   0.008345
[2025-10-07 05:43:16] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -43.586377 | E_var:     0.3802 | E_err:   0.009635
[2025-10-07 05:43:24] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -43.603356 | E_var:     0.3280 | E_err:   0.008948
[2025-10-07 05:43:32] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -43.590694 | E_var:     0.3439 | E_err:   0.009164
[2025-10-07 05:43:40] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -43.594537 | E_var:     0.3450 | E_err:   0.009177
[2025-10-07 05:43:47] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -43.596418 | E_var:     0.2845 | E_err:   0.008335
[2025-10-07 05:43:55] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -43.588657 | E_var:     0.2839 | E_err:   0.008325
[2025-10-07 05:44:03] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -43.590528 | E_var:     0.3388 | E_err:   0.009095
[2025-10-07 05:44:11] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -43.598498 | E_var:     0.2764 | E_err:   0.008215
[2025-10-07 05:44:19] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -43.605994 | E_var:     0.3228 | E_err:   0.008878
[2025-10-07 05:44:26] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -43.587701 | E_var:     0.2570 | E_err:   0.007921
[2025-10-07 05:44:34] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -43.587636 | E_var:     0.3159 | E_err:   0.008782
[2025-10-07 05:44:42] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -43.599742 | E_var:     0.2921 | E_err:   0.008445
[2025-10-07 05:44:50] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -43.589212 | E_var:     0.2913 | E_err:   0.008434
[2025-10-07 05:44:58] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -43.590888 | E_var:     0.3034 | E_err:   0.008607
[2025-10-07 05:45:05] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -43.574969 | E_var:     0.2907 | E_err:   0.008425
[2025-10-07 05:45:13] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -43.593610 | E_var:     0.3319 | E_err:   0.009002
[2025-10-07 05:45:21] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -43.587033 | E_var:     0.2710 | E_err:   0.008134
[2025-10-07 05:45:29] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -43.607497 | E_var:     0.2826 | E_err:   0.008307
[2025-10-07 05:45:36] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -43.584153 | E_var:     0.4919 | E_err:   0.010959
[2025-10-07 05:45:44] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -43.595010 | E_var:     0.3581 | E_err:   0.009350
[2025-10-07 05:45:52] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -43.586357 | E_var:     0.2737 | E_err:   0.008174
[2025-10-07 05:46:00] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -43.573811 | E_var:     0.4765 | E_err:   0.010785
[2025-10-07 05:46:08] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -43.582083 | E_var:     0.2723 | E_err:   0.008154
[2025-10-07 05:46:15] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -43.606332 | E_var:     0.3029 | E_err:   0.008600
[2025-10-07 05:46:15] 🔄 RESTART #1 | Period: 300
[2025-10-07 05:46:23] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -43.583912 | E_var:     0.3511 | E_err:   0.009258
[2025-10-07 05:46:31] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -43.579696 | E_var:     0.3275 | E_err:   0.008941
[2025-10-07 05:46:39] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -43.606274 | E_var:     0.3624 | E_err:   0.009407
[2025-10-07 05:46:47] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -43.589579 | E_var:     0.2811 | E_err:   0.008285
[2025-10-07 05:46:54] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -43.605462 | E_var:     0.2936 | E_err:   0.008466
[2025-10-07 05:47:02] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -43.580739 | E_var:     0.3064 | E_err:   0.008650
[2025-10-07 05:47:10] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -43.594371 | E_var:     0.2905 | E_err:   0.008422
[2025-10-07 05:47:18] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -43.579974 | E_var:     0.3330 | E_err:   0.009017
[2025-10-07 05:47:26] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -43.588875 | E_var:     0.3326 | E_err:   0.009011
[2025-10-07 05:47:33] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -43.604824 | E_var:     0.3513 | E_err:   0.009261
[2025-10-07 05:47:41] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -43.623239 | E_var:     0.4163 | E_err:   0.010081
[2025-10-07 05:47:49] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -43.589807 | E_var:     0.3485 | E_err:   0.009224
[2025-10-07 05:47:57] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -43.597010 | E_var:     0.3381 | E_err:   0.009085
[2025-10-07 05:48:05] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -43.587351 | E_var:     0.2430 | E_err:   0.007703
[2025-10-07 05:48:12] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -43.588110 | E_var:     0.2693 | E_err:   0.008109
[2025-10-07 05:48:20] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -43.583761 | E_var:     0.2555 | E_err:   0.007898
[2025-10-07 05:48:28] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -43.595294 | E_var:     0.2966 | E_err:   0.008509
[2025-10-07 05:48:36] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -43.570386 | E_var:     0.2973 | E_err:   0.008520
[2025-10-07 05:48:44] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -43.584767 | E_var:     0.3152 | E_err:   0.008772
[2025-10-07 05:48:51] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -43.588964 | E_var:     0.2749 | E_err:   0.008193
[2025-10-07 05:48:59] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -43.595838 | E_var:     0.2774 | E_err:   0.008230
[2025-10-07 05:49:07] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -43.590653 | E_var:     0.4572 | E_err:   0.010565
[2025-10-07 05:49:15] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -43.597336 | E_var:     0.5386 | E_err:   0.011468
[2025-10-07 05:49:23] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -43.589371 | E_var:     0.2785 | E_err:   0.008245
[2025-10-07 05:49:30] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -43.591109 | E_var:     0.2787 | E_err:   0.008248
[2025-10-07 05:49:38] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -43.603042 | E_var:     1.0924 | E_err:   0.016331
[2025-10-07 05:49:46] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -43.592529 | E_var:     0.4171 | E_err:   0.010091
[2025-10-07 05:49:54] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -43.620677 | E_var:     1.3111 | E_err:   0.017891
[2025-10-07 05:50:02] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -43.593816 | E_var:     0.3086 | E_err:   0.008680
[2025-10-07 05:50:09] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -43.602097 | E_var:     0.2875 | E_err:   0.008377
[2025-10-07 05:50:17] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -43.599770 | E_var:     0.3821 | E_err:   0.009659
[2025-10-07 05:50:25] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -43.608245 | E_var:     0.7320 | E_err:   0.013369
[2025-10-07 05:50:33] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -43.579472 | E_var:     0.3622 | E_err:   0.009404
[2025-10-07 05:50:41] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -43.586610 | E_var:     0.2946 | E_err:   0.008480
[2025-10-07 05:50:48] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -43.593119 | E_var:     0.3150 | E_err:   0.008770
[2025-10-07 05:50:56] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -43.593451 | E_var:     0.2681 | E_err:   0.008090
[2025-10-07 05:51:04] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -43.578564 | E_var:     0.3288 | E_err:   0.008960
[2025-10-07 05:51:12] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -43.584134 | E_var:     0.3396 | E_err:   0.009105
[2025-10-07 05:51:20] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -43.600270 | E_var:     0.3201 | E_err:   0.008840
[2025-10-07 05:51:27] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -43.594368 | E_var:     0.3415 | E_err:   0.009131
[2025-10-07 05:51:35] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -43.577139 | E_var:     0.5644 | E_err:   0.011738
[2025-10-07 05:51:43] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -43.597140 | E_var:     0.2428 | E_err:   0.007699
[2025-10-07 05:51:51] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -43.580652 | E_var:     0.3204 | E_err:   0.008844
[2025-10-07 05:51:59] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -43.590389 | E_var:     0.3492 | E_err:   0.009234
[2025-10-07 05:52:06] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -43.605262 | E_var:     0.3371 | E_err:   0.009072
[2025-10-07 05:52:14] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -43.576603 | E_var:     0.5131 | E_err:   0.011193
[2025-10-07 05:52:22] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -43.595709 | E_var:     0.4636 | E_err:   0.010639
[2025-10-07 05:52:30] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -43.587399 | E_var:     1.0031 | E_err:   0.015649
[2025-10-07 05:52:38] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -43.567145 | E_var:     1.3083 | E_err:   0.017872
[2025-10-07 05:52:45] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -43.586140 | E_var:     0.7855 | E_err:   0.013848
[2025-10-07 05:52:45] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 05:52:53] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -43.605583 | E_var:     0.4119 | E_err:   0.010029
[2025-10-07 05:53:01] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -43.587264 | E_var:     0.3456 | E_err:   0.009186
[2025-10-07 05:53:09] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -43.595972 | E_var:     0.3503 | E_err:   0.009248
[2025-10-07 05:53:17] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -43.582841 | E_var:     0.3801 | E_err:   0.009633
[2025-10-07 05:53:24] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -43.593636 | E_var:     0.2964 | E_err:   0.008507
[2025-10-07 05:53:32] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -43.592096 | E_var:     0.3264 | E_err:   0.008927
[2025-10-07 05:53:40] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -43.602354 | E_var:     0.3347 | E_err:   0.009040
[2025-10-07 05:53:48] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -43.561893 | E_var:     1.1367 | E_err:   0.016659
[2025-10-07 05:53:56] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -43.589345 | E_var:     0.6494 | E_err:   0.012592
[2025-10-07 05:54:03] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -43.579622 | E_var:     0.5117 | E_err:   0.011177
[2025-10-07 05:54:11] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -43.606444 | E_var:     1.2667 | E_err:   0.017585
[2025-10-07 05:54:19] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -43.592416 | E_var:     0.3420 | E_err:   0.009138
[2025-10-07 05:54:27] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -43.605273 | E_var:     0.2810 | E_err:   0.008283
[2025-10-07 05:54:35] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -43.581995 | E_var:     0.2622 | E_err:   0.008000
[2025-10-07 05:54:42] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -43.598772 | E_var:     0.2911 | E_err:   0.008430
[2025-10-07 05:54:50] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -43.589133 | E_var:     0.3204 | E_err:   0.008844
[2025-10-07 05:54:58] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -43.582408 | E_var:     0.4251 | E_err:   0.010188
[2025-10-07 05:55:06] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -43.597311 | E_var:     0.2464 | E_err:   0.007756
[2025-10-07 05:55:14] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -43.588651 | E_var:     0.2780 | E_err:   0.008239
[2025-10-07 05:55:21] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -43.587030 | E_var:     0.4077 | E_err:   0.009977
[2025-10-07 05:55:29] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -43.586268 | E_var:     0.2639 | E_err:   0.008027
[2025-10-07 05:55:37] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -43.599120 | E_var:     0.3992 | E_err:   0.009872
[2025-10-07 05:55:45] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -43.586914 | E_var:     0.3351 | E_err:   0.009045
[2025-10-07 05:55:53] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -43.580439 | E_var:     0.3942 | E_err:   0.009810
[2025-10-07 05:56:00] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -43.600682 | E_var:     0.3042 | E_err:   0.008618
[2025-10-07 05:56:08] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -43.579309 | E_var:     0.3529 | E_err:   0.009282
[2025-10-07 05:56:16] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -43.596429 | E_var:     0.2716 | E_err:   0.008144
[2025-10-07 05:56:24] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -43.580574 | E_var:     0.2823 | E_err:   0.008302
[2025-10-07 05:56:32] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -43.598628 | E_var:     0.6675 | E_err:   0.012765
[2025-10-07 05:56:39] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -43.575825 | E_var:     0.8031 | E_err:   0.014002
[2025-10-07 05:56:47] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -43.595485 | E_var:     0.7140 | E_err:   0.013203
[2025-10-07 05:56:55] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -43.588166 | E_var:     0.3026 | E_err:   0.008595
[2025-10-07 05:57:03] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -43.588163 | E_var:     0.4139 | E_err:   0.010053
[2025-10-07 05:57:11] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -43.581558 | E_var:     0.4302 | E_err:   0.010249
[2025-10-07 05:57:18] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -43.590964 | E_var:     0.3276 | E_err:   0.008943
[2025-10-07 05:57:26] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -43.581916 | E_var:     0.2765 | E_err:   0.008216
[2025-10-07 05:57:34] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -43.593962 | E_var:     0.2305 | E_err:   0.007502
[2025-10-07 05:57:42] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -43.602956 | E_var:     0.2792 | E_err:   0.008256
[2025-10-07 05:57:50] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -43.586299 | E_var:     0.2681 | E_err:   0.008091
[2025-10-07 05:57:57] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -43.590913 | E_var:     0.2816 | E_err:   0.008292
[2025-10-07 05:58:05] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -43.582752 | E_var:     0.2971 | E_err:   0.008517
[2025-10-07 05:58:13] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -43.593231 | E_var:     0.2342 | E_err:   0.007561
[2025-10-07 05:58:21] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -43.581734 | E_var:     0.3820 | E_err:   0.009658
[2025-10-07 05:58:29] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -43.598028 | E_var:     0.3075 | E_err:   0.008664
[2025-10-07 05:58:36] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -43.590857 | E_var:     0.2756 | E_err:   0.008203
[2025-10-07 05:58:44] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -43.591556 | E_var:     0.3444 | E_err:   0.009169
[2025-10-07 05:58:52] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -43.587746 | E_var:     1.2600 | E_err:   0.017539
[2025-10-07 05:59:00] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -43.597091 | E_var:     0.2963 | E_err:   0.008506
[2025-10-07 05:59:08] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -43.587778 | E_var:     0.4066 | E_err:   0.009963
[2025-10-07 05:59:15] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -43.602860 | E_var:     0.2991 | E_err:   0.008546
[2025-10-07 05:59:23] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -43.572270 | E_var:     1.0486 | E_err:   0.016000
[2025-10-07 05:59:31] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -43.584144 | E_var:     0.2748 | E_err:   0.008190
[2025-10-07 05:59:39] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -43.594967 | E_var:     0.2694 | E_err:   0.008111
[2025-10-07 05:59:46] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -43.567306 | E_var:     0.3372 | E_err:   0.009073
[2025-10-07 05:59:54] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -43.598420 | E_var:     0.3034 | E_err:   0.008607
[2025-10-07 06:00:02] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -43.582201 | E_var:     0.2953 | E_err:   0.008491
[2025-10-07 06:00:10] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -43.594732 | E_var:     0.3380 | E_err:   0.009084
[2025-10-07 06:00:18] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -43.584426 | E_var:     0.3125 | E_err:   0.008735
[2025-10-07 06:00:25] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -43.589302 | E_var:     0.2659 | E_err:   0.008057
[2025-10-07 06:00:33] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -43.590505 | E_var:     0.3811 | E_err:   0.009645
[2025-10-07 06:00:41] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -43.595102 | E_var:     0.4935 | E_err:   0.010976
[2025-10-07 06:00:49] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -43.586792 | E_var:     0.2799 | E_err:   0.008267
[2025-10-07 06:00:57] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -43.598515 | E_var:     0.6081 | E_err:   0.012185
[2025-10-07 06:01:04] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -43.589619 | E_var:     0.3230 | E_err:   0.008881
[2025-10-07 06:01:12] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -43.601416 | E_var:     0.3320 | E_err:   0.009003
[2025-10-07 06:01:20] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -43.589711 | E_var:     0.3160 | E_err:   0.008784
[2025-10-07 06:01:28] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -43.591522 | E_var:     0.3177 | E_err:   0.008807
[2025-10-07 06:01:36] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -43.582272 | E_var:     0.4007 | E_err:   0.009891
[2025-10-07 06:01:43] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -43.587716 | E_var:     0.3361 | E_err:   0.009058
[2025-10-07 06:01:51] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -43.592270 | E_var:     0.2571 | E_err:   0.007923
[2025-10-07 06:01:59] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -43.584654 | E_var:     0.2942 | E_err:   0.008475
[2025-10-07 06:02:07] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -43.578966 | E_var:     0.3389 | E_err:   0.009097
[2025-10-07 06:02:15] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -43.575605 | E_var:     0.7187 | E_err:   0.013246
[2025-10-07 06:02:22] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -43.586916 | E_var:     0.3896 | E_err:   0.009753
[2025-10-07 06:02:30] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -43.591159 | E_var:     0.2684 | E_err:   0.008094
[2025-10-07 06:02:38] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -43.596233 | E_var:     0.4095 | E_err:   0.009999
[2025-10-07 06:02:46] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -43.577618 | E_var:     0.4216 | E_err:   0.010145
[2025-10-07 06:02:54] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -43.592484 | E_var:     0.2513 | E_err:   0.007833
[2025-10-07 06:03:01] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -43.602873 | E_var:     0.4514 | E_err:   0.010498
[2025-10-07 06:03:09] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -43.588130 | E_var:     0.3184 | E_err:   0.008817
[2025-10-07 06:03:17] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -43.585528 | E_var:     0.4161 | E_err:   0.010079
[2025-10-07 06:03:25] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -43.579216 | E_var:     0.6339 | E_err:   0.012440
[2025-10-07 06:03:33] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -43.585375 | E_var:     0.6482 | E_err:   0.012580
[2025-10-07 06:03:40] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -43.603923 | E_var:     0.5965 | E_err:   0.012068
[2025-10-07 06:03:48] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -43.585837 | E_var:     0.6913 | E_err:   0.012991
[2025-10-07 06:03:56] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -43.595648 | E_var:     0.3957 | E_err:   0.009829
[2025-10-07 06:04:04] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -43.595098 | E_var:     0.2536 | E_err:   0.007868
[2025-10-07 06:04:12] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -43.593581 | E_var:     0.3049 | E_err:   0.008628
[2025-10-07 06:04:19] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -43.592155 | E_var:     0.3479 | E_err:   0.009217
[2025-10-07 06:04:27] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -43.588512 | E_var:     0.3171 | E_err:   0.008799
[2025-10-07 06:04:35] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -43.594789 | E_var:     0.4055 | E_err:   0.009949
[2025-10-07 06:04:43] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -43.575861 | E_var:     0.3528 | E_err:   0.009280
[2025-10-07 06:04:51] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -43.587918 | E_var:     0.2720 | E_err:   0.008149
[2025-10-07 06:04:58] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -43.602580 | E_var:     0.3029 | E_err:   0.008600
[2025-10-07 06:05:06] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -43.600951 | E_var:     0.2910 | E_err:   0.008429
[2025-10-07 06:05:14] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -43.584765 | E_var:     0.3079 | E_err:   0.008670
[2025-10-07 06:05:22] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -43.601343 | E_var:     0.3290 | E_err:   0.008962
[2025-10-07 06:05:30] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -43.582006 | E_var:     0.3011 | E_err:   0.008574
[2025-10-07 06:05:37] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -43.592013 | E_var:     0.3062 | E_err:   0.008646
[2025-10-07 06:05:45] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -43.598938 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 06:05:45] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 06:05:53] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -43.602702 | E_var:     0.2875 | E_err:   0.008377
[2025-10-07 06:06:01] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -43.587953 | E_var:     0.3200 | E_err:   0.008839
[2025-10-07 06:06:09] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -43.590269 | E_var:     0.3388 | E_err:   0.009094
[2025-10-07 06:06:16] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -43.593565 | E_var:     0.3014 | E_err:   0.008578
[2025-10-07 06:06:24] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -43.583649 | E_var:     0.7402 | E_err:   0.013443
[2025-10-07 06:06:32] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -43.590902 | E_var:     0.3235 | E_err:   0.008887
[2025-10-07 06:06:40] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -43.602805 | E_var:     0.3741 | E_err:   0.009557
[2025-10-07 06:06:47] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -43.596029 | E_var:     0.2750 | E_err:   0.008194
[2025-10-07 06:06:55] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -43.605108 | E_var:     0.2853 | E_err:   0.008346
[2025-10-07 06:07:03] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -43.595892 | E_var:     0.3557 | E_err:   0.009319
[2025-10-07 06:07:11] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -43.578262 | E_var:     0.6608 | E_err:   0.012701
[2025-10-07 06:07:19] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -43.584628 | E_var:     0.4115 | E_err:   0.010023
[2025-10-07 06:07:26] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -43.589057 | E_var:     0.3766 | E_err:   0.009589
[2025-10-07 06:07:34] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -43.585995 | E_var:     0.3297 | E_err:   0.008972
[2025-10-07 06:07:42] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -43.599223 | E_var:     0.3199 | E_err:   0.008838
[2025-10-07 06:07:50] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -43.598773 | E_var:     0.3443 | E_err:   0.009169
[2025-10-07 06:07:58] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -43.599767 | E_var:     0.2413 | E_err:   0.007675
[2025-10-07 06:08:05] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -43.575764 | E_var:     0.4448 | E_err:   0.010421
[2025-10-07 06:08:13] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -43.568508 | E_var:     0.2928 | E_err:   0.008455
[2025-10-07 06:08:21] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -43.600328 | E_var:     0.2837 | E_err:   0.008322
[2025-10-07 06:08:29] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -43.602679 | E_var:     0.3095 | E_err:   0.008693
[2025-10-07 06:08:37] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -43.598263 | E_var:     0.3583 | E_err:   0.009353
[2025-10-07 06:08:44] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -43.596901 | E_var:     0.2727 | E_err:   0.008160
[2025-10-07 06:08:52] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -43.587066 | E_var:     0.4123 | E_err:   0.010033
[2025-10-07 06:09:00] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -43.583599 | E_var:     0.3076 | E_err:   0.008666
[2025-10-07 06:09:08] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -43.600897 | E_var:     0.2701 | E_err:   0.008121
[2025-10-07 06:09:16] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -43.598828 | E_var:     0.3722 | E_err:   0.009533
[2025-10-07 06:09:23] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -43.594279 | E_var:     0.3223 | E_err:   0.008870
[2025-10-07 06:09:31] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -43.577844 | E_var:     0.2789 | E_err:   0.008252
[2025-10-07 06:09:39] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -43.582569 | E_var:     0.7048 | E_err:   0.013118
[2025-10-07 06:09:47] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -43.592592 | E_var:     0.3515 | E_err:   0.009264
[2025-10-07 06:09:55] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -43.577584 | E_var:     0.3156 | E_err:   0.008779
[2025-10-07 06:10:02] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -43.557273 | E_var:     0.9292 | E_err:   0.015061
[2025-10-07 06:10:10] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -43.591747 | E_var:     0.3319 | E_err:   0.009002
[2025-10-07 06:10:18] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -43.579855 | E_var:     0.6004 | E_err:   0.012107
[2025-10-07 06:10:26] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -43.599279 | E_var:     0.2803 | E_err:   0.008273
[2025-10-07 06:10:34] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -43.579441 | E_var:     0.2681 | E_err:   0.008091
[2025-10-07 06:10:41] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -43.597846 | E_var:     0.3638 | E_err:   0.009424
[2025-10-07 06:10:49] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -43.568960 | E_var:     0.3514 | E_err:   0.009262
[2025-10-07 06:10:57] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -43.594480 | E_var:     0.3799 | E_err:   0.009631
[2025-10-07 06:11:05] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -43.597527 | E_var:     0.2535 | E_err:   0.007867
[2025-10-07 06:11:13] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -43.596772 | E_var:     0.3268 | E_err:   0.008932
[2025-10-07 06:11:20] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -43.602295 | E_var:     0.3750 | E_err:   0.009568
[2025-10-07 06:11:28] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -43.589590 | E_var:     0.4184 | E_err:   0.010107
[2025-10-07 06:11:36] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -43.589417 | E_var:     0.2440 | E_err:   0.007718
[2025-10-07 06:11:44] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -43.602367 | E_var:     0.2814 | E_err:   0.008288
[2025-10-07 06:11:52] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -43.601051 | E_var:     0.3285 | E_err:   0.008955
[2025-10-07 06:11:59] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -43.610856 | E_var:     0.3737 | E_err:   0.009551
[2025-10-07 06:12:07] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -43.584589 | E_var:     0.3554 | E_err:   0.009315
[2025-10-07 06:12:15] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -43.584857 | E_var:     0.2641 | E_err:   0.008030
[2025-10-07 06:12:23] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -43.601087 | E_var:     0.3178 | E_err:   0.008808
[2025-10-07 06:12:31] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -43.583640 | E_var:     0.2727 | E_err:   0.008160
[2025-10-07 06:12:38] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -43.590891 | E_var:     0.3694 | E_err:   0.009497
[2025-10-07 06:12:46] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -43.596764 | E_var:     0.3460 | E_err:   0.009191
[2025-10-07 06:12:54] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -43.584652 | E_var:     0.2613 | E_err:   0.007987
[2025-10-07 06:13:02] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -43.585143 | E_var:     0.2821 | E_err:   0.008299
[2025-10-07 06:13:10] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -43.599717 | E_var:     0.3989 | E_err:   0.009868
[2025-10-07 06:13:17] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -43.594014 | E_var:     0.2348 | E_err:   0.007571
[2025-10-07 06:13:25] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -43.589752 | E_var:     0.3410 | E_err:   0.009125
[2025-10-07 06:13:33] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -43.595461 | E_var:     0.2855 | E_err:   0.008349
[2025-10-07 06:13:41] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -43.588477 | E_var:     0.2892 | E_err:   0.008403
[2025-10-07 06:13:49] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -43.585890 | E_var:     0.2816 | E_err:   0.008292
[2025-10-07 06:13:56] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -43.591439 | E_var:     0.2798 | E_err:   0.008265
[2025-10-07 06:14:04] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -43.607695 | E_var:     0.2989 | E_err:   0.008542
[2025-10-07 06:14:12] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -43.593821 | E_var:     0.2395 | E_err:   0.007646
[2025-10-07 06:14:20] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -43.579767 | E_var:     0.3029 | E_err:   0.008600
[2025-10-07 06:14:28] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -43.594101 | E_var:     0.2674 | E_err:   0.008080
[2025-10-07 06:14:35] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -43.596277 | E_var:     0.2817 | E_err:   0.008292
[2025-10-07 06:14:43] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -43.597678 | E_var:     0.3238 | E_err:   0.008892
[2025-10-07 06:14:51] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -43.609889 | E_var:     0.3434 | E_err:   0.009156
[2025-10-07 06:14:59] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -43.605217 | E_var:     0.2907 | E_err:   0.008424
[2025-10-07 06:15:07] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -43.594183 | E_var:     0.2365 | E_err:   0.007598
[2025-10-07 06:15:14] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -43.580731 | E_var:     0.3566 | E_err:   0.009331
[2025-10-07 06:15:22] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -43.591325 | E_var:     0.3868 | E_err:   0.009718
[2025-10-07 06:15:30] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -43.593597 | E_var:     0.5960 | E_err:   0.012063
[2025-10-07 06:15:38] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -43.588383 | E_var:     0.4627 | E_err:   0.010628
[2025-10-07 06:15:46] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -43.598386 | E_var:     0.3411 | E_err:   0.009126
[2025-10-07 06:15:53] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -43.595875 | E_var:     0.7006 | E_err:   0.013078
[2025-10-07 06:16:01] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -43.598176 | E_var:     0.4323 | E_err:   0.010274
[2025-10-07 06:16:09] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -43.588195 | E_var:     0.2735 | E_err:   0.008171
[2025-10-07 06:16:17] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -43.590995 | E_var:     0.2723 | E_err:   0.008154
[2025-10-07 06:16:25] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -43.594366 | E_var:     0.2601 | E_err:   0.007969
[2025-10-07 06:16:32] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -43.590183 | E_var:     0.3387 | E_err:   0.009094
[2025-10-07 06:16:40] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -43.562849 | E_var:     0.6226 | E_err:   0.012329
[2025-10-07 06:16:48] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -43.597849 | E_var:     0.5115 | E_err:   0.011175
[2025-10-07 06:16:56] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -43.601113 | E_var:     0.2772 | E_err:   0.008227
[2025-10-07 06:17:04] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -43.589338 | E_var:     0.3204 | E_err:   0.008844
[2025-10-07 06:17:11] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -43.596847 | E_var:     0.3489 | E_err:   0.009230
[2025-10-07 06:17:19] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -43.590582 | E_var:     0.8533 | E_err:   0.014434
[2025-10-07 06:17:27] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -43.579392 | E_var:     0.2796 | E_err:   0.008262
[2025-10-07 06:17:35] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -43.599565 | E_var:     0.3523 | E_err:   0.009274
[2025-10-07 06:17:42] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -43.603171 | E_var:     0.2877 | E_err:   0.008380
[2025-10-07 06:17:50] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -43.589431 | E_var:     0.3198 | E_err:   0.008836
[2025-10-07 06:17:58] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -43.592339 | E_var:     0.2290 | E_err:   0.007478
[2025-10-07 06:18:06] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -43.581890 | E_var:     0.4969 | E_err:   0.011014
[2025-10-07 06:18:14] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -43.574944 | E_var:     1.0631 | E_err:   0.016111
[2025-10-07 06:18:21] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -43.601109 | E_var:     0.2688 | E_err:   0.008100
[2025-10-07 06:18:29] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -43.568566 | E_var:     0.2874 | E_err:   0.008376
[2025-10-07 06:18:37] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -43.597178 | E_var:     0.3495 | E_err:   0.009237
[2025-10-07 06:18:45] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -43.587380 | E_var:     0.3571 | E_err:   0.009337
[2025-10-07 06:18:45] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 06:18:53] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -43.596501 | E_var:     0.2699 | E_err:   0.008118
[2025-10-07 06:19:00] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -43.593448 | E_var:     0.3462 | E_err:   0.009193
[2025-10-07 06:19:08] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -43.606192 | E_var:     0.3082 | E_err:   0.008675
[2025-10-07 06:19:16] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -43.604221 | E_var:     0.3592 | E_err:   0.009364
[2025-10-07 06:19:24] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -43.588572 | E_var:     0.6274 | E_err:   0.012376
[2025-10-07 06:19:32] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -43.597022 | E_var:     0.2746 | E_err:   0.008188
[2025-10-07 06:19:39] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -43.581089 | E_var:     0.2896 | E_err:   0.008408
[2025-10-07 06:19:47] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -43.587146 | E_var:     0.3728 | E_err:   0.009540
[2025-10-07 06:19:55] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -43.580994 | E_var:     0.2768 | E_err:   0.008220
[2025-10-07 06:20:03] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -43.589207 | E_var:     0.4308 | E_err:   0.010256
[2025-10-07 06:20:11] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -43.595279 | E_var:     0.3272 | E_err:   0.008938
[2025-10-07 06:20:18] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -43.577590 | E_var:     0.6314 | E_err:   0.012416
[2025-10-07 06:20:26] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -43.580504 | E_var:     0.3439 | E_err:   0.009164
[2025-10-07 06:20:34] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -43.610588 | E_var:     0.4596 | E_err:   0.010593
[2025-10-07 06:20:42] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -43.601007 | E_var:     0.3198 | E_err:   0.008836
[2025-10-07 06:20:50] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -43.582763 | E_var:     0.3699 | E_err:   0.009502
[2025-10-07 06:20:57] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -43.592067 | E_var:     0.3000 | E_err:   0.008558
[2025-10-07 06:21:05] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -43.598938 | E_var:     0.3861 | E_err:   0.009708
[2025-10-07 06:21:13] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -43.584012 | E_var:     0.3073 | E_err:   0.008661
[2025-10-07 06:21:21] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -43.596392 | E_var:     0.2701 | E_err:   0.008120
[2025-10-07 06:21:29] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -43.603119 | E_var:     0.3877 | E_err:   0.009729
[2025-10-07 06:21:36] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -43.600718 | E_var:     0.4036 | E_err:   0.009927
[2025-10-07 06:21:44] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -43.589922 | E_var:     0.2882 | E_err:   0.008388
[2025-10-07 06:21:52] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -43.577613 | E_var:     0.3174 | E_err:   0.008802
[2025-10-07 06:22:00] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -43.605720 | E_var:     0.3517 | E_err:   0.009267
[2025-10-07 06:22:08] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -43.589470 | E_var:     0.2989 | E_err:   0.008543
[2025-10-07 06:22:15] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -43.587847 | E_var:     0.2538 | E_err:   0.007872
[2025-10-07 06:22:23] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -43.588450 | E_var:     0.2763 | E_err:   0.008214
[2025-10-07 06:22:31] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -43.597639 | E_var:     0.3093 | E_err:   0.008689
[2025-10-07 06:22:39] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -43.589876 | E_var:     0.3395 | E_err:   0.009104
[2025-10-07 06:22:47] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -43.601148 | E_var:     0.3190 | E_err:   0.008825
[2025-10-07 06:22:54] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -43.582635 | E_var:     0.3544 | E_err:   0.009302
[2025-10-07 06:23:02] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -43.580812 | E_var:     0.3188 | E_err:   0.008823
[2025-10-07 06:23:10] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -43.592653 | E_var:     0.5145 | E_err:   0.011208
[2025-10-07 06:23:18] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -43.588262 | E_var:     0.3675 | E_err:   0.009472
[2025-10-07 06:23:26] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -43.595091 | E_var:     0.2692 | E_err:   0.008107
[2025-10-07 06:23:33] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -43.583898 | E_var:     0.6908 | E_err:   0.012986
[2025-10-07 06:23:41] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -43.614133 | E_var:     0.3703 | E_err:   0.009508
[2025-10-07 06:23:49] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -43.596455 | E_var:     0.2430 | E_err:   0.007702
[2025-10-07 06:23:57] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -43.582480 | E_var:     0.3269 | E_err:   0.008934
[2025-10-07 06:24:04] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -43.589616 | E_var:     0.3866 | E_err:   0.009716
[2025-10-07 06:24:12] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -43.596697 | E_var:     0.2814 | E_err:   0.008288
[2025-10-07 06:24:20] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -43.594886 | E_var:     0.5516 | E_err:   0.011605
[2025-10-07 06:24:28] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -43.592172 | E_var:     0.2505 | E_err:   0.007820
[2025-10-07 06:24:36] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -43.589180 | E_var:     0.3108 | E_err:   0.008711
[2025-10-07 06:24:43] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -43.602011 | E_var:     0.3117 | E_err:   0.008723
[2025-10-07 06:24:51] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -43.577319 | E_var:     0.3135 | E_err:   0.008748
[2025-10-07 06:24:59] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -43.586434 | E_var:     0.4411 | E_err:   0.010377
[2025-10-07 06:25:07] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -43.572534 | E_var:     0.3905 | E_err:   0.009763
[2025-10-07 06:25:15] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -43.582672 | E_var:     0.4136 | E_err:   0.010048
[2025-10-07 06:25:15] 🔄 RESTART #2 | Period: 600
[2025-10-07 06:25:22] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -43.587472 | E_var:     0.6776 | E_err:   0.012862
[2025-10-07 06:25:30] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -43.617045 | E_var:     0.3150 | E_err:   0.008769
[2025-10-07 06:25:38] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -43.592960 | E_var:     0.3147 | E_err:   0.008766
[2025-10-07 06:25:46] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -43.603616 | E_var:     0.4675 | E_err:   0.010684
[2025-10-07 06:25:54] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -43.583024 | E_var:     0.3599 | E_err:   0.009373
[2025-10-07 06:26:01] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -43.596046 | E_var:     0.2752 | E_err:   0.008197
[2025-10-07 06:26:09] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -43.584642 | E_var:     0.7513 | E_err:   0.013543
[2025-10-07 06:26:17] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -43.589425 | E_var:     0.3268 | E_err:   0.008932
[2025-10-07 06:26:25] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -43.588958 | E_var:     0.3111 | E_err:   0.008714
[2025-10-07 06:26:33] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -43.592996 | E_var:     0.2339 | E_err:   0.007557
[2025-10-07 06:26:40] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -43.597493 | E_var:     0.2491 | E_err:   0.007799
[2025-10-07 06:26:48] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -43.575705 | E_var:     0.3708 | E_err:   0.009514
[2025-10-07 06:26:56] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -43.589355 | E_var:     0.2717 | E_err:   0.008145
[2025-10-07 06:27:04] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -43.622676 | E_var:     1.1486 | E_err:   0.016745
[2025-10-07 06:27:12] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -43.583612 | E_var:     0.3331 | E_err:   0.009018
[2025-10-07 06:27:19] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -43.590590 | E_var:     0.2754 | E_err:   0.008200
[2025-10-07 06:27:27] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -43.587890 | E_var:     0.2894 | E_err:   0.008405
[2025-10-07 06:27:35] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -43.581438 | E_var:     0.4418 | E_err:   0.010386
[2025-10-07 06:27:43] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -43.593960 | E_var:     0.2699 | E_err:   0.008117
[2025-10-07 06:27:51] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -43.574020 | E_var:     0.6215 | E_err:   0.012318
[2025-10-07 06:27:58] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -43.586142 | E_var:     0.3093 | E_err:   0.008690
[2025-10-07 06:28:06] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -43.593892 | E_var:     0.3878 | E_err:   0.009730
[2025-10-07 06:28:14] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -43.586103 | E_var:     0.2860 | E_err:   0.008357
[2025-10-07 06:28:22] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -43.579369 | E_var:     0.2456 | E_err:   0.007744
[2025-10-07 06:28:30] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -43.602463 | E_var:     0.2799 | E_err:   0.008267
[2025-10-07 06:28:37] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -43.591337 | E_var:     0.3054 | E_err:   0.008635
[2025-10-07 06:28:45] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -43.602723 | E_var:     0.2905 | E_err:   0.008422
[2025-10-07 06:28:53] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -43.599423 | E_var:     0.2525 | E_err:   0.007852
[2025-10-07 06:29:01] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -43.581069 | E_var:     0.2808 | E_err:   0.008280
[2025-10-07 06:29:09] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -43.586471 | E_var:     0.2404 | E_err:   0.007661
[2025-10-07 06:29:16] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -43.596251 | E_var:     0.3839 | E_err:   0.009681
[2025-10-07 06:29:24] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -43.587279 | E_var:     0.2646 | E_err:   0.008038
[2025-10-07 06:29:32] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -43.590421 | E_var:     0.3487 | E_err:   0.009227
[2025-10-07 06:29:40] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -43.590343 | E_var:     0.3417 | E_err:   0.009133
[2025-10-07 06:29:48] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -43.608700 | E_var:     0.3034 | E_err:   0.008606
[2025-10-07 06:29:55] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -43.594194 | E_var:     0.2578 | E_err:   0.007934
[2025-10-07 06:30:03] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -43.570839 | E_var:     0.2483 | E_err:   0.007786
[2025-10-07 06:30:11] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -43.608283 | E_var:     0.3436 | E_err:   0.009159
[2025-10-07 06:30:19] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -43.573983 | E_var:     1.1221 | E_err:   0.016552
[2025-10-07 06:30:27] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -43.591391 | E_var:     0.2889 | E_err:   0.008398
[2025-10-07 06:30:34] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -43.591071 | E_var:     0.2870 | E_err:   0.008371
[2025-10-07 06:30:42] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -43.580406 | E_var:     0.3630 | E_err:   0.009414
[2025-10-07 06:30:50] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -43.596124 | E_var:     0.2733 | E_err:   0.008169
[2025-10-07 06:30:58] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -43.570961 | E_var:     0.4140 | E_err:   0.010053
[2025-10-07 06:31:06] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -43.590935 | E_var:     0.3826 | E_err:   0.009664
[2025-10-07 06:31:13] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -43.587401 | E_var:     0.2418 | E_err:   0.007684
[2025-10-07 06:31:21] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -43.597164 | E_var:     0.3764 | E_err:   0.009586
[2025-10-07 06:31:29] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -43.583035 | E_var:     0.3462 | E_err:   0.009194
[2025-10-07 06:31:37] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -43.593543 | E_var:     0.2492 | E_err:   0.007800
[2025-10-07 06:31:45] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -43.586797 | E_var:     0.3044 | E_err:   0.008621
[2025-10-07 06:31:45] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 06:31:52] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -43.587917 | E_var:     0.2472 | E_err:   0.007768
[2025-10-07 06:32:00] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -43.589938 | E_var:     0.2859 | E_err:   0.008355
[2025-10-07 06:32:08] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -43.575829 | E_var:     0.2746 | E_err:   0.008188
[2025-10-07 06:32:16] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -43.588395 | E_var:     0.2976 | E_err:   0.008523
[2025-10-07 06:32:24] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -43.580603 | E_var:     0.2513 | E_err:   0.007833
[2025-10-07 06:32:31] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -43.592806 | E_var:     0.2838 | E_err:   0.008323
[2025-10-07 06:32:39] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -43.595560 | E_var:     0.3323 | E_err:   0.009008
[2025-10-07 06:32:47] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -43.581518 | E_var:     1.3403 | E_err:   0.018089
[2025-10-07 06:32:55] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -43.592064 | E_var:     0.3041 | E_err:   0.008617
[2025-10-07 06:33:03] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -43.593168 | E_var:     0.3407 | E_err:   0.009120
[2025-10-07 06:33:10] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -43.603501 | E_var:     0.3935 | E_err:   0.009802
[2025-10-07 06:33:18] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -43.585448 | E_var:     0.3250 | E_err:   0.008908
[2025-10-07 06:33:26] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -43.601441 | E_var:     0.3067 | E_err:   0.008654
[2025-10-07 06:33:34] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -43.575999 | E_var:     1.8043 | E_err:   0.020988
[2025-10-07 06:33:41] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -43.597027 | E_var:     0.2513 | E_err:   0.007832
[2025-10-07 06:33:49] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -43.593306 | E_var:     0.2450 | E_err:   0.007734
[2025-10-07 06:33:57] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -43.595020 | E_var:     0.2606 | E_err:   0.007976
[2025-10-07 06:34:05] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -43.587738 | E_var:     0.2682 | E_err:   0.008092
[2025-10-07 06:34:13] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -43.599671 | E_var:     0.3343 | E_err:   0.009034
[2025-10-07 06:34:20] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -43.577927 | E_var:     0.3165 | E_err:   0.008790
[2025-10-07 06:34:28] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -43.597824 | E_var:     0.2927 | E_err:   0.008454
[2025-10-07 06:34:36] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -43.589777 | E_var:     0.2703 | E_err:   0.008123
[2025-10-07 06:34:44] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -43.598716 | E_var:     0.3285 | E_err:   0.008955
[2025-10-07 06:34:52] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -43.579574 | E_var:     0.2747 | E_err:   0.008189
[2025-10-07 06:34:59] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -43.584569 | E_var:     0.3187 | E_err:   0.008821
[2025-10-07 06:35:07] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -43.592096 | E_var:     0.3816 | E_err:   0.009652
[2025-10-07 06:35:15] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -43.590879 | E_var:     0.2898 | E_err:   0.008411
[2025-10-07 06:35:23] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -43.583648 | E_var:     0.2576 | E_err:   0.007931
[2025-10-07 06:35:31] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -43.590747 | E_var:     0.2908 | E_err:   0.008426
[2025-10-07 06:35:38] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -43.593365 | E_var:     0.3509 | E_err:   0.009256
[2025-10-07 06:35:46] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -43.605417 | E_var:     0.3275 | E_err:   0.008942
[2025-10-07 06:35:54] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -43.608512 | E_var:     0.2581 | E_err:   0.007939
[2025-10-07 06:36:02] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -43.602091 | E_var:     0.2506 | E_err:   0.007822
[2025-10-07 06:36:10] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -43.589991 | E_var:     0.3033 | E_err:   0.008605
[2025-10-07 06:36:17] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -43.590407 | E_var:     0.2419 | E_err:   0.007685
[2025-10-07 06:36:25] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -43.593899 | E_var:     0.3018 | E_err:   0.008584
[2025-10-07 06:36:33] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -43.579531 | E_var:     0.3367 | E_err:   0.009066
[2025-10-07 06:36:41] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -43.574236 | E_var:     0.3849 | E_err:   0.009694
[2025-10-07 06:36:49] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -43.584536 | E_var:     0.3756 | E_err:   0.009575
[2025-10-07 06:36:56] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -43.597105 | E_var:     0.2848 | E_err:   0.008339
[2025-10-07 06:37:04] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -43.606023 | E_var:     0.3420 | E_err:   0.009138
[2025-10-07 06:37:12] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -43.574250 | E_var:     0.5760 | E_err:   0.011859
[2025-10-07 06:37:20] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -43.576761 | E_var:     0.2969 | E_err:   0.008514
[2025-10-07 06:37:28] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -43.603947 | E_var:     0.2697 | E_err:   0.008114
[2025-10-07 06:37:35] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -43.584878 | E_var:     0.2746 | E_err:   0.008188
[2025-10-07 06:37:43] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -43.583697 | E_var:     0.2886 | E_err:   0.008393
[2025-10-07 06:37:51] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -43.591503 | E_var:     0.3869 | E_err:   0.009719
[2025-10-07 06:37:59] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -43.588087 | E_var:     0.2578 | E_err:   0.007933
[2025-10-07 06:38:07] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -43.595283 | E_var:     0.3115 | E_err:   0.008720
[2025-10-07 06:38:14] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -43.565405 | E_var:     0.3628 | E_err:   0.009411
[2025-10-07 06:38:22] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -43.612923 | E_var:     0.2726 | E_err:   0.008159
[2025-10-07 06:38:30] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -43.589273 | E_var:     0.2903 | E_err:   0.008419
[2025-10-07 06:38:38] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -43.589045 | E_var:     0.3228 | E_err:   0.008877
[2025-10-07 06:38:46] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -43.611806 | E_var:     0.3012 | E_err:   0.008575
[2025-10-07 06:38:53] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -43.592143 | E_var:     0.3011 | E_err:   0.008574
[2025-10-07 06:39:01] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -43.594602 | E_var:     0.2667 | E_err:   0.008070
[2025-10-07 06:39:09] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -43.606781 | E_var:     0.3420 | E_err:   0.009137
[2025-10-07 06:39:17] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -43.599343 | E_var:     0.2640 | E_err:   0.008028
[2025-10-07 06:39:25] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -43.589672 | E_var:     0.2705 | E_err:   0.008127
[2025-10-07 06:39:32] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -43.600962 | E_var:     0.2956 | E_err:   0.008495
[2025-10-07 06:39:40] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -43.592737 | E_var:     0.2969 | E_err:   0.008514
[2025-10-07 06:39:48] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -43.611250 | E_var:     0.5605 | E_err:   0.011698
[2025-10-07 06:39:56] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -43.594340 | E_var:     0.2926 | E_err:   0.008451
[2025-10-07 06:40:03] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -43.587434 | E_var:     0.3360 | E_err:   0.009057
[2025-10-07 06:40:11] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -43.590858 | E_var:     0.3946 | E_err:   0.009815
[2025-10-07 06:40:19] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -43.598064 | E_var:     0.3510 | E_err:   0.009257
[2025-10-07 06:40:27] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -43.599195 | E_var:     0.3094 | E_err:   0.008691
[2025-10-07 06:40:35] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -43.591161 | E_var:     0.3357 | E_err:   0.009053
[2025-10-07 06:40:42] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -43.588217 | E_var:     0.3724 | E_err:   0.009535
[2025-10-07 06:40:50] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -43.600304 | E_var:     0.2990 | E_err:   0.008544
[2025-10-07 06:40:58] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -43.582065 | E_var:     0.2944 | E_err:   0.008477
[2025-10-07 06:41:06] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -43.592135 | E_var:     0.2970 | E_err:   0.008516
[2025-10-07 06:41:14] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -43.594156 | E_var:     0.3053 | E_err:   0.008634
[2025-10-07 06:41:21] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -43.605956 | E_var:     0.3311 | E_err:   0.008990
[2025-10-07 06:41:29] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -43.598626 | E_var:     0.3623 | E_err:   0.009404
[2025-10-07 06:41:37] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -43.603521 | E_var:     0.6231 | E_err:   0.012334
[2025-10-07 06:41:45] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -43.603130 | E_var:     0.2673 | E_err:   0.008078
[2025-10-07 06:41:53] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -43.581899 | E_var:     0.3378 | E_err:   0.009082
[2025-10-07 06:42:00] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -43.597280 | E_var:     0.3084 | E_err:   0.008676
[2025-10-07 06:42:08] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -43.595590 | E_var:     0.3027 | E_err:   0.008596
[2025-10-07 06:42:16] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -43.578922 | E_var:     0.3517 | E_err:   0.009266
[2025-10-07 06:42:24] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -43.589170 | E_var:     0.3877 | E_err:   0.009729
[2025-10-07 06:42:32] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -43.603362 | E_var:     0.3688 | E_err:   0.009489
[2025-10-07 06:42:39] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -43.604999 | E_var:     0.3267 | E_err:   0.008931
[2025-10-07 06:42:47] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -43.579275 | E_var:     0.3165 | E_err:   0.008791
[2025-10-07 06:42:55] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -43.612302 | E_var:     0.4281 | E_err:   0.010223
[2025-10-07 06:43:03] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -43.577188 | E_var:     0.2765 | E_err:   0.008216
[2025-10-07 06:43:11] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -43.577378 | E_var:     0.3025 | E_err:   0.008594
[2025-10-07 06:43:18] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -43.601419 | E_var:     0.3292 | E_err:   0.008965
[2025-10-07 06:43:26] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -43.606550 | E_var:     0.3541 | E_err:   0.009298
[2025-10-07 06:43:34] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -43.588965 | E_var:     0.5188 | E_err:   0.011254
[2025-10-07 06:43:42] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -43.588132 | E_var:     0.2609 | E_err:   0.007981
[2025-10-07 06:43:50] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -43.597771 | E_var:     0.2328 | E_err:   0.007539
[2025-10-07 06:43:57] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -43.611317 | E_var:     0.2721 | E_err:   0.008151
[2025-10-07 06:44:05] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -43.582347 | E_var:     0.3345 | E_err:   0.009037
[2025-10-07 06:44:13] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -43.600152 | E_var:     0.3795 | E_err:   0.009625
[2025-10-07 06:44:21] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -43.589977 | E_var:     0.2663 | E_err:   0.008063
[2025-10-07 06:44:29] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -43.588888 | E_var:     0.3323 | E_err:   0.009007
[2025-10-07 06:44:36] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -43.588270 | E_var:     0.4786 | E_err:   0.010809
[2025-10-07 06:44:44] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -43.588589 | E_var:     0.3266 | E_err:   0.008930
[2025-10-07 06:44:44] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 06:44:52] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -43.577614 | E_var:     0.4019 | E_err:   0.009905
[2025-10-07 06:45:00] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -43.585954 | E_var:     0.3246 | E_err:   0.008903
[2025-10-07 06:45:08] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -43.574284 | E_var:     0.2532 | E_err:   0.007863
[2025-10-07 06:45:15] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -43.599479 | E_var:     0.2497 | E_err:   0.007808
[2025-10-07 06:45:23] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -43.577906 | E_var:     0.2594 | E_err:   0.007958
[2025-10-07 06:45:31] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -43.592491 | E_var:     0.2849 | E_err:   0.008340
[2025-10-07 06:45:39] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -43.598623 | E_var:     0.3207 | E_err:   0.008848
[2025-10-07 06:45:47] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -43.588840 | E_var:     0.2600 | E_err:   0.007967
[2025-10-07 06:45:54] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -43.603163 | E_var:     0.3385 | E_err:   0.009091
[2025-10-07 06:46:02] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -43.602693 | E_var:     0.2752 | E_err:   0.008197
[2025-10-07 06:46:10] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -43.597622 | E_var:     0.5532 | E_err:   0.011621
[2025-10-07 06:46:18] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -43.587620 | E_var:     0.3915 | E_err:   0.009776
[2025-10-07 06:46:26] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -43.585837 | E_var:     0.3019 | E_err:   0.008586
[2025-10-07 06:46:33] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -43.601629 | E_var:     0.3103 | E_err:   0.008703
[2025-10-07 06:46:41] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -43.604619 | E_var:     0.2522 | E_err:   0.007847
[2025-10-07 06:46:49] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -43.599449 | E_var:     0.2992 | E_err:   0.008546
[2025-10-07 06:46:57] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -43.602346 | E_var:     0.2520 | E_err:   0.007844
[2025-10-07 06:47:05] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -43.588856 | E_var:     0.3304 | E_err:   0.008982
[2025-10-07 06:47:12] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -43.593752 | E_var:     0.2967 | E_err:   0.008511
[2025-10-07 06:47:20] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -43.581197 | E_var:     0.2913 | E_err:   0.008433
[2025-10-07 06:47:28] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -43.599999 | E_var:     0.3965 | E_err:   0.009838
[2025-10-07 06:47:36] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -43.588272 | E_var:     0.3141 | E_err:   0.008757
[2025-10-07 06:47:44] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -43.594709 | E_var:     0.2730 | E_err:   0.008164
[2025-10-07 06:47:51] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -43.605871 | E_var:     0.3409 | E_err:   0.009123
[2025-10-07 06:47:59] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -43.588612 | E_var:     0.2612 | E_err:   0.007986
[2025-10-07 06:48:07] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -43.598363 | E_var:     0.2658 | E_err:   0.008055
[2025-10-07 06:48:15] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -43.589371 | E_var:     0.2723 | E_err:   0.008153
[2025-10-07 06:48:23] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -43.599699 | E_var:     0.3240 | E_err:   0.008894
[2025-10-07 06:48:30] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -43.607529 | E_var:     0.2941 | E_err:   0.008473
[2025-10-07 06:48:38] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -43.603366 | E_var:     0.5970 | E_err:   0.012073
[2025-10-07 06:48:46] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -43.582926 | E_var:     0.3585 | E_err:   0.009355
[2025-10-07 06:48:54] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -43.602073 | E_var:     0.3215 | E_err:   0.008860
[2025-10-07 06:49:01] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -43.604774 | E_var:     0.3115 | E_err:   0.008720
[2025-10-07 06:49:09] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -43.595473 | E_var:     0.3043 | E_err:   0.008620
[2025-10-07 06:49:17] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -43.604833 | E_var:     0.2916 | E_err:   0.008438
[2025-10-07 06:49:25] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -43.581340 | E_var:     0.2779 | E_err:   0.008237
[2025-10-07 06:49:33] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -43.588142 | E_var:     0.2969 | E_err:   0.008514
[2025-10-07 06:49:40] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -43.593404 | E_var:     0.3848 | E_err:   0.009693
[2025-10-07 06:49:48] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -43.599352 | E_var:     0.4047 | E_err:   0.009940
[2025-10-07 06:49:56] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -43.588134 | E_var:     0.3018 | E_err:   0.008584
[2025-10-07 06:50:04] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -43.603995 | E_var:     0.2853 | E_err:   0.008346
[2025-10-07 06:50:12] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -43.584412 | E_var:     0.3720 | E_err:   0.009530
[2025-10-07 06:50:19] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -43.593383 | E_var:     0.3329 | E_err:   0.009015
[2025-10-07 06:50:27] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -43.588255 | E_var:     0.2504 | E_err:   0.007819
[2025-10-07 06:50:35] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -43.600218 | E_var:     0.3122 | E_err:   0.008731
[2025-10-07 06:50:43] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -43.599475 | E_var:     0.2749 | E_err:   0.008193
[2025-10-07 06:50:51] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -43.595862 | E_var:     0.2810 | E_err:   0.008283
[2025-10-07 06:50:58] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -43.588708 | E_var:     0.2498 | E_err:   0.007809
[2025-10-07 06:51:06] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -43.594645 | E_var:     0.3036 | E_err:   0.008610
[2025-10-07 06:51:14] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -43.590722 | E_var:     0.2266 | E_err:   0.007437
[2025-10-07 06:51:22] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -43.601773 | E_var:     0.2910 | E_err:   0.008429
[2025-10-07 06:51:30] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -43.607763 | E_var:     0.2994 | E_err:   0.008550
[2025-10-07 06:51:37] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -43.589135 | E_var:     0.2379 | E_err:   0.007621
[2025-10-07 06:51:45] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -43.604565 | E_var:     0.2814 | E_err:   0.008288
[2025-10-07 06:51:53] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -43.600874 | E_var:     0.2754 | E_err:   0.008200
[2025-10-07 06:52:01] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -43.594376 | E_var:     0.3274 | E_err:   0.008940
[2025-10-07 06:52:09] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -43.580520 | E_var:     0.2628 | E_err:   0.008011
[2025-10-07 06:52:16] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -43.583336 | E_var:     0.2327 | E_err:   0.007538
[2025-10-07 06:52:24] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -43.591409 | E_var:     0.3596 | E_err:   0.009370
[2025-10-07 06:52:32] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -43.578598 | E_var:     0.3551 | E_err:   0.009311
[2025-10-07 06:52:40] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -43.593604 | E_var:     0.3636 | E_err:   0.009422
[2025-10-07 06:52:48] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -43.598970 | E_var:     0.2512 | E_err:   0.007831
[2025-10-07 06:52:55] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -43.575552 | E_var:     0.3197 | E_err:   0.008835
[2025-10-07 06:53:03] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -43.584136 | E_var:     0.3085 | E_err:   0.008679
[2025-10-07 06:53:11] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -43.601890 | E_var:     0.2956 | E_err:   0.008495
[2025-10-07 06:53:19] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -43.596132 | E_var:     0.2912 | E_err:   0.008431
[2025-10-07 06:53:27] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -43.596061 | E_var:     0.3291 | E_err:   0.008964
[2025-10-07 06:53:34] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -43.585144 | E_var:     0.3490 | E_err:   0.009231
[2025-10-07 06:53:42] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -43.598441 | E_var:     0.2764 | E_err:   0.008215
[2025-10-07 06:53:50] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -43.601912 | E_var:     0.3078 | E_err:   0.008668
[2025-10-07 06:53:58] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -43.592962 | E_var:     0.3216 | E_err:   0.008860
[2025-10-07 06:54:06] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -43.591468 | E_var:     0.2530 | E_err:   0.007860
[2025-10-07 06:54:13] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -43.609265 | E_var:     0.3372 | E_err:   0.009073
[2025-10-07 06:54:21] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -43.599449 | E_var:     0.2856 | E_err:   0.008350
[2025-10-07 06:54:29] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -43.582176 | E_var:     0.2949 | E_err:   0.008485
[2025-10-07 06:54:37] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -43.590619 | E_var:     0.2555 | E_err:   0.007898
[2025-10-07 06:54:45] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -43.584133 | E_var:     0.2517 | E_err:   0.007838
[2025-10-07 06:54:52] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -43.597729 | E_var:     0.4126 | E_err:   0.010037
[2025-10-07 06:55:00] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -43.598428 | E_var:     0.3220 | E_err:   0.008866
[2025-10-07 06:55:08] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -43.582324 | E_var:     0.3073 | E_err:   0.008662
[2025-10-07 06:55:16] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -43.600661 | E_var:     0.3423 | E_err:   0.009142
[2025-10-07 06:55:24] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -43.602760 | E_var:     0.3525 | E_err:   0.009277
[2025-10-07 06:55:31] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -43.587775 | E_var:     0.5250 | E_err:   0.011322
[2025-10-07 06:55:39] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -43.580743 | E_var:     0.2635 | E_err:   0.008020
[2025-10-07 06:55:47] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -43.597392 | E_var:     0.7189 | E_err:   0.013248
[2025-10-07 06:55:55] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -43.574329 | E_var:     0.3222 | E_err:   0.008869
[2025-10-07 06:56:03] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -43.587581 | E_var:     0.3217 | E_err:   0.008862
[2025-10-07 06:56:10] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -43.592221 | E_var:     0.2618 | E_err:   0.007995
[2025-10-07 06:56:18] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -43.595009 | E_var:     0.3125 | E_err:   0.008735
[2025-10-07 06:56:26] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -43.596839 | E_var:     0.2451 | E_err:   0.007736
[2025-10-07 06:56:34] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -43.599873 | E_var:     0.2869 | E_err:   0.008370
[2025-10-07 06:56:42] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -43.580403 | E_var:     0.3379 | E_err:   0.009083
[2025-10-07 06:56:49] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -43.604233 | E_var:     0.2515 | E_err:   0.007835
[2025-10-07 06:56:57] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -43.590668 | E_var:     0.2955 | E_err:   0.008494
[2025-10-07 06:57:05] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -43.598653 | E_var:     0.2570 | E_err:   0.007922
[2025-10-07 06:57:13] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -43.594884 | E_var:     0.3285 | E_err:   0.008955
[2025-10-07 06:57:21] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -43.592215 | E_var:     0.3184 | E_err:   0.008817
[2025-10-07 06:57:28] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -43.573088 | E_var:     0.2847 | E_err:   0.008336
[2025-10-07 06:57:36] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -43.574061 | E_var:     0.9923 | E_err:   0.015565
[2025-10-07 06:57:44] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -43.591350 | E_var:     0.3271 | E_err:   0.008937
[2025-10-07 06:57:44] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 06:57:52] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -43.596270 | E_var:     0.2921 | E_err:   0.008444
[2025-10-07 06:58:00] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -43.605218 | E_var:     0.3853 | E_err:   0.009699
[2025-10-07 06:58:07] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -43.583856 | E_var:     0.2823 | E_err:   0.008301
[2025-10-07 06:58:15] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -43.598953 | E_var:     0.2795 | E_err:   0.008261
[2025-10-07 06:58:23] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -43.611220 | E_var:     0.3255 | E_err:   0.008914
[2025-10-07 06:58:31] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -43.602533 | E_var:     0.2886 | E_err:   0.008394
[2025-10-07 06:58:39] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -43.595092 | E_var:     0.2671 | E_err:   0.008075
[2025-10-07 06:58:46] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -43.591481 | E_var:     0.3201 | E_err:   0.008840
[2025-10-07 06:58:54] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -43.603351 | E_var:     0.3337 | E_err:   0.009026
[2025-10-07 06:59:02] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -43.583891 | E_var:     0.2917 | E_err:   0.008439
[2025-10-07 06:59:10] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -43.586140 | E_var:     0.2516 | E_err:   0.007838
[2025-10-07 06:59:18] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -43.595411 | E_var:     0.3596 | E_err:   0.009370
[2025-10-07 06:59:25] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -43.600955 | E_var:     0.2915 | E_err:   0.008436
[2025-10-07 06:59:33] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -43.608506 | E_var:     0.2913 | E_err:   0.008432
[2025-10-07 06:59:41] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -43.603363 | E_var:     0.3005 | E_err:   0.008566
[2025-10-07 06:59:49] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -43.606705 | E_var:     0.3178 | E_err:   0.008808
[2025-10-07 06:59:57] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -43.596565 | E_var:     0.2430 | E_err:   0.007702
[2025-10-07 07:00:04] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -43.604433 | E_var:     0.2395 | E_err:   0.007647
[2025-10-07 07:00:12] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -43.599667 | E_var:     0.3584 | E_err:   0.009355
[2025-10-07 07:00:20] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -43.587669 | E_var:     0.3875 | E_err:   0.009727
[2025-10-07 07:00:28] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -43.575761 | E_var:     1.1497 | E_err:   0.016754
[2025-10-07 07:00:36] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -43.585890 | E_var:     0.3214 | E_err:   0.008859
[2025-10-07 07:00:43] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -43.591527 | E_var:     0.3139 | E_err:   0.008754
[2025-10-07 07:00:51] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -43.590208 | E_var:     0.3433 | E_err:   0.009155
[2025-10-07 07:00:59] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -43.591902 | E_var:     0.2944 | E_err:   0.008478
[2025-10-07 07:01:07] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -43.602594 | E_var:     0.3622 | E_err:   0.009404
[2025-10-07 07:01:14] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -43.594753 | E_var:     0.3666 | E_err:   0.009461
[2025-10-07 07:01:22] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -43.591460 | E_var:     0.3305 | E_err:   0.008982
[2025-10-07 07:01:30] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -43.602661 | E_var:     0.3007 | E_err:   0.008568
[2025-10-07 07:01:38] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -43.598556 | E_var:     0.3444 | E_err:   0.009169
[2025-10-07 07:01:46] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -43.594543 | E_var:     0.3364 | E_err:   0.009062
[2025-10-07 07:01:53] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -43.599502 | E_var:     0.2721 | E_err:   0.008150
[2025-10-07 07:02:01] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -43.595678 | E_var:     0.2555 | E_err:   0.007898
[2025-10-07 07:02:09] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -43.600981 | E_var:     0.2560 | E_err:   0.007906
[2025-10-07 07:02:17] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -43.591972 | E_var:     0.2881 | E_err:   0.008387
[2025-10-07 07:02:25] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -43.611410 | E_var:     0.2703 | E_err:   0.008124
[2025-10-07 07:02:32] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -43.599277 | E_var:     0.5190 | E_err:   0.011256
[2025-10-07 07:02:40] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -43.592554 | E_var:     0.2705 | E_err:   0.008127
[2025-10-07 07:02:48] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -43.596631 | E_var:     0.2935 | E_err:   0.008464
[2025-10-07 07:02:56] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -43.614681 | E_var:     0.2862 | E_err:   0.008359
[2025-10-07 07:03:04] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -43.593366 | E_var:     0.3109 | E_err:   0.008712
[2025-10-07 07:03:11] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -43.597656 | E_var:     0.3038 | E_err:   0.008612
[2025-10-07 07:03:19] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -43.593588 | E_var:     0.2533 | E_err:   0.007864
[2025-10-07 07:03:27] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -43.613348 | E_var:     0.4278 | E_err:   0.010219
[2025-10-07 07:03:35] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -43.604514 | E_var:     0.3449 | E_err:   0.009176
[2025-10-07 07:03:43] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -43.595246 | E_var:     0.2914 | E_err:   0.008434
[2025-10-07 07:03:50] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -43.603610 | E_var:     0.3599 | E_err:   0.009373
[2025-10-07 07:03:58] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -43.600851 | E_var:     0.3164 | E_err:   0.008789
[2025-10-07 07:04:06] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -43.603608 | E_var:     0.2943 | E_err:   0.008476
[2025-10-07 07:04:14] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -43.604444 | E_var:     0.2391 | E_err:   0.007640
[2025-10-07 07:04:22] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -43.603116 | E_var:     0.2487 | E_err:   0.007793
[2025-10-07 07:04:29] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -43.610184 | E_var:     0.4012 | E_err:   0.009897
[2025-10-07 07:04:37] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -43.609710 | E_var:     0.3184 | E_err:   0.008817
[2025-10-07 07:04:45] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -43.599417 | E_var:     0.3121 | E_err:   0.008729
[2025-10-07 07:04:53] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -43.591519 | E_var:     0.2367 | E_err:   0.007603
[2025-10-07 07:05:01] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -43.597839 | E_var:     0.2995 | E_err:   0.008551
[2025-10-07 07:05:08] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -43.595156 | E_var:     0.2600 | E_err:   0.007967
[2025-10-07 07:05:16] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -43.597372 | E_var:     0.3555 | E_err:   0.009316
[2025-10-07 07:05:24] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -43.613633 | E_var:     0.3162 | E_err:   0.008786
[2025-10-07 07:05:32] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -43.595884 | E_var:     0.2842 | E_err:   0.008330
[2025-10-07 07:05:40] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -43.599651 | E_var:     0.2514 | E_err:   0.007835
[2025-10-07 07:05:47] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -43.595257 | E_var:     0.4085 | E_err:   0.009986
[2025-10-07 07:05:55] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -43.596637 | E_var:     0.2649 | E_err:   0.008043
[2025-10-07 07:06:03] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -43.591814 | E_var:     0.2538 | E_err:   0.007871
[2025-10-07 07:06:11] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -43.594309 | E_var:     0.2883 | E_err:   0.008389
[2025-10-07 07:06:19] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -43.596947 | E_var:     0.2819 | E_err:   0.008296
[2025-10-07 07:06:26] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -43.589497 | E_var:     0.2624 | E_err:   0.008004
[2025-10-07 07:06:34] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -43.615597 | E_var:     0.2667 | E_err:   0.008069
[2025-10-07 07:06:42] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -43.591786 | E_var:     0.2640 | E_err:   0.008029
[2025-10-07 07:06:50] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -43.599822 | E_var:     0.2856 | E_err:   0.008350
[2025-10-07 07:06:58] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -43.590443 | E_var:     0.4033 | E_err:   0.009923
[2025-10-07 07:07:05] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -43.600132 | E_var:     0.2581 | E_err:   0.007939
[2025-10-07 07:07:13] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -43.570303 | E_var:     0.5022 | E_err:   0.011073
[2025-10-07 07:07:21] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -43.595231 | E_var:     0.3883 | E_err:   0.009736
[2025-10-07 07:07:29] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -43.586217 | E_var:     0.2396 | E_err:   0.007648
[2025-10-07 07:07:37] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -43.601754 | E_var:     0.2611 | E_err:   0.007984
[2025-10-07 07:07:44] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -43.592109 | E_var:     0.2871 | E_err:   0.008372
[2025-10-07 07:07:52] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -43.588161 | E_var:     0.3102 | E_err:   0.008702
[2025-10-07 07:08:00] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -43.607804 | E_var:     0.2939 | E_err:   0.008471
[2025-10-07 07:08:08] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -43.590806 | E_var:     0.2872 | E_err:   0.008374
[2025-10-07 07:08:16] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -43.603953 | E_var:     0.3029 | E_err:   0.008599
[2025-10-07 07:08:23] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -43.599169 | E_var:     0.3759 | E_err:   0.009580
[2025-10-07 07:08:31] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -43.583286 | E_var:     0.4932 | E_err:   0.010973
[2025-10-07 07:08:39] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -43.594748 | E_var:     0.2828 | E_err:   0.008309
[2025-10-07 07:08:47] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -43.597598 | E_var:     0.3311 | E_err:   0.008991
[2025-10-07 07:08:55] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -43.610481 | E_var:     0.2966 | E_err:   0.008510
[2025-10-07 07:09:02] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -43.597318 | E_var:     0.2822 | E_err:   0.008301
[2025-10-07 07:09:10] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -43.612664 | E_var:     0.2903 | E_err:   0.008418
[2025-10-07 07:09:18] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -43.602574 | E_var:     0.2500 | E_err:   0.007813
[2025-10-07 07:09:26] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -43.584379 | E_var:     0.3449 | E_err:   0.009177
[2025-10-07 07:09:34] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -43.598684 | E_var:     0.3034 | E_err:   0.008606
[2025-10-07 07:09:41] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -43.604012 | E_var:     0.3480 | E_err:   0.009217
[2025-10-07 07:09:49] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -43.581779 | E_var:     0.4429 | E_err:   0.010399
[2025-10-07 07:09:57] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -43.587141 | E_var:     0.3127 | E_err:   0.008737
[2025-10-07 07:10:05] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -43.595468 | E_var:     0.3379 | E_err:   0.009082
[2025-10-07 07:10:12] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -43.586684 | E_var:     0.2662 | E_err:   0.008061
[2025-10-07 07:10:20] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -43.570553 | E_var:     0.3884 | E_err:   0.009738
[2025-10-07 07:10:28] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -43.589037 | E_var:     0.2737 | E_err:   0.008175
[2025-10-07 07:10:36] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -43.590335 | E_var:     0.2682 | E_err:   0.008092
[2025-10-07 07:10:44] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -43.601158 | E_var:     0.3124 | E_err:   0.008733
[2025-10-07 07:10:44] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 07:10:52] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -43.598012 | E_var:     0.2887 | E_err:   0.008396
[2025-10-07 07:10:59] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -43.601408 | E_var:     0.2507 | E_err:   0.007824
[2025-10-07 07:11:07] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -43.603003 | E_var:     0.4090 | E_err:   0.009993
[2025-10-07 07:11:15] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -43.587969 | E_var:     0.3910 | E_err:   0.009771
[2025-10-07 07:11:23] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -43.605014 | E_var:     0.2494 | E_err:   0.007803
[2025-10-07 07:11:31] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -43.601987 | E_var:     0.2578 | E_err:   0.007933
[2025-10-07 07:11:38] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -43.585927 | E_var:     0.3224 | E_err:   0.008872
[2025-10-07 07:11:46] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -43.591813 | E_var:     0.2389 | E_err:   0.007637
[2025-10-07 07:11:54] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -43.597744 | E_var:     0.3285 | E_err:   0.008956
[2025-10-07 07:12:02] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -43.597908 | E_var:     0.2922 | E_err:   0.008447
[2025-10-07 07:12:10] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -43.602479 | E_var:     0.3058 | E_err:   0.008641
[2025-10-07 07:12:17] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -43.611539 | E_var:     0.2574 | E_err:   0.007927
[2025-10-07 07:12:25] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -43.607219 | E_var:     0.2883 | E_err:   0.008389
[2025-10-07 07:12:33] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -43.601717 | E_var:     1.2821 | E_err:   0.017692
[2025-10-07 07:12:41] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -43.601343 | E_var:     0.3129 | E_err:   0.008741
[2025-10-07 07:12:49] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -43.599156 | E_var:     0.7457 | E_err:   0.013493
[2025-10-07 07:12:56] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -43.602755 | E_var:     0.4193 | E_err:   0.010118
[2025-10-07 07:13:04] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -43.592839 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 07:13:12] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -43.603194 | E_var:     0.3026 | E_err:   0.008595
[2025-10-07 07:13:20] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -43.596685 | E_var:     0.2807 | E_err:   0.008278
[2025-10-07 07:13:28] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -43.591665 | E_var:     0.3547 | E_err:   0.009306
[2025-10-07 07:13:35] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -43.605211 | E_var:     0.2537 | E_err:   0.007869
[2025-10-07 07:13:43] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -43.600727 | E_var:     0.2651 | E_err:   0.008046
[2025-10-07 07:13:51] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -43.592021 | E_var:     0.3208 | E_err:   0.008850
[2025-10-07 07:13:59] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -43.593449 | E_var:     0.2353 | E_err:   0.007579
[2025-10-07 07:14:07] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -43.601369 | E_var:     0.2860 | E_err:   0.008356
[2025-10-07 07:14:14] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -43.600755 | E_var:     0.2521 | E_err:   0.007846
[2025-10-07 07:14:22] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -43.608971 | E_var:     0.3468 | E_err:   0.009201
[2025-10-07 07:14:30] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -43.599366 | E_var:     0.2864 | E_err:   0.008361
[2025-10-07 07:14:38] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -43.610754 | E_var:     0.3750 | E_err:   0.009568
[2025-10-07 07:14:46] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -43.584986 | E_var:     0.2450 | E_err:   0.007734
[2025-10-07 07:14:53] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -43.574288 | E_var:     0.2725 | E_err:   0.008157
[2025-10-07 07:15:01] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -43.584162 | E_var:     0.2929 | E_err:   0.008456
[2025-10-07 07:15:09] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -43.594837 | E_var:     0.2294 | E_err:   0.007484
[2025-10-07 07:15:17] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -43.598491 | E_var:     0.2859 | E_err:   0.008354
[2025-10-07 07:15:25] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -43.590349 | E_var:     0.2470 | E_err:   0.007766
[2025-10-07 07:15:32] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -43.599641 | E_var:     0.2421 | E_err:   0.007688
[2025-10-07 07:15:40] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -43.579339 | E_var:     0.4149 | E_err:   0.010064
[2025-10-07 07:15:48] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -43.592019 | E_var:     0.3973 | E_err:   0.009849
[2025-10-07 07:15:56] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -43.589952 | E_var:     0.3807 | E_err:   0.009640
[2025-10-07 07:16:03] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -43.585632 | E_var:     0.2325 | E_err:   0.007534
[2025-10-07 07:16:11] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -43.589023 | E_var:     0.3756 | E_err:   0.009576
[2025-10-07 07:16:19] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -43.596848 | E_var:     0.3048 | E_err:   0.008626
[2025-10-07 07:16:27] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -43.594813 | E_var:     0.2717 | E_err:   0.008145
[2025-10-07 07:16:35] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -43.595583 | E_var:     0.2865 | E_err:   0.008363
[2025-10-07 07:16:42] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -43.589182 | E_var:     0.3299 | E_err:   0.008974
[2025-10-07 07:16:50] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -43.586410 | E_var:     0.2905 | E_err:   0.008421
[2025-10-07 07:16:58] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -43.589459 | E_var:     0.3193 | E_err:   0.008829
[2025-10-07 07:17:06] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -43.582299 | E_var:     0.3837 | E_err:   0.009679
[2025-10-07 07:17:14] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -43.584431 | E_var:     0.2574 | E_err:   0.007928
[2025-10-07 07:17:21] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -43.583431 | E_var:     0.4118 | E_err:   0.010027
[2025-10-07 07:17:29] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -43.597075 | E_var:     0.2172 | E_err:   0.007283
[2025-10-07 07:17:37] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -43.592601 | E_var:     0.3196 | E_err:   0.008834
[2025-10-07 07:17:45] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -43.596663 | E_var:     0.2404 | E_err:   0.007660
[2025-10-07 07:17:53] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -43.602511 | E_var:     0.3651 | E_err:   0.009441
[2025-10-07 07:18:00] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -43.597198 | E_var:     0.2532 | E_err:   0.007862
[2025-10-07 07:18:08] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -43.587121 | E_var:     0.3324 | E_err:   0.009009
[2025-10-07 07:18:16] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -43.588670 | E_var:     0.2692 | E_err:   0.008107
[2025-10-07 07:18:24] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -43.584079 | E_var:     0.3075 | E_err:   0.008665
[2025-10-07 07:18:32] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -43.610019 | E_var:     0.2813 | E_err:   0.008287
[2025-10-07 07:18:39] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -43.602100 | E_var:     0.3808 | E_err:   0.009642
[2025-10-07 07:18:47] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -43.615757 | E_var:     0.3143 | E_err:   0.008760
[2025-10-07 07:18:55] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -43.591620 | E_var:     0.2696 | E_err:   0.008113
[2025-10-07 07:19:03] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -43.607100 | E_var:     0.2730 | E_err:   0.008165
[2025-10-07 07:19:11] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -43.593432 | E_var:     0.2479 | E_err:   0.007780
[2025-10-07 07:19:18] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -43.595368 | E_var:     0.4898 | E_err:   0.010935
[2025-10-07 07:19:26] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -43.598143 | E_var:     0.3078 | E_err:   0.008668
[2025-10-07 07:19:34] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -43.596773 | E_var:     0.3296 | E_err:   0.008970
[2025-10-07 07:19:42] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -43.588899 | E_var:     0.2700 | E_err:   0.008119
[2025-10-07 07:19:50] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -43.604935 | E_var:     0.3139 | E_err:   0.008754
[2025-10-07 07:19:57] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -43.603412 | E_var:     0.2545 | E_err:   0.007882
[2025-10-07 07:20:05] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -43.600278 | E_var:     0.2939 | E_err:   0.008471
[2025-10-07 07:20:13] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -43.597441 | E_var:     0.2642 | E_err:   0.008031
[2025-10-07 07:20:21] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -43.598438 | E_var:     0.2888 | E_err:   0.008397
[2025-10-07 07:20:28] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -43.591578 | E_var:     0.2881 | E_err:   0.008386
[2025-10-07 07:20:36] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -43.609338 | E_var:     1.3316 | E_err:   0.018031
[2025-10-07 07:20:44] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -43.577762 | E_var:     0.2860 | E_err:   0.008356
[2025-10-07 07:20:52] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -43.600150 | E_var:     0.2915 | E_err:   0.008436
[2025-10-07 07:21:00] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -43.597290 | E_var:     0.2833 | E_err:   0.008317
[2025-10-07 07:21:07] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -43.584464 | E_var:     0.4007 | E_err:   0.009890
[2025-10-07 07:21:15] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -43.585115 | E_var:     0.2459 | E_err:   0.007748
[2025-10-07 07:21:23] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -43.608256 | E_var:     0.3464 | E_err:   0.009196
[2025-10-07 07:21:31] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -43.601978 | E_var:     0.2921 | E_err:   0.008444
[2025-10-07 07:21:39] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -43.594554 | E_var:     0.2939 | E_err:   0.008471
[2025-10-07 07:21:46] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -43.598563 | E_var:     0.3366 | E_err:   0.009065
[2025-10-07 07:21:54] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -43.601359 | E_var:     0.2942 | E_err:   0.008475
[2025-10-07 07:22:02] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -43.601641 | E_var:     0.2750 | E_err:   0.008194
[2025-10-07 07:22:10] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -43.619232 | E_var:     0.3387 | E_err:   0.009093
[2025-10-07 07:22:18] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -43.613345 | E_var:     0.4158 | E_err:   0.010075
[2025-10-07 07:22:25] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -43.604042 | E_var:     0.2292 | E_err:   0.007480
[2025-10-07 07:22:33] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -43.581120 | E_var:     0.2439 | E_err:   0.007717
[2025-10-07 07:22:41] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -43.602648 | E_var:     0.2616 | E_err:   0.007992
[2025-10-07 07:22:49] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -43.595466 | E_var:     0.3769 | E_err:   0.009592
[2025-10-07 07:22:57] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -43.588689 | E_var:     0.3115 | E_err:   0.008720
[2025-10-07 07:23:04] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -43.602446 | E_var:     0.2776 | E_err:   0.008233
[2025-10-07 07:23:12] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -43.596338 | E_var:     0.4046 | E_err:   0.009938
[2025-10-07 07:23:20] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -43.581505 | E_var:     0.2811 | E_err:   0.008285
[2025-10-07 07:23:28] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -43.587072 | E_var:     0.3470 | E_err:   0.009204
[2025-10-07 07:23:36] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -43.598685 | E_var:     0.2618 | E_err:   0.007995
[2025-10-07 07:23:43] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -43.612325 | E_var:     0.4964 | E_err:   0.011008
[2025-10-07 07:23:43] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 07:23:51] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -43.586996 | E_var:     0.2664 | E_err:   0.008065
[2025-10-07 07:23:59] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -43.600377 | E_var:     0.2571 | E_err:   0.007923
[2025-10-07 07:24:07] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -43.593484 | E_var:     0.3568 | E_err:   0.009333
[2025-10-07 07:24:15] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -43.609020 | E_var:     0.2798 | E_err:   0.008265
[2025-10-07 07:24:22] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -43.582291 | E_var:     0.6040 | E_err:   0.012143
[2025-10-07 07:24:30] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -43.595753 | E_var:     0.2868 | E_err:   0.008367
[2025-10-07 07:24:38] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -43.593599 | E_var:     0.2950 | E_err:   0.008487
[2025-10-07 07:24:46] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -43.604677 | E_var:     0.2997 | E_err:   0.008554
[2025-10-07 07:24:54] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -43.605185 | E_var:     0.2809 | E_err:   0.008281
[2025-10-07 07:25:01] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -43.592194 | E_var:     0.2790 | E_err:   0.008254
[2025-10-07 07:25:09] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -43.590554 | E_var:     0.2606 | E_err:   0.007976
[2025-10-07 07:25:17] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -43.600523 | E_var:     0.2624 | E_err:   0.008004
[2025-10-07 07:25:25] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -43.596722 | E_var:     0.4251 | E_err:   0.010187
[2025-10-07 07:25:33] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -43.593334 | E_var:     0.2649 | E_err:   0.008043
[2025-10-07 07:25:40] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -43.580856 | E_var:     0.2651 | E_err:   0.008045
[2025-10-07 07:25:48] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -43.588354 | E_var:     0.2808 | E_err:   0.008280
[2025-10-07 07:25:56] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -43.607341 | E_var:     0.2255 | E_err:   0.007420
[2025-10-07 07:26:04] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -43.604417 | E_var:     0.3495 | E_err:   0.009237
[2025-10-07 07:26:12] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -43.591614 | E_var:     0.2396 | E_err:   0.007648
[2025-10-07 07:26:19] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -43.602629 | E_var:     0.2816 | E_err:   0.008292
[2025-10-07 07:26:27] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -43.605326 | E_var:     0.3107 | E_err:   0.008710
[2025-10-07 07:26:35] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -43.606699 | E_var:     0.3578 | E_err:   0.009347
[2025-10-07 07:26:43] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -43.600986 | E_var:     0.2905 | E_err:   0.008422
[2025-10-07 07:26:51] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -43.607806 | E_var:     0.2946 | E_err:   0.008481
[2025-10-07 07:26:58] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -43.594860 | E_var:     0.2126 | E_err:   0.007204
[2025-10-07 07:27:06] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -43.598460 | E_var:     0.2699 | E_err:   0.008118
[2025-10-07 07:27:14] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -43.594589 | E_var:     0.2736 | E_err:   0.008173
[2025-10-07 07:27:22] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -43.582786 | E_var:     0.2889 | E_err:   0.008399
[2025-10-07 07:27:29] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -43.604438 | E_var:     0.2418 | E_err:   0.007683
[2025-10-07 07:27:37] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -43.605667 | E_var:     0.3716 | E_err:   0.009524
[2025-10-07 07:27:45] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -43.584553 | E_var:     0.3965 | E_err:   0.009839
[2025-10-07 07:27:53] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -43.599402 | E_var:     0.2539 | E_err:   0.007873
[2025-10-07 07:28:01] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -43.581167 | E_var:     0.2729 | E_err:   0.008162
[2025-10-07 07:28:08] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -43.580049 | E_var:     0.3863 | E_err:   0.009711
[2025-10-07 07:28:16] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -43.588591 | E_var:     0.2568 | E_err:   0.007919
[2025-10-07 07:28:24] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -43.594515 | E_var:     0.2497 | E_err:   0.007807
[2025-10-07 07:28:32] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -43.593679 | E_var:     0.2565 | E_err:   0.007913
[2025-10-07 07:28:40] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -43.595827 | E_var:     0.2493 | E_err:   0.007802
[2025-10-07 07:28:47] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -43.605961 | E_var:     0.3042 | E_err:   0.008618
[2025-10-07 07:28:55] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -43.601000 | E_var:     0.3490 | E_err:   0.009230
[2025-10-07 07:29:03] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -43.589832 | E_var:     0.2987 | E_err:   0.008539
[2025-10-07 07:29:11] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -43.587387 | E_var:     0.2899 | E_err:   0.008414
[2025-10-07 07:29:19] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -43.593080 | E_var:     0.3234 | E_err:   0.008886
[2025-10-07 07:29:26] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -43.595525 | E_var:     0.4848 | E_err:   0.010879
[2025-10-07 07:29:34] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -43.603229 | E_var:     0.2705 | E_err:   0.008126
[2025-10-07 07:29:42] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -43.590319 | E_var:     0.3268 | E_err:   0.008932
[2025-10-07 07:29:50] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -43.597608 | E_var:     0.2845 | E_err:   0.008334
[2025-10-07 07:29:58] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -43.596462 | E_var:     0.3907 | E_err:   0.009766
[2025-10-07 07:30:05] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -43.614580 | E_var:     0.3044 | E_err:   0.008620
[2025-10-07 07:30:13] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -43.595897 | E_var:     0.2494 | E_err:   0.007804
[2025-10-07 07:30:21] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -43.587271 | E_var:     0.2963 | E_err:   0.008505
[2025-10-07 07:30:29] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -43.612748 | E_var:     0.5141 | E_err:   0.011203
[2025-10-07 07:30:37] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -43.598610 | E_var:     0.3648 | E_err:   0.009437
[2025-10-07 07:30:44] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -43.602193 | E_var:     0.3295 | E_err:   0.008969
[2025-10-07 07:30:52] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -43.606919 | E_var:     0.3296 | E_err:   0.008970
[2025-10-07 07:31:00] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -43.604754 | E_var:     0.2768 | E_err:   0.008220
[2025-10-07 07:31:08] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -43.587808 | E_var:     0.3553 | E_err:   0.009314
[2025-10-07 07:31:16] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -43.587266 | E_var:     0.3126 | E_err:   0.008736
[2025-10-07 07:31:23] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -43.578224 | E_var:     0.3152 | E_err:   0.008773
[2025-10-07 07:31:31] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -43.598906 | E_var:     0.2705 | E_err:   0.008126
[2025-10-07 07:31:39] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -43.594006 | E_var:     0.2424 | E_err:   0.007693
[2025-10-07 07:31:47] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -43.600398 | E_var:     0.3099 | E_err:   0.008699
[2025-10-07 07:31:55] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -43.592652 | E_var:     0.2765 | E_err:   0.008216
[2025-10-07 07:32:02] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -43.599761 | E_var:     0.2489 | E_err:   0.007795
[2025-10-07 07:32:10] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -43.603763 | E_var:     0.3195 | E_err:   0.008833
[2025-10-07 07:32:18] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -43.613180 | E_var:     0.2644 | E_err:   0.008034
[2025-10-07 07:32:26] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -43.600569 | E_var:     0.2950 | E_err:   0.008487
[2025-10-07 07:32:34] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -43.598885 | E_var:     0.4738 | E_err:   0.010756
[2025-10-07 07:32:41] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -43.586147 | E_var:     0.2643 | E_err:   0.008033
[2025-10-07 07:32:49] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -43.591511 | E_var:     0.4248 | E_err:   0.010184
[2025-10-07 07:32:57] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -43.596328 | E_var:     0.2721 | E_err:   0.008150
[2025-10-07 07:33:05] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -43.608841 | E_var:     0.2632 | E_err:   0.008016
[2025-10-07 07:33:12] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -43.589280 | E_var:     0.2381 | E_err:   0.007625
[2025-10-07 07:33:20] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -43.586815 | E_var:     0.2619 | E_err:   0.007997
[2025-10-07 07:33:28] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -43.597281 | E_var:     0.2582 | E_err:   0.007939
[2025-10-07 07:33:36] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -43.589107 | E_var:     0.2343 | E_err:   0.007563
[2025-10-07 07:33:44] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -43.609456 | E_var:     0.3548 | E_err:   0.009307
[2025-10-07 07:33:51] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -43.586538 | E_var:     0.6983 | E_err:   0.013057
[2025-10-07 07:33:59] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -43.605074 | E_var:     0.2932 | E_err:   0.008461
[2025-10-07 07:34:07] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -43.577027 | E_var:     0.5498 | E_err:   0.011586
[2025-10-07 07:34:15] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -43.588583 | E_var:     0.2504 | E_err:   0.007819
[2025-10-07 07:34:23] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -43.585487 | E_var:     0.3080 | E_err:   0.008672
[2025-10-07 07:34:30] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -43.604479 | E_var:     0.2438 | E_err:   0.007715
[2025-10-07 07:34:38] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -43.582614 | E_var:     0.3652 | E_err:   0.009442
[2025-10-07 07:34:46] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -43.569100 | E_var:     0.4195 | E_err:   0.010120
[2025-10-07 07:34:54] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -43.604343 | E_var:     0.2670 | E_err:   0.008074
[2025-10-07 07:35:02] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -43.595681 | E_var:     0.2298 | E_err:   0.007490
[2025-10-07 07:35:09] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -43.602990 | E_var:     0.2632 | E_err:   0.008016
[2025-10-07 07:35:17] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -43.615256 | E_var:     0.2131 | E_err:   0.007213
[2025-10-07 07:35:25] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -43.581720 | E_var:     0.8100 | E_err:   0.014062
[2025-10-07 07:35:33] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -43.602090 | E_var:     0.2529 | E_err:   0.007857
[2025-10-07 07:35:41] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -43.600180 | E_var:     0.4415 | E_err:   0.010382
[2025-10-07 07:35:48] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -43.613017 | E_var:     0.3064 | E_err:   0.008649
[2025-10-07 07:35:56] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -43.595109 | E_var:     0.3220 | E_err:   0.008866
[2025-10-07 07:36:04] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -43.589535 | E_var:     0.4277 | E_err:   0.010219
[2025-10-07 07:36:12] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -43.598298 | E_var:     0.3842 | E_err:   0.009685
[2025-10-07 07:36:20] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -43.597148 | E_var:     0.3056 | E_err:   0.008638
[2025-10-07 07:36:27] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -43.601538 | E_var:     0.3445 | E_err:   0.009172
[2025-10-07 07:36:35] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -43.585485 | E_var:     0.2469 | E_err:   0.007764
[2025-10-07 07:36:43] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -43.581673 | E_var:     0.3518 | E_err:   0.009268
[2025-10-07 07:36:43] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 07:36:51] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -43.601422 | E_var:     0.3555 | E_err:   0.009317
[2025-10-07 07:36:59] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -43.585005 | E_var:     0.4327 | E_err:   0.010278
[2025-10-07 07:37:06] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -43.597074 | E_var:     0.3114 | E_err:   0.008719
[2025-10-07 07:37:14] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -43.596568 | E_var:     0.2412 | E_err:   0.007674
[2025-10-07 07:37:22] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -43.602255 | E_var:     0.2840 | E_err:   0.008327
[2025-10-07 07:37:30] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -43.597706 | E_var:     0.3743 | E_err:   0.009560
[2025-10-07 07:37:38] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -43.591403 | E_var:     0.3244 | E_err:   0.008899
[2025-10-07 07:37:45] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -43.596694 | E_var:     0.2819 | E_err:   0.008296
[2025-10-07 07:37:53] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -43.592851 | E_var:     0.2430 | E_err:   0.007702
[2025-10-07 07:38:01] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -43.583208 | E_var:     0.5399 | E_err:   0.011481
[2025-10-07 07:38:09] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -43.612799 | E_var:     0.3224 | E_err:   0.008871
[2025-10-07 07:38:17] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -43.603117 | E_var:     0.2468 | E_err:   0.007762
[2025-10-07 07:38:24] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -43.592115 | E_var:     0.3583 | E_err:   0.009353
[2025-10-07 07:38:32] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -43.595988 | E_var:     0.3029 | E_err:   0.008599
[2025-10-07 07:38:40] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -43.589987 | E_var:     0.3511 | E_err:   0.009258
[2025-10-07 07:38:48] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -43.602406 | E_var:     0.3330 | E_err:   0.009016
[2025-10-07 07:38:56] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -43.595181 | E_var:     0.3833 | E_err:   0.009674
[2025-10-07 07:39:03] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -43.611189 | E_var:     0.2830 | E_err:   0.008312
[2025-10-07 07:39:11] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -43.587573 | E_var:     0.4506 | E_err:   0.010488
[2025-10-07 07:39:19] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -43.590359 | E_var:     0.4104 | E_err:   0.010009
[2025-10-07 07:39:27] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -43.599114 | E_var:     0.2795 | E_err:   0.008260
[2025-10-07 07:39:35] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -43.600226 | E_var:     0.3071 | E_err:   0.008659
[2025-10-07 07:39:42] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -43.607292 | E_var:     0.2595 | E_err:   0.007959
[2025-10-07 07:39:50] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -43.589574 | E_var:     0.3384 | E_err:   0.009089
[2025-10-07 07:39:58] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -43.589596 | E_var:     0.2204 | E_err:   0.007335
[2025-10-07 07:40:06] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -43.603104 | E_var:     0.3561 | E_err:   0.009324
[2025-10-07 07:40:14] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -43.603288 | E_var:     0.2837 | E_err:   0.008322
[2025-10-07 07:40:21] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -43.598369 | E_var:     0.2300 | E_err:   0.007494
[2025-10-07 07:40:29] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -43.594897 | E_var:     0.2761 | E_err:   0.008210
[2025-10-07 07:40:37] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -43.589178 | E_var:     2.0069 | E_err:   0.022135
[2025-10-07 07:40:45] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -43.613328 | E_var:     0.2437 | E_err:   0.007714
[2025-10-07 07:40:53] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -43.602739 | E_var:     0.3275 | E_err:   0.008941
[2025-10-07 07:41:00] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -43.586740 | E_var:     0.2515 | E_err:   0.007835
[2025-10-07 07:41:08] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -43.600627 | E_var:     0.3436 | E_err:   0.009159
[2025-10-07 07:41:16] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -43.596483 | E_var:     0.2704 | E_err:   0.008125
[2025-10-07 07:41:24] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -43.596370 | E_var:     0.2795 | E_err:   0.008260
[2025-10-07 07:41:32] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -43.595191 | E_var:     0.2303 | E_err:   0.007498
[2025-10-07 07:41:39] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -43.595668 | E_var:     0.3085 | E_err:   0.008679
[2025-10-07 07:41:47] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -43.601097 | E_var:     0.3110 | E_err:   0.008714
[2025-10-07 07:41:55] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -43.597555 | E_var:     0.3067 | E_err:   0.008654
[2025-10-07 07:42:03] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -43.603218 | E_var:     0.2984 | E_err:   0.008536
[2025-10-07 07:42:10] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -43.593738 | E_var:     0.3395 | E_err:   0.009104
[2025-10-07 07:42:18] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -43.587314 | E_var:     0.2820 | E_err:   0.008298
[2025-10-07 07:42:26] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -43.604697 | E_var:     0.2826 | E_err:   0.008306
[2025-10-07 07:42:34] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -43.597815 | E_var:     0.2344 | E_err:   0.007564
[2025-10-07 07:42:42] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -43.609002 | E_var:     0.3583 | E_err:   0.009353
[2025-10-07 07:42:49] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -43.603176 | E_var:     0.2376 | E_err:   0.007616
[2025-10-07 07:42:57] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -43.589054 | E_var:     0.2996 | E_err:   0.008552
[2025-10-07 07:43:05] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -43.583085 | E_var:     0.3951 | E_err:   0.009822
[2025-10-07 07:43:13] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -43.591799 | E_var:     0.2046 | E_err:   0.007068
[2025-10-07 07:43:13] ======================================================================================================
[2025-10-07 07:43:13] ✅ Training completed successfully
[2025-10-07 07:43:13] Total restarts: 2
[2025-10-07 07:43:15] Final Energy: -43.59179883 ± 0.00706816
[2025-10-07 07:43:15] Final Variance: 0.204632
[2025-10-07 07:43:15] ======================================================================================================
[2025-10-07 07:43:15] ======================================================================================================
[2025-10-07 07:43:15] Training completed | Runtime: 8242.1s
[2025-10-07 07:43:18] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 07:43:18] ======================================================================================================
