[2025-10-06 09:08:25] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.78/model_L4F4/training/checkpoints/final_GCNN.pkl
[2025-10-06 09:08:25]   - 迭代次数: final
[2025-10-06 09:08:26]   - 能量: -43.603305+0.000883j ± 0.008219, Var: 0.276719
[2025-10-06 09:08:26]   - 时间戳: 2025-10-06T09:08:15.082954+08:00
[2025-10-06 09:08:45] ✓ 变分状态参数已从checkpoint恢复
[2025-10-06 09:08:45] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-06 09:08:45] ======================================================================================================
[2025-10-06 09:08:45] GCNN for Shastry-Sutherland Model
[2025-10-06 09:08:45] ======================================================================================================
[2025-10-06 09:08:45] System parameters:
[2025-10-06 09:08:45]   - System size: L=5, N=100
[2025-10-06 09:08:45]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-10-06 09:08:45] ------------------------------------------------------------------------------------------------------
[2025-10-06 09:08:45] Model parameters:
[2025-10-06 09:08:45]   - Number of layers = 4
[2025-10-06 09:08:45]   - Number of features = 4
[2025-10-06 09:08:45]   - Total parameters = 19628
[2025-10-06 09:08:45] ------------------------------------------------------------------------------------------------------
[2025-10-06 09:08:45] Training parameters:
[2025-10-06 09:08:45]   - Total iterations: 1050
[2025-10-06 09:08:45]   - Annealing cycles: 3
[2025-10-06 09:08:45]   - Initial period: 150
[2025-10-06 09:08:45]   - Period multiplier: 2.0
[2025-10-06 09:08:45]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-06 09:08:45]   - Samples: 4096
[2025-10-06 09:08:45]   - Discarded samples: 0
[2025-10-06 09:08:45]   - Chunk size: 4096
[2025-10-06 09:08:45]   - Diagonal shift: 0.15
[2025-10-06 09:08:45]   - Gradient clipping: 1.0
[2025-10-06 09:08:45]   - Checkpoint enabled: interval=100
[2025-10-06 09:08:45]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.79/model_L4F4/training/checkpoints
[2025-10-06 09:08:45] ------------------------------------------------------------------------------------------------------
[2025-10-06 09:08:45] Device status:
[2025-10-06 09:08:45]   - Devices model: NVIDIA H200 NVL
[2025-10-06 09:08:45]   - Number of devices: 1
[2025-10-06 09:08:45]   - Sharding: True
[2025-10-06 09:08:45] ======================================================================================================
[2025-10-06 09:09:16] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -37.827863 | E_var:   402.9702 | E_err:   0.313658
[2025-10-06 09:09:36] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -43.871071 | E_var:    23.1586 | E_err:   0.075193
[2025-10-06 09:09:41] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -44.234203 | E_var:     0.3870 | E_err:   0.009720
[2025-10-06 09:09:46] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -44.235992 | E_var:     0.4545 | E_err:   0.010534
[2025-10-06 09:09:51] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -44.240614 | E_var:     0.3118 | E_err:   0.008725
[2025-10-06 09:09:56] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -44.246070 | E_var:     0.3075 | E_err:   0.008664
[2025-10-06 09:10:02] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -44.228934 | E_var:     0.2833 | E_err:   0.008316
[2025-10-06 09:10:07] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -44.235984 | E_var:     0.3144 | E_err:   0.008761
[2025-10-06 09:10:12] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -44.244024 | E_var:     0.2641 | E_err:   0.008030
[2025-10-06 09:10:17] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -44.244661 | E_var:     0.4410 | E_err:   0.010376
[2025-10-06 09:10:22] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -44.238017 | E_var:     0.2771 | E_err:   0.008224
[2025-10-06 09:10:27] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -44.249610 | E_var:     0.2854 | E_err:   0.008348
[2025-10-06 09:10:32] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -44.226862 | E_var:     0.3715 | E_err:   0.009524
[2025-10-06 09:10:38] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -44.234661 | E_var:     0.2657 | E_err:   0.008054
[2025-10-06 09:10:43] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -44.245405 | E_var:     0.2503 | E_err:   0.007818
[2025-10-06 09:10:48] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -44.248187 | E_var:     0.2413 | E_err:   0.007676
[2025-10-06 09:10:53] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -44.241360 | E_var:     0.2516 | E_err:   0.007838
[2025-10-06 09:10:58] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -44.236411 | E_var:     0.2841 | E_err:   0.008329
[2025-10-06 09:11:03] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -44.258469 | E_var:     0.2558 | E_err:   0.007903
[2025-10-06 09:11:08] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -44.244146 | E_var:     0.2591 | E_err:   0.007954
[2025-10-06 09:11:13] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -44.253923 | E_var:     0.2497 | E_err:   0.007807
[2025-10-06 09:11:19] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -44.242386 | E_var:     0.2651 | E_err:   0.008045
[2025-10-06 09:11:24] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -44.239031 | E_var:     0.2289 | E_err:   0.007476
[2025-10-06 09:11:29] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -44.233795 | E_var:     0.2940 | E_err:   0.008472
[2025-10-06 09:11:34] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -44.241094 | E_var:     0.2820 | E_err:   0.008298
[2025-10-06 09:11:39] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -44.250859 | E_var:     0.2236 | E_err:   0.007389
[2025-10-06 09:11:44] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -44.232994 | E_var:     0.4183 | E_err:   0.010105
[2025-10-06 09:11:49] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -44.241140 | E_var:     0.2311 | E_err:   0.007511
[2025-10-06 09:11:54] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -44.253721 | E_var:     0.2470 | E_err:   0.007766
[2025-10-06 09:12:00] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -44.235714 | E_var:     0.2316 | E_err:   0.007519
[2025-10-06 09:12:05] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -44.241080 | E_var:     0.2670 | E_err:   0.008074
[2025-10-06 09:12:10] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -44.248044 | E_var:     0.2272 | E_err:   0.007447
[2025-10-06 09:12:15] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -44.251204 | E_var:     0.2101 | E_err:   0.007162
[2025-10-06 09:12:20] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -44.237281 | E_var:     0.3623 | E_err:   0.009405
[2025-10-06 09:12:25] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -44.245269 | E_var:     0.2913 | E_err:   0.008433
[2025-10-06 09:12:30] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -44.258970 | E_var:     0.2805 | E_err:   0.008276
[2025-10-06 09:12:36] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -44.240391 | E_var:     0.2322 | E_err:   0.007529
[2025-10-06 09:12:41] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -44.230235 | E_var:     0.2666 | E_err:   0.008067
[2025-10-06 09:12:46] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -44.236110 | E_var:     0.2408 | E_err:   0.007668
[2025-10-06 09:12:51] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -44.237921 | E_var:     0.2578 | E_err:   0.007934
[2025-10-06 09:12:56] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -44.238180 | E_var:     0.2139 | E_err:   0.007227
[2025-10-06 09:13:01] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -44.240650 | E_var:     0.3225 | E_err:   0.008874
[2025-10-06 09:13:06] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -44.225126 | E_var:     0.2275 | E_err:   0.007453
[2025-10-06 09:13:11] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -44.240092 | E_var:     0.2455 | E_err:   0.007742
[2025-10-06 09:13:17] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -44.249239 | E_var:     0.3188 | E_err:   0.008822
[2025-10-06 09:13:22] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -44.238717 | E_var:     0.2426 | E_err:   0.007697
[2025-10-06 09:13:27] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -44.240791 | E_var:     0.3632 | E_err:   0.009417
[2025-10-06 09:13:32] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -44.227277 | E_var:     0.2761 | E_err:   0.008210
[2025-10-06 09:13:37] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -44.239033 | E_var:     0.2842 | E_err:   0.008329
[2025-10-06 09:13:42] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -44.233354 | E_var:     0.2423 | E_err:   0.007691
[2025-10-06 09:13:47] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -44.255973 | E_var:     0.2551 | E_err:   0.007892
[2025-10-06 09:13:52] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -44.235900 | E_var:     0.2467 | E_err:   0.007760
[2025-10-06 09:13:58] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -44.251757 | E_var:     0.2608 | E_err:   0.007980
[2025-10-06 09:14:03] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -44.234822 | E_var:     0.3071 | E_err:   0.008659
[2025-10-06 09:14:08] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -44.230553 | E_var:     0.2479 | E_err:   0.007779
[2025-10-06 09:14:13] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -44.237284 | E_var:     0.2251 | E_err:   0.007414
[2025-10-06 09:14:18] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -44.237485 | E_var:     0.2509 | E_err:   0.007827
[2025-10-06 09:14:23] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -44.242141 | E_var:     0.2318 | E_err:   0.007523
[2025-10-06 09:14:28] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -44.226088 | E_var:     0.4076 | E_err:   0.009976
[2025-10-06 09:14:33] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -44.242301 | E_var:     0.2443 | E_err:   0.007723
[2025-10-06 09:14:39] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -44.247759 | E_var:     0.2718 | E_err:   0.008147
[2025-10-06 09:14:44] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -44.232984 | E_var:     0.2315 | E_err:   0.007518
[2025-10-06 09:14:49] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -44.251595 | E_var:     0.2658 | E_err:   0.008055
[2025-10-06 09:14:54] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -44.249738 | E_var:     0.3565 | E_err:   0.009329
[2025-10-06 09:14:59] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -44.234662 | E_var:     0.2210 | E_err:   0.007345
[2025-10-06 09:15:04] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -44.231672 | E_var:     0.2776 | E_err:   0.008233
[2025-10-06 09:15:09] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -44.234360 | E_var:     0.3141 | E_err:   0.008757
[2025-10-06 09:15:15] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -44.237333 | E_var:     0.2761 | E_err:   0.008211
[2025-10-06 09:15:20] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -44.234463 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 09:15:25] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -44.220552 | E_var:     0.4709 | E_err:   0.010722
[2025-10-06 09:15:30] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -44.245966 | E_var:     0.2815 | E_err:   0.008290
[2025-10-06 09:15:35] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -44.240266 | E_var:     0.3812 | E_err:   0.009647
[2025-10-06 09:15:40] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -44.247025 | E_var:     0.2849 | E_err:   0.008339
[2025-10-06 09:15:45] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -44.239983 | E_var:     0.2487 | E_err:   0.007792
[2025-10-06 09:15:50] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -44.223002 | E_var:     0.2990 | E_err:   0.008544
[2025-10-06 09:15:56] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -44.223360 | E_var:     0.3030 | E_err:   0.008600
[2025-10-06 09:16:01] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -44.235270 | E_var:     0.2709 | E_err:   0.008133
[2025-10-06 09:16:06] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -44.237902 | E_var:     0.3001 | E_err:   0.008560
[2025-10-06 09:16:11] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -44.238011 | E_var:     0.2192 | E_err:   0.007315
[2025-10-06 09:16:16] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -44.249983 | E_var:     0.2112 | E_err:   0.007180
[2025-10-06 09:16:21] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -44.240620 | E_var:     0.2494 | E_err:   0.007804
[2025-10-06 09:16:26] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -44.234753 | E_var:     0.2919 | E_err:   0.008442
[2025-10-06 09:16:31] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -44.239744 | E_var:     0.2575 | E_err:   0.007929
[2025-10-06 09:16:37] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -44.241420 | E_var:     0.2426 | E_err:   0.007696
[2025-10-06 09:16:42] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -44.242641 | E_var:     0.2148 | E_err:   0.007241
[2025-10-06 09:16:47] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -44.225432 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 09:16:52] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -44.226185 | E_var:     0.3146 | E_err:   0.008764
[2025-10-06 09:16:57] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -44.233411 | E_var:     0.2494 | E_err:   0.007803
[2025-10-06 09:17:02] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -44.250762 | E_var:     0.2414 | E_err:   0.007676
[2025-10-06 09:17:07] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -44.237037 | E_var:     0.2587 | E_err:   0.007947
[2025-10-06 09:17:12] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -44.246898 | E_var:     0.2296 | E_err:   0.007486
[2025-10-06 09:17:18] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -44.229935 | E_var:     0.2491 | E_err:   0.007798
[2025-10-06 09:17:23] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -44.240768 | E_var:     0.2900 | E_err:   0.008414
[2025-10-06 09:17:28] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -44.245727 | E_var:     0.2441 | E_err:   0.007721
[2025-10-06 09:17:33] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -44.244224 | E_var:     0.2406 | E_err:   0.007664
[2025-10-06 09:17:38] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -44.242884 | E_var:     0.3629 | E_err:   0.009413
[2025-10-06 09:17:43] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -44.234274 | E_var:     0.2542 | E_err:   0.007878
[2025-10-06 09:17:48] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -44.242684 | E_var:     0.2602 | E_err:   0.007971
[2025-10-06 09:17:54] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -44.256802 | E_var:     0.2867 | E_err:   0.008366
[2025-10-06 09:17:59] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -44.238780 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 09:17:59] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-06 09:18:04] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -44.242653 | E_var:     0.2568 | E_err:   0.007918
[2025-10-06 09:18:09] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -44.249854 | E_var:     0.2341 | E_err:   0.007560
[2025-10-06 09:18:14] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -44.239979 | E_var:     0.2761 | E_err:   0.008210
[2025-10-06 09:18:19] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -44.242401 | E_var:     0.2535 | E_err:   0.007867
[2025-10-06 09:18:24] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -44.235565 | E_var:     0.2153 | E_err:   0.007250
[2025-10-06 09:18:29] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -44.232941 | E_var:     0.2017 | E_err:   0.007018
[2025-10-06 09:18:35] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -44.239857 | E_var:     0.3630 | E_err:   0.009414
[2025-10-06 09:18:40] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -44.241547 | E_var:     0.2608 | E_err:   0.007980
[2025-10-06 09:18:45] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -44.236441 | E_var:     0.2103 | E_err:   0.007166
[2025-10-06 09:18:50] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -44.234630 | E_var:     0.2453 | E_err:   0.007739
[2025-10-06 09:18:55] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -44.231977 | E_var:     0.2775 | E_err:   0.008231
[2025-10-06 09:19:00] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -44.237294 | E_var:     0.2097 | E_err:   0.007156
[2025-10-06 09:19:05] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -44.244344 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 09:19:10] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -44.233738 | E_var:     0.3346 | E_err:   0.009039
[2025-10-06 09:19:16] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -44.236571 | E_var:     0.2653 | E_err:   0.008048
[2025-10-06 09:19:21] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -44.241302 | E_var:     0.2299 | E_err:   0.007491
[2025-10-06 09:19:26] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -44.245594 | E_var:     0.2416 | E_err:   0.007681
[2025-10-06 09:19:31] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -44.235010 | E_var:     0.2581 | E_err:   0.007937
[2025-10-06 09:19:36] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -44.228878 | E_var:     0.2592 | E_err:   0.007955
[2025-10-06 09:19:41] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -44.243004 | E_var:     0.2392 | E_err:   0.007641
[2025-10-06 09:19:46] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -44.225868 | E_var:     0.2203 | E_err:   0.007334
[2025-10-06 09:19:51] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -44.234099 | E_var:     0.3551 | E_err:   0.009311
[2025-10-06 09:19:57] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -44.215932 | E_var:     0.2632 | E_err:   0.008017
[2025-10-06 09:20:02] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -44.224303 | E_var:     0.2872 | E_err:   0.008374
[2025-10-06 09:20:07] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -44.246109 | E_var:     0.2675 | E_err:   0.008082
[2025-10-06 09:20:12] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -44.231463 | E_var:     0.2759 | E_err:   0.008207
[2025-10-06 09:20:17] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -44.241299 | E_var:     0.2948 | E_err:   0.008483
[2025-10-06 09:20:22] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -44.226602 | E_var:     0.2571 | E_err:   0.007923
[2025-10-06 09:20:27] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -44.254881 | E_var:     0.3292 | E_err:   0.008965
[2025-10-06 09:20:32] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -44.231863 | E_var:     0.3458 | E_err:   0.009188
[2025-10-06 09:20:38] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -44.249034 | E_var:     0.2048 | E_err:   0.007071
[2025-10-06 09:20:43] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -44.240721 | E_var:     0.2337 | E_err:   0.007554
[2025-10-06 09:20:48] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -44.261251 | E_var:     0.3514 | E_err:   0.009262
[2025-10-06 09:20:53] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -44.231013 | E_var:     0.2767 | E_err:   0.008219
[2025-10-06 09:20:58] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -44.243970 | E_var:     0.2411 | E_err:   0.007672
[2025-10-06 09:21:03] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -44.239559 | E_var:     0.2900 | E_err:   0.008414
[2025-10-06 09:21:08] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -44.238532 | E_var:     0.2392 | E_err:   0.007642
[2025-10-06 09:21:14] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -44.241805 | E_var:     0.2678 | E_err:   0.008085
[2025-10-06 09:21:19] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -44.243619 | E_var:     0.2472 | E_err:   0.007769
[2025-10-06 09:21:24] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -44.236182 | E_var:     0.2417 | E_err:   0.007682
[2025-10-06 09:21:29] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -44.242777 | E_var:     0.2868 | E_err:   0.008367
[2025-10-06 09:21:34] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -44.254066 | E_var:     0.2900 | E_err:   0.008414
[2025-10-06 09:21:39] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -44.238851 | E_var:     0.2154 | E_err:   0.007252
[2025-10-06 09:21:44] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -44.239177 | E_var:     0.2027 | E_err:   0.007035
[2025-10-06 09:21:49] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -44.232745 | E_var:     0.3385 | E_err:   0.009091
[2025-10-06 09:21:55] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -44.250539 | E_var:     0.2410 | E_err:   0.007671
[2025-10-06 09:22:00] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -44.229207 | E_var:     0.2508 | E_err:   0.007825
[2025-10-06 09:22:05] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -44.251881 | E_var:     0.2577 | E_err:   0.007931
[2025-10-06 09:22:10] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -44.236495 | E_var:     0.2545 | E_err:   0.007882
[2025-10-06 09:22:15] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -44.245642 | E_var:     0.3200 | E_err:   0.008839
[2025-10-06 09:22:15] 🔄 RESTART #1 | Period: 300
[2025-10-06 09:22:20] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -44.233536 | E_var:     0.2883 | E_err:   0.008389
[2025-10-06 09:22:25] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -44.242654 | E_var:     0.2386 | E_err:   0.007632
[2025-10-06 09:22:30] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -44.245814 | E_var:     0.2213 | E_err:   0.007350
[2025-10-06 09:22:36] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -44.236172 | E_var:     0.3182 | E_err:   0.008813
[2025-10-06 09:22:41] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -44.239736 | E_var:     0.2112 | E_err:   0.007181
[2025-10-06 09:22:46] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -44.234415 | E_var:     0.2291 | E_err:   0.007479
[2025-10-06 09:22:51] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -44.238165 | E_var:     0.2403 | E_err:   0.007660
[2025-10-06 09:22:56] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -44.241714 | E_var:     0.2702 | E_err:   0.008122
[2025-10-06 09:23:01] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -44.245621 | E_var:     0.2449 | E_err:   0.007732
[2025-10-06 09:23:06] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -44.226768 | E_var:     0.2727 | E_err:   0.008159
[2025-10-06 09:23:11] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -44.242494 | E_var:     0.2205 | E_err:   0.007338
[2025-10-06 09:23:17] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -44.249433 | E_var:     0.2513 | E_err:   0.007833
[2025-10-06 09:23:22] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -44.239746 | E_var:     0.2744 | E_err:   0.008185
[2025-10-06 09:23:27] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -44.229310 | E_var:     0.1853 | E_err:   0.006727
[2025-10-06 09:23:32] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -44.232907 | E_var:     0.2459 | E_err:   0.007748
[2025-10-06 09:23:37] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -44.232695 | E_var:     0.2128 | E_err:   0.007207
[2025-10-06 09:23:42] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -44.253591 | E_var:     0.2130 | E_err:   0.007212
[2025-10-06 09:23:47] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -44.245135 | E_var:     0.2696 | E_err:   0.008114
[2025-10-06 09:23:52] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -44.240957 | E_var:     0.2556 | E_err:   0.007899
[2025-10-06 09:23:58] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -44.245722 | E_var:     0.2466 | E_err:   0.007760
[2025-10-06 09:24:03] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -44.245954 | E_var:     0.2534 | E_err:   0.007865
[2025-10-06 09:24:08] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -44.236155 | E_var:     0.2481 | E_err:   0.007783
[2025-10-06 09:24:13] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -44.236458 | E_var:     0.2418 | E_err:   0.007683
[2025-10-06 09:24:18] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -44.247711 | E_var:     0.3901 | E_err:   0.009759
[2025-10-06 09:24:23] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -44.242383 | E_var:     0.2908 | E_err:   0.008426
[2025-10-06 09:24:28] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -44.227467 | E_var:     0.2350 | E_err:   0.007574
[2025-10-06 09:24:33] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -44.234253 | E_var:     0.2188 | E_err:   0.007308
[2025-10-06 09:24:39] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -44.247558 | E_var:     0.2577 | E_err:   0.007932
[2025-10-06 09:24:44] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -44.225891 | E_var:     0.3286 | E_err:   0.008957
[2025-10-06 09:24:49] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -44.245406 | E_var:     0.2351 | E_err:   0.007575
[2025-10-06 09:24:54] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -44.234482 | E_var:     0.3360 | E_err:   0.009056
[2025-10-06 09:24:59] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -44.242298 | E_var:     0.3540 | E_err:   0.009297
[2025-10-06 09:25:04] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -44.225830 | E_var:     0.4167 | E_err:   0.010087
[2025-10-06 09:25:09] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -44.232910 | E_var:     0.3350 | E_err:   0.009044
[2025-10-06 09:25:14] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -44.235287 | E_var:     0.2087 | E_err:   0.007139
[2025-10-06 09:25:20] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -44.238697 | E_var:     0.2418 | E_err:   0.007683
[2025-10-06 09:25:25] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -44.249908 | E_var:     0.2122 | E_err:   0.007198
[2025-10-06 09:25:30] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -44.232043 | E_var:     0.4043 | E_err:   0.009935
[2025-10-06 09:25:35] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -44.253173 | E_var:     0.2449 | E_err:   0.007733
[2025-10-06 09:25:40] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -44.230842 | E_var:     0.2218 | E_err:   0.007358
[2025-10-06 09:25:45] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -44.239147 | E_var:     0.1996 | E_err:   0.006980
[2025-10-06 09:25:50] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -44.241361 | E_var:     0.3161 | E_err:   0.008785
[2025-10-06 09:25:55] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -44.247402 | E_var:     0.2561 | E_err:   0.007907
[2025-10-06 09:26:01] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -44.244333 | E_var:     0.2995 | E_err:   0.008552
[2025-10-06 09:26:06] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -44.243516 | E_var:     0.2180 | E_err:   0.007295
[2025-10-06 09:26:11] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -44.245411 | E_var:     0.2793 | E_err:   0.008258
[2025-10-06 09:26:16] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -44.243873 | E_var:     0.2740 | E_err:   0.008179
[2025-10-06 09:26:21] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -44.229998 | E_var:     0.2536 | E_err:   0.007869
[2025-10-06 09:26:26] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -44.223299 | E_var:     0.2448 | E_err:   0.007731
[2025-10-06 09:26:31] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -44.241237 | E_var:     0.2134 | E_err:   0.007218
[2025-10-06 09:26:31] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-06 09:26:36] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -44.238159 | E_var:     0.2482 | E_err:   0.007784
[2025-10-06 09:26:42] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -44.248273 | E_var:     0.2403 | E_err:   0.007660
[2025-10-06 09:26:47] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -44.243537 | E_var:     0.2214 | E_err:   0.007352
[2025-10-06 09:26:52] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -44.250753 | E_var:     0.3494 | E_err:   0.009236
[2025-10-06 09:26:57] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -44.244093 | E_var:     0.2842 | E_err:   0.008330
[2025-10-06 09:27:02] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -44.241091 | E_var:     0.2208 | E_err:   0.007343
[2025-10-06 09:27:07] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -44.234454 | E_var:     0.2308 | E_err:   0.007506
[2025-10-06 09:27:12] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -44.241919 | E_var:     0.2019 | E_err:   0.007022
[2025-10-06 09:27:17] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -44.241038 | E_var:     0.2991 | E_err:   0.008546
[2025-10-06 09:27:23] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -44.248080 | E_var:     0.2140 | E_err:   0.007228
[2025-10-06 09:27:28] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -44.234497 | E_var:     0.2483 | E_err:   0.007787
[2025-10-06 09:27:33] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -44.234366 | E_var:     0.3010 | E_err:   0.008573
[2025-10-06 09:27:38] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -44.246106 | E_var:     0.4226 | E_err:   0.010158
[2025-10-06 09:27:43] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -44.245228 | E_var:     0.2399 | E_err:   0.007652
[2025-10-06 09:27:48] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -44.235375 | E_var:     0.2430 | E_err:   0.007702
[2025-10-06 09:27:53] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -44.251623 | E_var:     0.2279 | E_err:   0.007459
[2025-10-06 09:27:58] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -44.250211 | E_var:     0.2539 | E_err:   0.007874
[2025-10-06 09:28:03] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -44.249235 | E_var:     0.3132 | E_err:   0.008744
[2025-10-06 09:28:09] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -44.238513 | E_var:     0.3513 | E_err:   0.009261
[2025-10-06 09:28:14] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -44.234889 | E_var:     0.2737 | E_err:   0.008174
[2025-10-06 09:28:19] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -44.241622 | E_var:     0.2801 | E_err:   0.008269
[2025-10-06 09:28:24] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -44.236794 | E_var:     0.2104 | E_err:   0.007167
[2025-10-06 09:28:29] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -44.245845 | E_var:     0.2130 | E_err:   0.007211
[2025-10-06 09:28:34] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -44.243965 | E_var:     0.2039 | E_err:   0.007056
[2025-10-06 09:28:39] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -44.232295 | E_var:     0.2536 | E_err:   0.007869
[2025-10-06 09:28:45] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -44.246703 | E_var:     0.2315 | E_err:   0.007518
[2025-10-06 09:28:50] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -44.232243 | E_var:     0.1990 | E_err:   0.006970
[2025-10-06 09:28:55] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -44.250380 | E_var:     0.2389 | E_err:   0.007637
[2025-10-06 09:29:00] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -44.238491 | E_var:     0.2671 | E_err:   0.008076
[2025-10-06 09:29:05] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -44.233519 | E_var:     0.2195 | E_err:   0.007320
[2025-10-06 09:29:10] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -44.232488 | E_var:     0.2909 | E_err:   0.008427
[2025-10-06 09:29:15] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -44.233557 | E_var:     0.2392 | E_err:   0.007642
[2025-10-06 09:29:20] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -44.236015 | E_var:     0.2637 | E_err:   0.008023
[2025-10-06 09:29:25] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -44.253438 | E_var:     0.2017 | E_err:   0.007017
[2025-10-06 09:29:31] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -44.233564 | E_var:     0.2840 | E_err:   0.008327
[2025-10-06 09:29:36] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -44.226972 | E_var:     0.2418 | E_err:   0.007683
[2025-10-06 09:29:41] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -44.236551 | E_var:     0.2753 | E_err:   0.008199
[2025-10-06 09:29:46] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -44.244363 | E_var:     0.2549 | E_err:   0.007888
[2025-10-06 09:29:51] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -44.239322 | E_var:     0.2642 | E_err:   0.008032
[2025-10-06 09:29:56] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -44.257001 | E_var:     0.2609 | E_err:   0.007981
[2025-10-06 09:30:01] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -44.241522 | E_var:     0.2371 | E_err:   0.007607
[2025-10-06 09:30:07] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -44.237208 | E_var:     0.2582 | E_err:   0.007939
[2025-10-06 09:30:12] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -44.241847 | E_var:     0.2667 | E_err:   0.008070
[2025-10-06 09:30:17] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -44.240558 | E_var:     0.2107 | E_err:   0.007172
[2025-10-06 09:30:22] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -44.253813 | E_var:     0.2562 | E_err:   0.007909
[2025-10-06 09:30:27] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -44.237615 | E_var:     0.2985 | E_err:   0.008537
[2025-10-06 09:30:32] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -44.254497 | E_var:     0.2480 | E_err:   0.007782
[2025-10-06 09:30:37] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -44.249084 | E_var:     0.2483 | E_err:   0.007786
[2025-10-06 09:30:42] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -44.240749 | E_var:     0.2211 | E_err:   0.007347
[2025-10-06 09:30:48] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -44.239704 | E_var:     0.3238 | E_err:   0.008891
[2025-10-06 09:30:53] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -44.243400 | E_var:     0.2245 | E_err:   0.007403
[2025-10-06 09:30:58] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -44.241618 | E_var:     0.2830 | E_err:   0.008312
[2025-10-06 09:31:03] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -44.233716 | E_var:     0.2508 | E_err:   0.007825
[2025-10-06 09:31:08] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -44.243584 | E_var:     0.3189 | E_err:   0.008823
[2025-10-06 09:31:13] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -44.237799 | E_var:     0.2614 | E_err:   0.007988
[2025-10-06 09:31:18] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -44.250710 | E_var:     0.2700 | E_err:   0.008119
[2025-10-06 09:31:23] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -44.242094 | E_var:     0.2423 | E_err:   0.007691
[2025-10-06 09:31:29] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -44.248055 | E_var:     0.2060 | E_err:   0.007091
[2025-10-06 09:31:34] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -44.245730 | E_var:     0.2108 | E_err:   0.007174
[2025-10-06 09:31:39] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -44.242654 | E_var:     0.2286 | E_err:   0.007470
[2025-10-06 09:31:44] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -44.235850 | E_var:     0.3002 | E_err:   0.008561
[2025-10-06 09:31:49] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -44.229147 | E_var:     0.2135 | E_err:   0.007219
[2025-10-06 09:31:54] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -44.247782 | E_var:     0.2139 | E_err:   0.007227
[2025-10-06 09:31:59] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -44.241204 | E_var:     0.2333 | E_err:   0.007547
[2025-10-06 09:32:04] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -44.231556 | E_var:     0.2430 | E_err:   0.007702
[2025-10-06 09:32:10] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -44.228621 | E_var:     0.2400 | E_err:   0.007655
[2025-10-06 09:32:15] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -44.243579 | E_var:     0.2736 | E_err:   0.008174
[2025-10-06 09:32:20] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -44.236113 | E_var:     0.2278 | E_err:   0.007458
[2025-10-06 09:32:25] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -44.235669 | E_var:     0.2351 | E_err:   0.007576
[2025-10-06 09:32:30] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -44.235089 | E_var:     0.4403 | E_err:   0.010368
[2025-10-06 09:32:35] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -44.248405 | E_var:     0.2767 | E_err:   0.008220
[2025-10-06 09:32:40] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -44.249241 | E_var:     0.2518 | E_err:   0.007841
[2025-10-06 09:32:45] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -44.230031 | E_var:     0.2704 | E_err:   0.008126
[2025-10-06 09:32:50] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -44.244587 | E_var:     0.2177 | E_err:   0.007291
[2025-10-06 09:32:56] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -44.248001 | E_var:     0.2987 | E_err:   0.008539
[2025-10-06 09:33:01] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -44.239033 | E_var:     0.2465 | E_err:   0.007757
[2025-10-06 09:33:06] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -44.254476 | E_var:     0.3157 | E_err:   0.008779
[2025-10-06 09:33:11] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -44.250249 | E_var:     0.3639 | E_err:   0.009426
[2025-10-06 09:33:16] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -44.233780 | E_var:     0.2153 | E_err:   0.007250
[2025-10-06 09:33:21] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -44.238871 | E_var:     0.2596 | E_err:   0.007962
[2025-10-06 09:33:26] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -44.241736 | E_var:     0.2582 | E_err:   0.007940
[2025-10-06 09:33:32] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -44.242597 | E_var:     0.2484 | E_err:   0.007788
[2025-10-06 09:33:37] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -44.242770 | E_var:     0.2333 | E_err:   0.007546
[2025-10-06 09:33:42] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -44.245727 | E_var:     0.2617 | E_err:   0.007994
[2025-10-06 09:33:47] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -44.239939 | E_var:     0.2967 | E_err:   0.008511
[2025-10-06 09:33:52] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -44.235464 | E_var:     0.3238 | E_err:   0.008891
[2025-10-06 09:33:57] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -44.230553 | E_var:     0.2321 | E_err:   0.007527
[2025-10-06 09:34:02] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -44.230592 | E_var:     0.2426 | E_err:   0.007696
[2025-10-06 09:34:07] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -44.258953 | E_var:     0.2126 | E_err:   0.007205
[2025-10-06 09:34:13] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -44.229930 | E_var:     0.2786 | E_err:   0.008247
[2025-10-06 09:34:18] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -44.245754 | E_var:     0.2676 | E_err:   0.008083
[2025-10-06 09:34:23] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -44.229152 | E_var:     0.2417 | E_err:   0.007681
[2025-10-06 09:34:28] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -44.245380 | E_var:     0.2673 | E_err:   0.008078
[2025-10-06 09:34:33] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -44.248190 | E_var:     0.2825 | E_err:   0.008305
[2025-10-06 09:34:38] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -44.245801 | E_var:     0.2212 | E_err:   0.007349
[2025-10-06 09:34:43] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -44.232626 | E_var:     0.2920 | E_err:   0.008443
[2025-10-06 09:34:48] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -44.245561 | E_var:     0.2048 | E_err:   0.007071
[2025-10-06 09:34:54] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -44.247736 | E_var:     0.3287 | E_err:   0.008958
[2025-10-06 09:34:59] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -44.248930 | E_var:     0.2091 | E_err:   0.007145
[2025-10-06 09:35:04] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -44.232306 | E_var:     0.2553 | E_err:   0.007894
[2025-10-06 09:35:04] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-06 09:35:09] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -44.243336 | E_var:     0.3189 | E_err:   0.008824
[2025-10-06 09:35:14] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -44.244796 | E_var:     0.2307 | E_err:   0.007505
[2025-10-06 09:35:19] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -44.242118 | E_var:     0.2281 | E_err:   0.007463
[2025-10-06 09:35:24] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -44.221584 | E_var:     0.2764 | E_err:   0.008214
[2025-10-06 09:35:29] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -44.241655 | E_var:     0.2592 | E_err:   0.007955
[2025-10-06 09:35:35] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -44.247426 | E_var:     0.2601 | E_err:   0.007969
[2025-10-06 09:35:40] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -44.239411 | E_var:     0.2272 | E_err:   0.007447
[2025-10-06 09:35:45] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -44.234319 | E_var:     0.3018 | E_err:   0.008584
[2025-10-06 09:35:50] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -44.226396 | E_var:     0.2449 | E_err:   0.007733
[2025-10-06 09:35:55] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -44.245709 | E_var:     0.2600 | E_err:   0.007967
[2025-10-06 09:36:00] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -44.235632 | E_var:     0.2339 | E_err:   0.007556
[2025-10-06 09:36:05] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -44.237512 | E_var:     0.2456 | E_err:   0.007743
[2025-10-06 09:36:10] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -44.255476 | E_var:     0.2420 | E_err:   0.007687
[2025-10-06 09:36:16] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -44.240929 | E_var:     0.2316 | E_err:   0.007520
[2025-10-06 09:36:21] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -44.241034 | E_var:     0.2136 | E_err:   0.007222
[2025-10-06 09:36:26] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -44.249026 | E_var:     0.3047 | E_err:   0.008625
[2025-10-06 09:36:31] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -44.240125 | E_var:     0.3630 | E_err:   0.009414
[2025-10-06 09:36:36] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -44.247242 | E_var:     0.2713 | E_err:   0.008138
[2025-10-06 09:36:41] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -44.253803 | E_var:     0.2283 | E_err:   0.007466
[2025-10-06 09:36:46] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -44.240999 | E_var:     0.2984 | E_err:   0.008536
[2025-10-06 09:36:51] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -44.239412 | E_var:     0.2656 | E_err:   0.008053
[2025-10-06 09:36:57] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -44.230538 | E_var:     0.2960 | E_err:   0.008500
[2025-10-06 09:37:02] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -44.240038 | E_var:     0.1901 | E_err:   0.006812
[2025-10-06 09:37:07] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -44.224789 | E_var:     0.2635 | E_err:   0.008021
[2025-10-06 09:37:12] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -44.238108 | E_var:     0.2596 | E_err:   0.007961
[2025-10-06 09:37:17] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -44.246364 | E_var:     0.2389 | E_err:   0.007638
[2025-10-06 09:37:22] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -44.233799 | E_var:     0.2311 | E_err:   0.007511
[2025-10-06 09:37:27] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -44.236358 | E_var:     0.2876 | E_err:   0.008380
[2025-10-06 09:37:32] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -44.233851 | E_var:     0.2567 | E_err:   0.007916
[2025-10-06 09:37:38] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -44.241233 | E_var:     0.2486 | E_err:   0.007791
[2025-10-06 09:37:43] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -44.237646 | E_var:     0.2176 | E_err:   0.007289
[2025-10-06 09:37:48] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -44.229135 | E_var:     0.3549 | E_err:   0.009309
[2025-10-06 09:37:53] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -44.243637 | E_var:     0.3555 | E_err:   0.009316
[2025-10-06 09:37:58] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -44.246493 | E_var:     0.2516 | E_err:   0.007838
[2025-10-06 09:38:03] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -44.237957 | E_var:     0.2375 | E_err:   0.007615
[2025-10-06 09:38:08] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -44.233738 | E_var:     0.2916 | E_err:   0.008438
[2025-10-06 09:38:13] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -44.228086 | E_var:     0.2292 | E_err:   0.007481
[2025-10-06 09:38:19] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -44.245070 | E_var:     0.1881 | E_err:   0.006777
[2025-10-06 09:38:24] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -44.248719 | E_var:     0.2117 | E_err:   0.007190
[2025-10-06 09:38:29] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -44.242863 | E_var:     0.2080 | E_err:   0.007127
[2025-10-06 09:38:34] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -44.251475 | E_var:     0.2160 | E_err:   0.007262
[2025-10-06 09:38:39] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -44.242024 | E_var:     0.2334 | E_err:   0.007548
[2025-10-06 09:38:44] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -44.246878 | E_var:     0.2115 | E_err:   0.007186
[2025-10-06 09:38:49] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -44.233871 | E_var:     0.2667 | E_err:   0.008070
[2025-10-06 09:38:54] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -44.242054 | E_var:     0.2716 | E_err:   0.008143
[2025-10-06 09:38:59] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -44.233262 | E_var:     0.2469 | E_err:   0.007763
[2025-10-06 09:39:05] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -44.246415 | E_var:     0.2322 | E_err:   0.007529
[2025-10-06 09:39:10] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -44.233669 | E_var:     0.2065 | E_err:   0.007100
[2025-10-06 09:39:15] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -44.246044 | E_var:     0.2787 | E_err:   0.008249
[2025-10-06 09:39:20] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -44.241081 | E_var:     0.2357 | E_err:   0.007586
[2025-10-06 09:39:25] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -44.237752 | E_var:     0.2429 | E_err:   0.007700
[2025-10-06 09:39:30] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -44.236003 | E_var:     0.3175 | E_err:   0.008804
[2025-10-06 09:39:35] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -44.239964 | E_var:     0.2111 | E_err:   0.007178
[2025-10-06 09:39:40] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -44.223263 | E_var:     0.2524 | E_err:   0.007850
[2025-10-06 09:39:46] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -44.244080 | E_var:     0.2691 | E_err:   0.008106
[2025-10-06 09:39:51] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -44.239590 | E_var:     0.2633 | E_err:   0.008017
[2025-10-06 09:39:56] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -44.225292 | E_var:     0.3566 | E_err:   0.009330
[2025-10-06 09:40:01] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -44.214620 | E_var:     0.2991 | E_err:   0.008545
[2025-10-06 09:40:06] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -44.240261 | E_var:     0.2625 | E_err:   0.008005
[2025-10-06 09:40:11] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -44.236659 | E_var:     0.2863 | E_err:   0.008360
[2025-10-06 09:40:16] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -44.244795 | E_var:     0.2134 | E_err:   0.007218
[2025-10-06 09:40:21] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -44.233975 | E_var:     0.2808 | E_err:   0.008280
[2025-10-06 09:40:27] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -44.240009 | E_var:     0.2410 | E_err:   0.007671
[2025-10-06 09:40:32] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -44.240645 | E_var:     0.2691 | E_err:   0.008106
[2025-10-06 09:40:37] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -44.247556 | E_var:     0.2318 | E_err:   0.007523
[2025-10-06 09:40:42] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -44.244493 | E_var:     0.2288 | E_err:   0.007473
[2025-10-06 09:40:47] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -44.246040 | E_var:     0.2730 | E_err:   0.008164
[2025-10-06 09:40:52] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -44.249007 | E_var:     0.2179 | E_err:   0.007294
[2025-10-06 09:40:57] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -44.230695 | E_var:     0.2165 | E_err:   0.007270
[2025-10-06 09:41:02] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -44.246593 | E_var:     0.2143 | E_err:   0.007234
[2025-10-06 09:41:08] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -44.242508 | E_var:     0.2306 | E_err:   0.007504
[2025-10-06 09:41:13] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -44.244149 | E_var:     0.1948 | E_err:   0.006896
[2025-10-06 09:41:18] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -44.235664 | E_var:     0.2436 | E_err:   0.007712
[2025-10-06 09:41:23] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -44.247365 | E_var:     0.2255 | E_err:   0.007420
[2025-10-06 09:41:28] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -44.234014 | E_var:     0.3184 | E_err:   0.008816
[2025-10-06 09:41:33] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -44.244430 | E_var:     0.2771 | E_err:   0.008226
[2025-10-06 09:41:38] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -44.228236 | E_var:     0.3299 | E_err:   0.008975
[2025-10-06 09:41:43] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -44.247992 | E_var:     0.2136 | E_err:   0.007221
[2025-10-06 09:41:49] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -44.249126 | E_var:     0.2384 | E_err:   0.007628
[2025-10-06 09:41:54] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -44.231257 | E_var:     0.2826 | E_err:   0.008307
[2025-10-06 09:41:59] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -44.240248 | E_var:     0.2260 | E_err:   0.007428
[2025-10-06 09:42:04] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -44.262148 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 09:42:09] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -44.232224 | E_var:     0.3333 | E_err:   0.009020
[2025-10-06 09:42:14] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -44.241663 | E_var:     0.2720 | E_err:   0.008148
[2025-10-06 09:42:19] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -44.253879 | E_var:     0.2990 | E_err:   0.008544
[2025-10-06 09:42:24] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -44.241649 | E_var:     0.3047 | E_err:   0.008625
[2025-10-06 09:42:30] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -44.219854 | E_var:     0.4105 | E_err:   0.010011
[2025-10-06 09:42:35] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -44.221331 | E_var:     0.2505 | E_err:   0.007820
[2025-10-06 09:42:40] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -44.243054 | E_var:     0.2268 | E_err:   0.007441
[2025-10-06 09:42:45] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -44.244982 | E_var:     0.2818 | E_err:   0.008294
[2025-10-06 09:42:50] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -44.242742 | E_var:     0.2804 | E_err:   0.008275
[2025-10-06 09:42:55] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -44.239864 | E_var:     0.2314 | E_err:   0.007517
[2025-10-06 09:43:00] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -44.245664 | E_var:     0.2669 | E_err:   0.008072
[2025-10-06 09:43:05] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -44.238339 | E_var:     0.2191 | E_err:   0.007314
[2025-10-06 09:43:11] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -44.238785 | E_var:     0.2360 | E_err:   0.007590
[2025-10-06 09:43:16] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -44.250452 | E_var:     0.2817 | E_err:   0.008294
[2025-10-06 09:43:21] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -44.221527 | E_var:     0.2603 | E_err:   0.007972
[2025-10-06 09:43:26] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -44.246295 | E_var:     0.2231 | E_err:   0.007380
[2025-10-06 09:43:31] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -44.244385 | E_var:     0.2731 | E_err:   0.008165
[2025-10-06 09:43:36] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -44.248936 | E_var:     0.2371 | E_err:   0.007607
[2025-10-06 09:43:36] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-06 09:43:41] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -44.244382 | E_var:     0.2850 | E_err:   0.008341
[2025-10-06 09:43:47] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -44.237151 | E_var:     0.3633 | E_err:   0.009418
[2025-10-06 09:43:52] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -44.250103 | E_var:     0.2173 | E_err:   0.007283
[2025-10-06 09:43:57] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -44.229371 | E_var:     0.3220 | E_err:   0.008867
[2025-10-06 09:44:02] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -44.229826 | E_var:     0.2369 | E_err:   0.007605
[2025-10-06 09:44:07] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -44.240905 | E_var:     0.3491 | E_err:   0.009232
[2025-10-06 09:44:12] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -44.234359 | E_var:     0.2564 | E_err:   0.007912
[2025-10-06 09:44:17] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -44.239619 | E_var:     0.3238 | E_err:   0.008892
[2025-10-06 09:44:22] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -44.241052 | E_var:     0.2414 | E_err:   0.007677
[2025-10-06 09:44:28] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -44.242479 | E_var:     0.3088 | E_err:   0.008682
[2025-10-06 09:44:33] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -44.239681 | E_var:     0.3287 | E_err:   0.008958
[2025-10-06 09:44:38] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -44.245318 | E_var:     0.3915 | E_err:   0.009777
[2025-10-06 09:44:43] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -44.234670 | E_var:     0.2637 | E_err:   0.008024
[2025-10-06 09:44:48] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -44.234892 | E_var:     0.2026 | E_err:   0.007033
[2025-10-06 09:44:53] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -44.248191 | E_var:     0.2970 | E_err:   0.008515
[2025-10-06 09:44:58] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -44.247130 | E_var:     0.2534 | E_err:   0.007865
[2025-10-06 09:45:03] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -44.254096 | E_var:     0.2589 | E_err:   0.007951
[2025-10-06 09:45:08] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -44.240770 | E_var:     0.2336 | E_err:   0.007553
[2025-10-06 09:45:14] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -44.247584 | E_var:     0.2995 | E_err:   0.008551
[2025-10-06 09:45:19] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -44.237649 | E_var:     0.2495 | E_err:   0.007805
[2025-10-06 09:45:24] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -44.235017 | E_var:     0.4419 | E_err:   0.010386
[2025-10-06 09:45:29] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -44.238629 | E_var:     0.2228 | E_err:   0.007375
[2025-10-06 09:45:34] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -44.242605 | E_var:     0.3623 | E_err:   0.009405
[2025-10-06 09:45:39] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -44.239205 | E_var:     0.2488 | E_err:   0.007794
[2025-10-06 09:45:44] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -44.239803 | E_var:     0.2410 | E_err:   0.007671
[2025-10-06 09:45:49] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -44.229879 | E_var:     0.2360 | E_err:   0.007591
[2025-10-06 09:45:55] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -44.239764 | E_var:     0.2216 | E_err:   0.007356
[2025-10-06 09:46:00] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -44.242406 | E_var:     0.2110 | E_err:   0.007177
[2025-10-06 09:46:05] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -44.228237 | E_var:     0.3514 | E_err:   0.009262
[2025-10-06 09:46:10] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -44.237452 | E_var:     0.2418 | E_err:   0.007683
[2025-10-06 09:46:15] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -44.239587 | E_var:     0.2606 | E_err:   0.007977
[2025-10-06 09:46:20] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -44.243321 | E_var:     0.2598 | E_err:   0.007964
[2025-10-06 09:46:25] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -44.231517 | E_var:     0.3762 | E_err:   0.009584
[2025-10-06 09:46:30] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -44.235013 | E_var:     0.2688 | E_err:   0.008101
[2025-10-06 09:46:36] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -44.226044 | E_var:     0.4720 | E_err:   0.010735
[2025-10-06 09:46:41] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -44.243893 | E_var:     0.2320 | E_err:   0.007526
[2025-10-06 09:46:46] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -44.242495 | E_var:     0.2370 | E_err:   0.007607
[2025-10-06 09:46:51] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -44.242496 | E_var:     0.1951 | E_err:   0.006902
[2025-10-06 09:46:56] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -44.256092 | E_var:     0.2315 | E_err:   0.007518
[2025-10-06 09:47:01] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -44.236233 | E_var:     0.2682 | E_err:   0.008092
[2025-10-06 09:47:06] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -44.245691 | E_var:     0.2538 | E_err:   0.007872
[2025-10-06 09:47:11] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -44.233201 | E_var:     0.2891 | E_err:   0.008401
[2025-10-06 09:47:17] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -44.236284 | E_var:     0.2935 | E_err:   0.008466
[2025-10-06 09:47:22] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -44.222314 | E_var:     0.2932 | E_err:   0.008460
[2025-10-06 09:47:27] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -44.247262 | E_var:     0.2209 | E_err:   0.007344
[2025-10-06 09:47:32] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -44.247381 | E_var:     0.2309 | E_err:   0.007508
[2025-10-06 09:47:37] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -44.239586 | E_var:     0.2856 | E_err:   0.008351
[2025-10-06 09:47:42] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -44.231206 | E_var:     0.2960 | E_err:   0.008501
[2025-10-06 09:47:47] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -44.227530 | E_var:     0.2893 | E_err:   0.008404
[2025-10-06 09:47:52] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -44.234792 | E_var:     0.2227 | E_err:   0.007373
[2025-10-06 09:47:52] 🔄 RESTART #2 | Period: 600
[2025-10-06 09:47:58] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -44.249084 | E_var:     0.2586 | E_err:   0.007945
[2025-10-06 09:48:03] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -44.245062 | E_var:     0.2546 | E_err:   0.007884
[2025-10-06 09:48:08] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -44.229603 | E_var:     0.1974 | E_err:   0.006942
[2025-10-06 09:48:13] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -44.246004 | E_var:     0.2217 | E_err:   0.007357
[2025-10-06 09:48:18] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -44.230792 | E_var:     0.3755 | E_err:   0.009575
[2025-10-06 09:48:23] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -44.223663 | E_var:     0.3319 | E_err:   0.009001
[2025-10-06 09:48:28] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -44.245508 | E_var:     0.2212 | E_err:   0.007348
[2025-10-06 09:48:33] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -44.248328 | E_var:     0.2882 | E_err:   0.008389
[2025-10-06 09:48:38] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -44.243480 | E_var:     0.3250 | E_err:   0.008907
[2025-10-06 09:48:44] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -44.245327 | E_var:     0.2523 | E_err:   0.007848
[2025-10-06 09:48:49] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -44.247475 | E_var:     0.2446 | E_err:   0.007727
[2025-10-06 09:48:54] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -44.226701 | E_var:     0.5681 | E_err:   0.011777
[2025-10-06 09:48:59] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -44.248166 | E_var:     0.1842 | E_err:   0.006706
[2025-10-06 09:49:04] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -44.235626 | E_var:     0.2338 | E_err:   0.007555
[2025-10-06 09:49:09] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -44.236858 | E_var:     0.2294 | E_err:   0.007484
[2025-10-06 09:49:14] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -44.245420 | E_var:     0.3422 | E_err:   0.009141
[2025-10-06 09:49:19] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -44.235977 | E_var:     0.2643 | E_err:   0.008033
[2025-10-06 09:49:25] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -44.250770 | E_var:     0.2271 | E_err:   0.007447
[2025-10-06 09:49:30] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -44.234180 | E_var:     0.2575 | E_err:   0.007929
[2025-10-06 09:49:35] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -44.235402 | E_var:     0.3412 | E_err:   0.009126
[2025-10-06 09:49:40] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -44.248549 | E_var:     0.2650 | E_err:   0.008043
[2025-10-06 09:49:45] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -44.223682 | E_var:     0.4623 | E_err:   0.010624
[2025-10-06 09:49:50] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -44.246696 | E_var:     0.2568 | E_err:   0.007917
[2025-10-06 09:49:55] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -44.225990 | E_var:     0.3154 | E_err:   0.008775
[2025-10-06 09:50:00] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -44.245167 | E_var:     0.2476 | E_err:   0.007775
[2025-10-06 09:50:06] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -44.247554 | E_var:     0.2697 | E_err:   0.008114
[2025-10-06 09:50:11] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -44.249570 | E_var:     0.2103 | E_err:   0.007165
[2025-10-06 09:50:16] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -44.252811 | E_var:     0.2655 | E_err:   0.008050
[2025-10-06 09:50:21] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -44.234238 | E_var:     0.2279 | E_err:   0.007459
[2025-10-06 09:50:26] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -44.248582 | E_var:     0.2681 | E_err:   0.008091
[2025-10-06 09:50:31] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -44.245644 | E_var:     0.2958 | E_err:   0.008498
[2025-10-06 09:50:36] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -44.242342 | E_var:     0.2401 | E_err:   0.007657
[2025-10-06 09:50:41] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -44.239824 | E_var:     0.2217 | E_err:   0.007357
[2025-10-06 09:50:47] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -44.243505 | E_var:     0.2520 | E_err:   0.007844
[2025-10-06 09:50:52] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -44.228041 | E_var:     0.2706 | E_err:   0.008128
[2025-10-06 09:50:57] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -44.235442 | E_var:     0.2908 | E_err:   0.008427
[2025-10-06 09:51:02] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -44.238086 | E_var:     0.3234 | E_err:   0.008885
[2025-10-06 09:51:07] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -44.233027 | E_var:     0.2441 | E_err:   0.007719
[2025-10-06 09:51:12] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -44.243372 | E_var:     0.2278 | E_err:   0.007458
[2025-10-06 09:51:17] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -44.240512 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 09:51:22] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -44.241439 | E_var:     0.2369 | E_err:   0.007606
[2025-10-06 09:51:28] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -44.237998 | E_var:     0.2116 | E_err:   0.007188
[2025-10-06 09:51:33] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -44.229829 | E_var:     0.3090 | E_err:   0.008686
[2025-10-06 09:51:38] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -44.216222 | E_var:     0.3328 | E_err:   0.009014
[2025-10-06 09:51:43] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -44.251675 | E_var:     0.2405 | E_err:   0.007662
[2025-10-06 09:51:48] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -44.234036 | E_var:     0.2626 | E_err:   0.008006
[2025-10-06 09:51:53] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -44.234314 | E_var:     0.3099 | E_err:   0.008699
[2025-10-06 09:51:58] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -44.252775 | E_var:     0.2675 | E_err:   0.008081
[2025-10-06 09:52:03] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -44.239567 | E_var:     0.2498 | E_err:   0.007809
[2025-10-06 09:52:09] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -44.244020 | E_var:     0.1928 | E_err:   0.006861
[2025-10-06 09:52:09] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-06 09:52:14] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -44.249249 | E_var:     0.2456 | E_err:   0.007744
[2025-10-06 09:52:19] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -44.231129 | E_var:     0.2163 | E_err:   0.007268
[2025-10-06 09:52:24] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -44.219666 | E_var:     0.2827 | E_err:   0.008307
[2025-10-06 09:52:29] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -44.229655 | E_var:     0.2322 | E_err:   0.007529
[2025-10-06 09:52:34] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -44.222361 | E_var:     0.2576 | E_err:   0.007930
[2025-10-06 09:52:39] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -44.255522 | E_var:     0.2548 | E_err:   0.007888
[2025-10-06 09:52:44] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -44.236409 | E_var:     0.4055 | E_err:   0.009949
[2025-10-06 09:52:50] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -44.231245 | E_var:     0.3083 | E_err:   0.008676
[2025-10-06 09:52:55] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -44.239878 | E_var:     0.2180 | E_err:   0.007296
[2025-10-06 09:53:00] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -44.241503 | E_var:     0.2036 | E_err:   0.007050
[2025-10-06 09:53:05] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -44.240789 | E_var:     0.2432 | E_err:   0.007705
[2025-10-06 09:53:10] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -44.238520 | E_var:     0.2071 | E_err:   0.007111
[2025-10-06 09:53:15] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -44.255753 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 09:53:20] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -44.242704 | E_var:     0.2742 | E_err:   0.008182
[2025-10-06 09:53:25] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -44.245351 | E_var:     0.2849 | E_err:   0.008340
[2025-10-06 09:53:31] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -44.235561 | E_var:     0.2349 | E_err:   0.007573
[2025-10-06 09:53:36] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -44.244258 | E_var:     0.2270 | E_err:   0.007445
[2025-10-06 09:53:41] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -44.234275 | E_var:     0.2296 | E_err:   0.007488
[2025-10-06 09:53:46] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -44.222682 | E_var:     0.2573 | E_err:   0.007925
[2025-10-06 09:53:51] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -44.233709 | E_var:     0.2596 | E_err:   0.007962
[2025-10-06 09:53:56] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -44.237638 | E_var:     0.2254 | E_err:   0.007419
[2025-10-06 09:54:01] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -44.237578 | E_var:     0.3016 | E_err:   0.008581
[2025-10-06 09:54:06] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -44.233949 | E_var:     0.2681 | E_err:   0.008090
[2025-10-06 09:54:12] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -44.250588 | E_var:     0.2462 | E_err:   0.007754
[2025-10-06 09:54:17] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -44.234497 | E_var:     0.2414 | E_err:   0.007677
[2025-10-06 09:54:22] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -44.236389 | E_var:     0.2931 | E_err:   0.008458
[2025-10-06 09:54:27] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -44.256936 | E_var:     0.3032 | E_err:   0.008604
[2025-10-06 09:54:32] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -44.235212 | E_var:     0.3027 | E_err:   0.008596
[2025-10-06 09:54:37] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -44.237336 | E_var:     0.2536 | E_err:   0.007869
[2025-10-06 09:54:42] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -44.242351 | E_var:     0.2435 | E_err:   0.007711
[2025-10-06 09:54:47] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -44.248332 | E_var:     0.4542 | E_err:   0.010531
[2025-10-06 09:54:53] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -44.230592 | E_var:     0.2704 | E_err:   0.008125
[2025-10-06 09:54:58] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -44.233386 | E_var:     0.2467 | E_err:   0.007761
[2025-10-06 09:55:03] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -44.262187 | E_var:     0.2362 | E_err:   0.007594
[2025-10-06 09:55:08] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -44.257229 | E_var:     0.2125 | E_err:   0.007202
[2025-10-06 09:55:13] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -44.231002 | E_var:     0.2622 | E_err:   0.008000
[2025-10-06 09:55:18] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -44.248887 | E_var:     0.2573 | E_err:   0.007926
[2025-10-06 09:55:23] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -44.244180 | E_var:     0.3000 | E_err:   0.008558
[2025-10-06 09:55:28] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -44.250963 | E_var:     0.2901 | E_err:   0.008415
[2025-10-06 09:55:34] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -44.249255 | E_var:     0.2311 | E_err:   0.007511
[2025-10-06 09:55:39] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -44.251960 | E_var:     0.2316 | E_err:   0.007520
[2025-10-06 09:55:44] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -44.254644 | E_var:     0.4495 | E_err:   0.010476
[2025-10-06 09:55:49] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -44.228313 | E_var:     0.2535 | E_err:   0.007867
[2025-10-06 09:55:54] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -44.234499 | E_var:     0.3183 | E_err:   0.008816
[2025-10-06 09:55:59] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -44.240897 | E_var:     0.2256 | E_err:   0.007422
[2025-10-06 09:56:04] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -44.243910 | E_var:     0.2565 | E_err:   0.007913
[2025-10-06 09:56:09] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -44.238968 | E_var:     0.2462 | E_err:   0.007753
[2025-10-06 09:56:14] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -44.251407 | E_var:     0.2126 | E_err:   0.007205
[2025-10-06 09:56:20] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -44.234941 | E_var:     0.2565 | E_err:   0.007913
[2025-10-06 09:56:25] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -44.242978 | E_var:     0.2066 | E_err:   0.007102
[2025-10-06 09:56:30] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -44.234762 | E_var:     0.2719 | E_err:   0.008148
[2025-10-06 09:56:35] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -44.248085 | E_var:     0.2202 | E_err:   0.007332
[2025-10-06 09:56:40] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -44.240875 | E_var:     0.2052 | E_err:   0.007078
[2025-10-06 09:56:45] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -44.249339 | E_var:     0.2122 | E_err:   0.007198
[2025-10-06 09:56:50] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -44.224828 | E_var:     0.4511 | E_err:   0.010495
[2025-10-06 09:56:56] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -44.240532 | E_var:     0.2298 | E_err:   0.007490
[2025-10-06 09:57:01] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -44.235213 | E_var:     0.2399 | E_err:   0.007653
[2025-10-06 09:57:06] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -44.239595 | E_var:     0.2639 | E_err:   0.008027
[2025-10-06 09:57:11] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -44.250407 | E_var:     0.2417 | E_err:   0.007682
[2025-10-06 09:57:16] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -44.235493 | E_var:     0.2201 | E_err:   0.007330
[2025-10-06 09:57:21] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -44.240648 | E_var:     0.2039 | E_err:   0.007055
[2025-10-06 09:57:26] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -44.243644 | E_var:     0.2165 | E_err:   0.007271
[2025-10-06 09:57:31] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -44.238035 | E_var:     0.2525 | E_err:   0.007851
[2025-10-06 09:57:37] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -44.245395 | E_var:     0.2314 | E_err:   0.007516
[2025-10-06 09:57:42] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -44.237006 | E_var:     0.2594 | E_err:   0.007958
[2025-10-06 09:57:47] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -44.258623 | E_var:     0.2604 | E_err:   0.007973
[2025-10-06 09:57:52] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -44.238869 | E_var:     0.2524 | E_err:   0.007850
[2025-10-06 09:57:57] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -44.254421 | E_var:     0.2338 | E_err:   0.007555
[2025-10-06 09:58:02] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -44.240863 | E_var:     0.2387 | E_err:   0.007635
[2025-10-06 09:58:07] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -44.234731 | E_var:     0.2852 | E_err:   0.008344
[2025-10-06 09:58:12] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -44.235566 | E_var:     0.2324 | E_err:   0.007532
[2025-10-06 09:58:18] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -44.233006 | E_var:     0.2788 | E_err:   0.008251
[2025-10-06 09:58:23] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -44.234662 | E_var:     0.3388 | E_err:   0.009095
[2025-10-06 09:58:28] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -44.230532 | E_var:     0.2845 | E_err:   0.008335
[2025-10-06 09:58:33] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -44.251742 | E_var:     0.2247 | E_err:   0.007407
[2025-10-06 09:58:38] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -44.228470 | E_var:     0.3041 | E_err:   0.008617
[2025-10-06 09:58:43] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -44.230008 | E_var:     0.3020 | E_err:   0.008587
[2025-10-06 09:58:48] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -44.233828 | E_var:     0.2840 | E_err:   0.008326
[2025-10-06 09:58:53] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -44.238466 | E_var:     0.2348 | E_err:   0.007571
[2025-10-06 09:58:59] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -44.249082 | E_var:     0.2426 | E_err:   0.007696
[2025-10-06 09:59:04] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -44.232342 | E_var:     0.2699 | E_err:   0.008118
[2025-10-06 09:59:09] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -44.240449 | E_var:     0.2532 | E_err:   0.007862
[2025-10-06 09:59:14] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -44.245737 | E_var:     0.2020 | E_err:   0.007023
[2025-10-06 09:59:19] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -44.232300 | E_var:     0.3088 | E_err:   0.008683
[2025-10-06 09:59:24] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -44.237200 | E_var:     0.2596 | E_err:   0.007961
[2025-10-06 09:59:29] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -44.240104 | E_var:     0.2386 | E_err:   0.007633
[2025-10-06 09:59:34] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -44.240519 | E_var:     0.2369 | E_err:   0.007605
[2025-10-06 09:59:40] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -44.236556 | E_var:     0.2383 | E_err:   0.007628
[2025-10-06 09:59:45] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -44.238415 | E_var:     0.2380 | E_err:   0.007623
[2025-10-06 09:59:50] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -44.246491 | E_var:     0.2409 | E_err:   0.007670
[2025-10-06 09:59:55] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -44.243153 | E_var:     0.2393 | E_err:   0.007644
[2025-10-06 10:00:00] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -44.237846 | E_var:     0.2476 | E_err:   0.007775
[2025-10-06 10:00:05] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -44.241241 | E_var:     0.1946 | E_err:   0.006894
[2025-10-06 10:00:10] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -44.230589 | E_var:     0.2699 | E_err:   0.008117
[2025-10-06 10:00:15] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -44.242106 | E_var:     0.2243 | E_err:   0.007401
[2025-10-06 10:00:20] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -44.234821 | E_var:     0.2931 | E_err:   0.008459
[2025-10-06 10:00:26] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -44.248910 | E_var:     0.2384 | E_err:   0.007629
[2025-10-06 10:00:34] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -44.228264 | E_var:     0.2324 | E_err:   0.007533
[2025-10-06 10:00:55] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -44.233975 | E_var:     0.2860 | E_err:   0.008356
[2025-10-06 10:01:00] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -44.240493 | E_var:     0.2312 | E_err:   0.007513
[2025-10-06 10:01:00] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-06 10:01:05] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -44.232634 | E_var:     0.2122 | E_err:   0.007198
[2025-10-06 10:01:10] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -44.243192 | E_var:     0.2231 | E_err:   0.007380
[2025-10-06 10:01:15] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -44.238055 | E_var:     0.2072 | E_err:   0.007112
[2025-10-06 10:01:20] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -44.243262 | E_var:     0.5026 | E_err:   0.011078
[2025-10-06 10:01:25] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -44.245602 | E_var:     0.2033 | E_err:   0.007045
[2025-10-06 10:01:31] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -44.233226 | E_var:     0.3177 | E_err:   0.008807
[2025-10-06 10:01:36] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -44.240742 | E_var:     0.2453 | E_err:   0.007739
[2025-10-06 10:01:41] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -44.237648 | E_var:     0.2877 | E_err:   0.008381
[2025-10-06 10:01:46] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -44.222289 | E_var:     0.3306 | E_err:   0.008984
[2025-10-06 10:01:51] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -44.242367 | E_var:     0.2522 | E_err:   0.007847
[2025-10-06 10:01:56] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -44.250577 | E_var:     0.2418 | E_err:   0.007684
[2025-10-06 10:02:01] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -44.249216 | E_var:     0.2471 | E_err:   0.007768
[2025-10-06 10:02:06] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -44.228224 | E_var:     0.2506 | E_err:   0.007821
[2025-10-06 10:02:12] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -44.248950 | E_var:     0.2526 | E_err:   0.007853
[2025-10-06 10:02:17] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -44.257760 | E_var:     0.2634 | E_err:   0.008020
[2025-10-06 10:02:22] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -44.242171 | E_var:     0.2378 | E_err:   0.007620
[2025-10-06 10:02:27] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -44.238201 | E_var:     0.2093 | E_err:   0.007148
[2025-10-06 10:02:32] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -44.230155 | E_var:     0.3126 | E_err:   0.008736
[2025-10-06 10:02:37] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -44.244636 | E_var:     0.2990 | E_err:   0.008543
[2025-10-06 10:02:42] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -44.233704 | E_var:     0.2723 | E_err:   0.008154
[2025-10-06 10:02:47] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -44.247790 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 10:02:53] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -44.238607 | E_var:     0.2038 | E_err:   0.007054
[2025-10-06 10:02:58] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -44.236723 | E_var:     0.2404 | E_err:   0.007661
[2025-10-06 10:03:03] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -44.233044 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 10:03:08] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -44.249658 | E_var:     0.2506 | E_err:   0.007822
[2025-10-06 10:03:13] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -44.243286 | E_var:     0.2624 | E_err:   0.008005
[2025-10-06 10:03:18] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -44.250833 | E_var:     0.2485 | E_err:   0.007790
[2025-10-06 10:03:23] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -44.234083 | E_var:     0.2417 | E_err:   0.007682
[2025-10-06 10:03:28] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -44.230575 | E_var:     0.2262 | E_err:   0.007431
[2025-10-06 10:03:34] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -44.248996 | E_var:     0.2619 | E_err:   0.007997
[2025-10-06 10:03:39] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -44.247320 | E_var:     0.2072 | E_err:   0.007113
[2025-10-06 10:03:44] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -44.238912 | E_var:     0.2546 | E_err:   0.007885
[2025-10-06 10:03:49] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -44.237593 | E_var:     0.2270 | E_err:   0.007444
[2025-10-06 10:03:54] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -44.239077 | E_var:     0.2588 | E_err:   0.007950
[2025-10-06 10:03:59] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -44.256846 | E_var:     0.2001 | E_err:   0.006989
[2025-10-06 10:04:04] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -44.244669 | E_var:     0.2568 | E_err:   0.007918
[2025-10-06 10:04:09] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -44.236490 | E_var:     0.3848 | E_err:   0.009693
[2025-10-06 10:04:15] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -44.230534 | E_var:     0.2583 | E_err:   0.007941
[2025-10-06 10:04:20] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -44.235041 | E_var:     0.2873 | E_err:   0.008375
[2025-10-06 10:04:25] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -44.245114 | E_var:     0.2599 | E_err:   0.007966
[2025-10-06 10:04:30] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -44.250575 | E_var:     0.2409 | E_err:   0.007669
[2025-10-06 10:04:35] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -44.247909 | E_var:     0.2432 | E_err:   0.007706
[2025-10-06 10:04:40] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -44.258622 | E_var:     0.2197 | E_err:   0.007324
[2025-10-06 10:04:45] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -44.243837 | E_var:     0.2598 | E_err:   0.007964
[2025-10-06 10:04:50] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -44.243468 | E_var:     0.2381 | E_err:   0.007625
[2025-10-06 10:04:56] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -44.233126 | E_var:     0.2842 | E_err:   0.008330
[2025-10-06 10:05:01] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -44.238122 | E_var:     0.2398 | E_err:   0.007651
[2025-10-06 10:05:06] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -44.237712 | E_var:     0.2913 | E_err:   0.008433
[2025-10-06 10:05:11] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -44.225330 | E_var:     0.3565 | E_err:   0.009329
[2025-10-06 10:05:16] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -44.256660 | E_var:     0.2254 | E_err:   0.007418
[2025-10-06 10:05:21] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -44.247673 | E_var:     0.2297 | E_err:   0.007488
[2025-10-06 10:05:26] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -44.240623 | E_var:     0.2172 | E_err:   0.007282
[2025-10-06 10:05:31] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -44.241260 | E_var:     0.2513 | E_err:   0.007832
[2025-10-06 10:05:37] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -44.246837 | E_var:     0.3014 | E_err:   0.008578
[2025-10-06 10:05:42] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -44.247058 | E_var:     0.3141 | E_err:   0.008758
[2025-10-06 10:05:47] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -44.232692 | E_var:     0.2352 | E_err:   0.007577
[2025-10-06 10:05:52] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -44.240392 | E_var:     0.2419 | E_err:   0.007685
[2025-10-06 10:05:57] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -44.245126 | E_var:     0.2064 | E_err:   0.007099
[2025-10-06 10:06:02] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -44.247325 | E_var:     0.2097 | E_err:   0.007156
[2025-10-06 10:06:07] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -44.231746 | E_var:     0.2796 | E_err:   0.008262
[2025-10-06 10:06:12] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -44.240303 | E_var:     0.1966 | E_err:   0.006928
[2025-10-06 10:06:18] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -44.252606 | E_var:     0.2248 | E_err:   0.007408
[2025-10-06 10:06:23] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -44.237418 | E_var:     0.4454 | E_err:   0.010427
[2025-10-06 10:06:28] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -44.230934 | E_var:     0.2642 | E_err:   0.008032
[2025-10-06 10:06:33] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -44.247822 | E_var:     0.2532 | E_err:   0.007863
[2025-10-06 10:06:38] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -44.237400 | E_var:     0.2443 | E_err:   0.007723
[2025-10-06 10:06:43] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -44.246113 | E_var:     0.2284 | E_err:   0.007467
[2025-10-06 10:06:48] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -44.241764 | E_var:     0.2312 | E_err:   0.007513
[2025-10-06 10:06:53] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -44.227663 | E_var:     0.1998 | E_err:   0.006985
[2025-10-06 10:06:59] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -44.241856 | E_var:     0.5573 | E_err:   0.011665
[2025-10-06 10:07:04] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -44.256838 | E_var:     0.2681 | E_err:   0.008090
[2025-10-06 10:07:09] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -44.218071 | E_var:     0.4975 | E_err:   0.011021
[2025-10-06 10:07:14] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -44.218141 | E_var:     1.0858 | E_err:   0.016281
[2025-10-06 10:07:19] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -44.223424 | E_var:     0.8572 | E_err:   0.014466
[2025-10-06 10:07:24] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -44.242982 | E_var:     0.2868 | E_err:   0.008368
[2025-10-06 10:07:29] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -44.233625 | E_var:     0.3290 | E_err:   0.008962
[2025-10-06 10:07:34] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -44.248390 | E_var:     0.2411 | E_err:   0.007673
[2025-10-06 10:07:40] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -44.228193 | E_var:     0.2093 | E_err:   0.007148
[2025-10-06 10:07:45] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -44.245649 | E_var:     0.2209 | E_err:   0.007343
[2025-10-06 10:07:50] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -44.229621 | E_var:     0.2391 | E_err:   0.007641
[2025-10-06 10:07:55] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -44.246451 | E_var:     0.3246 | E_err:   0.008902
[2025-10-06 10:08:00] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -44.233996 | E_var:     0.2827 | E_err:   0.008308
[2025-10-06 10:08:05] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -44.246543 | E_var:     0.2073 | E_err:   0.007115
[2025-10-06 10:08:10] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -44.243163 | E_var:     0.2259 | E_err:   0.007427
[2025-10-06 10:08:15] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -44.253671 | E_var:     0.2116 | E_err:   0.007188
[2025-10-06 10:08:21] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -44.232840 | E_var:     0.2294 | E_err:   0.007483
[2025-10-06 10:08:26] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -44.231806 | E_var:     0.2298 | E_err:   0.007491
[2025-10-06 10:08:31] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -44.234597 | E_var:     0.2373 | E_err:   0.007611
[2025-10-06 10:08:36] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -44.235366 | E_var:     0.2326 | E_err:   0.007536
[2025-10-06 10:08:41] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -44.227873 | E_var:     0.2563 | E_err:   0.007911
[2025-10-06 10:08:46] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -44.234469 | E_var:     0.2608 | E_err:   0.007979
[2025-10-06 10:08:51] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -44.251779 | E_var:     0.3418 | E_err:   0.009135
[2025-10-06 10:08:56] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -44.226763 | E_var:     0.3308 | E_err:   0.008986
[2025-10-06 10:09:02] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -44.246527 | E_var:     0.2461 | E_err:   0.007752
[2025-10-06 10:09:07] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -44.242090 | E_var:     0.3687 | E_err:   0.009488
[2025-10-06 10:09:12] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -44.242302 | E_var:     0.2595 | E_err:   0.007960
[2025-10-06 10:09:17] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -44.232376 | E_var:     0.2065 | E_err:   0.007101
[2025-10-06 10:09:22] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -44.235865 | E_var:     0.2414 | E_err:   0.007677
[2025-10-06 10:09:27] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -44.243141 | E_var:     0.2703 | E_err:   0.008123
[2025-10-06 10:09:32] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -44.245558 | E_var:     0.2174 | E_err:   0.007286
[2025-10-06 10:09:32] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-06 10:09:38] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -44.246200 | E_var:     0.2555 | E_err:   0.007897
[2025-10-06 10:09:43] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -44.243475 | E_var:     0.2729 | E_err:   0.008162
[2025-10-06 10:09:48] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -44.230880 | E_var:     0.2651 | E_err:   0.008045
[2025-10-06 10:09:53] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -44.239468 | E_var:     0.2797 | E_err:   0.008264
[2025-10-06 10:09:58] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -44.237680 | E_var:     0.2438 | E_err:   0.007715
[2025-10-06 10:10:03] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -44.254274 | E_var:     0.2378 | E_err:   0.007619
[2025-10-06 10:10:08] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -44.246083 | E_var:     0.2434 | E_err:   0.007708
[2025-10-06 10:10:13] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -44.252982 | E_var:     0.2185 | E_err:   0.007303
[2025-10-06 10:10:19] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -44.244994 | E_var:     0.2425 | E_err:   0.007695
[2025-10-06 10:10:24] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -44.249046 | E_var:     0.2571 | E_err:   0.007922
[2025-10-06 10:10:29] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -44.251709 | E_var:     0.2207 | E_err:   0.007340
[2025-10-06 10:10:34] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -44.250737 | E_var:     0.2827 | E_err:   0.008308
[2025-10-06 10:10:39] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -44.237836 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 10:10:44] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -44.237849 | E_var:     0.2509 | E_err:   0.007827
[2025-10-06 10:10:49] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -44.238424 | E_var:     0.2714 | E_err:   0.008139
[2025-10-06 10:10:54] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -44.246341 | E_var:     0.2457 | E_err:   0.007745
[2025-10-06 10:11:00] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -44.239502 | E_var:     0.3075 | E_err:   0.008664
[2025-10-06 10:11:05] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -44.245853 | E_var:     0.2954 | E_err:   0.008493
[2025-10-06 10:11:10] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -44.235873 | E_var:     0.3405 | E_err:   0.009118
[2025-10-06 10:11:15] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -44.240239 | E_var:     0.2433 | E_err:   0.007708
[2025-10-06 10:11:20] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -44.253523 | E_var:     0.2427 | E_err:   0.007698
[2025-10-06 10:11:25] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -44.250623 | E_var:     0.2330 | E_err:   0.007542
[2025-10-06 10:11:30] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -44.237115 | E_var:     0.2184 | E_err:   0.007302
[2025-10-06 10:11:35] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -44.228260 | E_var:     0.3659 | E_err:   0.009452
[2025-10-06 10:11:41] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -44.235678 | E_var:     0.2690 | E_err:   0.008103
[2025-10-06 10:11:46] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -44.234032 | E_var:     0.2386 | E_err:   0.007632
[2025-10-06 10:11:51] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -44.254129 | E_var:     0.2283 | E_err:   0.007465
[2025-10-06 10:11:56] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -44.247412 | E_var:     0.2339 | E_err:   0.007556
[2025-10-06 10:12:01] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -44.250065 | E_var:     0.2541 | E_err:   0.007876
[2025-10-06 10:12:06] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -44.227667 | E_var:     0.2278 | E_err:   0.007457
[2025-10-06 10:12:11] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -44.242321 | E_var:     0.2988 | E_err:   0.008542
[2025-10-06 10:12:16] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -44.237358 | E_var:     0.3001 | E_err:   0.008559
[2025-10-06 10:12:22] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -44.236438 | E_var:     0.2879 | E_err:   0.008384
[2025-10-06 10:12:27] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -44.241077 | E_var:     0.2137 | E_err:   0.007224
[2025-10-06 10:12:32] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -44.238429 | E_var:     0.2393 | E_err:   0.007644
[2025-10-06 10:12:37] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -44.244352 | E_var:     0.1903 | E_err:   0.006817
[2025-10-06 10:12:42] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -44.250620 | E_var:     0.1988 | E_err:   0.006967
[2025-10-06 10:12:47] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -44.242720 | E_var:     0.3885 | E_err:   0.009739
[2025-10-06 10:12:52] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -44.257851 | E_var:     0.2512 | E_err:   0.007831
[2025-10-06 10:12:57] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -44.244000 | E_var:     0.2052 | E_err:   0.007078
[2025-10-06 10:13:03] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -44.235347 | E_var:     0.2189 | E_err:   0.007311
[2025-10-06 10:13:08] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -44.257013 | E_var:     0.2295 | E_err:   0.007485
[2025-10-06 10:13:13] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -44.245110 | E_var:     0.2269 | E_err:   0.007442
[2025-10-06 10:13:18] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -44.225328 | E_var:     0.3301 | E_err:   0.008977
[2025-10-06 10:13:23] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -44.254879 | E_var:     0.2398 | E_err:   0.007651
[2025-10-06 10:13:28] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -44.228351 | E_var:     0.3010 | E_err:   0.008573
[2025-10-06 10:13:33] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -44.238793 | E_var:     0.2124 | E_err:   0.007202
[2025-10-06 10:13:38] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -44.242119 | E_var:     0.2170 | E_err:   0.007278
[2025-10-06 10:13:44] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -44.231171 | E_var:     0.2551 | E_err:   0.007892
[2025-10-06 10:13:49] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -44.239820 | E_var:     0.2645 | E_err:   0.008036
[2025-10-06 10:13:54] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -44.243372 | E_var:     0.2615 | E_err:   0.007990
[2025-10-06 10:13:59] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -44.232843 | E_var:     0.3790 | E_err:   0.009620
[2025-10-06 10:14:04] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -44.255154 | E_var:     0.2791 | E_err:   0.008254
[2025-10-06 10:14:09] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -44.240945 | E_var:     0.3048 | E_err:   0.008627
[2025-10-06 10:14:14] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -44.240068 | E_var:     0.2206 | E_err:   0.007338
[2025-10-06 10:14:19] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -44.252078 | E_var:     0.2660 | E_err:   0.008059
[2025-10-06 10:14:25] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -44.234280 | E_var:     0.3111 | E_err:   0.008716
[2025-10-06 10:14:30] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -44.236021 | E_var:     0.3079 | E_err:   0.008670
[2025-10-06 10:14:35] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -44.238602 | E_var:     0.2684 | E_err:   0.008095
[2025-10-06 10:14:40] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -44.244151 | E_var:     0.2120 | E_err:   0.007194
[2025-10-06 10:14:45] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -44.250644 | E_var:     0.2380 | E_err:   0.007623
[2025-10-06 10:14:50] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -44.244086 | E_var:     0.2660 | E_err:   0.008059
[2025-10-06 10:14:55] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -44.232788 | E_var:     0.2598 | E_err:   0.007963
[2025-10-06 10:15:00] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -44.238754 | E_var:     0.2984 | E_err:   0.008535
[2025-10-06 10:15:06] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -44.252826 | E_var:     0.3754 | E_err:   0.009574
[2025-10-06 10:15:11] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -44.244063 | E_var:     0.1937 | E_err:   0.006878
[2025-10-06 10:15:16] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -44.230580 | E_var:     0.2334 | E_err:   0.007548
[2025-10-06 10:15:21] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -44.238488 | E_var:     0.2163 | E_err:   0.007266
[2025-10-06 10:15:26] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -44.243483 | E_var:     0.2235 | E_err:   0.007388
[2025-10-06 10:15:31] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -44.237185 | E_var:     0.2308 | E_err:   0.007507
[2025-10-06 10:15:36] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -44.238586 | E_var:     0.4049 | E_err:   0.009943
[2025-10-06 10:15:41] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -44.244063 | E_var:     0.3104 | E_err:   0.008706
[2025-10-06 10:15:47] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -44.235306 | E_var:     0.2445 | E_err:   0.007726
[2025-10-06 10:15:52] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -44.241342 | E_var:     0.2439 | E_err:   0.007717
[2025-10-06 10:15:57] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -44.251039 | E_var:     0.2749 | E_err:   0.008192
[2025-10-06 10:16:02] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -44.228114 | E_var:     0.2866 | E_err:   0.008365
[2025-10-06 10:16:07] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -44.247485 | E_var:     0.3332 | E_err:   0.009020
[2025-10-06 10:16:12] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -44.229839 | E_var:     0.2273 | E_err:   0.007449
[2025-10-06 10:16:17] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -44.233653 | E_var:     0.2075 | E_err:   0.007118
[2025-10-06 10:16:22] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -44.247576 | E_var:     0.2141 | E_err:   0.007230
[2025-10-06 10:16:28] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -44.246089 | E_var:     0.2237 | E_err:   0.007389
[2025-10-06 10:16:33] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -44.254266 | E_var:     0.2674 | E_err:   0.008079
[2025-10-06 10:16:38] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -44.240658 | E_var:     0.2034 | E_err:   0.007047
[2025-10-06 10:16:43] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -44.236100 | E_var:     0.2519 | E_err:   0.007843
[2025-10-06 10:16:48] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -44.232748 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 10:16:53] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -44.235735 | E_var:     0.2484 | E_err:   0.007788
[2025-10-06 10:16:58] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -44.243349 | E_var:     0.2299 | E_err:   0.007492
[2025-10-06 10:17:03] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -44.250563 | E_var:     0.2130 | E_err:   0.007211
[2025-10-06 10:17:09] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -44.243687 | E_var:     0.2019 | E_err:   0.007022
[2025-10-06 10:17:14] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -44.235955 | E_var:     0.2252 | E_err:   0.007415
[2025-10-06 10:17:19] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -44.256521 | E_var:     0.2831 | E_err:   0.008313
[2025-10-06 10:17:24] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -44.234198 | E_var:     0.2978 | E_err:   0.008526
[2025-10-06 10:17:29] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -44.223146 | E_var:     0.2472 | E_err:   0.007768
[2025-10-06 10:17:34] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -44.241457 | E_var:     0.2623 | E_err:   0.008002
[2025-10-06 10:17:39] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -44.252597 | E_var:     0.1919 | E_err:   0.006845
[2025-10-06 10:17:45] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -44.236498 | E_var:     0.2008 | E_err:   0.007002
[2025-10-06 10:17:50] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -44.247604 | E_var:     0.2833 | E_err:   0.008317
[2025-10-06 10:17:55] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -44.242067 | E_var:     0.2184 | E_err:   0.007303
[2025-10-06 10:18:00] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -44.245185 | E_var:     0.3419 | E_err:   0.009137
[2025-10-06 10:18:05] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -44.250330 | E_var:     0.2962 | E_err:   0.008504
[2025-10-06 10:18:05] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-06 10:18:10] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -44.249879 | E_var:     0.2421 | E_err:   0.007687
[2025-10-06 10:18:15] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -44.236497 | E_var:     0.2201 | E_err:   0.007331
[2025-10-06 10:18:21] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -44.240907 | E_var:     0.2714 | E_err:   0.008141
[2025-10-06 10:18:26] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -44.246459 | E_var:     0.2321 | E_err:   0.007527
[2025-10-06 10:18:31] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -44.254216 | E_var:     0.2392 | E_err:   0.007642
[2025-10-06 10:18:36] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -44.233500 | E_var:     0.2013 | E_err:   0.007011
[2025-10-06 10:18:41] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -44.242379 | E_var:     0.2300 | E_err:   0.007494
[2025-10-06 10:18:46] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -44.230640 | E_var:     0.2310 | E_err:   0.007509
[2025-10-06 10:18:51] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -44.245546 | E_var:     0.2248 | E_err:   0.007408
[2025-10-06 10:18:56] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -44.257221 | E_var:     0.8951 | E_err:   0.014783
[2025-10-06 10:19:02] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -44.241059 | E_var:     0.2193 | E_err:   0.007318
[2025-10-06 10:19:07] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -44.239491 | E_var:     0.2466 | E_err:   0.007759
[2025-10-06 10:19:12] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -44.247022 | E_var:     0.3159 | E_err:   0.008782
[2025-10-06 10:19:17] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -44.246517 | E_var:     0.1996 | E_err:   0.006980
[2025-10-06 10:19:22] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -44.233405 | E_var:     0.2258 | E_err:   0.007425
[2025-10-06 10:19:27] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -44.247357 | E_var:     0.2973 | E_err:   0.008520
[2025-10-06 10:19:32] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -44.237804 | E_var:     0.2551 | E_err:   0.007892
[2025-10-06 10:19:37] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -44.243614 | E_var:     0.2554 | E_err:   0.007897
[2025-10-06 10:19:42] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -44.242258 | E_var:     0.2323 | E_err:   0.007531
[2025-10-06 10:19:48] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -44.248650 | E_var:     0.2092 | E_err:   0.007147
[2025-10-06 10:19:53] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -44.235303 | E_var:     0.2477 | E_err:   0.007777
[2025-10-06 10:19:58] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -44.248074 | E_var:     0.2361 | E_err:   0.007593
[2025-10-06 10:20:03] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -44.242779 | E_var:     0.3697 | E_err:   0.009501
[2025-10-06 10:20:08] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -44.240250 | E_var:     0.2477 | E_err:   0.007776
[2025-10-06 10:20:13] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -44.246243 | E_var:     0.2197 | E_err:   0.007324
[2025-10-06 10:20:18] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -44.233734 | E_var:     0.4407 | E_err:   0.010372
[2025-10-06 10:20:23] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -44.260289 | E_var:     0.2307 | E_err:   0.007505
[2025-10-06 10:20:29] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -44.238243 | E_var:     0.2055 | E_err:   0.007083
[2025-10-06 10:20:34] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -44.243057 | E_var:     0.2098 | E_err:   0.007156
[2025-10-06 10:20:39] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -44.255131 | E_var:     0.2706 | E_err:   0.008128
[2025-10-06 10:20:44] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -44.243923 | E_var:     0.1995 | E_err:   0.006980
[2025-10-06 10:20:49] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -44.236205 | E_var:     0.2404 | E_err:   0.007661
[2025-10-06 10:20:54] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -44.244806 | E_var:     0.2553 | E_err:   0.007895
[2025-10-06 10:20:59] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -44.226444 | E_var:     0.2132 | E_err:   0.007215
[2025-10-06 10:21:04] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -44.237438 | E_var:     0.2398 | E_err:   0.007652
[2025-10-06 10:21:10] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -44.242056 | E_var:     0.2441 | E_err:   0.007720
[2025-10-06 10:21:15] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -44.237003 | E_var:     0.2271 | E_err:   0.007446
[2025-10-06 10:21:20] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -44.246029 | E_var:     0.2192 | E_err:   0.007316
[2025-10-06 10:21:25] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -44.239947 | E_var:     0.2090 | E_err:   0.007143
[2025-10-06 10:21:30] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -44.239613 | E_var:     0.2348 | E_err:   0.007572
[2025-10-06 10:21:35] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -44.252827 | E_var:     0.3525 | E_err:   0.009277
[2025-10-06 10:21:40] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -44.247133 | E_var:     0.2504 | E_err:   0.007818
[2025-10-06 10:21:45] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -44.256497 | E_var:     0.3278 | E_err:   0.008946
[2025-10-06 10:21:51] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -44.231938 | E_var:     0.4865 | E_err:   0.010898
[2025-10-06 10:21:56] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -44.265649 | E_var:     0.2174 | E_err:   0.007285
[2025-10-06 10:22:01] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -44.235858 | E_var:     0.2178 | E_err:   0.007291
[2025-10-06 10:22:06] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -44.257856 | E_var:     0.2660 | E_err:   0.008059
[2025-10-06 10:22:11] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -44.247323 | E_var:     0.2268 | E_err:   0.007441
[2025-10-06 10:22:16] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -44.249780 | E_var:     0.3730 | E_err:   0.009542
[2025-10-06 10:22:21] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -44.245443 | E_var:     0.2071 | E_err:   0.007110
[2025-10-06 10:22:27] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -44.224407 | E_var:     0.2051 | E_err:   0.007076
[2025-10-06 10:22:32] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -44.244840 | E_var:     0.2410 | E_err:   0.007670
[2025-10-06 10:22:37] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -44.240872 | E_var:     0.3482 | E_err:   0.009220
[2025-10-06 10:22:42] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -44.241622 | E_var:     0.2279 | E_err:   0.007460
[2025-10-06 10:22:47] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -44.240913 | E_var:     0.2221 | E_err:   0.007364
[2025-10-06 10:22:52] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -44.237588 | E_var:     0.2056 | E_err:   0.007085
[2025-10-06 10:22:57] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -44.236068 | E_var:     0.2116 | E_err:   0.007187
[2025-10-06 10:23:02] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -44.241434 | E_var:     0.2667 | E_err:   0.008070
[2025-10-06 10:23:07] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -44.247431 | E_var:     0.2571 | E_err:   0.007922
[2025-10-06 10:23:13] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -44.260303 | E_var:     0.2100 | E_err:   0.007161
[2025-10-06 10:23:18] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -44.244343 | E_var:     0.2479 | E_err:   0.007780
[2025-10-06 10:23:23] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -44.251481 | E_var:     0.2912 | E_err:   0.008431
[2025-10-06 10:23:28] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -44.239110 | E_var:     0.2609 | E_err:   0.007982
[2025-10-06 10:23:33] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -44.246482 | E_var:     0.2096 | E_err:   0.007153
[2025-10-06 10:23:38] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -44.226985 | E_var:     0.3124 | E_err:   0.008733
[2025-10-06 10:23:43] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -44.242235 | E_var:     0.2393 | E_err:   0.007644
[2025-10-06 10:23:48] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -44.242050 | E_var:     0.2603 | E_err:   0.007972
[2025-10-06 10:23:54] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -44.231855 | E_var:     0.3004 | E_err:   0.008564
[2025-10-06 10:23:59] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -44.241537 | E_var:     0.2074 | E_err:   0.007116
[2025-10-06 10:24:04] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -44.243337 | E_var:     0.2032 | E_err:   0.007044
[2025-10-06 10:24:09] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -44.248209 | E_var:     0.3319 | E_err:   0.009002
[2025-10-06 10:24:14] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -44.243557 | E_var:     0.3159 | E_err:   0.008782
[2025-10-06 10:24:19] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -44.229000 | E_var:     0.2426 | E_err:   0.007697
[2025-10-06 10:24:24] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -44.242626 | E_var:     0.2324 | E_err:   0.007532
[2025-10-06 10:24:29] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -44.248094 | E_var:     0.2270 | E_err:   0.007444
[2025-10-06 10:24:35] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -44.248391 | E_var:     0.2827 | E_err:   0.008308
[2025-10-06 10:24:40] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -44.241786 | E_var:     0.2758 | E_err:   0.008206
[2025-10-06 10:24:45] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -44.246872 | E_var:     0.2429 | E_err:   0.007700
[2025-10-06 10:24:50] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -44.250369 | E_var:     0.2199 | E_err:   0.007326
[2025-10-06 10:24:55] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -44.243720 | E_var:     0.2897 | E_err:   0.008410
[2025-10-06 10:25:00] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -44.249981 | E_var:     0.2376 | E_err:   0.007616
[2025-10-06 10:25:05] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -44.240275 | E_var:     0.2543 | E_err:   0.007879
[2025-10-06 10:25:10] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -44.248176 | E_var:     0.2050 | E_err:   0.007075
[2025-10-06 10:25:16] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -44.243302 | E_var:     0.2247 | E_err:   0.007406
[2025-10-06 10:25:21] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -44.240091 | E_var:     0.2344 | E_err:   0.007564
[2025-10-06 10:25:26] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -44.250195 | E_var:     0.2865 | E_err:   0.008363
[2025-10-06 10:25:31] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -44.237605 | E_var:     0.2140 | E_err:   0.007228
[2025-10-06 10:25:36] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -44.224361 | E_var:     0.2982 | E_err:   0.008533
[2025-10-06 10:25:41] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -44.247223 | E_var:     0.2571 | E_err:   0.007923
[2025-10-06 10:25:46] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -44.246680 | E_var:     0.2592 | E_err:   0.007954
[2025-10-06 10:25:51] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -44.249542 | E_var:     0.3382 | E_err:   0.009087
[2025-10-06 10:25:57] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -44.247938 | E_var:     0.2202 | E_err:   0.007332
[2025-10-06 10:26:02] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -44.244430 | E_var:     0.3341 | E_err:   0.009031
[2025-10-06 10:26:07] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -44.238580 | E_var:     0.2302 | E_err:   0.007496
[2025-10-06 10:26:12] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -44.244926 | E_var:     0.2535 | E_err:   0.007866
[2025-10-06 10:26:17] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -44.228067 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 10:26:22] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -44.245165 | E_var:     0.2398 | E_err:   0.007651
[2025-10-06 10:26:27] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -44.234088 | E_var:     0.2112 | E_err:   0.007180
[2025-10-06 10:26:32] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -44.243238 | E_var:     0.2330 | E_err:   0.007542
[2025-10-06 10:26:38] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -44.259640 | E_var:     0.8143 | E_err:   0.014100
[2025-10-06 10:26:38] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-06 10:26:43] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -44.232607 | E_var:     0.2666 | E_err:   0.008068
[2025-10-06 10:26:48] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -44.252186 | E_var:     0.2320 | E_err:   0.007527
[2025-10-06 10:26:53] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -44.243351 | E_var:     0.3007 | E_err:   0.008568
[2025-10-06 10:26:58] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -44.242618 | E_var:     0.2651 | E_err:   0.008046
[2025-10-06 10:27:03] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -44.244256 | E_var:     0.3674 | E_err:   0.009471
[2025-10-06 10:27:08] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -44.241891 | E_var:     0.2108 | E_err:   0.007174
[2025-10-06 10:27:14] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -44.237134 | E_var:     0.2725 | E_err:   0.008157
[2025-10-06 10:27:19] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -44.253310 | E_var:     0.2471 | E_err:   0.007767
[2025-10-06 10:27:24] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -44.237464 | E_var:     0.2729 | E_err:   0.008163
[2025-10-06 10:27:29] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -44.247641 | E_var:     0.1745 | E_err:   0.006527
[2025-10-06 10:27:34] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -44.246383 | E_var:     0.2569 | E_err:   0.007920
[2025-10-06 10:27:39] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -44.237927 | E_var:     0.2393 | E_err:   0.007643
[2025-10-06 10:27:44] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -44.258582 | E_var:     0.3464 | E_err:   0.009196
[2025-10-06 10:27:49] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -44.236206 | E_var:     0.3195 | E_err:   0.008832
[2025-10-06 10:27:55] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -44.233152 | E_var:     0.2405 | E_err:   0.007662
[2025-10-06 10:28:00] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -44.248390 | E_var:     0.3949 | E_err:   0.009819
[2025-10-06 10:28:05] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -44.237457 | E_var:     0.2308 | E_err:   0.007506
[2025-10-06 10:28:10] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -44.234491 | E_var:     0.1992 | E_err:   0.006974
[2025-10-06 10:28:15] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -44.247934 | E_var:     0.2564 | E_err:   0.007912
[2025-10-06 10:28:20] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -44.256485 | E_var:     0.6792 | E_err:   0.012877
[2025-10-06 10:28:25] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -44.246056 | E_var:     0.3456 | E_err:   0.009186
[2025-10-06 10:28:30] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -44.243990 | E_var:     0.2784 | E_err:   0.008245
[2025-10-06 10:28:36] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -44.258196 | E_var:     0.2293 | E_err:   0.007481
[2025-10-06 10:28:41] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -44.246004 | E_var:     0.3254 | E_err:   0.008913
[2025-10-06 10:28:46] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -44.229165 | E_var:     0.2981 | E_err:   0.008531
[2025-10-06 10:28:51] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -44.237682 | E_var:     0.2617 | E_err:   0.007993
[2025-10-06 10:28:56] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -44.257548 | E_var:     0.2400 | E_err:   0.007655
[2025-10-06 10:29:01] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -44.228200 | E_var:     0.2685 | E_err:   0.008097
[2025-10-06 10:29:06] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -44.235818 | E_var:     0.2554 | E_err:   0.007897
[2025-10-06 10:29:11] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -44.247992 | E_var:     0.2185 | E_err:   0.007303
[2025-10-06 10:29:17] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -44.246020 | E_var:     0.1976 | E_err:   0.006946
[2025-10-06 10:29:22] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -44.252284 | E_var:     0.2228 | E_err:   0.007376
[2025-10-06 10:29:27] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -44.232343 | E_var:     0.2691 | E_err:   0.008106
[2025-10-06 10:29:32] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -44.245782 | E_var:     0.2592 | E_err:   0.007954
[2025-10-06 10:29:37] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -44.241573 | E_var:     0.2090 | E_err:   0.007144
[2025-10-06 10:29:42] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -44.237073 | E_var:     0.2552 | E_err:   0.007893
[2025-10-06 10:29:47] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -44.228209 | E_var:     0.2270 | E_err:   0.007445
[2025-10-06 10:29:52] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -44.247062 | E_var:     0.2298 | E_err:   0.007491
[2025-10-06 10:29:58] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -44.245697 | E_var:     0.2383 | E_err:   0.007627
[2025-10-06 10:30:03] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -44.257800 | E_var:     0.2173 | E_err:   0.007283
[2025-10-06 10:30:08] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -44.257211 | E_var:     0.2351 | E_err:   0.007576
[2025-10-06 10:30:13] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -44.241006 | E_var:     0.2602 | E_err:   0.007970
[2025-10-06 10:30:18] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -44.250474 | E_var:     0.2459 | E_err:   0.007749
[2025-10-06 10:30:23] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -44.237574 | E_var:     0.2538 | E_err:   0.007872
[2025-10-06 10:30:28] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -44.233149 | E_var:     0.3519 | E_err:   0.009268
[2025-10-06 10:30:33] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -44.239238 | E_var:     0.2817 | E_err:   0.008293
[2025-10-06 10:30:39] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -44.249953 | E_var:     0.4170 | E_err:   0.010090
[2025-10-06 10:30:44] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -44.242622 | E_var:     0.2372 | E_err:   0.007610
[2025-10-06 10:30:49] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -44.245606 | E_var:     0.2213 | E_err:   0.007351
[2025-10-06 10:30:54] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -44.237886 | E_var:     0.2335 | E_err:   0.007550
[2025-10-06 10:30:59] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -44.238081 | E_var:     0.2495 | E_err:   0.007805
[2025-10-06 10:31:04] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -44.241903 | E_var:     0.2302 | E_err:   0.007497
[2025-10-06 10:31:09] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -44.253831 | E_var:     0.2454 | E_err:   0.007741
[2025-10-06 10:31:14] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -44.242148 | E_var:     0.2140 | E_err:   0.007228
[2025-10-06 10:31:20] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -44.226814 | E_var:     1.0980 | E_err:   0.016373
[2025-10-06 10:31:25] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -44.259393 | E_var:     0.2561 | E_err:   0.007908
[2025-10-06 10:31:30] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -44.252846 | E_var:     0.2066 | E_err:   0.007102
[2025-10-06 10:31:35] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -44.236284 | E_var:     0.6848 | E_err:   0.012930
[2025-10-06 10:31:40] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -44.238427 | E_var:     0.2170 | E_err:   0.007279
[2025-10-06 10:31:45] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -44.229546 | E_var:     0.2586 | E_err:   0.007946
[2025-10-06 10:31:50] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -44.253265 | E_var:     0.3396 | E_err:   0.009106
[2025-10-06 10:31:55] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -44.253637 | E_var:     0.2461 | E_err:   0.007751
[2025-10-06 10:32:01] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -44.239461 | E_var:     0.2481 | E_err:   0.007783
[2025-10-06 10:32:06] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -44.245096 | E_var:     0.2093 | E_err:   0.007148
[2025-10-06 10:32:11] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -44.245142 | E_var:     0.2896 | E_err:   0.008408
[2025-10-06 10:32:16] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -44.264165 | E_var:     0.2556 | E_err:   0.007899
[2025-10-06 10:32:21] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -44.239736 | E_var:     0.2036 | E_err:   0.007050
[2025-10-06 10:32:26] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -44.247453 | E_var:     0.2250 | E_err:   0.007412
[2025-10-06 10:32:31] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -44.256570 | E_var:     0.2635 | E_err:   0.008021
[2025-10-06 10:32:36] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -44.248807 | E_var:     0.2685 | E_err:   0.008097
[2025-10-06 10:32:42] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -44.240133 | E_var:     0.3324 | E_err:   0.009008
[2025-10-06 10:32:47] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -44.245031 | E_var:     0.2822 | E_err:   0.008301
[2025-10-06 10:32:52] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -44.236486 | E_var:     0.2696 | E_err:   0.008113
[2025-10-06 10:32:57] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -44.238789 | E_var:     0.1979 | E_err:   0.006951
[2025-10-06 10:33:02] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -44.247716 | E_var:     0.2456 | E_err:   0.007743
[2025-10-06 10:33:07] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -44.237813 | E_var:     0.2581 | E_err:   0.007938
[2025-10-06 10:33:12] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -44.244547 | E_var:     0.2014 | E_err:   0.007012
[2025-10-06 10:33:17] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -44.242607 | E_var:     0.2108 | E_err:   0.007173
[2025-10-06 10:33:23] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -44.248507 | E_var:     0.4908 | E_err:   0.010946
[2025-10-06 10:33:28] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -44.228859 | E_var:     0.2516 | E_err:   0.007837
[2025-10-06 10:33:33] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -44.235861 | E_var:     0.2297 | E_err:   0.007489
[2025-10-06 10:33:38] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -44.244964 | E_var:     0.2299 | E_err:   0.007491
[2025-10-06 10:33:43] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -44.244831 | E_var:     0.2469 | E_err:   0.007763
[2025-10-06 10:33:48] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -44.245018 | E_var:     0.2346 | E_err:   0.007569
[2025-10-06 10:33:53] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -44.254169 | E_var:     0.2441 | E_err:   0.007719
[2025-10-06 10:33:58] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -44.247717 | E_var:     0.2475 | E_err:   0.007773
[2025-10-06 10:34:04] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -44.240996 | E_var:     0.2696 | E_err:   0.008114
[2025-10-06 10:34:09] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -44.238651 | E_var:     0.2099 | E_err:   0.007158
[2025-10-06 10:34:14] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -44.244726 | E_var:     0.2424 | E_err:   0.007693
[2025-10-06 10:34:19] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -44.233511 | E_var:     0.2264 | E_err:   0.007435
[2025-10-06 10:34:24] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -44.243952 | E_var:     0.2665 | E_err:   0.008066
[2025-10-06 10:34:29] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -44.237515 | E_var:     0.2337 | E_err:   0.007554
[2025-10-06 10:34:34] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -44.239219 | E_var:     0.5476 | E_err:   0.011562
[2025-10-06 10:34:39] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -44.235955 | E_var:     0.2371 | E_err:   0.007608
[2025-10-06 10:34:45] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -44.242810 | E_var:     0.2115 | E_err:   0.007185
[2025-10-06 10:34:50] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -44.248043 | E_var:     0.6784 | E_err:   0.012869
[2025-10-06 10:34:55] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -44.241410 | E_var:     0.2948 | E_err:   0.008483
[2025-10-06 10:35:00] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -44.234410 | E_var:     0.3353 | E_err:   0.009048
[2025-10-06 10:35:05] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -44.236989 | E_var:     0.2190 | E_err:   0.007313
[2025-10-06 10:35:10] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -44.248368 | E_var:     0.2057 | E_err:   0.007087
[2025-10-06 10:35:10] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-06 10:35:15] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -44.244363 | E_var:     0.4034 | E_err:   0.009925
[2025-10-06 10:35:21] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -44.232861 | E_var:     0.2889 | E_err:   0.008398
[2025-10-06 10:35:26] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -44.247324 | E_var:     0.3620 | E_err:   0.009401
[2025-10-06 10:35:31] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -44.239277 | E_var:     0.2486 | E_err:   0.007790
[2025-10-06 10:35:36] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -44.248048 | E_var:     0.2458 | E_err:   0.007747
[2025-10-06 10:35:41] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -44.245565 | E_var:     0.2919 | E_err:   0.008441
[2025-10-06 10:35:46] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -44.228895 | E_var:     0.3154 | E_err:   0.008776
[2025-10-06 10:35:51] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -44.244429 | E_var:     0.2451 | E_err:   0.007736
[2025-10-06 10:35:56] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -44.247190 | E_var:     0.3017 | E_err:   0.008583
[2025-10-06 10:36:02] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -44.238916 | E_var:     0.3971 | E_err:   0.009846
[2025-10-06 10:36:07] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -44.253504 | E_var:     0.1917 | E_err:   0.006841
[2025-10-06 10:36:12] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -44.253001 | E_var:     0.3568 | E_err:   0.009334
[2025-10-06 10:36:17] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -44.248757 | E_var:     0.2664 | E_err:   0.008064
[2025-10-06 10:36:22] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -44.244762 | E_var:     0.2543 | E_err:   0.007879
[2025-10-06 10:36:27] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -44.240970 | E_var:     0.2513 | E_err:   0.007833
[2025-10-06 10:36:32] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -44.238901 | E_var:     0.3834 | E_err:   0.009675
[2025-10-06 10:36:38] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -44.256017 | E_var:     0.2805 | E_err:   0.008275
[2025-10-06 10:36:43] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -44.251945 | E_var:     0.3218 | E_err:   0.008864
[2025-10-06 10:36:48] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -44.239526 | E_var:     0.2472 | E_err:   0.007769
[2025-10-06 10:36:53] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -44.249774 | E_var:     0.1938 | E_err:   0.006878
[2025-10-06 10:36:58] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -44.228042 | E_var:     0.9878 | E_err:   0.015529
[2025-10-06 10:37:03] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -44.240126 | E_var:     0.3923 | E_err:   0.009786
[2025-10-06 10:37:08] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -44.234832 | E_var:     0.4433 | E_err:   0.010403
[2025-10-06 10:37:13] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -44.239615 | E_var:     0.2372 | E_err:   0.007610
[2025-10-06 10:37:19] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -44.237179 | E_var:     0.2499 | E_err:   0.007811
[2025-10-06 10:37:24] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -44.251239 | E_var:     0.2512 | E_err:   0.007831
[2025-10-06 10:37:29] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -44.233161 | E_var:     0.2332 | E_err:   0.007546
[2025-10-06 10:37:34] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -44.242584 | E_var:     0.1995 | E_err:   0.006979
[2025-10-06 10:37:39] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -44.221830 | E_var:     0.3881 | E_err:   0.009734
[2025-10-06 10:37:44] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -44.238312 | E_var:     0.2254 | E_err:   0.007418
[2025-10-06 10:37:50] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -44.241507 | E_var:     0.2087 | E_err:   0.007137
[2025-10-06 10:37:55] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -44.263205 | E_var:     0.3188 | E_err:   0.008822
[2025-10-06 10:38:00] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -44.228261 | E_var:     0.2470 | E_err:   0.007765
[2025-10-06 10:38:05] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -44.247637 | E_var:     0.4304 | E_err:   0.010250
[2025-10-06 10:38:10] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -44.242138 | E_var:     0.2546 | E_err:   0.007885
[2025-10-06 10:38:15] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -44.237628 | E_var:     0.2649 | E_err:   0.008041
[2025-10-06 10:38:20] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -44.243873 | E_var:     0.2235 | E_err:   0.007387
[2025-10-06 10:38:25] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -44.242178 | E_var:     0.2464 | E_err:   0.007757
[2025-10-06 10:38:31] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -44.243771 | E_var:     0.2557 | E_err:   0.007901
[2025-10-06 10:38:36] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -44.243050 | E_var:     0.2044 | E_err:   0.007064
[2025-10-06 10:38:41] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -44.249898 | E_var:     0.3133 | E_err:   0.008746
[2025-10-06 10:38:46] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -44.225309 | E_var:     0.2599 | E_err:   0.007966
[2025-10-06 10:38:51] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -44.236674 | E_var:     0.2310 | E_err:   0.007510
[2025-10-06 10:38:56] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -44.255744 | E_var:     0.2565 | E_err:   0.007914
[2025-10-06 10:39:01] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -44.244962 | E_var:     0.2390 | E_err:   0.007639
[2025-10-06 10:39:06] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -44.236921 | E_var:     0.2266 | E_err:   0.007438
[2025-10-06 10:39:12] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -44.239721 | E_var:     0.2498 | E_err:   0.007809
[2025-10-06 10:39:17] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -44.245531 | E_var:     0.2381 | E_err:   0.007624
[2025-10-06 10:39:22] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -44.240789 | E_var:     0.2411 | E_err:   0.007672
[2025-10-06 10:39:27] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -44.238868 | E_var:     0.2581 | E_err:   0.007938
[2025-10-06 10:39:27] ======================================================================================================
[2025-10-06 10:39:27] ✅ Training completed successfully
[2025-10-06 10:39:27] Total restarts: 2
[2025-10-06 10:39:29] Final Energy: -44.23886799 ± 0.00793845
[2025-10-06 10:39:29] Final Variance: 0.258126
[2025-10-06 10:39:29] ======================================================================================================
[2025-10-06 10:39:29] ======================================================================================================
[2025-10-06 10:39:29] Training completed | Runtime: 5444.0s
[2025-10-06 10:39:30] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-06 10:39:30] ======================================================================================================
