[2025-10-07 07:43:38] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.78/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 07:43:38]   - 迭代次数: final
[2025-10-07 07:43:38]   - 能量: -43.591799+0.000669j ± 0.007068, Var: 0.204632
[2025-10-07 07:43:38]   - 时间戳: 2025-10-07T07:43:18.615012+08:00
[2025-10-07 07:43:59] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 07:43:59] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 07:43:59] ======================================================================================================
[2025-10-07 07:43:59] GCNN for Shastry-Sutherland Model
[2025-10-07 07:43:59] ======================================================================================================
[2025-10-07 07:43:59] System parameters:
[2025-10-07 07:43:59]   - System size: L=5, N=100
[2025-10-07 07:43:59]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-10-07 07:43:59] ------------------------------------------------------------------------------------------------------
[2025-10-07 07:43:59] Model parameters:
[2025-10-07 07:43:59]   - Number of layers = 6
[2025-10-07 07:43:59]   - Number of features = 4
[2025-10-07 07:43:59]   - Total parameters = 32444
[2025-10-07 07:43:59] ------------------------------------------------------------------------------------------------------
[2025-10-07 07:43:59] Training parameters:
[2025-10-07 07:43:59]   - Total iterations: 1050
[2025-10-07 07:43:59]   - Annealing cycles: 3
[2025-10-07 07:43:59]   - Initial period: 150
[2025-10-07 07:43:59]   - Period multiplier: 2.0
[2025-10-07 07:43:59]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 07:43:59]   - Samples: 4096
[2025-10-07 07:43:59]   - Discarded samples: 0
[2025-10-07 07:43:59]   - Chunk size: 4096
[2025-10-07 07:43:59]   - Diagonal shift: 0.15
[2025-10-07 07:43:59]   - Gradient clipping: 1.0
[2025-10-07 07:43:59]   - Checkpoint enabled: interval=100
[2025-10-07 07:43:59]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.79/model_L6F4/training/checkpoints
[2025-10-07 07:43:59] ------------------------------------------------------------------------------------------------------
[2025-10-07 07:43:59] Device status:
[2025-10-07 07:43:59]   - Devices model: NVIDIA H200 NVL
[2025-10-07 07:43:59]   - Number of devices: 1
[2025-10-07 07:43:59]   - Sharding: True
[2025-10-07 07:44:00] ======================================================================================================
[2025-10-07 07:44:41] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -43.945187 | E_var:     1.0713 | E_err:   0.016172
[2025-10-07 07:45:07] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -44.106801 | E_var:     0.5783 | E_err:   0.011883
[2025-10-07 07:45:15] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -44.177298 | E_var:     0.5362 | E_err:   0.011441
[2025-10-07 07:45:23] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -44.207062 | E_var:     0.4478 | E_err:   0.010456
[2025-10-07 07:45:31] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -44.213916 | E_var:     0.4537 | E_err:   0.010524
[2025-10-07 07:45:39] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -44.216503 | E_var:     0.3422 | E_err:   0.009140
[2025-10-07 07:45:47] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -44.220063 | E_var:     0.3327 | E_err:   0.009013
[2025-10-07 07:45:54] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -44.220519 | E_var:     0.3968 | E_err:   0.009842
[2025-10-07 07:46:02] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -44.228824 | E_var:     0.3448 | E_err:   0.009175
[2025-10-07 07:46:10] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -44.214645 | E_var:     0.2912 | E_err:   0.008432
[2025-10-07 07:46:18] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -44.230363 | E_var:     0.3346 | E_err:   0.009039
[2025-10-07 07:46:26] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -44.228042 | E_var:     0.3165 | E_err:   0.008790
[2025-10-07 07:46:33] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -44.225150 | E_var:     0.2688 | E_err:   0.008102
[2025-10-07 07:46:41] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -44.259143 | E_var:     0.4602 | E_err:   0.010599
[2025-10-07 07:46:49] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -44.229123 | E_var:     0.3557 | E_err:   0.009319
[2025-10-07 07:46:57] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -44.244945 | E_var:     0.2992 | E_err:   0.008547
[2025-10-07 07:47:05] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -44.237472 | E_var:     0.2632 | E_err:   0.008016
[2025-10-07 07:47:13] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -44.244272 | E_var:     0.3035 | E_err:   0.008607
[2025-10-07 07:47:20] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -44.240457 | E_var:     0.4383 | E_err:   0.010345
[2025-10-07 07:47:28] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -44.234548 | E_var:     0.2654 | E_err:   0.008049
[2025-10-07 07:47:36] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -44.249656 | E_var:     0.2409 | E_err:   0.007668
[2025-10-07 07:47:44] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -44.238318 | E_var:     0.2140 | E_err:   0.007228
[2025-10-07 07:47:52] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -44.237627 | E_var:     0.3068 | E_err:   0.008655
[2025-10-07 07:47:59] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -44.244413 | E_var:     0.3228 | E_err:   0.008878
[2025-10-07 07:48:07] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -44.227122 | E_var:     0.2871 | E_err:   0.008373
[2025-10-07 07:48:15] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -44.228253 | E_var:     0.2586 | E_err:   0.007945
[2025-10-07 07:48:23] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -44.231359 | E_var:     0.2236 | E_err:   0.007389
[2025-10-07 07:48:31] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -44.216762 | E_var:     0.3464 | E_err:   0.009196
[2025-10-07 07:48:39] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -44.239075 | E_var:     0.3880 | E_err:   0.009732
[2025-10-07 07:48:46] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -44.233619 | E_var:     0.2905 | E_err:   0.008421
[2025-10-07 07:48:54] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -44.231023 | E_var:     0.2735 | E_err:   0.008172
[2025-10-07 07:49:02] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -44.230039 | E_var:     0.3002 | E_err:   0.008561
[2025-10-07 07:49:10] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -44.226300 | E_var:     0.2336 | E_err:   0.007552
[2025-10-07 07:49:18] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -44.224180 | E_var:     0.3106 | E_err:   0.008708
[2025-10-07 07:49:25] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -44.222819 | E_var:     0.2180 | E_err:   0.007295
[2025-10-07 07:49:33] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -44.236186 | E_var:     0.2527 | E_err:   0.007855
[2025-10-07 07:49:41] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -44.222728 | E_var:     0.2818 | E_err:   0.008295
[2025-10-07 07:49:49] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -44.232295 | E_var:     0.3215 | E_err:   0.008859
[2025-10-07 07:49:57] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -44.231185 | E_var:     0.4112 | E_err:   0.010020
[2025-10-07 07:50:05] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -44.215664 | E_var:     0.4066 | E_err:   0.009963
[2025-10-07 07:50:12] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -44.233680 | E_var:     0.2734 | E_err:   0.008170
[2025-10-07 07:50:20] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -44.234544 | E_var:     0.3035 | E_err:   0.008608
[2025-10-07 07:50:28] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -44.225692 | E_var:     0.2278 | E_err:   0.007457
[2025-10-07 07:50:36] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -44.238562 | E_var:     0.2634 | E_err:   0.008020
[2025-10-07 07:50:44] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -44.247338 | E_var:     0.2727 | E_err:   0.008159
[2025-10-07 07:50:51] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -44.237035 | E_var:     0.6636 | E_err:   0.012729
[2025-10-07 07:50:59] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -44.221770 | E_var:     0.2878 | E_err:   0.008383
[2025-10-07 07:51:07] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -44.228378 | E_var:     0.2275 | E_err:   0.007453
[2025-10-07 07:51:15] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -44.226571 | E_var:     0.3862 | E_err:   0.009710
[2025-10-07 07:51:23] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -44.242509 | E_var:     0.2530 | E_err:   0.007860
[2025-10-07 07:51:31] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -44.235118 | E_var:     0.2753 | E_err:   0.008198
[2025-10-07 07:51:38] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -44.232541 | E_var:     0.3739 | E_err:   0.009554
[2025-10-07 07:51:46] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -44.230833 | E_var:     0.2676 | E_err:   0.008084
[2025-10-07 07:51:54] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -44.238024 | E_var:     0.2407 | E_err:   0.007666
[2025-10-07 07:52:02] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -44.234330 | E_var:     0.2442 | E_err:   0.007721
[2025-10-07 07:52:10] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -44.243089 | E_var:     0.2620 | E_err:   0.007998
[2025-10-07 07:52:18] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -44.236672 | E_var:     0.3013 | E_err:   0.008577
[2025-10-07 07:52:25] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -44.236441 | E_var:     0.2491 | E_err:   0.007798
[2025-10-07 07:52:33] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -44.228858 | E_var:     0.2786 | E_err:   0.008247
[2025-10-07 07:52:41] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -44.245131 | E_var:     0.2537 | E_err:   0.007871
[2025-10-07 07:52:49] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -44.219514 | E_var:     0.2935 | E_err:   0.008464
[2025-10-07 07:52:57] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -44.242199 | E_var:     0.2908 | E_err:   0.008426
[2025-10-07 07:53:04] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -44.248695 | E_var:     0.3306 | E_err:   0.008984
[2025-10-07 07:53:12] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -44.235201 | E_var:     0.2730 | E_err:   0.008164
[2025-10-07 07:53:20] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -44.239358 | E_var:     0.2956 | E_err:   0.008496
[2025-10-07 07:53:28] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -44.237835 | E_var:     0.2399 | E_err:   0.007652
[2025-10-07 07:53:36] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -44.231854 | E_var:     0.2693 | E_err:   0.008109
[2025-10-07 07:53:44] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -44.240616 | E_var:     0.3066 | E_err:   0.008652
[2025-10-07 07:53:51] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -44.236658 | E_var:     0.2431 | E_err:   0.007705
[2025-10-07 07:53:59] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -44.241556 | E_var:     0.3044 | E_err:   0.008620
[2025-10-07 07:54:07] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -44.239259 | E_var:     0.2674 | E_err:   0.008080
[2025-10-07 07:54:15] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -44.254451 | E_var:     0.3212 | E_err:   0.008855
[2025-10-07 07:54:23] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -44.235769 | E_var:     0.2442 | E_err:   0.007722
[2025-10-07 07:54:30] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -44.220337 | E_var:     0.2390 | E_err:   0.007638
[2025-10-07 07:54:38] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -44.233698 | E_var:     0.2573 | E_err:   0.007925
[2025-10-07 07:54:46] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -44.219121 | E_var:     0.2767 | E_err:   0.008219
[2025-10-07 07:54:54] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -44.226442 | E_var:     0.2108 | E_err:   0.007174
[2025-10-07 07:55:02] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -44.231706 | E_var:     0.2293 | E_err:   0.007481
[2025-10-07 07:55:10] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -44.233941 | E_var:     0.2519 | E_err:   0.007842
[2025-10-07 07:55:17] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -44.233530 | E_var:     0.2521 | E_err:   0.007845
[2025-10-07 07:55:25] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -44.226084 | E_var:     0.2776 | E_err:   0.008232
[2025-10-07 07:55:33] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -44.232125 | E_var:     0.2574 | E_err:   0.007927
[2025-10-07 07:55:41] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -44.235962 | E_var:     0.6982 | E_err:   0.013056
[2025-10-07 07:55:49] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -44.228489 | E_var:     0.2443 | E_err:   0.007723
[2025-10-07 07:55:56] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -44.216542 | E_var:     0.3097 | E_err:   0.008696
[2025-10-07 07:56:04] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -44.238965 | E_var:     0.3305 | E_err:   0.008982
[2025-10-07 07:56:12] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -44.242958 | E_var:     0.2773 | E_err:   0.008227
[2025-10-07 07:56:20] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -44.232246 | E_var:     0.2761 | E_err:   0.008210
[2025-10-07 07:56:28] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -44.237192 | E_var:     0.2675 | E_err:   0.008081
[2025-10-07 07:56:36] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -44.245474 | E_var:     0.2460 | E_err:   0.007749
[2025-10-07 07:56:43] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -44.247679 | E_var:     0.2886 | E_err:   0.008394
[2025-10-07 07:56:51] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -44.237548 | E_var:     0.3096 | E_err:   0.008694
[2025-10-07 07:56:59] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -44.245674 | E_var:     0.2995 | E_err:   0.008551
[2025-10-07 07:57:07] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -44.241666 | E_var:     0.2749 | E_err:   0.008192
[2025-10-07 07:57:15] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -44.233730 | E_var:     0.2408 | E_err:   0.007668
[2025-10-07 07:57:22] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -44.232708 | E_var:     0.2996 | E_err:   0.008553
[2025-10-07 07:57:30] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -44.242319 | E_var:     0.2927 | E_err:   0.008454
[2025-10-07 07:57:38] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -44.244400 | E_var:     0.2392 | E_err:   0.007641
[2025-10-07 07:57:46] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -44.232250 | E_var:     0.2921 | E_err:   0.008445
[2025-10-07 07:57:54] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -44.225137 | E_var:     0.2125 | E_err:   0.007203
[2025-10-07 07:57:54] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 07:58:02] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -44.245165 | E_var:     0.2481 | E_err:   0.007783
[2025-10-07 07:58:09] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -44.228048 | E_var:     0.3384 | E_err:   0.009090
[2025-10-07 07:58:17] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -44.232236 | E_var:     0.2779 | E_err:   0.008237
[2025-10-07 07:58:25] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -44.237606 | E_var:     0.3011 | E_err:   0.008573
[2025-10-07 07:58:33] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -44.244784 | E_var:     0.3043 | E_err:   0.008619
[2025-10-07 07:58:41] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -44.246359 | E_var:     0.2709 | E_err:   0.008133
[2025-10-07 07:58:49] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -44.246405 | E_var:     0.2927 | E_err:   0.008454
[2025-10-07 07:58:56] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -44.248825 | E_var:     0.2595 | E_err:   0.007960
[2025-10-07 07:59:04] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -44.226129 | E_var:     0.2339 | E_err:   0.007557
[2025-10-07 07:59:12] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -44.231058 | E_var:     0.2242 | E_err:   0.007398
[2025-10-07 07:59:20] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -44.226428 | E_var:     0.3079 | E_err:   0.008670
[2025-10-07 07:59:28] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -44.231880 | E_var:     0.2553 | E_err:   0.007895
[2025-10-07 07:59:35] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -44.254258 | E_var:     0.2902 | E_err:   0.008418
[2025-10-07 07:59:43] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -44.223319 | E_var:     0.3246 | E_err:   0.008903
[2025-10-07 07:59:51] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -44.243969 | E_var:     0.3458 | E_err:   0.009188
[2025-10-07 07:59:59] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -44.236108 | E_var:     0.2629 | E_err:   0.008012
[2025-10-07 08:00:07] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -44.220997 | E_var:     0.3173 | E_err:   0.008802
[2025-10-07 08:00:15] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -44.240095 | E_var:     0.3511 | E_err:   0.009259
[2025-10-07 08:00:22] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -44.228573 | E_var:     0.4019 | E_err:   0.009906
[2025-10-07 08:00:30] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -44.230734 | E_var:     0.2416 | E_err:   0.007679
[2025-10-07 08:00:38] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -44.246203 | E_var:     0.2672 | E_err:   0.008077
[2025-10-07 08:00:46] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -44.243923 | E_var:     0.2217 | E_err:   0.007357
[2025-10-07 08:00:54] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -44.228454 | E_var:     0.3212 | E_err:   0.008855
[2025-10-07 08:01:01] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -44.240046 | E_var:     0.2939 | E_err:   0.008471
[2025-10-07 08:01:09] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -44.244219 | E_var:     0.2519 | E_err:   0.007842
[2025-10-07 08:01:17] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -44.234201 | E_var:     0.3490 | E_err:   0.009230
[2025-10-07 08:01:25] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -44.237980 | E_var:     0.3376 | E_err:   0.009079
[2025-10-07 08:01:33] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -44.225800 | E_var:     0.6285 | E_err:   0.012387
[2025-10-07 08:01:41] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -44.229716 | E_var:     0.3278 | E_err:   0.008946
[2025-10-07 08:01:48] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -44.247031 | E_var:     0.2504 | E_err:   0.007819
[2025-10-07 08:01:56] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -44.225198 | E_var:     0.3558 | E_err:   0.009321
[2025-10-07 08:02:04] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -44.236348 | E_var:     0.2242 | E_err:   0.007399
[2025-10-07 08:02:12] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -44.244560 | E_var:     0.2721 | E_err:   0.008151
[2025-10-07 08:02:20] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -44.239542 | E_var:     0.2776 | E_err:   0.008233
[2025-10-07 08:02:27] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -44.230887 | E_var:     0.7315 | E_err:   0.013363
[2025-10-07 08:02:35] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -44.230073 | E_var:     0.2685 | E_err:   0.008097
[2025-10-07 08:02:43] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -44.242001 | E_var:     0.3215 | E_err:   0.008860
[2025-10-07 08:02:51] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -44.228522 | E_var:     0.2918 | E_err:   0.008441
[2025-10-07 08:02:59] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -44.228958 | E_var:     0.2263 | E_err:   0.007434
[2025-10-07 08:03:06] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -44.234659 | E_var:     0.2411 | E_err:   0.007672
[2025-10-07 08:03:14] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -44.229704 | E_var:     0.2299 | E_err:   0.007492
[2025-10-07 08:03:22] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -44.246067 | E_var:     0.2835 | E_err:   0.008320
[2025-10-07 08:03:30] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -44.225714 | E_var:     0.2243 | E_err:   0.007400
[2025-10-07 08:03:38] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -44.242566 | E_var:     0.2550 | E_err:   0.007890
[2025-10-07 08:03:46] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -44.230001 | E_var:     0.2661 | E_err:   0.008061
[2025-10-07 08:03:53] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -44.219535 | E_var:     0.2662 | E_err:   0.008062
[2025-10-07 08:04:01] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -44.215305 | E_var:     0.3450 | E_err:   0.009177
[2025-10-07 08:04:09] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -44.250303 | E_var:     0.3014 | E_err:   0.008579
[2025-10-07 08:04:17] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -44.244463 | E_var:     0.2527 | E_err:   0.007855
[2025-10-07 08:04:25] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -44.233232 | E_var:     0.3105 | E_err:   0.008706
[2025-10-07 08:04:25] 🔄 RESTART #1 | Period: 300
[2025-10-07 08:04:32] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -44.239057 | E_var:     0.2198 | E_err:   0.007326
[2025-10-07 08:04:40] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -44.236218 | E_var:     0.2841 | E_err:   0.008329
[2025-10-07 08:04:48] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -44.236790 | E_var:     0.3376 | E_err:   0.009078
[2025-10-07 08:04:56] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -44.250555 | E_var:     0.5648 | E_err:   0.011742
[2025-10-07 08:05:04] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -44.249566 | E_var:     0.2527 | E_err:   0.007854
[2025-10-07 08:05:12] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -44.247327 | E_var:     0.3129 | E_err:   0.008740
[2025-10-07 08:05:19] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -44.239087 | E_var:     0.2076 | E_err:   0.007119
[2025-10-07 08:05:27] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -44.231759 | E_var:     0.2322 | E_err:   0.007529
[2025-10-07 08:05:35] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -44.224862 | E_var:     0.2678 | E_err:   0.008085
[2025-10-07 08:05:43] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -44.230258 | E_var:     0.3598 | E_err:   0.009372
[2025-10-07 08:05:51] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -44.232029 | E_var:     0.2379 | E_err:   0.007621
[2025-10-07 08:05:58] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -44.231865 | E_var:     0.4620 | E_err:   0.010620
[2025-10-07 08:06:06] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -44.228237 | E_var:     0.2921 | E_err:   0.008445
[2025-10-07 08:06:14] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -44.227291 | E_var:     0.2411 | E_err:   0.007672
[2025-10-07 08:06:22] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -44.222476 | E_var:     0.2827 | E_err:   0.008307
[2025-10-07 08:06:30] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -44.228697 | E_var:     0.2707 | E_err:   0.008129
[2025-10-07 08:06:38] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -44.236626 | E_var:     0.2606 | E_err:   0.007976
[2025-10-07 08:06:45] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -44.204007 | E_var:     0.3693 | E_err:   0.009496
[2025-10-07 08:06:53] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -44.224162 | E_var:     0.3000 | E_err:   0.008558
[2025-10-07 08:07:01] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -44.236182 | E_var:     0.2527 | E_err:   0.007854
[2025-10-07 08:07:09] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -44.234613 | E_var:     0.2376 | E_err:   0.007616
[2025-10-07 08:07:17] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -44.228428 | E_var:     0.3637 | E_err:   0.009423
[2025-10-07 08:07:24] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -44.229262 | E_var:     0.2464 | E_err:   0.007755
[2025-10-07 08:07:32] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -44.216422 | E_var:     0.2454 | E_err:   0.007740
[2025-10-07 08:07:40] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -44.243379 | E_var:     0.2570 | E_err:   0.007922
[2025-10-07 08:07:48] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -44.238290 | E_var:     0.2719 | E_err:   0.008147
[2025-10-07 08:07:56] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -44.234335 | E_var:     0.2622 | E_err:   0.008000
[2025-10-07 08:08:04] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -44.233163 | E_var:     0.3281 | E_err:   0.008951
[2025-10-07 08:08:11] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -44.232260 | E_var:     0.3106 | E_err:   0.008708
[2025-10-07 08:08:19] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -44.243538 | E_var:     0.2400 | E_err:   0.007655
[2025-10-07 08:08:27] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -44.228085 | E_var:     0.3078 | E_err:   0.008669
[2025-10-07 08:08:35] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -44.226206 | E_var:     0.2978 | E_err:   0.008527
[2025-10-07 08:08:43] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -44.245478 | E_var:     0.2713 | E_err:   0.008138
[2025-10-07 08:08:51] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -44.231184 | E_var:     0.2719 | E_err:   0.008147
[2025-10-07 08:08:58] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -44.231042 | E_var:     0.2607 | E_err:   0.007978
[2025-10-07 08:09:06] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -44.227526 | E_var:     0.3336 | E_err:   0.009025
[2025-10-07 08:09:14] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -44.233716 | E_var:     0.2192 | E_err:   0.007316
[2025-10-07 08:09:22] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -44.235489 | E_var:     0.2636 | E_err:   0.008022
[2025-10-07 08:09:30] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -44.227509 | E_var:     0.2544 | E_err:   0.007882
[2025-10-07 08:09:37] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -44.236690 | E_var:     0.2169 | E_err:   0.007278
[2025-10-07 08:09:45] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -44.231545 | E_var:     0.4016 | E_err:   0.009902
[2025-10-07 08:09:53] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -44.231563 | E_var:     0.2097 | E_err:   0.007156
[2025-10-07 08:10:01] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -44.235202 | E_var:     0.2527 | E_err:   0.007855
[2025-10-07 08:10:09] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -44.235914 | E_var:     0.2712 | E_err:   0.008137
[2025-10-07 08:10:17] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -44.233784 | E_var:     0.2374 | E_err:   0.007613
[2025-10-07 08:10:24] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -44.251467 | E_var:     0.3771 | E_err:   0.009596
[2025-10-07 08:10:32] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -44.236712 | E_var:     0.2954 | E_err:   0.008492
[2025-10-07 08:10:40] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -44.239297 | E_var:     0.4401 | E_err:   0.010366
[2025-10-07 08:10:48] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -44.235188 | E_var:     0.2180 | E_err:   0.007296
[2025-10-07 08:10:56] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -44.229948 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 08:10:56] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 08:11:03] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -44.231153 | E_var:     0.2561 | E_err:   0.007908
[2025-10-07 08:11:11] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -44.217623 | E_var:     0.2898 | E_err:   0.008411
[2025-10-07 08:11:19] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -44.245701 | E_var:     0.2484 | E_err:   0.007788
[2025-10-07 08:11:27] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -44.238487 | E_var:     0.3114 | E_err:   0.008720
[2025-10-07 08:11:35] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -44.239836 | E_var:     0.2299 | E_err:   0.007492
[2025-10-07 08:11:43] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -44.222808 | E_var:     0.3218 | E_err:   0.008864
[2025-10-07 08:11:50] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -44.234734 | E_var:     0.2401 | E_err:   0.007657
[2025-10-07 08:11:58] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -44.242978 | E_var:     0.2469 | E_err:   0.007764
[2025-10-07 08:12:06] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -44.233648 | E_var:     0.2592 | E_err:   0.007956
[2025-10-07 08:12:14] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -44.243236 | E_var:     0.2842 | E_err:   0.008330
[2025-10-07 08:12:22] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -44.229116 | E_var:     0.2391 | E_err:   0.007641
[2025-10-07 08:12:29] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -44.235660 | E_var:     0.4449 | E_err:   0.010422
[2025-10-07 08:12:37] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -44.222016 | E_var:     0.2669 | E_err:   0.008073
[2025-10-07 08:12:45] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -44.228058 | E_var:     0.2658 | E_err:   0.008055
[2025-10-07 08:12:53] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -44.232610 | E_var:     0.2838 | E_err:   0.008324
[2025-10-07 08:13:01] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -44.248961 | E_var:     0.3068 | E_err:   0.008655
[2025-10-07 08:13:09] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -44.229133 | E_var:     0.2289 | E_err:   0.007475
[2025-10-07 08:13:16] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -44.214978 | E_var:     0.2821 | E_err:   0.008299
[2025-10-07 08:13:24] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -44.226962 | E_var:     0.2413 | E_err:   0.007676
[2025-10-07 08:13:32] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -44.229003 | E_var:     0.2592 | E_err:   0.007955
[2025-10-07 08:13:40] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -44.245731 | E_var:     0.7212 | E_err:   0.013269
[2025-10-07 08:13:48] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -44.232568 | E_var:     0.2843 | E_err:   0.008332
[2025-10-07 08:13:55] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -44.235165 | E_var:     0.2694 | E_err:   0.008110
[2025-10-07 08:14:03] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -44.219960 | E_var:     0.2497 | E_err:   0.007808
[2025-10-07 08:14:11] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -44.240416 | E_var:     0.2820 | E_err:   0.008297
[2025-10-07 08:14:19] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -44.232240 | E_var:     0.3225 | E_err:   0.008873
[2025-10-07 08:14:27] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -44.240885 | E_var:     0.2480 | E_err:   0.007781
[2025-10-07 08:14:35] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -44.228210 | E_var:     0.3582 | E_err:   0.009352
[2025-10-07 08:14:42] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -44.236165 | E_var:     0.3034 | E_err:   0.008607
[2025-10-07 08:14:50] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -44.246378 | E_var:     0.2515 | E_err:   0.007837
[2025-10-07 08:14:58] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -44.239558 | E_var:     0.2678 | E_err:   0.008085
[2025-10-07 08:15:06] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -44.223529 | E_var:     0.2770 | E_err:   0.008224
[2025-10-07 08:15:14] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -44.251566 | E_var:     0.2206 | E_err:   0.007339
[2025-10-07 08:15:21] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -44.236822 | E_var:     0.2249 | E_err:   0.007411
[2025-10-07 08:15:29] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -44.243963 | E_var:     0.2333 | E_err:   0.007547
[2025-10-07 08:15:37] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -44.233402 | E_var:     0.3719 | E_err:   0.009529
[2025-10-07 08:15:45] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -44.239398 | E_var:     0.3625 | E_err:   0.009408
[2025-10-07 08:15:53] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -44.234808 | E_var:     0.2426 | E_err:   0.007696
[2025-10-07 08:16:01] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -44.224494 | E_var:     0.3638 | E_err:   0.009424
[2025-10-07 08:16:08] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -44.226920 | E_var:     0.3346 | E_err:   0.009039
[2025-10-07 08:16:16] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -44.227577 | E_var:     0.3953 | E_err:   0.009824
[2025-10-07 08:16:24] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -44.232515 | E_var:     0.2694 | E_err:   0.008110
[2025-10-07 08:16:32] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -44.230026 | E_var:     0.2456 | E_err:   0.007743
[2025-10-07 08:16:40] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -44.241595 | E_var:     0.3668 | E_err:   0.009464
[2025-10-07 08:16:47] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -44.245411 | E_var:     0.2980 | E_err:   0.008530
[2025-10-07 08:16:55] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -44.240517 | E_var:     0.3167 | E_err:   0.008794
[2025-10-07 08:17:03] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -44.236322 | E_var:     0.2006 | E_err:   0.006998
[2025-10-07 08:17:11] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -44.229903 | E_var:     0.2588 | E_err:   0.007949
[2025-10-07 08:17:19] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -44.222880 | E_var:     0.2346 | E_err:   0.007568
[2025-10-07 08:17:27] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -44.222480 | E_var:     0.2481 | E_err:   0.007783
[2025-10-07 08:17:34] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -44.249454 | E_var:     0.3398 | E_err:   0.009108
[2025-10-07 08:17:42] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -44.232174 | E_var:     0.3167 | E_err:   0.008793
[2025-10-07 08:17:50] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -44.231056 | E_var:     0.2965 | E_err:   0.008508
[2025-10-07 08:17:58] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -44.225615 | E_var:     0.2834 | E_err:   0.008318
[2025-10-07 08:18:06] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -44.242412 | E_var:     0.2205 | E_err:   0.007336
[2025-10-07 08:18:13] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -44.236317 | E_var:     0.2279 | E_err:   0.007459
[2025-10-07 08:18:21] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -44.244137 | E_var:     0.2213 | E_err:   0.007350
[2025-10-07 08:18:29] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -44.242032 | E_var:     0.2370 | E_err:   0.007606
[2025-10-07 08:18:37] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -44.253140 | E_var:     0.3798 | E_err:   0.009629
[2025-10-07 08:18:45] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -44.234357 | E_var:     0.3821 | E_err:   0.009659
[2025-10-07 08:18:53] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -44.227799 | E_var:     0.3446 | E_err:   0.009172
[2025-10-07 08:19:00] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -44.254091 | E_var:     0.9369 | E_err:   0.015124
[2025-10-07 08:19:08] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -44.240792 | E_var:     0.3857 | E_err:   0.009704
[2025-10-07 08:19:16] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -44.232008 | E_var:     0.2398 | E_err:   0.007651
[2025-10-07 08:19:24] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -44.239999 | E_var:     0.2550 | E_err:   0.007890
[2025-10-07 08:19:32] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -44.242465 | E_var:     0.2892 | E_err:   0.008403
[2025-10-07 08:19:40] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -44.237534 | E_var:     0.2714 | E_err:   0.008140
[2025-10-07 08:19:47] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -44.229922 | E_var:     0.2899 | E_err:   0.008413
[2025-10-07 08:19:55] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -44.234650 | E_var:     0.2680 | E_err:   0.008088
[2025-10-07 08:20:03] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -44.250658 | E_var:     0.2102 | E_err:   0.007163
[2025-10-07 08:20:11] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -44.237461 | E_var:     0.3437 | E_err:   0.009160
[2025-10-07 08:20:19] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -44.240225 | E_var:     0.4023 | E_err:   0.009911
[2025-10-07 08:20:26] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -44.238767 | E_var:     0.3497 | E_err:   0.009240
[2025-10-07 08:20:34] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -44.214001 | E_var:     0.5008 | E_err:   0.011057
[2025-10-07 08:20:42] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -44.236726 | E_var:     0.2351 | E_err:   0.007575
[2025-10-07 08:20:50] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -44.240402 | E_var:     0.2668 | E_err:   0.008070
[2025-10-07 08:20:58] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -44.243952 | E_var:     0.2426 | E_err:   0.007696
[2025-10-07 08:21:05] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -44.236498 | E_var:     0.2492 | E_err:   0.007800
[2025-10-07 08:21:13] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -44.228254 | E_var:     0.2773 | E_err:   0.008229
[2025-10-07 08:21:21] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -44.240578 | E_var:     0.3100 | E_err:   0.008700
[2025-10-07 08:21:29] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -44.240627 | E_var:     0.2488 | E_err:   0.007794
[2025-10-07 08:21:37] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -44.246560 | E_var:     0.2874 | E_err:   0.008377
[2025-10-07 08:21:45] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -44.242346 | E_var:     0.2318 | E_err:   0.007522
[2025-10-07 08:21:52] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -44.248385 | E_var:     0.3328 | E_err:   0.009013
[2025-10-07 08:22:00] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -44.232628 | E_var:     0.3160 | E_err:   0.008784
[2025-10-07 08:22:08] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -44.240043 | E_var:     0.3634 | E_err:   0.009420
[2025-10-07 08:22:16] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -44.221705 | E_var:     0.2819 | E_err:   0.008295
[2025-10-07 08:22:24] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -44.232041 | E_var:     0.2057 | E_err:   0.007086
[2025-10-07 08:22:31] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -44.239071 | E_var:     0.2217 | E_err:   0.007357
[2025-10-07 08:22:39] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -44.238724 | E_var:     0.3834 | E_err:   0.009675
[2025-10-07 08:22:47] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -44.240963 | E_var:     0.3059 | E_err:   0.008642
[2025-10-07 08:22:55] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -44.231243 | E_var:     0.3012 | E_err:   0.008576
[2025-10-07 08:23:03] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -44.247455 | E_var:     0.3033 | E_err:   0.008605
[2025-10-07 08:23:11] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -44.234231 | E_var:     0.2805 | E_err:   0.008275
[2025-10-07 08:23:18] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -44.234577 | E_var:     0.2253 | E_err:   0.007416
[2025-10-07 08:23:26] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -44.236492 | E_var:     0.2739 | E_err:   0.008177
[2025-10-07 08:23:34] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -44.238682 | E_var:     0.2657 | E_err:   0.008054
[2025-10-07 08:23:42] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -44.238356 | E_var:     0.2903 | E_err:   0.008418
[2025-10-07 08:23:50] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -44.237092 | E_var:     0.2904 | E_err:   0.008420
[2025-10-07 08:23:57] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -44.228165 | E_var:     0.2763 | E_err:   0.008214
[2025-10-07 08:23:58] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 08:24:05] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -44.248621 | E_var:     0.2560 | E_err:   0.007906
[2025-10-07 08:24:13] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -44.237082 | E_var:     0.2586 | E_err:   0.007946
[2025-10-07 08:24:21] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -44.222981 | E_var:     0.2229 | E_err:   0.007377
[2025-10-07 08:24:29] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -44.228002 | E_var:     0.2678 | E_err:   0.008086
[2025-10-07 08:24:37] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -44.234919 | E_var:     0.2684 | E_err:   0.008095
[2025-10-07 08:24:44] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -44.233823 | E_var:     0.2080 | E_err:   0.007126
[2025-10-07 08:24:52] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -44.238264 | E_var:     0.2453 | E_err:   0.007738
[2025-10-07 08:25:00] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -44.235372 | E_var:     0.2479 | E_err:   0.007780
[2025-10-07 08:25:08] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -44.236564 | E_var:     0.3189 | E_err:   0.008823
[2025-10-07 08:25:16] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -44.251109 | E_var:     0.2850 | E_err:   0.008341
[2025-10-07 08:25:24] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -44.232904 | E_var:     0.2359 | E_err:   0.007589
[2025-10-07 08:25:31] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -44.248394 | E_var:     0.2927 | E_err:   0.008454
[2025-10-07 08:25:39] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -44.214048 | E_var:     2.6815 | E_err:   0.025586
[2025-10-07 08:25:47] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -44.224958 | E_var:     0.2983 | E_err:   0.008534
[2025-10-07 08:25:55] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -44.231675 | E_var:     0.3352 | E_err:   0.009047
[2025-10-07 08:26:03] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -44.230244 | E_var:     0.2901 | E_err:   0.008416
[2025-10-07 08:26:10] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -44.241293 | E_var:     0.1964 | E_err:   0.006924
[2025-10-07 08:26:18] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -44.238668 | E_var:     0.2538 | E_err:   0.007871
[2025-10-07 08:26:26] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -44.228682 | E_var:     0.2917 | E_err:   0.008438
[2025-10-07 08:26:34] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -44.246056 | E_var:     0.2687 | E_err:   0.008100
[2025-10-07 08:26:42] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -44.228714 | E_var:     0.2549 | E_err:   0.007889
[2025-10-07 08:26:50] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -44.241204 | E_var:     0.2470 | E_err:   0.007766
[2025-10-07 08:26:57] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -44.249061 | E_var:     0.3250 | E_err:   0.008908
[2025-10-07 08:27:05] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -44.232562 | E_var:     0.2939 | E_err:   0.008471
[2025-10-07 08:27:13] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -44.232166 | E_var:     0.2480 | E_err:   0.007781
[2025-10-07 08:27:21] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -44.231162 | E_var:     0.2497 | E_err:   0.007808
[2025-10-07 08:27:29] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -44.241036 | E_var:     0.2923 | E_err:   0.008448
[2025-10-07 08:27:36] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -44.250515 | E_var:     0.2493 | E_err:   0.007802
[2025-10-07 08:27:44] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -44.238530 | E_var:     0.2149 | E_err:   0.007244
[2025-10-07 08:27:52] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -44.230932 | E_var:     0.2854 | E_err:   0.008347
[2025-10-07 08:28:00] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -44.215601 | E_var:     0.2228 | E_err:   0.007375
[2025-10-07 08:28:08] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -44.232517 | E_var:     0.2746 | E_err:   0.008188
[2025-10-07 08:28:16] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -44.232072 | E_var:     0.2715 | E_err:   0.008142
[2025-10-07 08:28:23] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -44.231792 | E_var:     0.3153 | E_err:   0.008774
[2025-10-07 08:28:31] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -44.229294 | E_var:     0.2395 | E_err:   0.007646
[2025-10-07 08:28:39] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -44.244461 | E_var:     0.1998 | E_err:   0.006984
[2025-10-07 08:28:47] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -44.246454 | E_var:     0.2972 | E_err:   0.008518
[2025-10-07 08:28:55] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -44.223832 | E_var:     0.2374 | E_err:   0.007613
[2025-10-07 08:29:02] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -44.239863 | E_var:     0.2443 | E_err:   0.007724
[2025-10-07 08:29:10] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -44.248913 | E_var:     0.2350 | E_err:   0.007575
[2025-10-07 08:29:18] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -44.233914 | E_var:     0.2218 | E_err:   0.007359
[2025-10-07 08:29:26] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -44.236520 | E_var:     0.2960 | E_err:   0.008501
[2025-10-07 08:29:34] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -44.244156 | E_var:     0.2646 | E_err:   0.008037
[2025-10-07 08:29:42] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -44.229658 | E_var:     0.2230 | E_err:   0.007379
[2025-10-07 08:29:49] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -44.233846 | E_var:     0.3780 | E_err:   0.009607
[2025-10-07 08:29:57] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -44.242842 | E_var:     0.2873 | E_err:   0.008375
[2025-10-07 08:30:05] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -44.238762 | E_var:     0.2444 | E_err:   0.007725
[2025-10-07 08:30:13] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -44.239700 | E_var:     0.2922 | E_err:   0.008446
[2025-10-07 08:30:21] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -44.235353 | E_var:     0.2219 | E_err:   0.007361
[2025-10-07 08:30:28] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -44.224532 | E_var:     0.2387 | E_err:   0.007633
[2025-10-07 08:30:36] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -44.238224 | E_var:     0.2375 | E_err:   0.007615
[2025-10-07 08:30:44] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -44.243676 | E_var:     0.2693 | E_err:   0.008109
[2025-10-07 08:30:52] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -44.242100 | E_var:     0.2919 | E_err:   0.008441
[2025-10-07 08:31:00] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -44.229850 | E_var:     0.4214 | E_err:   0.010143
[2025-10-07 08:31:08] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -44.231460 | E_var:     0.2769 | E_err:   0.008221
[2025-10-07 08:31:15] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -44.237720 | E_var:     0.2577 | E_err:   0.007932
[2025-10-07 08:31:23] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -44.214009 | E_var:     0.3049 | E_err:   0.008627
[2025-10-07 08:31:31] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -44.256049 | E_var:     0.2557 | E_err:   0.007901
[2025-10-07 08:31:39] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -44.234939 | E_var:     0.3547 | E_err:   0.009306
[2025-10-07 08:31:47] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -44.241845 | E_var:     0.2950 | E_err:   0.008487
[2025-10-07 08:31:54] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -44.236957 | E_var:     0.3200 | E_err:   0.008839
[2025-10-07 08:32:02] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -44.226576 | E_var:     0.3160 | E_err:   0.008783
[2025-10-07 08:32:10] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -44.234226 | E_var:     0.2824 | E_err:   0.008303
[2025-10-07 08:32:18] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -44.247769 | E_var:     0.2293 | E_err:   0.007482
[2025-10-07 08:32:26] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -44.230388 | E_var:     0.2951 | E_err:   0.008487
[2025-10-07 08:32:33] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -44.244466 | E_var:     0.2416 | E_err:   0.007679
[2025-10-07 08:32:41] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -44.238159 | E_var:     0.2368 | E_err:   0.007604
[2025-10-07 08:32:49] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -44.255163 | E_var:     0.3138 | E_err:   0.008752
[2025-10-07 08:32:57] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -44.237936 | E_var:     0.3138 | E_err:   0.008753
[2025-10-07 08:33:05] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -44.237071 | E_var:     0.2376 | E_err:   0.007616
[2025-10-07 08:33:13] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -44.228847 | E_var:     0.2858 | E_err:   0.008354
[2025-10-07 08:33:20] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -44.246696 | E_var:     0.3185 | E_err:   0.008818
[2025-10-07 08:33:28] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -44.234997 | E_var:     0.2132 | E_err:   0.007215
[2025-10-07 08:33:36] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -44.232848 | E_var:     0.3097 | E_err:   0.008696
[2025-10-07 08:33:44] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -44.234374 | E_var:     0.3063 | E_err:   0.008647
[2025-10-07 08:33:52] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -44.227089 | E_var:     0.2620 | E_err:   0.007997
[2025-10-07 08:34:00] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -44.249710 | E_var:     0.2540 | E_err:   0.007875
[2025-10-07 08:34:07] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -44.221044 | E_var:     0.4201 | E_err:   0.010127
[2025-10-07 08:34:15] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -44.240491 | E_var:     0.3154 | E_err:   0.008775
[2025-10-07 08:34:23] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -44.230904 | E_var:     0.2679 | E_err:   0.008087
[2025-10-07 08:34:31] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -44.223948 | E_var:     0.3029 | E_err:   0.008599
[2025-10-07 08:34:39] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -44.240998 | E_var:     0.2786 | E_err:   0.008247
[2025-10-07 08:34:46] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -44.232797 | E_var:     0.3677 | E_err:   0.009475
[2025-10-07 08:34:54] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -44.224707 | E_var:     0.3152 | E_err:   0.008773
[2025-10-07 08:35:02] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -44.240672 | E_var:     0.2199 | E_err:   0.007326
[2025-10-07 08:35:10] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -44.240832 | E_var:     0.2833 | E_err:   0.008316
[2025-10-07 08:35:18] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -44.222577 | E_var:     0.2771 | E_err:   0.008225
[2025-10-07 08:35:25] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -44.230439 | E_var:     0.3351 | E_err:   0.009045
[2025-10-07 08:35:33] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -44.245488 | E_var:     0.3248 | E_err:   0.008905
[2025-10-07 08:35:41] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -44.237290 | E_var:     0.2519 | E_err:   0.007843
[2025-10-07 08:35:49] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -44.242368 | E_var:     0.2424 | E_err:   0.007693
[2025-10-07 08:35:57] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -44.228271 | E_var:     0.3237 | E_err:   0.008890
[2025-10-07 08:36:05] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -44.243526 | E_var:     0.2868 | E_err:   0.008368
[2025-10-07 08:36:12] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -44.220709 | E_var:     0.3031 | E_err:   0.008603
[2025-10-07 08:36:20] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -44.228690 | E_var:     0.2419 | E_err:   0.007685
[2025-10-07 08:36:28] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -44.238769 | E_var:     0.2236 | E_err:   0.007388
[2025-10-07 08:36:36] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -44.230978 | E_var:     0.2798 | E_err:   0.008265
[2025-10-07 08:36:44] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -44.239222 | E_var:     0.2673 | E_err:   0.008078
[2025-10-07 08:36:51] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -44.243335 | E_var:     0.3267 | E_err:   0.008931
[2025-10-07 08:36:59] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -44.234397 | E_var:     0.2804 | E_err:   0.008274
[2025-10-07 08:36:59] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 08:37:07] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -44.234220 | E_var:     0.3201 | E_err:   0.008840
[2025-10-07 08:37:15] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -44.238532 | E_var:     0.2388 | E_err:   0.007635
[2025-10-07 08:37:23] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -44.240173 | E_var:     0.2834 | E_err:   0.008318
[2025-10-07 08:37:31] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -44.245396 | E_var:     0.2716 | E_err:   0.008143
[2025-10-07 08:37:38] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -44.237223 | E_var:     0.2974 | E_err:   0.008521
[2025-10-07 08:37:46] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -44.251539 | E_var:     0.2480 | E_err:   0.007781
[2025-10-07 08:37:54] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -44.233132 | E_var:     0.2788 | E_err:   0.008251
[2025-10-07 08:38:02] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -44.241798 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 08:38:10] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -44.250760 | E_var:     0.2922 | E_err:   0.008446
[2025-10-07 08:38:18] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -44.236713 | E_var:     0.2694 | E_err:   0.008110
[2025-10-07 08:38:25] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -44.235248 | E_var:     0.2290 | E_err:   0.007478
[2025-10-07 08:38:33] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -44.245115 | E_var:     0.2317 | E_err:   0.007521
[2025-10-07 08:38:41] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -44.225642 | E_var:     0.2006 | E_err:   0.006998
[2025-10-07 08:38:49] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -44.231876 | E_var:     0.4585 | E_err:   0.010580
[2025-10-07 08:38:57] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -44.205956 | E_var:     0.2891 | E_err:   0.008401
[2025-10-07 08:39:04] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -44.240871 | E_var:     0.2916 | E_err:   0.008438
[2025-10-07 08:39:12] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -44.260162 | E_var:     0.3035 | E_err:   0.008608
[2025-10-07 08:39:20] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -44.239035 | E_var:     0.2192 | E_err:   0.007315
[2025-10-07 08:39:28] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -44.230416 | E_var:     0.2876 | E_err:   0.008379
[2025-10-07 08:39:36] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -44.240401 | E_var:     0.2381 | E_err:   0.007625
[2025-10-07 08:39:44] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -44.236311 | E_var:     0.2714 | E_err:   0.008140
[2025-10-07 08:39:52] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -44.261622 | E_var:     0.7945 | E_err:   0.013927
[2025-10-07 08:39:59] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -44.248599 | E_var:     0.3495 | E_err:   0.009237
[2025-10-07 08:40:07] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -44.238058 | E_var:     0.2780 | E_err:   0.008238
[2025-10-07 08:40:15] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -44.238660 | E_var:     0.3002 | E_err:   0.008562
[2025-10-07 08:40:23] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -44.238800 | E_var:     0.2518 | E_err:   0.007841
[2025-10-07 08:40:31] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -44.242977 | E_var:     0.2334 | E_err:   0.007549
[2025-10-07 08:40:38] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -44.228263 | E_var:     0.3342 | E_err:   0.009033
[2025-10-07 08:40:46] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -44.227703 | E_var:     0.2839 | E_err:   0.008326
[2025-10-07 08:40:54] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -44.233871 | E_var:     0.2318 | E_err:   0.007523
[2025-10-07 08:41:02] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -44.230761 | E_var:     0.3419 | E_err:   0.009137
[2025-10-07 08:41:10] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -44.252342 | E_var:     0.2760 | E_err:   0.008209
[2025-10-07 08:41:18] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -44.243944 | E_var:     0.2635 | E_err:   0.008020
[2025-10-07 08:41:25] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -44.237259 | E_var:     0.2444 | E_err:   0.007724
[2025-10-07 08:41:33] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -44.239511 | E_var:     0.2405 | E_err:   0.007662
[2025-10-07 08:41:41] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -44.241999 | E_var:     0.2682 | E_err:   0.008092
[2025-10-07 08:41:49] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -44.234516 | E_var:     0.2776 | E_err:   0.008232
[2025-10-07 08:41:57] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -44.233700 | E_var:     0.3187 | E_err:   0.008821
[2025-10-07 08:42:05] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -44.244525 | E_var:     0.3508 | E_err:   0.009255
[2025-10-07 08:42:12] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -44.233040 | E_var:     0.3066 | E_err:   0.008651
[2025-10-07 08:42:20] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -44.244593 | E_var:     0.2867 | E_err:   0.008367
[2025-10-07 08:42:28] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -44.250030 | E_var:     0.3091 | E_err:   0.008687
[2025-10-07 08:42:36] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -44.230157 | E_var:     0.2688 | E_err:   0.008101
[2025-10-07 08:42:44] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -44.242536 | E_var:     0.2163 | E_err:   0.007267
[2025-10-07 08:42:51] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -44.237431 | E_var:     0.3445 | E_err:   0.009172
[2025-10-07 08:42:59] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -44.241763 | E_var:     0.2442 | E_err:   0.007721
[2025-10-07 08:43:07] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -44.234005 | E_var:     0.2575 | E_err:   0.007928
[2025-10-07 08:43:15] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -44.233394 | E_var:     0.2501 | E_err:   0.007813
[2025-10-07 08:43:23] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -44.235393 | E_var:     0.2596 | E_err:   0.007962
[2025-10-07 08:43:31] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -44.231012 | E_var:     0.3343 | E_err:   0.009034
[2025-10-07 08:43:31] 🔄 RESTART #2 | Period: 600
[2025-10-07 08:43:38] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -44.231841 | E_var:     0.2922 | E_err:   0.008447
[2025-10-07 08:43:46] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -44.227018 | E_var:     0.2801 | E_err:   0.008269
[2025-10-07 08:43:54] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -44.227075 | E_var:     0.2961 | E_err:   0.008502
[2025-10-07 08:44:02] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -44.228358 | E_var:     0.3006 | E_err:   0.008567
[2025-10-07 08:44:10] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -44.243066 | E_var:     0.2656 | E_err:   0.008053
[2025-10-07 08:44:17] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -44.242701 | E_var:     0.3194 | E_err:   0.008830
[2025-10-07 08:44:25] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -44.237782 | E_var:     0.2655 | E_err:   0.008050
[2025-10-07 08:44:33] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -44.220891 | E_var:     0.3586 | E_err:   0.009357
[2025-10-07 08:44:41] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -44.248653 | E_var:     0.3237 | E_err:   0.008890
[2025-10-07 08:44:49] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -44.228505 | E_var:     0.2140 | E_err:   0.007228
[2025-10-07 08:44:57] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -44.244822 | E_var:     0.3299 | E_err:   0.008974
[2025-10-07 08:45:04] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -44.249636 | E_var:     0.2433 | E_err:   0.007706
[2025-10-07 08:45:12] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -44.244179 | E_var:     0.3530 | E_err:   0.009284
[2025-10-07 08:45:20] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -44.249781 | E_var:     0.2316 | E_err:   0.007520
[2025-10-07 08:45:28] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -44.245020 | E_var:     0.3000 | E_err:   0.008558
[2025-10-07 08:45:36] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -44.248662 | E_var:     0.3694 | E_err:   0.009497
[2025-10-07 08:45:43] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -44.237805 | E_var:     0.2553 | E_err:   0.007894
[2025-10-07 08:45:51] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -44.234081 | E_var:     0.2858 | E_err:   0.008353
[2025-10-07 08:45:59] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -44.244144 | E_var:     0.1992 | E_err:   0.006974
[2025-10-07 08:46:07] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -44.263350 | E_var:     0.2955 | E_err:   0.008493
[2025-10-07 08:46:15] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -44.233642 | E_var:     0.3047 | E_err:   0.008624
[2025-10-07 08:46:23] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -44.230903 | E_var:     0.2680 | E_err:   0.008089
[2025-10-07 08:46:30] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -44.236405 | E_var:     0.3541 | E_err:   0.009298
[2025-10-07 08:46:38] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -44.240217 | E_var:     0.3082 | E_err:   0.008674
[2025-10-07 08:46:46] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -44.243759 | E_var:     0.2660 | E_err:   0.008059
[2025-10-07 08:46:54] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -44.235632 | E_var:     0.2346 | E_err:   0.007568
[2025-10-07 08:47:02] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -44.245137 | E_var:     0.2165 | E_err:   0.007271
[2025-10-07 08:47:09] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -44.235250 | E_var:     0.2155 | E_err:   0.007253
[2025-10-07 08:47:17] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -44.233589 | E_var:     0.2863 | E_err:   0.008360
[2025-10-07 08:47:25] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -44.236089 | E_var:     0.2453 | E_err:   0.007739
[2025-10-07 08:47:33] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -44.225917 | E_var:     0.2664 | E_err:   0.008065
[2025-10-07 08:47:41] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -44.227526 | E_var:     0.4141 | E_err:   0.010055
[2025-10-07 08:47:49] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -44.254387 | E_var:     0.3423 | E_err:   0.009141
[2025-10-07 08:47:56] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -44.227333 | E_var:     0.3538 | E_err:   0.009294
[2025-10-07 08:48:04] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -44.244027 | E_var:     0.6162 | E_err:   0.012266
[2025-10-07 08:48:12] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -44.228179 | E_var:     0.2354 | E_err:   0.007580
[2025-10-07 08:48:20] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -44.249656 | E_var:     0.2517 | E_err:   0.007839
[2025-10-07 08:48:28] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -44.223978 | E_var:     0.2974 | E_err:   0.008521
[2025-10-07 08:48:35] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -44.241301 | E_var:     0.2646 | E_err:   0.008037
[2025-10-07 08:48:43] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -44.240129 | E_var:     0.2838 | E_err:   0.008323
[2025-10-07 08:48:51] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -44.236497 | E_var:     0.2569 | E_err:   0.007920
[2025-10-07 08:48:59] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -44.228754 | E_var:     0.2284 | E_err:   0.007468
[2025-10-07 08:49:07] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -44.235211 | E_var:     0.2767 | E_err:   0.008218
[2025-10-07 08:49:15] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -44.238283 | E_var:     0.3541 | E_err:   0.009298
[2025-10-07 08:49:22] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -44.232523 | E_var:     0.2701 | E_err:   0.008121
[2025-10-07 08:49:30] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -44.238321 | E_var:     0.3067 | E_err:   0.008653
[2025-10-07 08:49:38] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -44.249003 | E_var:     0.2551 | E_err:   0.007892
[2025-10-07 08:49:46] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -44.232760 | E_var:     0.3046 | E_err:   0.008623
[2025-10-07 08:49:54] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -44.230890 | E_var:     0.2981 | E_err:   0.008531
[2025-10-07 08:50:01] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -44.237201 | E_var:     0.4231 | E_err:   0.010163
[2025-10-07 08:50:01] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 08:50:09] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -44.237711 | E_var:     0.2263 | E_err:   0.007432
[2025-10-07 08:50:17] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -44.235759 | E_var:     0.2668 | E_err:   0.008071
[2025-10-07 08:50:25] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -44.238614 | E_var:     0.2881 | E_err:   0.008387
[2025-10-07 08:50:33] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -44.228433 | E_var:     0.2726 | E_err:   0.008158
[2025-10-07 08:50:41] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -44.234219 | E_var:     0.3414 | E_err:   0.009130
[2025-10-07 08:50:48] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -44.234965 | E_var:     0.2509 | E_err:   0.007826
[2025-10-07 08:50:56] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -44.237408 | E_var:     0.2475 | E_err:   0.007773
[2025-10-07 08:51:04] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -44.226082 | E_var:     0.3089 | E_err:   0.008684
[2025-10-07 08:51:12] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -44.235025 | E_var:     0.2960 | E_err:   0.008500
[2025-10-07 08:51:20] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -44.237965 | E_var:     0.2717 | E_err:   0.008145
[2025-10-07 08:51:27] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -44.244594 | E_var:     0.2492 | E_err:   0.007800
[2025-10-07 08:51:35] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -44.248854 | E_var:     0.3783 | E_err:   0.009611
[2025-10-07 08:51:43] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -44.254014 | E_var:     0.2412 | E_err:   0.007674
[2025-10-07 08:51:51] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -44.228329 | E_var:     0.3024 | E_err:   0.008592
[2025-10-07 08:51:59] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -44.248693 | E_var:     0.3238 | E_err:   0.008891
[2025-10-07 08:52:07] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -44.241504 | E_var:     0.2043 | E_err:   0.007063
[2025-10-07 08:52:14] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -44.234159 | E_var:     0.3164 | E_err:   0.008790
[2025-10-07 08:52:22] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -44.235250 | E_var:     0.2842 | E_err:   0.008330
[2025-10-07 08:52:30] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -44.231932 | E_var:     0.3362 | E_err:   0.009060
[2025-10-07 08:52:38] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -44.243604 | E_var:     0.2209 | E_err:   0.007343
[2025-10-07 08:52:46] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -44.236787 | E_var:     0.2910 | E_err:   0.008429
[2025-10-07 08:52:54] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -44.247351 | E_var:     0.3134 | E_err:   0.008747
[2025-10-07 08:53:01] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -44.244710 | E_var:     0.2401 | E_err:   0.007656
[2025-10-07 08:53:09] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -44.241980 | E_var:     0.2434 | E_err:   0.007709
[2025-10-07 08:53:17] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -44.234663 | E_var:     0.2484 | E_err:   0.007788
[2025-10-07 08:53:25] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -44.241828 | E_var:     0.2643 | E_err:   0.008033
[2025-10-07 08:53:33] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -44.234381 | E_var:     0.3912 | E_err:   0.009772
[2025-10-07 08:53:40] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -44.247195 | E_var:     0.2675 | E_err:   0.008082
[2025-10-07 08:53:48] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -44.230563 | E_var:     0.2876 | E_err:   0.008379
[2025-10-07 08:53:56] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -44.221695 | E_var:     0.2819 | E_err:   0.008296
[2025-10-07 08:54:04] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -44.239527 | E_var:     0.2413 | E_err:   0.007675
[2025-10-07 08:54:12] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -44.229131 | E_var:     0.2673 | E_err:   0.008079
[2025-10-07 08:54:20] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -44.232944 | E_var:     0.2506 | E_err:   0.007821
[2025-10-07 08:54:27] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -44.242029 | E_var:     0.2293 | E_err:   0.007482
[2025-10-07 08:54:35] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -44.229228 | E_var:     0.2852 | E_err:   0.008344
[2025-10-07 08:54:43] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -44.243427 | E_var:     0.3320 | E_err:   0.009003
[2025-10-07 08:54:51] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -44.241611 | E_var:     0.2540 | E_err:   0.007875
[2025-10-07 08:54:59] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -44.249353 | E_var:     0.2627 | E_err:   0.008009
[2025-10-07 08:55:06] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -44.242008 | E_var:     0.3375 | E_err:   0.009078
[2025-10-07 08:55:14] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -44.230706 | E_var:     0.2945 | E_err:   0.008479
[2025-10-07 08:55:22] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -44.243151 | E_var:     0.2545 | E_err:   0.007883
[2025-10-07 08:55:30] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -44.244489 | E_var:     0.2557 | E_err:   0.007901
[2025-10-07 08:55:38] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -44.247065 | E_var:     0.2608 | E_err:   0.007979
[2025-10-07 08:55:46] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -44.224081 | E_var:     0.2760 | E_err:   0.008208
[2025-10-07 08:55:53] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -44.234274 | E_var:     0.2184 | E_err:   0.007302
[2025-10-07 08:56:01] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -44.236441 | E_var:     0.2805 | E_err:   0.008275
[2025-10-07 08:56:09] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -44.238735 | E_var:     0.2331 | E_err:   0.007544
[2025-10-07 08:56:17] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -44.235694 | E_var:     0.3096 | E_err:   0.008694
[2025-10-07 08:56:25] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -44.236275 | E_var:     0.2197 | E_err:   0.007324
[2025-10-07 08:56:32] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -44.225594 | E_var:     0.2146 | E_err:   0.007239
[2025-10-07 08:56:40] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -44.246510 | E_var:     0.2413 | E_err:   0.007675
[2025-10-07 08:56:48] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -44.231905 | E_var:     0.2920 | E_err:   0.008443
[2025-10-07 08:56:56] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -44.246694 | E_var:     0.2816 | E_err:   0.008291
[2025-10-07 08:57:04] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -44.234541 | E_var:     0.2523 | E_err:   0.007848
[2025-10-07 08:57:12] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -44.231863 | E_var:     0.4024 | E_err:   0.009912
[2025-10-07 08:57:19] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -44.238535 | E_var:     0.2956 | E_err:   0.008496
[2025-10-07 08:57:27] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -44.244868 | E_var:     0.3645 | E_err:   0.009434
[2025-10-07 08:57:35] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -44.240710 | E_var:     0.3110 | E_err:   0.008714
[2025-10-07 08:57:43] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -44.237057 | E_var:     0.4395 | E_err:   0.010358
[2025-10-07 08:57:51] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -44.252017 | E_var:     0.3225 | E_err:   0.008874
[2025-10-07 08:57:58] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -44.230286 | E_var:     0.3028 | E_err:   0.008598
[2025-10-07 08:58:06] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -44.240550 | E_var:     0.2918 | E_err:   0.008441
[2025-10-07 08:58:14] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -44.239590 | E_var:     0.2216 | E_err:   0.007356
[2025-10-07 08:58:22] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -44.231097 | E_var:     0.2301 | E_err:   0.007495
[2025-10-07 08:58:30] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -44.226840 | E_var:     0.2202 | E_err:   0.007332
[2025-10-07 08:58:38] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -44.231843 | E_var:     0.2298 | E_err:   0.007489
[2025-10-07 08:58:45] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -44.246455 | E_var:     0.2918 | E_err:   0.008441
[2025-10-07 08:58:53] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -44.236973 | E_var:     0.2589 | E_err:   0.007951
[2025-10-07 08:59:01] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -44.232065 | E_var:     0.2475 | E_err:   0.007773
[2025-10-07 08:59:09] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -44.245183 | E_var:     0.2393 | E_err:   0.007643
[2025-10-07 08:59:17] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -44.245782 | E_var:     0.2741 | E_err:   0.008180
[2025-10-07 08:59:24] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -44.222341 | E_var:     0.2780 | E_err:   0.008238
[2025-10-07 08:59:32] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -44.244419 | E_var:     0.3248 | E_err:   0.008905
[2025-10-07 08:59:40] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -44.229425 | E_var:     0.2905 | E_err:   0.008422
[2025-10-07 08:59:48] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -44.231069 | E_var:     0.3909 | E_err:   0.009769
[2025-10-07 08:59:56] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -44.239531 | E_var:     0.2643 | E_err:   0.008033
[2025-10-07 09:00:04] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -44.239212 | E_var:     0.2797 | E_err:   0.008263
[2025-10-07 09:00:11] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -44.236687 | E_var:     0.2901 | E_err:   0.008415
[2025-10-07 09:00:19] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -44.232258 | E_var:     0.2544 | E_err:   0.007881
[2025-10-07 09:00:27] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -44.226359 | E_var:     0.2531 | E_err:   0.007861
[2025-10-07 09:00:35] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -44.235206 | E_var:     0.2730 | E_err:   0.008164
[2025-10-07 09:00:43] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -44.231139 | E_var:     0.3002 | E_err:   0.008561
[2025-10-07 09:00:51] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -44.225716 | E_var:     0.2224 | E_err:   0.007368
[2025-10-07 09:00:59] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -44.248708 | E_var:     0.2689 | E_err:   0.008102
[2025-10-07 09:01:07] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -44.236561 | E_var:     0.3909 | E_err:   0.009769
[2025-10-07 09:01:15] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -44.236427 | E_var:     0.2125 | E_err:   0.007203
[2025-10-07 09:01:22] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -44.237731 | E_var:     0.2870 | E_err:   0.008370
[2025-10-07 09:01:30] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -44.239385 | E_var:     0.3220 | E_err:   0.008867
[2025-10-07 09:01:38] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -44.255251 | E_var:     0.2323 | E_err:   0.007531
[2025-10-07 09:01:46] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -44.239717 | E_var:     0.2992 | E_err:   0.008547
[2025-10-07 09:01:54] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -44.235327 | E_var:     0.3128 | E_err:   0.008739
[2025-10-07 09:02:02] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -44.245991 | E_var:     0.2413 | E_err:   0.007675
[2025-10-07 09:02:09] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -44.240611 | E_var:     0.2918 | E_err:   0.008441
[2025-10-07 09:02:17] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -44.235133 | E_var:     0.2450 | E_err:   0.007735
[2025-10-07 09:02:25] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -44.230168 | E_var:     0.3369 | E_err:   0.009069
[2025-10-07 09:02:33] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -44.241285 | E_var:     0.3009 | E_err:   0.008571
[2025-10-07 09:02:41] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -44.222431 | E_var:     0.2331 | E_err:   0.007544
[2025-10-07 09:02:48] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -44.234652 | E_var:     0.2138 | E_err:   0.007224
[2025-10-07 09:02:56] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -44.237659 | E_var:     0.2777 | E_err:   0.008234
[2025-10-07 09:03:04] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -44.242052 | E_var:     0.2333 | E_err:   0.007547
[2025-10-07 09:03:04] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 09:03:12] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -44.233583 | E_var:     0.3135 | E_err:   0.008748
[2025-10-07 09:03:20] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -44.241625 | E_var:     0.4109 | E_err:   0.010016
[2025-10-07 09:03:28] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -44.231709 | E_var:     0.3217 | E_err:   0.008862
[2025-10-07 09:03:35] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -44.250135 | E_var:     0.2518 | E_err:   0.007840
[2025-10-07 09:03:43] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -44.231080 | E_var:     0.2322 | E_err:   0.007529
[2025-10-07 09:03:51] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -44.239484 | E_var:     0.2451 | E_err:   0.007735
[2025-10-07 09:03:59] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -44.241166 | E_var:     0.2935 | E_err:   0.008465
[2025-10-07 09:04:07] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -44.235557 | E_var:     0.3521 | E_err:   0.009271
[2025-10-07 09:04:14] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -44.226985 | E_var:     0.3656 | E_err:   0.009447
[2025-10-07 09:04:22] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -44.228847 | E_var:     0.2254 | E_err:   0.007419
[2025-10-07 09:04:30] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -44.241343 | E_var:     0.2491 | E_err:   0.007799
[2025-10-07 09:04:38] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -44.235163 | E_var:     0.2429 | E_err:   0.007701
[2025-10-07 09:04:46] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -44.230302 | E_var:     0.2498 | E_err:   0.007810
[2025-10-07 09:04:54] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -44.231940 | E_var:     0.3806 | E_err:   0.009639
[2025-10-07 09:05:01] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -44.236864 | E_var:     0.2320 | E_err:   0.007526
[2025-10-07 09:05:09] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -44.228164 | E_var:     0.2436 | E_err:   0.007712
[2025-10-07 09:05:17] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -44.235661 | E_var:     0.2344 | E_err:   0.007565
[2025-10-07 09:05:25] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -44.236363 | E_var:     0.2414 | E_err:   0.007676
[2025-10-07 09:05:33] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -44.232774 | E_var:     0.2212 | E_err:   0.007349
[2025-10-07 09:05:40] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -44.237126 | E_var:     0.1867 | E_err:   0.006751
[2025-10-07 09:05:48] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -44.228001 | E_var:     0.2529 | E_err:   0.007857
[2025-10-07 09:05:56] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -44.238738 | E_var:     0.2749 | E_err:   0.008193
[2025-10-07 09:06:04] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -44.228969 | E_var:     0.2624 | E_err:   0.008003
[2025-10-07 09:06:12] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -44.239042 | E_var:     0.2295 | E_err:   0.007485
[2025-10-07 09:06:20] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -44.225861 | E_var:     0.2450 | E_err:   0.007734
[2025-10-07 09:06:27] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -44.237530 | E_var:     0.2389 | E_err:   0.007637
[2025-10-07 09:06:35] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -44.238807 | E_var:     0.2596 | E_err:   0.007961
[2025-10-07 09:06:43] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -44.235264 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 09:06:51] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -44.234035 | E_var:     0.2753 | E_err:   0.008199
[2025-10-07 09:06:59] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -44.239355 | E_var:     0.2964 | E_err:   0.008507
[2025-10-07 09:07:06] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -44.237364 | E_var:     0.2431 | E_err:   0.007703
[2025-10-07 09:07:14] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -44.251103 | E_var:     0.2912 | E_err:   0.008432
[2025-10-07 09:07:22] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -44.253807 | E_var:     0.2689 | E_err:   0.008103
[2025-10-07 09:07:30] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -44.242311 | E_var:     0.2292 | E_err:   0.007481
[2025-10-07 09:07:38] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -44.234737 | E_var:     0.2964 | E_err:   0.008507
[2025-10-07 09:07:46] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -44.222875 | E_var:     0.2941 | E_err:   0.008473
[2025-10-07 09:07:53] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -44.240274 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 09:08:01] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -44.226460 | E_var:     0.3305 | E_err:   0.008982
[2025-10-07 09:08:09] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -44.231049 | E_var:     0.2950 | E_err:   0.008486
[2025-10-07 09:08:17] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -44.231063 | E_var:     0.2215 | E_err:   0.007354
[2025-10-07 09:08:25] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -44.239113 | E_var:     0.1823 | E_err:   0.006672
[2025-10-07 09:08:32] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -44.240075 | E_var:     0.2642 | E_err:   0.008031
[2025-10-07 09:08:40] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -44.231552 | E_var:     0.2810 | E_err:   0.008283
[2025-10-07 09:08:48] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -44.232074 | E_var:     0.2598 | E_err:   0.007964
[2025-10-07 09:08:56] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -44.241743 | E_var:     0.2331 | E_err:   0.007544
[2025-10-07 09:09:04] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -44.245569 | E_var:     0.3872 | E_err:   0.009723
[2025-10-07 09:09:12] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -44.231038 | E_var:     0.2178 | E_err:   0.007292
[2025-10-07 09:09:19] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -44.219850 | E_var:     0.2607 | E_err:   0.007978
[2025-10-07 09:09:27] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -44.234660 | E_var:     0.2384 | E_err:   0.007629
[2025-10-07 09:09:35] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -44.237669 | E_var:     0.3032 | E_err:   0.008604
[2025-10-07 09:09:43] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -44.243638 | E_var:     0.2627 | E_err:   0.008009
[2025-10-07 09:09:51] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -44.239087 | E_var:     0.2116 | E_err:   0.007188
[2025-10-07 09:09:58] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -44.241161 | E_var:     0.2214 | E_err:   0.007353
[2025-10-07 09:10:06] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -44.227689 | E_var:     0.3379 | E_err:   0.009083
[2025-10-07 09:10:14] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -44.238324 | E_var:     0.2384 | E_err:   0.007629
[2025-10-07 09:10:22] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -44.236428 | E_var:     0.2488 | E_err:   0.007793
[2025-10-07 09:10:30] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -44.237514 | E_var:     0.2282 | E_err:   0.007465
[2025-10-07 09:10:38] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -44.231467 | E_var:     0.2615 | E_err:   0.007989
[2025-10-07 09:10:45] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -44.253044 | E_var:     0.2639 | E_err:   0.008027
[2025-10-07 09:10:53] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -44.238984 | E_var:     0.2380 | E_err:   0.007623
[2025-10-07 09:11:01] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -44.242260 | E_var:     0.3251 | E_err:   0.008909
[2025-10-07 09:11:09] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -44.257900 | E_var:     0.2930 | E_err:   0.008457
[2025-10-07 09:11:17] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -44.243437 | E_var:     0.2038 | E_err:   0.007053
[2025-10-07 09:11:24] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -44.236861 | E_var:     0.2978 | E_err:   0.008526
[2025-10-07 09:11:32] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -44.226847 | E_var:     0.3727 | E_err:   0.009539
[2025-10-07 09:11:40] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -44.246991 | E_var:     0.2263 | E_err:   0.007433
[2025-10-07 09:11:48] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -44.247145 | E_var:     0.3747 | E_err:   0.009565
[2025-10-07 09:11:56] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -44.230598 | E_var:     0.2492 | E_err:   0.007800
[2025-10-07 09:12:04] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -44.256761 | E_var:     0.2731 | E_err:   0.008165
[2025-10-07 09:12:11] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -44.235812 | E_var:     0.2557 | E_err:   0.007901
[2025-10-07 09:12:19] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -44.247334 | E_var:     0.2473 | E_err:   0.007770
[2025-10-07 09:12:27] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -44.224289 | E_var:     0.3473 | E_err:   0.009208
[2025-10-07 09:12:35] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -44.229992 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 09:12:43] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -44.239800 | E_var:     0.2563 | E_err:   0.007910
[2025-10-07 09:12:50] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -44.253536 | E_var:     0.2673 | E_err:   0.008079
[2025-10-07 09:12:58] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -44.245787 | E_var:     0.3465 | E_err:   0.009197
[2025-10-07 09:13:06] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -44.224857 | E_var:     0.2407 | E_err:   0.007665
[2025-10-07 09:13:14] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -44.237708 | E_var:     0.3026 | E_err:   0.008595
[2025-10-07 09:13:22] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -44.231770 | E_var:     0.4389 | E_err:   0.010351
[2025-10-07 09:13:30] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -44.230735 | E_var:     0.2870 | E_err:   0.008371
[2025-10-07 09:13:37] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -44.230997 | E_var:     0.3608 | E_err:   0.009386
[2025-10-07 09:13:45] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -44.239860 | E_var:     0.2569 | E_err:   0.007919
[2025-10-07 09:13:53] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -44.227213 | E_var:     0.3165 | E_err:   0.008791
[2025-10-07 09:14:01] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -44.234774 | E_var:     0.2267 | E_err:   0.007439
[2025-10-07 09:14:09] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -44.234290 | E_var:     0.2353 | E_err:   0.007579
[2025-10-07 09:14:16] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -44.243663 | E_var:     0.1899 | E_err:   0.006808
[2025-10-07 09:14:24] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -44.242571 | E_var:     0.2495 | E_err:   0.007805
[2025-10-07 09:14:32] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -44.232017 | E_var:     0.2343 | E_err:   0.007563
[2025-10-07 09:14:40] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -44.226293 | E_var:     0.2890 | E_err:   0.008400
[2025-10-07 09:14:48] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -44.237450 | E_var:     0.2926 | E_err:   0.008451
[2025-10-07 09:14:55] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -44.233749 | E_var:     0.2317 | E_err:   0.007522
[2025-10-07 09:15:03] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -44.225529 | E_var:     0.2803 | E_err:   0.008273
[2025-10-07 09:15:11] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -44.246102 | E_var:     0.2647 | E_err:   0.008039
[2025-10-07 09:15:19] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -44.224160 | E_var:     0.2567 | E_err:   0.007916
[2025-10-07 09:15:27] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -44.232448 | E_var:     0.2666 | E_err:   0.008068
[2025-10-07 09:15:35] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -44.229883 | E_var:     0.3697 | E_err:   0.009501
[2025-10-07 09:15:42] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -44.231554 | E_var:     0.2548 | E_err:   0.007888
[2025-10-07 09:15:50] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -44.233772 | E_var:     0.2270 | E_err:   0.007445
[2025-10-07 09:15:58] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -44.242340 | E_var:     0.2590 | E_err:   0.007952
[2025-10-07 09:16:06] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -44.228735 | E_var:     0.2598 | E_err:   0.007963
[2025-10-07 09:16:06] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 09:16:14] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -44.254009 | E_var:     0.2354 | E_err:   0.007581
[2025-10-07 09:16:22] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -44.223818 | E_var:     0.2655 | E_err:   0.008051
[2025-10-07 09:16:29] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -44.232837 | E_var:     0.2277 | E_err:   0.007456
[2025-10-07 09:16:37] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -44.226364 | E_var:     0.2869 | E_err:   0.008369
[2025-10-07 09:16:45] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -44.237212 | E_var:     0.2525 | E_err:   0.007851
[2025-10-07 09:16:53] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -44.237535 | E_var:     0.3063 | E_err:   0.008647
[2025-10-07 09:17:01] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -44.227228 | E_var:     0.2366 | E_err:   0.007600
[2025-10-07 09:17:08] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -44.227087 | E_var:     0.3385 | E_err:   0.009091
[2025-10-07 09:17:16] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -44.233849 | E_var:     0.1949 | E_err:   0.006898
[2025-10-07 09:17:24] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -44.243410 | E_var:     0.2495 | E_err:   0.007804
[2025-10-07 09:17:32] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -44.232313 | E_var:     0.4202 | E_err:   0.010129
[2025-10-07 09:17:40] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -44.250959 | E_var:     0.3233 | E_err:   0.008884
[2025-10-07 09:17:48] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -44.235908 | E_var:     0.3343 | E_err:   0.009034
[2025-10-07 09:17:55] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -44.229053 | E_var:     0.2662 | E_err:   0.008061
[2025-10-07 09:18:03] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -44.232114 | E_var:     0.2336 | E_err:   0.007551
[2025-10-07 09:18:11] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -44.255105 | E_var:     0.2167 | E_err:   0.007273
[2025-10-07 09:18:19] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -44.228167 | E_var:     0.2262 | E_err:   0.007431
[2025-10-07 09:18:27] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -44.229841 | E_var:     0.2762 | E_err:   0.008212
[2025-10-07 09:18:34] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -44.248887 | E_var:     0.2536 | E_err:   0.007868
[2025-10-07 09:18:42] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -44.229264 | E_var:     0.3247 | E_err:   0.008904
[2025-10-07 09:18:50] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -44.243264 | E_var:     0.2379 | E_err:   0.007622
[2025-10-07 09:18:58] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -44.232775 | E_var:     0.2591 | E_err:   0.007953
[2025-10-07 09:19:06] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -44.235315 | E_var:     0.2467 | E_err:   0.007761
[2025-10-07 09:19:14] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -44.235224 | E_var:     0.2317 | E_err:   0.007521
[2025-10-07 09:19:21] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -44.246106 | E_var:     0.2343 | E_err:   0.007563
[2025-10-07 09:19:29] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -44.218030 | E_var:     0.2320 | E_err:   0.007526
[2025-10-07 09:19:37] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -44.231305 | E_var:     0.2657 | E_err:   0.008054
[2025-10-07 09:19:45] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -44.247844 | E_var:     0.2223 | E_err:   0.007368
[2025-10-07 09:19:53] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -44.233195 | E_var:     0.2789 | E_err:   0.008252
[2025-10-07 09:20:00] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -44.235508 | E_var:     0.3247 | E_err:   0.008904
[2025-10-07 09:20:08] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -44.245936 | E_var:     0.2800 | E_err:   0.008268
[2025-10-07 09:20:16] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -44.251685 | E_var:     0.3720 | E_err:   0.009530
[2025-10-07 09:20:24] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -44.251248 | E_var:     0.3336 | E_err:   0.009025
[2025-10-07 09:20:32] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -44.240490 | E_var:     0.2514 | E_err:   0.007834
[2025-10-07 09:20:39] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -44.225487 | E_var:     0.2448 | E_err:   0.007730
[2025-10-07 09:20:47] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -44.234953 | E_var:     0.2111 | E_err:   0.007178
[2025-10-07 09:20:55] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -44.243475 | E_var:     0.2501 | E_err:   0.007814
[2025-10-07 09:21:03] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -44.238911 | E_var:     0.2795 | E_err:   0.008260
[2025-10-07 09:21:11] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -44.248854 | E_var:     0.2269 | E_err:   0.007443
[2025-10-07 09:21:19] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -44.230519 | E_var:     0.3528 | E_err:   0.009280
[2025-10-07 09:21:26] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -44.231985 | E_var:     0.2440 | E_err:   0.007717
[2025-10-07 09:21:34] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -44.241673 | E_var:     0.3136 | E_err:   0.008750
[2025-10-07 09:21:42] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -44.246833 | E_var:     0.2729 | E_err:   0.008162
[2025-10-07 09:21:50] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -44.216080 | E_var:     0.2965 | E_err:   0.008508
[2025-10-07 09:21:58] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -44.232406 | E_var:     0.3056 | E_err:   0.008638
[2025-10-07 09:22:05] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -44.236605 | E_var:     0.2674 | E_err:   0.008080
[2025-10-07 09:22:13] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -44.234255 | E_var:     0.2441 | E_err:   0.007719
[2025-10-07 09:22:21] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -44.229069 | E_var:     0.2307 | E_err:   0.007504
[2025-10-07 09:22:29] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -44.234800 | E_var:     0.2493 | E_err:   0.007802
[2025-10-07 09:22:37] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -44.231326 | E_var:     0.2063 | E_err:   0.007097
[2025-10-07 09:22:45] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -44.235950 | E_var:     0.1942 | E_err:   0.006885
[2025-10-07 09:22:53] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -44.232742 | E_var:     0.2476 | E_err:   0.007775
[2025-10-07 09:23:01] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -44.228532 | E_var:     0.2816 | E_err:   0.008292
[2025-10-07 09:23:09] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -44.222507 | E_var:     0.4783 | E_err:   0.010806
[2025-10-07 09:23:17] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -44.237419 | E_var:     0.2426 | E_err:   0.007696
[2025-10-07 09:23:24] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -44.236952 | E_var:     0.2185 | E_err:   0.007305
[2025-10-07 09:23:32] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -44.225933 | E_var:     0.3497 | E_err:   0.009239
[2025-10-07 09:23:40] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -44.244125 | E_var:     0.3120 | E_err:   0.008728
[2025-10-07 09:23:48] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -44.249917 | E_var:     0.2348 | E_err:   0.007572
[2025-10-07 09:23:56] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -44.237017 | E_var:     0.2203 | E_err:   0.007333
[2025-10-07 09:24:04] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -44.237638 | E_var:     0.2310 | E_err:   0.007510
[2025-10-07 09:24:11] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -44.244401 | E_var:     0.3051 | E_err:   0.008630
[2025-10-07 09:24:19] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -44.242722 | E_var:     0.2472 | E_err:   0.007768
[2025-10-07 09:24:27] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -44.240687 | E_var:     0.2232 | E_err:   0.007382
[2025-10-07 09:24:35] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -44.241898 | E_var:     0.2457 | E_err:   0.007745
[2025-10-07 09:24:43] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -44.244482 | E_var:     0.2272 | E_err:   0.007447
[2025-10-07 09:24:50] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -44.252550 | E_var:     0.3261 | E_err:   0.008923
[2025-10-07 09:24:58] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -44.235060 | E_var:     0.3187 | E_err:   0.008821
[2025-10-07 09:25:06] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -44.257934 | E_var:     0.2835 | E_err:   0.008319
[2025-10-07 09:25:14] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -44.236955 | E_var:     0.2241 | E_err:   0.007397
[2025-10-07 09:25:22] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -44.219559 | E_var:     0.2591 | E_err:   0.007954
[2025-10-07 09:25:30] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -44.237749 | E_var:     0.2659 | E_err:   0.008058
[2025-10-07 09:25:37] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -44.235602 | E_var:     0.3337 | E_err:   0.009027
[2025-10-07 09:25:45] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -44.242740 | E_var:     0.2442 | E_err:   0.007721
[2025-10-07 09:25:53] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -44.236014 | E_var:     0.2593 | E_err:   0.007957
[2025-10-07 09:26:01] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -44.245280 | E_var:     0.2638 | E_err:   0.008025
[2025-10-07 09:26:09] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -44.252826 | E_var:     0.2824 | E_err:   0.008303
[2025-10-07 09:26:17] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -44.235549 | E_var:     0.2138 | E_err:   0.007225
[2025-10-07 09:26:24] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -44.241295 | E_var:     0.2832 | E_err:   0.008315
[2025-10-07 09:26:32] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -44.231106 | E_var:     0.2864 | E_err:   0.008362
[2025-10-07 09:26:40] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -44.246599 | E_var:     0.3536 | E_err:   0.009292
[2025-10-07 09:26:48] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -44.225244 | E_var:     0.2146 | E_err:   0.007239
[2025-10-07 09:26:56] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -44.248744 | E_var:     0.2346 | E_err:   0.007569
[2025-10-07 09:27:03] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -44.232635 | E_var:     0.2717 | E_err:   0.008145
[2025-10-07 09:27:11] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -44.239683 | E_var:     0.2106 | E_err:   0.007171
[2025-10-07 09:27:19] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -44.235217 | E_var:     0.2271 | E_err:   0.007445
[2025-10-07 09:27:27] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -44.243088 | E_var:     0.2272 | E_err:   0.007448
[2025-10-07 09:27:35] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -44.247415 | E_var:     0.3594 | E_err:   0.009367
[2025-10-07 09:27:42] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -44.230868 | E_var:     0.2121 | E_err:   0.007196
[2025-10-07 09:27:50] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -44.243595 | E_var:     0.2430 | E_err:   0.007703
[2025-10-07 09:27:58] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -44.245100 | E_var:     0.2443 | E_err:   0.007723
[2025-10-07 09:28:06] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -44.240297 | E_var:     0.3345 | E_err:   0.009037
[2025-10-07 09:28:14] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -44.233364 | E_var:     0.2885 | E_err:   0.008392
[2025-10-07 09:28:22] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -44.231150 | E_var:     0.6078 | E_err:   0.012182
[2025-10-07 09:28:29] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -44.245949 | E_var:     0.2123 | E_err:   0.007199
[2025-10-07 09:28:37] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -44.233333 | E_var:     0.2475 | E_err:   0.007773
[2025-10-07 09:28:45] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -44.227279 | E_var:     0.3171 | E_err:   0.008798
[2025-10-07 09:28:53] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -44.250631 | E_var:     0.2668 | E_err:   0.008071
[2025-10-07 09:29:01] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -44.260391 | E_var:     0.1935 | E_err:   0.006873
[2025-10-07 09:29:09] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -44.248071 | E_var:     0.2625 | E_err:   0.008005
[2025-10-07 09:29:09] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 09:29:16] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -44.245571 | E_var:     0.2799 | E_err:   0.008266
[2025-10-07 09:29:24] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -44.241456 | E_var:     0.2260 | E_err:   0.007428
[2025-10-07 09:29:32] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -44.249892 | E_var:     0.2498 | E_err:   0.007810
[2025-10-07 09:29:40] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -44.242039 | E_var:     0.1997 | E_err:   0.006983
[2025-10-07 09:29:48] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -44.236560 | E_var:     0.2209 | E_err:   0.007344
[2025-10-07 09:29:55] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -44.227106 | E_var:     0.3622 | E_err:   0.009404
[2025-10-07 09:30:03] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -44.245705 | E_var:     0.2755 | E_err:   0.008201
[2025-10-07 09:30:11] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -44.236964 | E_var:     0.2344 | E_err:   0.007564
[2025-10-07 09:30:19] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -44.234333 | E_var:     0.2355 | E_err:   0.007582
[2025-10-07 09:30:27] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -44.236272 | E_var:     0.2989 | E_err:   0.008542
[2025-10-07 09:30:35] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -44.235581 | E_var:     0.3711 | E_err:   0.009518
[2025-10-07 09:30:42] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -44.247697 | E_var:     0.1884 | E_err:   0.006783
[2025-10-07 09:30:50] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -44.223704 | E_var:     0.2368 | E_err:   0.007603
[2025-10-07 09:30:58] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -44.232235 | E_var:     0.2166 | E_err:   0.007273
[2025-10-07 09:31:06] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -44.251434 | E_var:     0.2810 | E_err:   0.008283
[2025-10-07 09:31:14] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -44.234107 | E_var:     0.2418 | E_err:   0.007683
[2025-10-07 09:31:21] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -44.238314 | E_var:     0.2917 | E_err:   0.008439
[2025-10-07 09:31:29] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -44.230353 | E_var:     0.2904 | E_err:   0.008421
[2025-10-07 09:31:37] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -44.230149 | E_var:     0.2544 | E_err:   0.007881
[2025-10-07 09:31:45] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -44.233920 | E_var:     0.2954 | E_err:   0.008493
[2025-10-07 09:31:53] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -44.257143 | E_var:     0.3317 | E_err:   0.008999
[2025-10-07 09:32:01] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -44.242756 | E_var:     0.2788 | E_err:   0.008251
[2025-10-07 09:32:08] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -44.241541 | E_var:     0.2243 | E_err:   0.007400
[2025-10-07 09:32:16] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -44.246735 | E_var:     0.3246 | E_err:   0.008902
[2025-10-07 09:32:24] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -44.238617 | E_var:     0.4821 | E_err:   0.010849
[2025-10-07 09:32:32] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -44.241997 | E_var:     0.2256 | E_err:   0.007422
[2025-10-07 09:32:40] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -44.234757 | E_var:     0.2780 | E_err:   0.008238
[2025-10-07 09:32:47] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -44.242438 | E_var:     0.2392 | E_err:   0.007641
[2025-10-07 09:32:55] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -44.231591 | E_var:     0.3631 | E_err:   0.009416
[2025-10-07 09:33:03] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -44.230205 | E_var:     0.3051 | E_err:   0.008631
[2025-10-07 09:33:11] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -44.236969 | E_var:     0.2101 | E_err:   0.007161
[2025-10-07 09:33:19] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -44.242828 | E_var:     0.2878 | E_err:   0.008383
[2025-10-07 09:33:27] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -44.243342 | E_var:     0.2826 | E_err:   0.008306
[2025-10-07 09:33:34] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -44.246282 | E_var:     0.2403 | E_err:   0.007659
[2025-10-07 09:33:42] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -44.236870 | E_var:     0.2821 | E_err:   0.008299
[2025-10-07 09:33:50] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -44.255066 | E_var:     0.2631 | E_err:   0.008014
[2025-10-07 09:33:58] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -44.220008 | E_var:     0.8502 | E_err:   0.014407
[2025-10-07 09:34:06] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -44.237200 | E_var:     0.2932 | E_err:   0.008460
[2025-10-07 09:34:13] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -44.236523 | E_var:     0.2069 | E_err:   0.007108
[2025-10-07 09:34:21] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -44.244875 | E_var:     0.2920 | E_err:   0.008443
[2025-10-07 09:34:29] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -44.250661 | E_var:     0.2724 | E_err:   0.008156
[2025-10-07 09:34:37] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -44.236774 | E_var:     0.2413 | E_err:   0.007675
[2025-10-07 09:34:45] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -44.246012 | E_var:     0.2579 | E_err:   0.007935
[2025-10-07 09:34:53] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -44.233165 | E_var:     0.2640 | E_err:   0.008028
[2025-10-07 09:35:00] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -44.245858 | E_var:     0.2179 | E_err:   0.007293
[2025-10-07 09:35:08] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -44.244652 | E_var:     0.2245 | E_err:   0.007403
[2025-10-07 09:35:16] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -44.233146 | E_var:     0.2702 | E_err:   0.008122
[2025-10-07 09:35:24] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -44.233537 | E_var:     0.2544 | E_err:   0.007882
[2025-10-07 09:35:32] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -44.247270 | E_var:     0.2483 | E_err:   0.007785
[2025-10-07 09:35:39] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -44.244594 | E_var:     0.3421 | E_err:   0.009139
[2025-10-07 09:35:47] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -44.246736 | E_var:     0.1936 | E_err:   0.006876
[2025-10-07 09:35:55] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -44.253888 | E_var:     0.1986 | E_err:   0.006963
[2025-10-07 09:36:03] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -44.232990 | E_var:     0.2998 | E_err:   0.008555
[2025-10-07 09:36:11] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -44.248254 | E_var:     0.2351 | E_err:   0.007575
[2025-10-07 09:36:19] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -44.227153 | E_var:     0.2177 | E_err:   0.007290
[2025-10-07 09:36:26] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -44.238799 | E_var:     0.2430 | E_err:   0.007703
[2025-10-07 09:36:34] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -44.234038 | E_var:     0.2445 | E_err:   0.007726
[2025-10-07 09:36:42] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -44.242548 | E_var:     0.2708 | E_err:   0.008131
[2025-10-07 09:36:50] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -44.234768 | E_var:     0.2362 | E_err:   0.007594
[2025-10-07 09:36:58] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -44.235238 | E_var:     0.2445 | E_err:   0.007726
[2025-10-07 09:37:05] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -44.242279 | E_var:     0.2018 | E_err:   0.007019
[2025-10-07 09:37:13] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -44.237409 | E_var:     0.2515 | E_err:   0.007836
[2025-10-07 09:37:21] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -44.248726 | E_var:     0.2183 | E_err:   0.007300
[2025-10-07 09:37:29] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -44.223412 | E_var:     0.2771 | E_err:   0.008224
[2025-10-07 09:37:37] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -44.232468 | E_var:     0.2974 | E_err:   0.008520
[2025-10-07 09:37:45] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -44.233130 | E_var:     0.2080 | E_err:   0.007126
[2025-10-07 09:37:52] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -44.235070 | E_var:     0.2476 | E_err:   0.007774
[2025-10-07 09:38:00] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -44.232124 | E_var:     0.2983 | E_err:   0.008534
[2025-10-07 09:38:08] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -44.234524 | E_var:     0.3507 | E_err:   0.009253
[2025-10-07 09:38:16] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -44.250853 | E_var:     0.2964 | E_err:   0.008507
[2025-10-07 09:38:24] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -44.242472 | E_var:     0.2426 | E_err:   0.007696
[2025-10-07 09:38:31] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -44.257164 | E_var:     0.2354 | E_err:   0.007582
[2025-10-07 09:38:39] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -44.247896 | E_var:     0.2840 | E_err:   0.008326
[2025-10-07 09:38:47] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -44.240680 | E_var:     0.2348 | E_err:   0.007571
[2025-10-07 09:38:55] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -44.233081 | E_var:     0.2792 | E_err:   0.008255
[2025-10-07 09:39:03] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -44.240320 | E_var:     0.2681 | E_err:   0.008091
[2025-10-07 09:39:11] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -44.225501 | E_var:     0.2829 | E_err:   0.008311
[2025-10-07 09:39:18] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -44.226834 | E_var:     0.3230 | E_err:   0.008880
[2025-10-07 09:39:26] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -44.240251 | E_var:     0.2558 | E_err:   0.007902
[2025-10-07 09:39:34] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -44.224835 | E_var:     0.2222 | E_err:   0.007366
[2025-10-07 09:39:42] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -44.231639 | E_var:     0.2815 | E_err:   0.008290
[2025-10-07 09:39:50] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -44.254211 | E_var:     0.2733 | E_err:   0.008168
[2025-10-07 09:39:57] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -44.235588 | E_var:     0.4327 | E_err:   0.010278
[2025-10-07 09:40:05] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -44.239370 | E_var:     0.2248 | E_err:   0.007408
[2025-10-07 09:40:13] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -44.244606 | E_var:     0.2862 | E_err:   0.008360
[2025-10-07 09:40:21] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -44.240899 | E_var:     0.2568 | E_err:   0.007918
[2025-10-07 09:40:29] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -44.229818 | E_var:     0.2203 | E_err:   0.007334
[2025-10-07 09:40:37] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -44.240160 | E_var:     0.2598 | E_err:   0.007964
[2025-10-07 09:40:44] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -44.236979 | E_var:     0.2312 | E_err:   0.007514
[2025-10-07 09:40:52] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -44.243372 | E_var:     0.3687 | E_err:   0.009487
[2025-10-07 09:41:00] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -44.217476 | E_var:     0.9194 | E_err:   0.014982
[2025-10-07 09:41:08] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -44.235456 | E_var:     0.2692 | E_err:   0.008107
[2025-10-07 09:41:16] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -44.231602 | E_var:     0.2182 | E_err:   0.007299
[2025-10-07 09:41:23] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -44.235342 | E_var:     0.3189 | E_err:   0.008823
[2025-10-07 09:41:31] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -44.231763 | E_var:     0.2704 | E_err:   0.008125
[2025-10-07 09:41:39] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -44.242967 | E_var:     0.3204 | E_err:   0.008845
[2025-10-07 09:41:47] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -44.227344 | E_var:     0.2165 | E_err:   0.007270
[2025-10-07 09:41:55] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -44.245000 | E_var:     0.3312 | E_err:   0.008993
[2025-10-07 09:42:03] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -44.237358 | E_var:     0.2658 | E_err:   0.008056
[2025-10-07 09:42:10] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -44.228121 | E_var:     0.2614 | E_err:   0.007989
[2025-10-07 09:42:10] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 09:42:18] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -44.236052 | E_var:     0.2318 | E_err:   0.007522
[2025-10-07 09:42:26] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -44.241340 | E_var:     0.3370 | E_err:   0.009070
[2025-10-07 09:42:34] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -44.246694 | E_var:     0.7822 | E_err:   0.013819
[2025-10-07 09:42:42] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -44.245786 | E_var:     0.2282 | E_err:   0.007465
[2025-10-07 09:42:50] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -44.231827 | E_var:     0.2326 | E_err:   0.007535
[2025-10-07 09:42:57] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -44.227073 | E_var:     0.2723 | E_err:   0.008154
[2025-10-07 09:43:05] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -44.245219 | E_var:     0.3465 | E_err:   0.009197
[2025-10-07 09:43:13] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -44.220678 | E_var:     0.2748 | E_err:   0.008191
[2025-10-07 09:43:21] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -44.241207 | E_var:     0.2863 | E_err:   0.008360
[2025-10-07 09:43:29] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -44.229818 | E_var:     0.4644 | E_err:   0.010648
[2025-10-07 09:43:36] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -44.228981 | E_var:     0.2360 | E_err:   0.007590
[2025-10-07 09:43:44] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -44.240064 | E_var:     0.2940 | E_err:   0.008473
[2025-10-07 09:43:52] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -44.256676 | E_var:     0.3772 | E_err:   0.009596
[2025-10-07 09:44:00] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -44.242438 | E_var:     0.2735 | E_err:   0.008171
[2025-10-07 09:44:08] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -44.230789 | E_var:     0.2558 | E_err:   0.007903
[2025-10-07 09:44:16] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -44.238281 | E_var:     0.2186 | E_err:   0.007306
[2025-10-07 09:44:23] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -44.230634 | E_var:     0.2162 | E_err:   0.007264
[2025-10-07 09:44:31] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -44.241673 | E_var:     0.2181 | E_err:   0.007297
[2025-10-07 09:44:39] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -44.245195 | E_var:     0.2103 | E_err:   0.007166
[2025-10-07 09:44:47] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -44.244847 | E_var:     0.3117 | E_err:   0.008723
[2025-10-07 09:44:55] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -44.236394 | E_var:     0.2719 | E_err:   0.008148
[2025-10-07 09:45:02] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -44.224188 | E_var:     0.2007 | E_err:   0.006999
[2025-10-07 09:45:10] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -44.229488 | E_var:     0.1995 | E_err:   0.006979
[2025-10-07 09:45:18] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -44.233613 | E_var:     0.2814 | E_err:   0.008289
[2025-10-07 09:45:26] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -44.235409 | E_var:     0.2976 | E_err:   0.008525
[2025-10-07 09:45:34] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -44.242292 | E_var:     0.2278 | E_err:   0.007458
[2025-10-07 09:45:41] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -44.227371 | E_var:     0.2364 | E_err:   0.007597
[2025-10-07 09:45:49] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -44.243962 | E_var:     0.2481 | E_err:   0.007783
[2025-10-07 09:45:57] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -44.228930 | E_var:     0.2264 | E_err:   0.007435
[2025-10-07 09:46:05] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -44.247041 | E_var:     0.2512 | E_err:   0.007831
[2025-10-07 09:46:13] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -44.248121 | E_var:     0.2995 | E_err:   0.008551
[2025-10-07 09:46:21] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -44.225432 | E_var:     0.4035 | E_err:   0.009925
[2025-10-07 09:46:28] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -44.247388 | E_var:     0.2214 | E_err:   0.007353
[2025-10-07 09:46:36] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -44.215138 | E_var:     0.3810 | E_err:   0.009645
[2025-10-07 09:46:44] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -44.244711 | E_var:     0.2720 | E_err:   0.008149
[2025-10-07 09:46:52] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -44.229753 | E_var:     0.2452 | E_err:   0.007737
[2025-10-07 09:47:00] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -44.229841 | E_var:     0.4658 | E_err:   0.010664
[2025-10-07 09:47:07] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -44.225774 | E_var:     0.2401 | E_err:   0.007656
[2025-10-07 09:47:15] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -44.247139 | E_var:     0.2620 | E_err:   0.007997
[2025-10-07 09:47:23] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -44.234526 | E_var:     0.2887 | E_err:   0.008395
[2025-10-07 09:47:31] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -44.252819 | E_var:     0.2337 | E_err:   0.007554
[2025-10-07 09:47:39] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -44.243270 | E_var:     0.3181 | E_err:   0.008813
[2025-10-07 09:47:47] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -44.244640 | E_var:     0.3110 | E_err:   0.008713
[2025-10-07 09:47:54] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -44.232583 | E_var:     0.2549 | E_err:   0.007889
[2025-10-07 09:48:02] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -44.262801 | E_var:     0.5110 | E_err:   0.011169
[2025-10-07 09:48:10] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -44.236025 | E_var:     0.2830 | E_err:   0.008312
[2025-10-07 09:48:18] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -44.241987 | E_var:     0.2447 | E_err:   0.007729
[2025-10-07 09:48:26] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -44.241476 | E_var:     0.3445 | E_err:   0.009170
[2025-10-07 09:48:33] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -44.217799 | E_var:     0.8421 | E_err:   0.014338
[2025-10-07 09:48:41] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -44.244540 | E_var:     0.2498 | E_err:   0.007810
[2025-10-07 09:48:49] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -44.226017 | E_var:     0.2446 | E_err:   0.007727
[2025-10-07 09:48:57] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -44.236481 | E_var:     0.3367 | E_err:   0.009067
[2025-10-07 09:49:05] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -44.248925 | E_var:     0.3150 | E_err:   0.008770
[2025-10-07 09:49:13] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -44.243953 | E_var:     0.3237 | E_err:   0.008890
[2025-10-07 09:49:20] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -44.241589 | E_var:     0.2851 | E_err:   0.008342
[2025-10-07 09:49:28] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -44.238355 | E_var:     0.2593 | E_err:   0.007956
[2025-10-07 09:49:36] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -44.238249 | E_var:     0.2407 | E_err:   0.007666
[2025-10-07 09:49:44] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -44.235890 | E_var:     0.3060 | E_err:   0.008643
[2025-10-07 09:49:52] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -44.233572 | E_var:     0.2569 | E_err:   0.007919
[2025-10-07 09:49:59] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -44.228899 | E_var:     0.2082 | E_err:   0.007130
[2025-10-07 09:50:07] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -44.241295 | E_var:     0.2367 | E_err:   0.007601
[2025-10-07 09:50:15] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -44.233524 | E_var:     0.2768 | E_err:   0.008220
[2025-10-07 09:50:23] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -44.237079 | E_var:     0.2543 | E_err:   0.007879
[2025-10-07 09:50:31] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -44.240099 | E_var:     0.2804 | E_err:   0.008274
[2025-10-07 09:50:39] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -44.232699 | E_var:     0.2206 | E_err:   0.007339
[2025-10-07 09:50:46] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -44.232553 | E_var:     0.1949 | E_err:   0.006898
[2025-10-07 09:50:54] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -44.246870 | E_var:     0.2731 | E_err:   0.008165
[2025-10-07 09:51:02] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -44.246692 | E_var:     0.2514 | E_err:   0.007834
[2025-10-07 09:51:10] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -44.252006 | E_var:     0.2218 | E_err:   0.007359
[2025-10-07 09:51:18] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -44.220324 | E_var:     0.2705 | E_err:   0.008127
[2025-10-07 09:51:25] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -44.232013 | E_var:     0.2656 | E_err:   0.008053
[2025-10-07 09:51:33] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -44.246863 | E_var:     0.2170 | E_err:   0.007279
[2025-10-07 09:51:41] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -44.231889 | E_var:     0.2469 | E_err:   0.007764
[2025-10-07 09:51:49] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -44.243223 | E_var:     0.2122 | E_err:   0.007197
[2025-10-07 09:51:57] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -44.232382 | E_var:     0.3146 | E_err:   0.008763
[2025-10-07 09:52:05] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -44.226717 | E_var:     0.2460 | E_err:   0.007749
[2025-10-07 09:52:12] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -44.232072 | E_var:     0.2399 | E_err:   0.007654
[2025-10-07 09:52:20] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -44.242253 | E_var:     0.2547 | E_err:   0.007885
[2025-10-07 09:52:28] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -44.237194 | E_var:     0.3287 | E_err:   0.008958
[2025-10-07 09:52:36] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -44.236214 | E_var:     0.5442 | E_err:   0.011526
[2025-10-07 09:52:44] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -44.232169 | E_var:     0.2537 | E_err:   0.007870
[2025-10-07 09:52:51] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -44.245690 | E_var:     0.3235 | E_err:   0.008887
[2025-10-07 09:52:59] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -44.247227 | E_var:     0.3878 | E_err:   0.009730
[2025-10-07 09:53:07] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -44.256016 | E_var:     0.2348 | E_err:   0.007571
[2025-10-07 09:53:15] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -44.241093 | E_var:     0.2387 | E_err:   0.007634
[2025-10-07 09:53:23] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -44.223276 | E_var:     0.3973 | E_err:   0.009848
[2025-10-07 09:53:31] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -44.232998 | E_var:     0.3041 | E_err:   0.008617
[2025-10-07 09:53:38] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -44.235437 | E_var:     0.3224 | E_err:   0.008873
[2025-10-07 09:53:46] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -44.236009 | E_var:     0.2408 | E_err:   0.007667
[2025-10-07 09:53:54] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -44.240351 | E_var:     0.2497 | E_err:   0.007808
[2025-10-07 09:54:02] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -44.239020 | E_var:     0.2352 | E_err:   0.007578
[2025-10-07 09:54:10] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -44.232676 | E_var:     0.2303 | E_err:   0.007499
[2025-10-07 09:54:17] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -44.231414 | E_var:     0.2394 | E_err:   0.007645
[2025-10-07 09:54:25] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -44.229719 | E_var:     0.2568 | E_err:   0.007918
[2025-10-07 09:54:33] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -44.237758 | E_var:     0.2496 | E_err:   0.007806
[2025-10-07 09:54:41] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -44.237458 | E_var:     0.5865 | E_err:   0.011967
[2025-10-07 09:54:49] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -44.243628 | E_var:     0.2250 | E_err:   0.007411
[2025-10-07 09:54:57] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -44.235141 | E_var:     0.3597 | E_err:   0.009372
[2025-10-07 09:55:04] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -44.235195 | E_var:     0.2627 | E_err:   0.008009
[2025-10-07 09:55:12] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -44.237234 | E_var:     0.2881 | E_err:   0.008387
[2025-10-07 09:55:12] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 09:55:20] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -44.245901 | E_var:     0.2138 | E_err:   0.007224
[2025-10-07 09:55:28] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -44.232593 | E_var:     0.2318 | E_err:   0.007523
[2025-10-07 09:55:36] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -44.254651 | E_var:     0.2511 | E_err:   0.007830
[2025-10-07 09:55:44] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -44.247794 | E_var:     0.5207 | E_err:   0.011275
[2025-10-07 09:55:51] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -44.235793 | E_var:     0.2332 | E_err:   0.007545
[2025-10-07 09:55:59] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -44.233585 | E_var:     0.2123 | E_err:   0.007199
[2025-10-07 09:56:07] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -44.242678 | E_var:     0.3925 | E_err:   0.009789
[2025-10-07 09:56:15] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -44.222534 | E_var:     0.3096 | E_err:   0.008694
[2025-10-07 09:56:23] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -44.238524 | E_var:     0.3023 | E_err:   0.008591
[2025-10-07 09:56:30] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -44.237337 | E_var:     0.2773 | E_err:   0.008227
[2025-10-07 09:56:38] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -44.239297 | E_var:     0.2283 | E_err:   0.007466
[2025-10-07 09:56:46] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -44.248069 | E_var:     0.2152 | E_err:   0.007248
[2025-10-07 09:56:54] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -44.244016 | E_var:     0.3458 | E_err:   0.009189
[2025-10-07 09:57:02] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -44.230823 | E_var:     0.2581 | E_err:   0.007937
[2025-10-07 09:57:10] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -44.247360 | E_var:     0.3377 | E_err:   0.009080
[2025-10-07 09:57:17] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -44.233777 | E_var:     0.2714 | E_err:   0.008140
[2025-10-07 09:57:25] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -44.242376 | E_var:     0.3558 | E_err:   0.009320
[2025-10-07 09:57:33] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -44.239210 | E_var:     0.2756 | E_err:   0.008202
[2025-10-07 09:57:41] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -44.236620 | E_var:     0.2253 | E_err:   0.007417
[2025-10-07 09:57:49] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -44.237181 | E_var:     0.2461 | E_err:   0.007752
[2025-10-07 09:57:56] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -44.241900 | E_var:     0.3678 | E_err:   0.009476
[2025-10-07 09:58:04] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -44.239114 | E_var:     0.2244 | E_err:   0.007402
[2025-10-07 09:58:12] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -44.244500 | E_var:     0.3727 | E_err:   0.009539
[2025-10-07 09:58:20] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -44.248412 | E_var:     0.2148 | E_err:   0.007242
[2025-10-07 09:58:28] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -44.241911 | E_var:     0.2337 | E_err:   0.007553
[2025-10-07 09:58:36] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -44.231939 | E_var:     0.2693 | E_err:   0.008108
[2025-10-07 09:58:43] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -44.238813 | E_var:     0.2483 | E_err:   0.007786
[2025-10-07 09:58:51] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -44.237801 | E_var:     0.2305 | E_err:   0.007501
[2025-10-07 09:58:59] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -44.249943 | E_var:     0.2780 | E_err:   0.008239
[2025-10-07 09:59:07] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -44.232202 | E_var:     0.2136 | E_err:   0.007221
[2025-10-07 09:59:15] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -44.229484 | E_var:     0.2101 | E_err:   0.007162
[2025-10-07 09:59:22] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -44.241589 | E_var:     0.2504 | E_err:   0.007819
[2025-10-07 09:59:30] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -44.240481 | E_var:     0.2802 | E_err:   0.008271
[2025-10-07 09:59:38] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -44.223397 | E_var:     0.2931 | E_err:   0.008459
[2025-10-07 09:59:46] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -44.236344 | E_var:     0.2392 | E_err:   0.007642
[2025-10-07 09:59:54] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -44.249707 | E_var:     0.2569 | E_err:   0.007919
[2025-10-07 10:00:01] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -44.250152 | E_var:     2.2631 | E_err:   0.023505
[2025-10-07 10:00:09] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -44.254811 | E_var:     0.2831 | E_err:   0.008313
[2025-10-07 10:00:17] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -44.236444 | E_var:     0.2177 | E_err:   0.007290
[2025-10-07 10:00:25] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -44.232866 | E_var:     0.2006 | E_err:   0.006997
[2025-10-07 10:00:33] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -44.242185 | E_var:     0.2727 | E_err:   0.008159
[2025-10-07 10:00:41] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -44.254159 | E_var:     0.2481 | E_err:   0.007783
[2025-10-07 10:00:48] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -44.248251 | E_var:     0.3054 | E_err:   0.008635
[2025-10-07 10:00:56] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -44.225659 | E_var:     0.4143 | E_err:   0.010057
[2025-10-07 10:01:04] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -44.241326 | E_var:     0.2530 | E_err:   0.007859
[2025-10-07 10:01:12] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -44.227374 | E_var:     0.2503 | E_err:   0.007817
[2025-10-07 10:01:20] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -44.238411 | E_var:     0.2020 | E_err:   0.007022
[2025-10-07 10:01:27] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -44.249047 | E_var:     0.2779 | E_err:   0.008237
[2025-10-07 10:01:35] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -44.224833 | E_var:     0.2639 | E_err:   0.008027
[2025-10-07 10:01:43] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -44.235225 | E_var:     0.2966 | E_err:   0.008509
[2025-10-07 10:01:43] ======================================================================================================
[2025-10-07 10:01:43] ✅ Training completed successfully
[2025-10-07 10:01:43] Total restarts: 2
[2025-10-07 10:01:46] Final Energy: -44.23522472 ± 0.00850941
[2025-10-07 10:01:46] Final Variance: 0.296592
[2025-10-07 10:01:46] ======================================================================================================
[2025-10-07 10:01:46] ======================================================================================================
[2025-10-07 10:01:46] Training completed | Runtime: 8266.3s
[2025-10-07 10:01:48] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 10:01:48] ======================================================================================================
