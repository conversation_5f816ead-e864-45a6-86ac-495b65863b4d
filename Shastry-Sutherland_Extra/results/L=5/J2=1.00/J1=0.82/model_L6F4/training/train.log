[2025-10-07 14:38:46] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.81/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 14:38:46]   - 迭代次数: final
[2025-10-07 14:38:46]   - 能量: -45.524129+0.000250j ± 0.006370, Var: 0.166228
[2025-10-07 14:38:46]   - 时间戳: 2025-10-07T14:38:17.864985+08:00
[2025-10-07 14:39:07] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 14:39:07] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 14:39:07] ======================================================================================================
[2025-10-07 14:39:07] GCNN for Shastry-Sutherland Model
[2025-10-07 14:39:07] ======================================================================================================
[2025-10-07 14:39:07] System parameters:
[2025-10-07 14:39:07]   - System size: L=5, N=100
[2025-10-07 14:39:07]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-10-07 14:39:07] ------------------------------------------------------------------------------------------------------
[2025-10-07 14:39:07] Model parameters:
[2025-10-07 14:39:07]   - Number of layers = 6
[2025-10-07 14:39:07]   - Number of features = 4
[2025-10-07 14:39:07]   - Total parameters = 32444
[2025-10-07 14:39:07] ------------------------------------------------------------------------------------------------------
[2025-10-07 14:39:07] Training parameters:
[2025-10-07 14:39:07]   - Total iterations: 1050
[2025-10-07 14:39:07]   - Annealing cycles: 3
[2025-10-07 14:39:07]   - Initial period: 150
[2025-10-07 14:39:07]   - Period multiplier: 2.0
[2025-10-07 14:39:07]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 14:39:07]   - Samples: 4096
[2025-10-07 14:39:07]   - Discarded samples: 0
[2025-10-07 14:39:07]   - Chunk size: 4096
[2025-10-07 14:39:07]   - Diagonal shift: 0.15
[2025-10-07 14:39:07]   - Gradient clipping: 1.0
[2025-10-07 14:39:07]   - Checkpoint enabled: interval=100
[2025-10-07 14:39:07]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.82/model_L6F4/training/checkpoints
[2025-10-07 14:39:07] ------------------------------------------------------------------------------------------------------
[2025-10-07 14:39:07] Device status:
[2025-10-07 14:39:07]   - Devices model: NVIDIA H200 NVL
[2025-10-07 14:39:07]   - Number of devices: 1
[2025-10-07 14:39:07]   - Sharding: True
[2025-10-07 14:39:08] ======================================================================================================
[2025-10-07 14:39:49] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -46.139782 | E_var:     0.4606 | E_err:   0.010604
[2025-10-07 14:40:15] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -46.160341 | E_var:     0.3189 | E_err:   0.008824
[2025-10-07 14:40:23] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -46.180014 | E_var:     0.3019 | E_err:   0.008585
[2025-10-07 14:40:31] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -46.174529 | E_var:     0.2288 | E_err:   0.007475
[2025-10-07 14:40:39] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -46.173810 | E_var:     0.2300 | E_err:   0.007493
[2025-10-07 14:40:46] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -46.173924 | E_var:     0.2107 | E_err:   0.007172
[2025-10-07 14:40:54] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -46.166515 | E_var:     0.1843 | E_err:   0.006708
[2025-10-07 14:41:02] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -46.154016 | E_var:     0.3031 | E_err:   0.008602
[2025-10-07 14:41:10] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -46.179594 | E_var:     0.8611 | E_err:   0.014499
[2025-10-07 14:41:18] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -46.165899 | E_var:     0.3077 | E_err:   0.008668
[2025-10-07 14:41:26] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -46.164737 | E_var:     0.2511 | E_err:   0.007830
[2025-10-07 14:41:33] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -46.171459 | E_var:     0.2133 | E_err:   0.007216
[2025-10-07 14:41:41] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -46.163610 | E_var:     0.2515 | E_err:   0.007836
[2025-10-07 14:41:49] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -46.171500 | E_var:     0.2075 | E_err:   0.007117
[2025-10-07 14:41:57] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -46.172425 | E_var:     0.1947 | E_err:   0.006894
[2025-10-07 14:42:05] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -46.150683 | E_var:     0.1791 | E_err:   0.006613
[2025-10-07 14:42:13] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -46.159407 | E_var:     0.1935 | E_err:   0.006874
[2025-10-07 14:42:20] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -46.153569 | E_var:     0.2311 | E_err:   0.007512
[2025-10-07 14:42:28] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -46.155982 | E_var:     0.1798 | E_err:   0.006625
[2025-10-07 14:42:36] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -46.169163 | E_var:     0.2555 | E_err:   0.007897
[2025-10-07 14:42:44] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -46.155688 | E_var:     0.2406 | E_err:   0.007665
[2025-10-07 14:42:52] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -46.150494 | E_var:     0.2097 | E_err:   0.007156
[2025-10-07 14:42:59] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -46.172718 | E_var:     0.1925 | E_err:   0.006855
[2025-10-07 14:43:07] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -46.164405 | E_var:     0.2003 | E_err:   0.006993
[2025-10-07 14:43:15] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -46.170688 | E_var:     0.1910 | E_err:   0.006828
[2025-10-07 14:43:23] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -46.155939 | E_var:     0.1896 | E_err:   0.006804
[2025-10-07 14:43:31] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -46.163331 | E_var:     0.2573 | E_err:   0.007926
[2025-10-07 14:43:39] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -46.168763 | E_var:     0.1884 | E_err:   0.006783
[2025-10-07 14:43:46] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -46.168660 | E_var:     0.2446 | E_err:   0.007728
[2025-10-07 14:43:54] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -46.180326 | E_var:     0.4993 | E_err:   0.011040
[2025-10-07 14:44:02] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -46.165262 | E_var:     0.2335 | E_err:   0.007549
[2025-10-07 14:44:10] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -46.158394 | E_var:     0.2432 | E_err:   0.007706
[2025-10-07 14:44:18] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -46.174051 | E_var:     0.1868 | E_err:   0.006754
[2025-10-07 14:44:25] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -46.157332 | E_var:     0.2079 | E_err:   0.007125
[2025-10-07 14:44:33] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -46.176021 | E_var:     0.2543 | E_err:   0.007880
[2025-10-07 14:44:41] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -46.165752 | E_var:     0.2119 | E_err:   0.007193
[2025-10-07 14:44:49] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -46.158999 | E_var:     0.2170 | E_err:   0.007279
[2025-10-07 14:44:57] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -46.164969 | E_var:     0.2322 | E_err:   0.007529
[2025-10-07 14:45:05] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -46.164283 | E_var:     0.1773 | E_err:   0.006579
[2025-10-07 14:45:12] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -46.162504 | E_var:     0.1824 | E_err:   0.006673
[2025-10-07 14:45:20] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -46.171716 | E_var:     0.1803 | E_err:   0.006634
[2025-10-07 14:45:28] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -46.169274 | E_var:     0.1703 | E_err:   0.006448
[2025-10-07 14:45:36] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -46.175830 | E_var:     0.2354 | E_err:   0.007582
[2025-10-07 14:45:44] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -46.166656 | E_var:     0.2164 | E_err:   0.007269
[2025-10-07 14:45:51] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -46.159855 | E_var:     0.1973 | E_err:   0.006941
[2025-10-07 14:45:59] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -46.167976 | E_var:     0.1720 | E_err:   0.006481
[2025-10-07 14:46:07] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -46.159666 | E_var:     0.1713 | E_err:   0.006467
[2025-10-07 14:46:15] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -46.161857 | E_var:     0.2674 | E_err:   0.008080
[2025-10-07 14:46:23] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -46.184351 | E_var:     0.1964 | E_err:   0.006925
[2025-10-07 14:46:31] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -46.157853 | E_var:     0.2429 | E_err:   0.007701
[2025-10-07 14:46:38] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -46.180409 | E_var:     0.1975 | E_err:   0.006944
[2025-10-07 14:46:46] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -46.173598 | E_var:     0.1828 | E_err:   0.006680
[2025-10-07 14:46:54] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -46.171544 | E_var:     0.1692 | E_err:   0.006427
[2025-10-07 14:47:02] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -46.166403 | E_var:     0.2137 | E_err:   0.007223
[2025-10-07 14:47:10] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -46.175083 | E_var:     0.1770 | E_err:   0.006573
[2025-10-07 14:47:17] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -46.172740 | E_var:     0.1823 | E_err:   0.006672
[2025-10-07 14:47:25] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -46.180679 | E_var:     0.2502 | E_err:   0.007815
[2025-10-07 14:47:33] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -46.162099 | E_var:     0.2464 | E_err:   0.007756
[2025-10-07 14:47:41] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -46.165043 | E_var:     0.1642 | E_err:   0.006331
[2025-10-07 14:47:49] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -46.167165 | E_var:     0.1795 | E_err:   0.006620
[2025-10-07 14:47:57] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -46.167229 | E_var:     0.1840 | E_err:   0.006702
[2025-10-07 14:48:04] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -46.165843 | E_var:     0.1970 | E_err:   0.006934
[2025-10-07 14:48:12] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -46.169223 | E_var:     0.2295 | E_err:   0.007485
[2025-10-07 14:48:20] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -46.169956 | E_var:     0.1544 | E_err:   0.006139
[2025-10-07 14:48:28] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -46.153713 | E_var:     0.2191 | E_err:   0.007314
[2025-10-07 14:48:36] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -46.170264 | E_var:     0.2429 | E_err:   0.007701
[2025-10-07 14:48:43] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -46.171779 | E_var:     0.2306 | E_err:   0.007503
[2025-10-07 14:48:51] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -46.161999 | E_var:     0.1728 | E_err:   0.006496
[2025-10-07 14:48:59] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -46.163012 | E_var:     0.3240 | E_err:   0.008893
[2025-10-07 14:49:07] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -46.168449 | E_var:     0.1614 | E_err:   0.006277
[2025-10-07 14:49:15] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -46.154019 | E_var:     0.6970 | E_err:   0.013045
[2025-10-07 14:49:23] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -46.171126 | E_var:     0.2050 | E_err:   0.007075
[2025-10-07 14:49:30] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -46.153689 | E_var:     0.2385 | E_err:   0.007631
[2025-10-07 14:49:38] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -46.164170 | E_var:     0.1445 | E_err:   0.005940
[2025-10-07 14:49:46] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -46.168572 | E_var:     0.1661 | E_err:   0.006369
[2025-10-07 14:49:54] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -46.170946 | E_var:     0.1946 | E_err:   0.006893
[2025-10-07 14:50:02] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -46.167975 | E_var:     0.2144 | E_err:   0.007236
[2025-10-07 14:50:09] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -46.163654 | E_var:     0.1692 | E_err:   0.006428
[2025-10-07 14:50:17] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -46.163128 | E_var:     0.2067 | E_err:   0.007104
[2025-10-07 14:50:25] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -46.173693 | E_var:     0.1551 | E_err:   0.006153
[2025-10-07 14:50:33] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -46.176563 | E_var:     0.1636 | E_err:   0.006320
[2025-10-07 14:50:41] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -46.167074 | E_var:     0.2179 | E_err:   0.007294
[2025-10-07 14:50:49] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -46.158132 | E_var:     0.6391 | E_err:   0.012491
[2025-10-07 14:50:56] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -46.158911 | E_var:     0.2049 | E_err:   0.007072
[2025-10-07 14:51:04] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -46.165620 | E_var:     0.1744 | E_err:   0.006525
[2025-10-07 14:51:12] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -46.165451 | E_var:     0.1612 | E_err:   0.006274
[2025-10-07 14:51:20] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -46.156938 | E_var:     0.1430 | E_err:   0.005908
[2025-10-07 14:51:28] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -46.177131 | E_var:     0.2519 | E_err:   0.007843
[2025-10-07 14:51:35] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -46.170482 | E_var:     0.1664 | E_err:   0.006374
[2025-10-07 14:51:43] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -46.169245 | E_var:     0.1844 | E_err:   0.006710
[2025-10-07 14:51:51] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -46.177012 | E_var:     0.1902 | E_err:   0.006815
[2025-10-07 14:51:59] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -46.162135 | E_var:     0.1831 | E_err:   0.006685
[2025-10-07 14:52:07] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -46.169408 | E_var:     0.2014 | E_err:   0.007013
[2025-10-07 14:52:15] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -46.163004 | E_var:     0.3614 | E_err:   0.009393
[2025-10-07 14:52:22] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -46.165202 | E_var:     0.2058 | E_err:   0.007088
[2025-10-07 14:52:30] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -46.163619 | E_var:     0.2051 | E_err:   0.007076
[2025-10-07 14:52:38] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -46.170326 | E_var:     0.1937 | E_err:   0.006877
[2025-10-07 14:52:46] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -46.169845 | E_var:     0.1577 | E_err:   0.006205
[2025-10-07 14:52:54] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -46.160099 | E_var:     0.2596 | E_err:   0.007962
[2025-10-07 14:53:01] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -46.170439 | E_var:     0.2410 | E_err:   0.007671
[2025-10-07 14:53:01] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 14:53:09] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -46.158378 | E_var:     0.2342 | E_err:   0.007562
[2025-10-07 14:53:17] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -46.166903 | E_var:     0.2328 | E_err:   0.007539
[2025-10-07 14:53:25] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -46.177346 | E_var:     0.2763 | E_err:   0.008214
[2025-10-07 14:53:33] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -46.151372 | E_var:     0.2264 | E_err:   0.007435
[2025-10-07 14:53:41] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -46.172264 | E_var:     0.1923 | E_err:   0.006853
[2025-10-07 14:53:48] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -46.163760 | E_var:     0.1613 | E_err:   0.006275
[2025-10-07 14:53:56] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -46.161297 | E_var:     0.1902 | E_err:   0.006815
[2025-10-07 14:54:04] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -46.176935 | E_var:     0.2214 | E_err:   0.007352
[2025-10-07 14:54:12] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -46.173170 | E_var:     0.1695 | E_err:   0.006433
[2025-10-07 14:54:20] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -46.162504 | E_var:     0.1879 | E_err:   0.006773
[2025-10-07 14:54:27] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -46.153356 | E_var:     0.1897 | E_err:   0.006806
[2025-10-07 14:54:35] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -46.155474 | E_var:     0.2568 | E_err:   0.007918
[2025-10-07 14:54:43] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -46.173217 | E_var:     0.1872 | E_err:   0.006761
[2025-10-07 14:54:51] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -46.164751 | E_var:     0.1584 | E_err:   0.006218
[2025-10-07 14:54:59] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -46.156599 | E_var:     0.2207 | E_err:   0.007341
[2025-10-07 14:55:07] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -46.179601 | E_var:     0.2078 | E_err:   0.007122
[2025-10-07 14:55:14] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -46.167522 | E_var:     0.2423 | E_err:   0.007691
[2025-10-07 14:55:22] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -46.158758 | E_var:     0.2007 | E_err:   0.007001
[2025-10-07 14:55:30] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -46.162301 | E_var:     0.1870 | E_err:   0.006757
[2025-10-07 14:55:38] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -46.170105 | E_var:     0.2510 | E_err:   0.007828
[2025-10-07 14:55:46] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -46.160516 | E_var:     0.1657 | E_err:   0.006361
[2025-10-07 14:55:53] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -46.173304 | E_var:     0.1899 | E_err:   0.006809
[2025-10-07 14:56:01] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -46.176777 | E_var:     0.2251 | E_err:   0.007413
[2025-10-07 14:56:09] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -46.159447 | E_var:     0.2108 | E_err:   0.007175
[2025-10-07 14:56:17] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -46.172739 | E_var:     0.2038 | E_err:   0.007054
[2025-10-07 14:56:25] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -46.163786 | E_var:     0.2267 | E_err:   0.007440
[2025-10-07 14:56:33] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -46.163299 | E_var:     0.2122 | E_err:   0.007197
[2025-10-07 14:56:40] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -46.174577 | E_var:     0.2230 | E_err:   0.007379
[2025-10-07 14:56:48] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -46.175077 | E_var:     0.1679 | E_err:   0.006403
[2025-10-07 14:56:56] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -46.169887 | E_var:     0.1938 | E_err:   0.006879
[2025-10-07 14:57:04] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -46.151383 | E_var:     0.2075 | E_err:   0.007117
[2025-10-07 14:57:12] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -46.169051 | E_var:     0.1812 | E_err:   0.006651
[2025-10-07 14:57:19] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -46.173337 | E_var:     0.2151 | E_err:   0.007246
[2025-10-07 14:57:27] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -46.170561 | E_var:     0.2177 | E_err:   0.007291
[2025-10-07 14:57:35] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -46.169653 | E_var:     0.1958 | E_err:   0.006913
[2025-10-07 14:57:43] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -46.181651 | E_var:     0.3551 | E_err:   0.009311
[2025-10-07 14:57:51] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -46.162575 | E_var:     0.1533 | E_err:   0.006118
[2025-10-07 14:57:58] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -46.161818 | E_var:     0.2128 | E_err:   0.007208
[2025-10-07 14:58:06] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -46.175969 | E_var:     0.1935 | E_err:   0.006873
[2025-10-07 14:58:14] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -46.167240 | E_var:     0.1640 | E_err:   0.006327
[2025-10-07 14:58:22] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -46.164886 | E_var:     0.1945 | E_err:   0.006891
[2025-10-07 14:58:30] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -46.149922 | E_var:     0.2179 | E_err:   0.007294
[2025-10-07 14:58:38] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -46.153377 | E_var:     0.2418 | E_err:   0.007684
[2025-10-07 14:58:45] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -46.169339 | E_var:     0.2421 | E_err:   0.007688
[2025-10-07 14:58:53] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -46.164016 | E_var:     0.1895 | E_err:   0.006803
[2025-10-07 14:59:01] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -46.173790 | E_var:     0.2008 | E_err:   0.007001
[2025-10-07 14:59:09] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -46.172215 | E_var:     0.1527 | E_err:   0.006105
[2025-10-07 14:59:17] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -46.166660 | E_var:     0.2961 | E_err:   0.008503
[2025-10-07 14:59:24] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -46.162158 | E_var:     0.2360 | E_err:   0.007591
[2025-10-07 14:59:32] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -46.168337 | E_var:     0.2221 | E_err:   0.007363
[2025-10-07 14:59:32] 🔄 RESTART #1 | Period: 300
[2025-10-07 14:59:40] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -46.166026 | E_var:     0.1483 | E_err:   0.006017
[2025-10-07 14:59:48] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -46.179553 | E_var:     0.2980 | E_err:   0.008530
[2025-10-07 14:59:56] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -46.166920 | E_var:     0.1668 | E_err:   0.006381
[2025-10-07 15:00:04] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -46.177100 | E_var:     0.1857 | E_err:   0.006734
[2025-10-07 15:00:11] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -46.171822 | E_var:     0.2027 | E_err:   0.007035
[2025-10-07 15:00:19] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -46.158896 | E_var:     0.1622 | E_err:   0.006293
[2025-10-07 15:00:27] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -46.163567 | E_var:     0.2039 | E_err:   0.007055
[2025-10-07 15:00:35] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -46.169540 | E_var:     0.1629 | E_err:   0.006306
[2025-10-07 15:00:43] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -46.165787 | E_var:     0.2105 | E_err:   0.007170
[2025-10-07 15:00:50] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -46.168929 | E_var:     0.2449 | E_err:   0.007732
[2025-10-07 15:00:58] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -46.158032 | E_var:     0.1906 | E_err:   0.006821
[2025-10-07 15:01:06] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -46.171219 | E_var:     0.1620 | E_err:   0.006288
[2025-10-07 15:01:14] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -46.174309 | E_var:     0.1876 | E_err:   0.006767
[2025-10-07 15:01:22] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -46.175611 | E_var:     0.1768 | E_err:   0.006571
[2025-10-07 15:01:30] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -46.160677 | E_var:     0.1892 | E_err:   0.006797
[2025-10-07 15:01:37] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -46.156608 | E_var:     0.2295 | E_err:   0.007486
[2025-10-07 15:01:45] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -46.161898 | E_var:     0.2976 | E_err:   0.008524
[2025-10-07 15:01:53] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -46.170482 | E_var:     0.1746 | E_err:   0.006529
[2025-10-07 15:02:01] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -46.175542 | E_var:     0.2071 | E_err:   0.007111
[2025-10-07 15:02:09] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -46.153669 | E_var:     0.1652 | E_err:   0.006351
[2025-10-07 15:02:16] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -46.175451 | E_var:     0.2629 | E_err:   0.008011
[2025-10-07 15:02:24] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -46.162544 | E_var:     0.2085 | E_err:   0.007134
[2025-10-07 15:02:32] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -46.164451 | E_var:     0.1579 | E_err:   0.006208
[2025-10-07 15:02:40] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -46.167794 | E_var:     0.1738 | E_err:   0.006513
[2025-10-07 15:02:48] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -46.164704 | E_var:     0.1853 | E_err:   0.006726
[2025-10-07 15:02:56] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -46.166749 | E_var:     0.2151 | E_err:   0.007246
[2025-10-07 15:03:03] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -46.175593 | E_var:     0.2215 | E_err:   0.007354
[2025-10-07 15:03:11] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -46.157104 | E_var:     0.2016 | E_err:   0.007016
[2025-10-07 15:03:19] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -46.183269 | E_var:     0.3842 | E_err:   0.009684
[2025-10-07 15:03:27] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -46.163849 | E_var:     0.2103 | E_err:   0.007165
[2025-10-07 15:03:35] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -46.171221 | E_var:     0.1838 | E_err:   0.006698
[2025-10-07 15:03:42] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -46.166832 | E_var:     0.1775 | E_err:   0.006583
[2025-10-07 15:03:50] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -46.169419 | E_var:     0.1758 | E_err:   0.006551
[2025-10-07 15:03:58] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -46.167814 | E_var:     0.1994 | E_err:   0.006978
[2025-10-07 15:04:06] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -46.165397 | E_var:     0.1812 | E_err:   0.006651
[2025-10-07 15:04:14] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -46.162330 | E_var:     0.3735 | E_err:   0.009549
[2025-10-07 15:04:22] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -46.174164 | E_var:     0.1267 | E_err:   0.005561
[2025-10-07 15:04:29] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -46.182660 | E_var:     0.1889 | E_err:   0.006792
[2025-10-07 15:04:37] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -46.165838 | E_var:     0.1717 | E_err:   0.006474
[2025-10-07 15:04:45] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -46.167432 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 15:04:53] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -46.162730 | E_var:     0.1800 | E_err:   0.006629
[2025-10-07 15:05:01] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -46.157294 | E_var:     0.1773 | E_err:   0.006580
[2025-10-07 15:05:08] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -46.164995 | E_var:     0.2836 | E_err:   0.008321
[2025-10-07 15:05:16] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -46.180305 | E_var:     0.2260 | E_err:   0.007428
[2025-10-07 15:05:24] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -46.163990 | E_var:     0.2163 | E_err:   0.007267
[2025-10-07 15:05:32] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -46.157308 | E_var:     0.1922 | E_err:   0.006849
[2025-10-07 15:05:40] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -46.166430 | E_var:     0.1873 | E_err:   0.006762
[2025-10-07 15:05:47] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -46.162339 | E_var:     0.1529 | E_err:   0.006110
[2025-10-07 15:05:55] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -46.171427 | E_var:     0.2117 | E_err:   0.007188
[2025-10-07 15:06:03] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -46.164362 | E_var:     0.1554 | E_err:   0.006160
[2025-10-07 15:06:03] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 15:06:11] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -46.176649 | E_var:     0.1765 | E_err:   0.006564
[2025-10-07 15:06:19] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -46.172112 | E_var:     0.2080 | E_err:   0.007126
[2025-10-07 15:06:27] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -46.171175 | E_var:     0.2731 | E_err:   0.008165
[2025-10-07 15:06:34] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -46.154369 | E_var:     0.1885 | E_err:   0.006784
[2025-10-07 15:06:42] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -46.171591 | E_var:     0.1807 | E_err:   0.006641
[2025-10-07 15:06:50] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -46.163294 | E_var:     0.2104 | E_err:   0.007167
[2025-10-07 15:06:58] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -46.178592 | E_var:     0.1772 | E_err:   0.006577
[2025-10-07 15:07:06] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -46.161436 | E_var:     0.3007 | E_err:   0.008568
[2025-10-07 15:07:13] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -46.159965 | E_var:     0.1931 | E_err:   0.006865
[2025-10-07 15:07:21] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -46.170531 | E_var:     0.1671 | E_err:   0.006387
[2025-10-07 15:07:29] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -46.170731 | E_var:     0.1583 | E_err:   0.006216
[2025-10-07 15:07:37] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -46.169075 | E_var:     0.2300 | E_err:   0.007494
[2025-10-07 15:07:45] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -46.158693 | E_var:     0.1402 | E_err:   0.005850
[2025-10-07 15:07:53] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -46.162912 | E_var:     0.1912 | E_err:   0.006832
[2025-10-07 15:08:00] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -46.173044 | E_var:     0.1556 | E_err:   0.006164
[2025-10-07 15:08:08] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -46.159210 | E_var:     0.1923 | E_err:   0.006851
[2025-10-07 15:08:16] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -46.162339 | E_var:     0.1840 | E_err:   0.006703
[2025-10-07 15:08:24] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -46.148903 | E_var:     0.2626 | E_err:   0.008007
[2025-10-07 15:08:32] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -46.168103 | E_var:     0.2094 | E_err:   0.007150
[2025-10-07 15:08:39] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -46.151496 | E_var:     0.2276 | E_err:   0.007454
[2025-10-07 15:08:47] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -46.167512 | E_var:     0.1579 | E_err:   0.006209
[2025-10-07 15:08:55] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -46.165008 | E_var:     0.3789 | E_err:   0.009617
[2025-10-07 15:09:03] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -46.154731 | E_var:     0.1949 | E_err:   0.006898
[2025-10-07 15:09:11] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -46.164392 | E_var:     0.1703 | E_err:   0.006447
[2025-10-07 15:09:19] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -46.166960 | E_var:     0.2344 | E_err:   0.007565
[2025-10-07 15:09:26] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -46.164120 | E_var:     0.1977 | E_err:   0.006948
[2025-10-07 15:09:34] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -46.161506 | E_var:     0.1711 | E_err:   0.006463
[2025-10-07 15:09:42] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -46.168409 | E_var:     0.1682 | E_err:   0.006409
[2025-10-07 15:09:50] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -46.159371 | E_var:     0.1930 | E_err:   0.006864
[2025-10-07 15:09:58] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -46.176590 | E_var:     0.2191 | E_err:   0.007314
[2025-10-07 15:10:05] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -46.176449 | E_var:     0.2109 | E_err:   0.007176
[2025-10-07 15:10:13] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -46.182223 | E_var:     0.2044 | E_err:   0.007065
[2025-10-07 15:10:21] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -46.170192 | E_var:     0.2774 | E_err:   0.008229
[2025-10-07 15:10:29] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -46.164246 | E_var:     0.2031 | E_err:   0.007042
[2025-10-07 15:10:37] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -46.166056 | E_var:     0.2056 | E_err:   0.007085
[2025-10-07 15:10:45] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -46.163732 | E_var:     0.1969 | E_err:   0.006933
[2025-10-07 15:10:52] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -46.179521 | E_var:     0.2075 | E_err:   0.007117
[2025-10-07 15:11:00] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -46.168364 | E_var:     0.1553 | E_err:   0.006158
[2025-10-07 15:11:08] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -46.169082 | E_var:     0.1995 | E_err:   0.006979
[2025-10-07 15:11:16] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -46.171422 | E_var:     0.1709 | E_err:   0.006459
[2025-10-07 15:11:24] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -46.171399 | E_var:     0.2212 | E_err:   0.007349
[2025-10-07 15:11:31] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -46.171885 | E_var:     0.1769 | E_err:   0.006571
[2025-10-07 15:11:39] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -46.174310 | E_var:     0.2015 | E_err:   0.007014
[2025-10-07 15:11:47] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -46.172747 | E_var:     0.1553 | E_err:   0.006158
[2025-10-07 15:11:55] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -46.173311 | E_var:     0.2192 | E_err:   0.007315
[2025-10-07 15:12:03] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -46.174187 | E_var:     0.3064 | E_err:   0.008648
[2025-10-07 15:12:11] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -46.174679 | E_var:     0.1860 | E_err:   0.006738
[2025-10-07 15:12:18] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -46.179665 | E_var:     0.2076 | E_err:   0.007118
[2025-10-07 15:12:26] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -46.173787 | E_var:     0.1878 | E_err:   0.006771
[2025-10-07 15:12:34] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -46.160807 | E_var:     0.1692 | E_err:   0.006426
[2025-10-07 15:12:42] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -46.055418 | E_var:     0.6215 | E_err:   0.012318
[2025-10-07 15:12:50] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -46.073542 | E_var:     0.4939 | E_err:   0.010981
[2025-10-07 15:12:57] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -46.109942 | E_var:     0.3659 | E_err:   0.009451
[2025-10-07 15:13:05] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -46.111529 | E_var:     0.3832 | E_err:   0.009673
[2025-10-07 15:13:13] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -46.139591 | E_var:     0.2964 | E_err:   0.008507
[2025-10-07 15:13:21] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -46.144202 | E_var:     0.3819 | E_err:   0.009656
[2025-10-07 15:13:29] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -46.152115 | E_var:     0.2264 | E_err:   0.007435
[2025-10-07 15:13:36] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -46.149996 | E_var:     0.3040 | E_err:   0.008615
[2025-10-07 15:13:44] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -46.152834 | E_var:     0.2419 | E_err:   0.007684
[2025-10-07 15:13:52] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -46.143711 | E_var:     0.2589 | E_err:   0.007950
[2025-10-07 15:14:00] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -46.153885 | E_var:     0.2629 | E_err:   0.008012
[2025-10-07 15:14:08] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -46.156198 | E_var:     0.2499 | E_err:   0.007811
[2025-10-07 15:14:16] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -46.158574 | E_var:     0.2401 | E_err:   0.007657
[2025-10-07 15:14:23] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -46.165134 | E_var:     0.2027 | E_err:   0.007034
[2025-10-07 15:14:31] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -46.158662 | E_var:     0.2026 | E_err:   0.007034
[2025-10-07 15:14:39] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -46.156813 | E_var:     0.2000 | E_err:   0.006987
[2025-10-07 15:14:47] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -46.155980 | E_var:     0.2055 | E_err:   0.007083
[2025-10-07 15:14:55] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -46.153142 | E_var:     0.1628 | E_err:   0.006305
[2025-10-07 15:15:02] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -46.168366 | E_var:     0.2223 | E_err:   0.007367
[2025-10-07 15:15:10] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -46.162291 | E_var:     0.1951 | E_err:   0.006901
[2025-10-07 15:15:18] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -46.166024 | E_var:     0.2355 | E_err:   0.007583
[2025-10-07 15:15:26] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -46.174043 | E_var:     0.2182 | E_err:   0.007299
[2025-10-07 15:15:34] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -46.166986 | E_var:     0.2319 | E_err:   0.007524
[2025-10-07 15:15:42] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -46.159888 | E_var:     0.1977 | E_err:   0.006947
[2025-10-07 15:15:49] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -46.158689 | E_var:     0.1768 | E_err:   0.006569
[2025-10-07 15:15:57] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -46.168731 | E_var:     0.2108 | E_err:   0.007174
[2025-10-07 15:16:05] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -46.174982 | E_var:     0.2550 | E_err:   0.007890
[2025-10-07 15:16:13] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -46.168333 | E_var:     0.2237 | E_err:   0.007390
[2025-10-07 15:16:21] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -46.174375 | E_var:     0.3195 | E_err:   0.008832
[2025-10-07 15:16:28] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -46.156114 | E_var:     0.2464 | E_err:   0.007755
[2025-10-07 15:16:36] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -46.168875 | E_var:     0.2092 | E_err:   0.007146
[2025-10-07 15:16:44] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -46.166662 | E_var:     0.1793 | E_err:   0.006617
[2025-10-07 15:16:52] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -46.166143 | E_var:     0.4077 | E_err:   0.009977
[2025-10-07 15:17:00] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -46.162132 | E_var:     0.2335 | E_err:   0.007551
[2025-10-07 15:17:08] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -46.178352 | E_var:     0.2156 | E_err:   0.007255
[2025-10-07 15:17:15] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -46.159108 | E_var:     0.1802 | E_err:   0.006634
[2025-10-07 15:17:23] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -46.177260 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 15:17:31] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -46.168437 | E_var:     0.1876 | E_err:   0.006767
[2025-10-07 15:17:39] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -46.157235 | E_var:     0.1656 | E_err:   0.006358
[2025-10-07 15:17:47] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -46.175862 | E_var:     0.2259 | E_err:   0.007426
[2025-10-07 15:17:54] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -46.169183 | E_var:     0.2062 | E_err:   0.007095
[2025-10-07 15:18:02] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -46.171378 | E_var:     0.1830 | E_err:   0.006683
[2025-10-07 15:18:10] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -46.164047 | E_var:     0.1706 | E_err:   0.006454
[2025-10-07 15:18:18] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -46.164060 | E_var:     0.1718 | E_err:   0.006476
[2025-10-07 15:18:26] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -46.155009 | E_var:     0.1963 | E_err:   0.006923
[2025-10-07 15:18:33] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -46.160793 | E_var:     0.1881 | E_err:   0.006776
[2025-10-07 15:18:41] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -46.156965 | E_var:     0.1881 | E_err:   0.006776
[2025-10-07 15:18:49] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -46.168406 | E_var:     0.1616 | E_err:   0.006282
[2025-10-07 15:18:57] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -46.165843 | E_var:     0.2069 | E_err:   0.007108
[2025-10-07 15:19:05] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -46.158564 | E_var:     0.2605 | E_err:   0.007975
[2025-10-07 15:19:05] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 15:19:13] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -46.168188 | E_var:     0.1756 | E_err:   0.006548
[2025-10-07 15:19:20] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -46.159090 | E_var:     0.1753 | E_err:   0.006543
[2025-10-07 15:19:28] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -46.171692 | E_var:     0.1813 | E_err:   0.006653
[2025-10-07 15:19:36] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -46.175013 | E_var:     0.2251 | E_err:   0.007413
[2025-10-07 15:19:44] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -46.160390 | E_var:     0.1807 | E_err:   0.006642
[2025-10-07 15:19:52] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -46.158757 | E_var:     0.2085 | E_err:   0.007134
[2025-10-07 15:20:00] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -46.171266 | E_var:     0.1725 | E_err:   0.006490
[2025-10-07 15:20:07] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -46.162850 | E_var:     0.2170 | E_err:   0.007279
[2025-10-07 15:20:15] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -46.157344 | E_var:     0.2774 | E_err:   0.008229
[2025-10-07 15:20:23] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -46.174262 | E_var:     0.1615 | E_err:   0.006280
[2025-10-07 15:20:31] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -46.162855 | E_var:     0.1810 | E_err:   0.006647
[2025-10-07 15:20:39] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -46.174688 | E_var:     0.2144 | E_err:   0.007235
[2025-10-07 15:20:46] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -46.167005 | E_var:     0.1597 | E_err:   0.006243
[2025-10-07 15:20:54] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -46.151270 | E_var:     0.2652 | E_err:   0.008047
[2025-10-07 15:21:02] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -46.162614 | E_var:     0.1715 | E_err:   0.006470
[2025-10-07 15:21:10] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -46.168391 | E_var:     0.1805 | E_err:   0.006638
[2025-10-07 15:21:18] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -46.166142 | E_var:     0.1767 | E_err:   0.006569
[2025-10-07 15:21:26] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -46.156439 | E_var:     0.1948 | E_err:   0.006896
[2025-10-07 15:21:33] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -46.167754 | E_var:     0.1796 | E_err:   0.006621
[2025-10-07 15:21:41] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -46.166778 | E_var:     0.1713 | E_err:   0.006468
[2025-10-07 15:21:49] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -46.159426 | E_var:     0.2307 | E_err:   0.007505
[2025-10-07 15:21:57] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -46.169267 | E_var:     0.2347 | E_err:   0.007569
[2025-10-07 15:22:05] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -46.181680 | E_var:     0.1644 | E_err:   0.006335
[2025-10-07 15:22:12] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -46.179230 | E_var:     0.2059 | E_err:   0.007089
[2025-10-07 15:22:20] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -46.169018 | E_var:     0.1738 | E_err:   0.006513
[2025-10-07 15:22:28] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -46.171060 | E_var:     0.2117 | E_err:   0.007189
[2025-10-07 15:22:36] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -46.163761 | E_var:     0.1817 | E_err:   0.006660
[2025-10-07 15:22:44] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -46.159828 | E_var:     0.1901 | E_err:   0.006813
[2025-10-07 15:22:52] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -46.179962 | E_var:     0.2075 | E_err:   0.007117
[2025-10-07 15:22:59] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -46.158842 | E_var:     0.3232 | E_err:   0.008883
[2025-10-07 15:23:07] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -46.160506 | E_var:     0.1564 | E_err:   0.006179
[2025-10-07 15:23:15] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -46.172953 | E_var:     0.1989 | E_err:   0.006969
[2025-10-07 15:23:23] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -46.150557 | E_var:     0.3321 | E_err:   0.009004
[2025-10-07 15:23:31] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -46.168255 | E_var:     0.2377 | E_err:   0.007617
[2025-10-07 15:23:38] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -46.162715 | E_var:     0.2031 | E_err:   0.007041
[2025-10-07 15:23:46] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -46.173329 | E_var:     0.1816 | E_err:   0.006658
[2025-10-07 15:23:54] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -46.159126 | E_var:     0.2658 | E_err:   0.008056
[2025-10-07 15:24:02] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -46.156005 | E_var:     0.1841 | E_err:   0.006705
[2025-10-07 15:24:10] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -46.162989 | E_var:     0.1882 | E_err:   0.006779
[2025-10-07 15:24:18] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -46.156894 | E_var:     0.1821 | E_err:   0.006667
[2025-10-07 15:24:25] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -46.164120 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 15:24:33] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -46.164496 | E_var:     0.1591 | E_err:   0.006233
[2025-10-07 15:24:41] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -46.166949 | E_var:     0.2031 | E_err:   0.007042
[2025-10-07 15:24:49] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -46.157233 | E_var:     0.2166 | E_err:   0.007272
[2025-10-07 15:24:57] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -46.160319 | E_var:     0.2190 | E_err:   0.007313
[2025-10-07 15:25:04] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -46.156729 | E_var:     0.1969 | E_err:   0.006933
[2025-10-07 15:25:12] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -46.164118 | E_var:     0.1693 | E_err:   0.006430
[2025-10-07 15:25:20] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -46.165196 | E_var:     0.1659 | E_err:   0.006365
[2025-10-07 15:25:28] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -46.179733 | E_var:     0.2870 | E_err:   0.008371
[2025-10-07 15:25:36] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -46.166112 | E_var:     0.1734 | E_err:   0.006507
[2025-10-07 15:25:44] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -46.159006 | E_var:     0.2173 | E_err:   0.007284
[2025-10-07 15:25:51] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -46.174051 | E_var:     0.1887 | E_err:   0.006788
[2025-10-07 15:25:59] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -46.166539 | E_var:     0.1799 | E_err:   0.006627
[2025-10-07 15:26:07] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -46.159194 | E_var:     0.1615 | E_err:   0.006280
[2025-10-07 15:26:15] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -46.157852 | E_var:     0.3084 | E_err:   0.008678
[2025-10-07 15:26:23] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -46.166905 | E_var:     0.2028 | E_err:   0.007037
[2025-10-07 15:26:30] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -46.160008 | E_var:     0.2078 | E_err:   0.007123
[2025-10-07 15:26:38] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -46.164482 | E_var:     0.1767 | E_err:   0.006569
[2025-10-07 15:26:46] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -46.173523 | E_var:     0.2048 | E_err:   0.007070
[2025-10-07 15:26:54] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -46.160833 | E_var:     0.1993 | E_err:   0.006976
[2025-10-07 15:27:02] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -46.163272 | E_var:     0.1914 | E_err:   0.006836
[2025-10-07 15:27:10] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -46.169588 | E_var:     0.2776 | E_err:   0.008232
[2025-10-07 15:27:17] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -46.157313 | E_var:     0.2127 | E_err:   0.007205
[2025-10-07 15:27:25] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -46.163796 | E_var:     0.1855 | E_err:   0.006730
[2025-10-07 15:27:33] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -46.172435 | E_var:     0.1757 | E_err:   0.006549
[2025-10-07 15:27:41] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -46.158962 | E_var:     0.2635 | E_err:   0.008021
[2025-10-07 15:27:49] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -46.160787 | E_var:     0.1994 | E_err:   0.006978
[2025-10-07 15:27:56] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -46.159978 | E_var:     0.1682 | E_err:   0.006408
[2025-10-07 15:28:04] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -46.150761 | E_var:     0.3240 | E_err:   0.008894
[2025-10-07 15:28:12] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -46.173011 | E_var:     0.1737 | E_err:   0.006511
[2025-10-07 15:28:20] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -46.175969 | E_var:     0.1996 | E_err:   0.006980
[2025-10-07 15:28:28] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -46.177866 | E_var:     0.1489 | E_err:   0.006029
[2025-10-07 15:28:35] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -46.164622 | E_var:     0.1769 | E_err:   0.006572
[2025-10-07 15:28:43] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -46.172360 | E_var:     0.2575 | E_err:   0.007928
[2025-10-07 15:28:51] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -46.167777 | E_var:     0.2461 | E_err:   0.007752
[2025-10-07 15:28:59] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -46.166183 | E_var:     0.1890 | E_err:   0.006793
[2025-10-07 15:29:07] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -46.168575 | E_var:     0.1382 | E_err:   0.005809
[2025-10-07 15:29:15] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -46.172181 | E_var:     0.2008 | E_err:   0.007001
[2025-10-07 15:29:22] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -46.160477 | E_var:     0.1952 | E_err:   0.006904
[2025-10-07 15:29:30] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -46.174176 | E_var:     0.2061 | E_err:   0.007093
[2025-10-07 15:29:38] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -46.159252 | E_var:     0.1743 | E_err:   0.006523
[2025-10-07 15:29:46] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -46.161658 | E_var:     0.2104 | E_err:   0.007167
[2025-10-07 15:29:54] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -46.154860 | E_var:     0.2489 | E_err:   0.007796
[2025-10-07 15:30:01] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -46.170623 | E_var:     0.2046 | E_err:   0.007067
[2025-10-07 15:30:09] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -46.169945 | E_var:     0.1905 | E_err:   0.006821
[2025-10-07 15:30:17] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -46.182217 | E_var:     0.2370 | E_err:   0.007607
[2025-10-07 15:30:25] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -46.152423 | E_var:     0.1893 | E_err:   0.006798
[2025-10-07 15:30:33] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -46.156499 | E_var:     0.2368 | E_err:   0.007604
[2025-10-07 15:30:41] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -46.166689 | E_var:     0.1904 | E_err:   0.006818
[2025-10-07 15:30:48] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -46.161738 | E_var:     0.2036 | E_err:   0.007051
[2025-10-07 15:30:56] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -46.166778 | E_var:     0.1898 | E_err:   0.006806
[2025-10-07 15:31:04] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -46.173310 | E_var:     0.1466 | E_err:   0.005983
[2025-10-07 15:31:12] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -46.180311 | E_var:     0.1639 | E_err:   0.006326
[2025-10-07 15:31:20] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -46.166323 | E_var:     0.2237 | E_err:   0.007390
[2025-10-07 15:31:27] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -46.167878 | E_var:     0.1952 | E_err:   0.006903
[2025-10-07 15:31:35] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -46.165473 | E_var:     0.1856 | E_err:   0.006732
[2025-10-07 15:31:43] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -46.167112 | E_var:     0.1789 | E_err:   0.006609
[2025-10-07 15:31:51] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -46.168047 | E_var:     0.2193 | E_err:   0.007317
[2025-10-07 15:31:59] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -46.164999 | E_var:     0.1891 | E_err:   0.006794
[2025-10-07 15:32:06] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -46.154232 | E_var:     0.2159 | E_err:   0.007260
[2025-10-07 15:32:06] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 15:32:14] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -46.168447 | E_var:     0.1848 | E_err:   0.006717
[2025-10-07 15:32:22] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -46.154085 | E_var:     0.1534 | E_err:   0.006120
[2025-10-07 15:32:30] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -46.167283 | E_var:     0.1982 | E_err:   0.006957
[2025-10-07 15:32:38] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -46.178490 | E_var:     0.1755 | E_err:   0.006547
[2025-10-07 15:32:46] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -46.171891 | E_var:     0.1970 | E_err:   0.006936
[2025-10-07 15:32:53] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -46.154998 | E_var:     0.1542 | E_err:   0.006135
[2025-10-07 15:33:01] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -46.188703 | E_var:     0.1949 | E_err:   0.006899
[2025-10-07 15:33:09] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -46.171534 | E_var:     0.2117 | E_err:   0.007190
[2025-10-07 15:33:17] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -46.173033 | E_var:     0.2086 | E_err:   0.007137
[2025-10-07 15:33:25] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -46.169267 | E_var:     0.2158 | E_err:   0.007259
[2025-10-07 15:33:33] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -46.165537 | E_var:     0.1629 | E_err:   0.006307
[2025-10-07 15:33:40] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -46.178464 | E_var:     0.2013 | E_err:   0.007011
[2025-10-07 15:33:48] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -46.169604 | E_var:     0.1810 | E_err:   0.006647
[2025-10-07 15:33:56] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -46.155511 | E_var:     0.2565 | E_err:   0.007913
[2025-10-07 15:34:04] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -46.163412 | E_var:     0.2440 | E_err:   0.007717
[2025-10-07 15:34:12] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -46.172940 | E_var:     0.2431 | E_err:   0.007703
[2025-10-07 15:34:19] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -46.165304 | E_var:     0.1552 | E_err:   0.006155
[2025-10-07 15:34:27] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -46.170348 | E_var:     0.1928 | E_err:   0.006861
[2025-10-07 15:34:35] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -46.160919 | E_var:     0.2304 | E_err:   0.007501
[2025-10-07 15:34:43] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -46.164937 | E_var:     0.2439 | E_err:   0.007716
[2025-10-07 15:34:51] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -46.168340 | E_var:     0.1900 | E_err:   0.006811
[2025-10-07 15:34:58] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -46.165474 | E_var:     0.1720 | E_err:   0.006480
[2025-10-07 15:35:06] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -46.178696 | E_var:     0.2416 | E_err:   0.007679
[2025-10-07 15:35:14] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -46.167023 | E_var:     0.1551 | E_err:   0.006154
[2025-10-07 15:35:22] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -46.165058 | E_var:     0.2390 | E_err:   0.007638
[2025-10-07 15:35:30] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -46.163016 | E_var:     0.2326 | E_err:   0.007536
[2025-10-07 15:35:38] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -46.165687 | E_var:     0.2271 | E_err:   0.007446
[2025-10-07 15:35:45] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -46.171403 | E_var:     0.1759 | E_err:   0.006552
[2025-10-07 15:35:53] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -46.169260 | E_var:     0.2038 | E_err:   0.007053
[2025-10-07 15:36:01] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -46.168780 | E_var:     0.1629 | E_err:   0.006306
[2025-10-07 15:36:09] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -46.151447 | E_var:     0.3231 | E_err:   0.008881
[2025-10-07 15:36:17] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -46.164993 | E_var:     0.2232 | E_err:   0.007381
[2025-10-07 15:36:24] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -46.170323 | E_var:     0.2108 | E_err:   0.007175
[2025-10-07 15:36:32] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -46.162904 | E_var:     0.1921 | E_err:   0.006847
[2025-10-07 15:36:40] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -46.173645 | E_var:     0.2193 | E_err:   0.007318
[2025-10-07 15:36:48] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -46.167512 | E_var:     0.1839 | E_err:   0.006701
[2025-10-07 15:36:56] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -46.166252 | E_var:     0.2724 | E_err:   0.008155
[2025-10-07 15:37:03] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -46.171544 | E_var:     0.1777 | E_err:   0.006587
[2025-10-07 15:37:11] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -46.171385 | E_var:     0.1955 | E_err:   0.006909
[2025-10-07 15:37:19] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -46.170637 | E_var:     0.1534 | E_err:   0.006120
[2025-10-07 15:37:27] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -46.162851 | E_var:     0.1693 | E_err:   0.006430
[2025-10-07 15:37:35] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -46.168816 | E_var:     0.1752 | E_err:   0.006539
[2025-10-07 15:37:43] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -46.165439 | E_var:     0.1878 | E_err:   0.006771
[2025-10-07 15:37:50] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -46.173916 | E_var:     0.1825 | E_err:   0.006675
[2025-10-07 15:37:58] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -46.161911 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 15:38:06] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -46.158869 | E_var:     0.1701 | E_err:   0.006445
[2025-10-07 15:38:14] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -46.168207 | E_var:     0.2570 | E_err:   0.007921
[2025-10-07 15:38:22] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -46.154073 | E_var:     0.1799 | E_err:   0.006627
[2025-10-07 15:38:30] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -46.169784 | E_var:     0.1634 | E_err:   0.006316
[2025-10-07 15:38:37] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -46.168954 | E_var:     0.1953 | E_err:   0.006905
[2025-10-07 15:38:37] 🔄 RESTART #2 | Period: 600
[2025-10-07 15:38:45] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -46.168015 | E_var:     0.1802 | E_err:   0.006633
[2025-10-07 15:38:53] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -46.164712 | E_var:     0.2668 | E_err:   0.008070
[2025-10-07 15:39:01] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -46.170418 | E_var:     0.2098 | E_err:   0.007157
[2025-10-07 15:39:09] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -46.168080 | E_var:     0.2056 | E_err:   0.007086
[2025-10-07 15:39:16] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -46.180691 | E_var:     0.1742 | E_err:   0.006522
[2025-10-07 15:39:24] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -46.172063 | E_var:     0.1750 | E_err:   0.006537
[2025-10-07 15:39:32] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -46.168258 | E_var:     0.1622 | E_err:   0.006293
[2025-10-07 15:39:40] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -46.153209 | E_var:     0.2327 | E_err:   0.007538
[2025-10-07 15:39:48] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -46.170403 | E_var:     0.2308 | E_err:   0.007506
[2025-10-07 15:39:56] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -46.169709 | E_var:     0.2183 | E_err:   0.007301
[2025-10-07 15:40:03] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -46.176176 | E_var:     0.1963 | E_err:   0.006922
[2025-10-07 15:40:11] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -46.153435 | E_var:     0.1932 | E_err:   0.006868
[2025-10-07 15:40:19] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -46.161251 | E_var:     0.2584 | E_err:   0.007942
[2025-10-07 15:40:27] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -46.168197 | E_var:     0.2472 | E_err:   0.007768
[2025-10-07 15:40:35] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -46.170529 | E_var:     0.1818 | E_err:   0.006662
[2025-10-07 15:40:42] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -46.160614 | E_var:     0.2651 | E_err:   0.008045
[2025-10-07 15:40:50] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -46.164735 | E_var:     0.1605 | E_err:   0.006261
[2025-10-07 15:40:58] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -46.170149 | E_var:     0.1626 | E_err:   0.006300
[2025-10-07 15:41:06] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -46.168154 | E_var:     0.1736 | E_err:   0.006511
[2025-10-07 15:41:14] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -46.168565 | E_var:     0.3698 | E_err:   0.009502
[2025-10-07 15:41:22] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -46.163167 | E_var:     0.2544 | E_err:   0.007881
[2025-10-07 15:41:29] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -46.175297 | E_var:     0.1760 | E_err:   0.006555
[2025-10-07 15:41:37] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -46.168025 | E_var:     0.1649 | E_err:   0.006345
[2025-10-07 15:41:45] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -46.163265 | E_var:     0.1701 | E_err:   0.006444
[2025-10-07 15:41:53] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -46.168216 | E_var:     0.1793 | E_err:   0.006616
[2025-10-07 15:42:01] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -46.170107 | E_var:     0.1892 | E_err:   0.006797
[2025-10-07 15:42:08] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -46.164028 | E_var:     0.1740 | E_err:   0.006517
[2025-10-07 15:42:16] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -46.163888 | E_var:     0.2102 | E_err:   0.007164
[2025-10-07 15:42:24] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -46.161635 | E_var:     0.2483 | E_err:   0.007786
[2025-10-07 15:42:32] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -46.167110 | E_var:     0.2197 | E_err:   0.007324
[2025-10-07 15:42:40] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -46.173082 | E_var:     0.2008 | E_err:   0.007001
[2025-10-07 15:42:48] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -46.159846 | E_var:     0.1716 | E_err:   0.006473
[2025-10-07 15:42:55] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -46.171184 | E_var:     0.1606 | E_err:   0.006261
[2025-10-07 15:43:03] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -46.171656 | E_var:     0.1774 | E_err:   0.006580
[2025-10-07 15:43:11] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -46.167207 | E_var:     0.2470 | E_err:   0.007766
[2025-10-07 15:43:19] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -46.179797 | E_var:     0.1966 | E_err:   0.006927
[2025-10-07 15:43:27] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -46.170855 | E_var:     0.2124 | E_err:   0.007200
[2025-10-07 15:43:34] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -46.173030 | E_var:     0.2739 | E_err:   0.008178
[2025-10-07 15:43:42] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -46.160397 | E_var:     0.1888 | E_err:   0.006790
[2025-10-07 15:43:50] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -46.164670 | E_var:     0.1631 | E_err:   0.006309
[2025-10-07 15:43:58] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -46.167420 | E_var:     0.2339 | E_err:   0.007557
[2025-10-07 15:44:06] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -46.168890 | E_var:     0.1670 | E_err:   0.006384
[2025-10-07 15:44:14] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -46.166287 | E_var:     0.2148 | E_err:   0.007241
[2025-10-07 15:44:21] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -46.182175 | E_var:     0.1599 | E_err:   0.006248
[2025-10-07 15:44:29] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -46.177211 | E_var:     0.1691 | E_err:   0.006426
[2025-10-07 15:44:37] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -46.160957 | E_var:     0.1570 | E_err:   0.006192
[2025-10-07 15:44:45] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -46.157393 | E_var:     0.1597 | E_err:   0.006245
[2025-10-07 15:44:53] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -46.178593 | E_var:     0.2425 | E_err:   0.007695
[2025-10-07 15:45:00] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -46.173321 | E_var:     0.1788 | E_err:   0.006607
[2025-10-07 15:45:08] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -46.162002 | E_var:     0.2599 | E_err:   0.007966
[2025-10-07 15:45:08] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 15:45:16] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -46.162677 | E_var:     0.2409 | E_err:   0.007670
[2025-10-07 15:45:24] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -46.157486 | E_var:     0.2427 | E_err:   0.007697
[2025-10-07 15:45:32] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -46.171456 | E_var:     0.2104 | E_err:   0.007167
[2025-10-07 15:45:40] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -46.175837 | E_var:     0.2017 | E_err:   0.007018
[2025-10-07 15:45:47] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -46.163177 | E_var:     0.2097 | E_err:   0.007155
[2025-10-07 15:45:55] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -46.172943 | E_var:     0.1899 | E_err:   0.006810
[2025-10-07 15:46:03] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -46.157582 | E_var:     0.1857 | E_err:   0.006733
[2025-10-07 15:46:11] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -46.171507 | E_var:     0.1953 | E_err:   0.006904
[2025-10-07 15:46:19] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -46.169834 | E_var:     0.1811 | E_err:   0.006649
[2025-10-07 15:46:27] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -46.179681 | E_var:     0.6208 | E_err:   0.012311
[2025-10-07 15:46:34] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -46.169481 | E_var:     0.1884 | E_err:   0.006783
[2025-10-07 15:46:42] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -46.167780 | E_var:     0.2037 | E_err:   0.007053
[2025-10-07 15:46:50] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -46.169875 | E_var:     0.1832 | E_err:   0.006688
[2025-10-07 15:46:58] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -46.162686 | E_var:     0.2166 | E_err:   0.007272
[2025-10-07 15:47:06] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -46.178814 | E_var:     0.2367 | E_err:   0.007601
[2025-10-07 15:47:13] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -46.165387 | E_var:     0.1701 | E_err:   0.006445
[2025-10-07 15:47:21] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -46.165433 | E_var:     0.1494 | E_err:   0.006039
[2025-10-07 15:47:29] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -46.164817 | E_var:     0.1782 | E_err:   0.006597
[2025-10-07 15:47:37] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -46.161452 | E_var:     0.2463 | E_err:   0.007754
[2025-10-07 15:47:45] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -46.152899 | E_var:     0.1519 | E_err:   0.006089
[2025-10-07 15:47:53] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -46.165905 | E_var:     0.2331 | E_err:   0.007544
[2025-10-07 15:48:00] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -46.166875 | E_var:     0.2092 | E_err:   0.007146
[2025-10-07 15:48:08] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -46.162710 | E_var:     0.2842 | E_err:   0.008329
[2025-10-07 15:48:16] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -46.165307 | E_var:     0.2481 | E_err:   0.007783
[2025-10-07 15:48:24] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -46.163422 | E_var:     0.1885 | E_err:   0.006784
[2025-10-07 15:48:32] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -46.163920 | E_var:     0.2449 | E_err:   0.007733
[2025-10-07 15:48:39] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -46.168375 | E_var:     0.2306 | E_err:   0.007504
[2025-10-07 15:48:47] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -46.173456 | E_var:     0.6426 | E_err:   0.012525
[2025-10-07 15:48:55] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -46.160868 | E_var:     0.1804 | E_err:   0.006637
[2025-10-07 15:49:03] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -46.158668 | E_var:     0.1725 | E_err:   0.006489
[2025-10-07 15:49:11] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -46.166616 | E_var:     0.2104 | E_err:   0.007167
[2025-10-07 15:49:19] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -46.164578 | E_var:     0.1594 | E_err:   0.006238
[2025-10-07 15:49:26] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -46.155003 | E_var:     0.3437 | E_err:   0.009161
[2025-10-07 15:49:34] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -46.169673 | E_var:     0.1619 | E_err:   0.006287
[2025-10-07 15:49:42] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -46.169165 | E_var:     0.1812 | E_err:   0.006651
[2025-10-07 15:49:50] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -46.166636 | E_var:     0.1832 | E_err:   0.006688
[2025-10-07 15:49:58] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -46.177776 | E_var:     0.1767 | E_err:   0.006568
[2025-10-07 15:50:05] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -46.180867 | E_var:     0.2416 | E_err:   0.007681
[2025-10-07 15:50:13] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -46.168057 | E_var:     0.1801 | E_err:   0.006631
[2025-10-07 15:50:21] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -46.161880 | E_var:     0.1634 | E_err:   0.006315
[2025-10-07 15:50:29] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -46.152240 | E_var:     0.3130 | E_err:   0.008741
[2025-10-07 15:50:37] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -46.157312 | E_var:     0.1979 | E_err:   0.006952
[2025-10-07 15:50:45] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -46.170521 | E_var:     0.1811 | E_err:   0.006648
[2025-10-07 15:50:52] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -46.174445 | E_var:     0.1916 | E_err:   0.006839
[2025-10-07 15:51:00] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -46.168774 | E_var:     0.1623 | E_err:   0.006296
[2025-10-07 15:51:08] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -46.171656 | E_var:     0.1775 | E_err:   0.006583
[2025-10-07 15:51:16] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -46.169738 | E_var:     0.1892 | E_err:   0.006796
[2025-10-07 15:51:24] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -46.169503 | E_var:     0.2134 | E_err:   0.007218
[2025-10-07 15:51:31] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -46.165162 | E_var:     0.2791 | E_err:   0.008254
[2025-10-07 15:51:39] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -46.150918 | E_var:     0.1886 | E_err:   0.006785
[2025-10-07 15:51:47] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -46.167498 | E_var:     0.1837 | E_err:   0.006697
[2025-10-07 15:51:55] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -46.160863 | E_var:     0.2025 | E_err:   0.007031
[2025-10-07 15:52:03] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -46.178735 | E_var:     0.1869 | E_err:   0.006756
[2025-10-07 15:52:10] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -46.166754 | E_var:     0.1798 | E_err:   0.006625
[2025-10-07 15:52:18] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -46.165970 | E_var:     0.1809 | E_err:   0.006646
[2025-10-07 15:52:26] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -46.168563 | E_var:     0.1626 | E_err:   0.006300
[2025-10-07 15:52:34] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -46.175089 | E_var:     0.2553 | E_err:   0.007894
[2025-10-07 15:52:42] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -46.159372 | E_var:     0.1743 | E_err:   0.006523
[2025-10-07 15:52:50] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -46.164601 | E_var:     0.1610 | E_err:   0.006269
[2025-10-07 15:52:57] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -46.171533 | E_var:     0.1586 | E_err:   0.006222
[2025-10-07 15:53:05] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -46.173347 | E_var:     0.1525 | E_err:   0.006102
[2025-10-07 15:53:13] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -46.178580 | E_var:     0.2485 | E_err:   0.007790
[2025-10-07 15:53:21] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -46.167790 | E_var:     0.2416 | E_err:   0.007680
[2025-10-07 15:53:29] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -46.172129 | E_var:     0.4436 | E_err:   0.010406
[2025-10-07 15:53:36] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -46.166580 | E_var:     0.2763 | E_err:   0.008213
[2025-10-07 15:53:44] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -46.171450 | E_var:     0.3912 | E_err:   0.009773
[2025-10-07 15:53:52] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -46.174475 | E_var:     0.1903 | E_err:   0.006816
[2025-10-07 15:54:00] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -46.174016 | E_var:     0.2594 | E_err:   0.007959
[2025-10-07 15:54:08] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -46.170252 | E_var:     0.1839 | E_err:   0.006701
[2025-10-07 15:54:16] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -46.167791 | E_var:     0.1596 | E_err:   0.006241
[2025-10-07 15:54:23] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -46.174893 | E_var:     0.1562 | E_err:   0.006175
[2025-10-07 15:54:31] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -46.158370 | E_var:     0.2222 | E_err:   0.007365
[2025-10-07 15:54:39] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -46.158405 | E_var:     0.1788 | E_err:   0.006606
[2025-10-07 15:54:47] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -46.153303 | E_var:     0.1908 | E_err:   0.006826
[2025-10-07 15:54:55] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -46.164243 | E_var:     0.2158 | E_err:   0.007259
[2025-10-07 15:55:02] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -46.160047 | E_var:     0.1862 | E_err:   0.006742
[2025-10-07 15:55:10] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -46.160533 | E_var:     0.1903 | E_err:   0.006817
[2025-10-07 15:55:18] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -46.153045 | E_var:     0.1630 | E_err:   0.006308
[2025-10-07 15:55:26] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -46.160714 | E_var:     0.1911 | E_err:   0.006830
[2025-10-07 15:55:34] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -46.159209 | E_var:     0.2301 | E_err:   0.007496
[2025-10-07 15:55:42] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -46.164829 | E_var:     0.1691 | E_err:   0.006425
[2025-10-07 15:55:49] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -46.164772 | E_var:     0.3352 | E_err:   0.009046
[2025-10-07 15:55:57] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -46.174469 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 15:56:05] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -46.168759 | E_var:     0.1803 | E_err:   0.006634
[2025-10-07 15:56:13] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -46.167230 | E_var:     0.2426 | E_err:   0.007697
[2025-10-07 15:56:21] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -46.152448 | E_var:     0.1889 | E_err:   0.006791
[2025-10-07 15:56:28] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -46.157230 | E_var:     0.2166 | E_err:   0.007272
[2025-10-07 15:56:36] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -46.151171 | E_var:     0.2045 | E_err:   0.007065
[2025-10-07 15:56:44] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -46.166129 | E_var:     0.2102 | E_err:   0.007163
[2025-10-07 15:56:52] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -46.162195 | E_var:     0.2098 | E_err:   0.007156
[2025-10-07 15:57:00] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -46.176223 | E_var:     0.2094 | E_err:   0.007150
[2025-10-07 15:57:08] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -46.176536 | E_var:     0.2040 | E_err:   0.007058
[2025-10-07 15:57:15] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -46.159224 | E_var:     0.2399 | E_err:   0.007652
[2025-10-07 15:57:23] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -46.168677 | E_var:     0.2245 | E_err:   0.007403
[2025-10-07 15:57:31] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -46.171752 | E_var:     0.1874 | E_err:   0.006765
[2025-10-07 15:57:39] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -46.169092 | E_var:     0.2087 | E_err:   0.007139
[2025-10-07 15:57:47] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -46.156114 | E_var:     0.1636 | E_err:   0.006319
[2025-10-07 15:57:54] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -46.167429 | E_var:     0.1923 | E_err:   0.006853
[2025-10-07 15:58:02] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -46.176438 | E_var:     0.1760 | E_err:   0.006554
[2025-10-07 15:58:10] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -46.173952 | E_var:     0.2226 | E_err:   0.007372
[2025-10-07 15:58:10] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 15:58:18] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -46.173250 | E_var:     0.1803 | E_err:   0.006635
[2025-10-07 15:58:26] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -46.167191 | E_var:     0.1711 | E_err:   0.006464
[2025-10-07 15:58:34] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -46.169443 | E_var:     0.1535 | E_err:   0.006123
[2025-10-07 15:58:41] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -46.166359 | E_var:     0.2014 | E_err:   0.007013
[2025-10-07 15:58:49] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -46.170191 | E_var:     0.1982 | E_err:   0.006956
[2025-10-07 15:58:57] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -46.165588 | E_var:     0.2287 | E_err:   0.007473
[2025-10-07 15:59:05] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -46.169740 | E_var:     0.1496 | E_err:   0.006043
[2025-10-07 15:59:13] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -46.166683 | E_var:     0.2054 | E_err:   0.007082
[2025-10-07 15:59:20] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -46.167538 | E_var:     0.1910 | E_err:   0.006830
[2025-10-07 15:59:28] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -46.169328 | E_var:     0.2548 | E_err:   0.007887
[2025-10-07 15:59:36] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -46.163221 | E_var:     0.1980 | E_err:   0.006954
[2025-10-07 15:59:44] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -46.173734 | E_var:     0.1741 | E_err:   0.006520
[2025-10-07 15:59:52] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -46.160898 | E_var:     0.2034 | E_err:   0.007047
[2025-10-07 16:00:00] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -46.157538 | E_var:     0.3072 | E_err:   0.008660
[2025-10-07 16:00:07] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -46.175840 | E_var:     0.1662 | E_err:   0.006370
[2025-10-07 16:00:15] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -46.169462 | E_var:     0.3854 | E_err:   0.009700
[2025-10-07 16:00:23] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -46.169815 | E_var:     0.2271 | E_err:   0.007446
[2025-10-07 16:00:31] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -46.160544 | E_var:     0.2128 | E_err:   0.007208
[2025-10-07 16:00:39] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -46.163838 | E_var:     0.1742 | E_err:   0.006521
[2025-10-07 16:00:46] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -46.163462 | E_var:     0.1971 | E_err:   0.006937
[2025-10-07 16:00:54] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -46.165290 | E_var:     0.1832 | E_err:   0.006687
[2025-10-07 16:01:02] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -46.167034 | E_var:     0.2375 | E_err:   0.007615
[2025-10-07 16:01:10] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -46.170964 | E_var:     0.2198 | E_err:   0.007326
[2025-10-07 16:01:18] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -46.169865 | E_var:     0.1436 | E_err:   0.005922
[2025-10-07 16:01:26] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -46.165119 | E_var:     0.1662 | E_err:   0.006370
[2025-10-07 16:01:33] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -46.171029 | E_var:     0.2959 | E_err:   0.008500
[2025-10-07 16:01:41] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -46.148402 | E_var:     0.4481 | E_err:   0.010460
[2025-10-07 16:01:49] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -46.164964 | E_var:     0.1769 | E_err:   0.006572
[2025-10-07 16:01:57] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -46.163540 | E_var:     0.1923 | E_err:   0.006853
[2025-10-07 16:02:05] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -46.185834 | E_var:     0.2144 | E_err:   0.007235
[2025-10-07 16:02:12] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -46.169654 | E_var:     0.1854 | E_err:   0.006728
[2025-10-07 16:02:20] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -46.168036 | E_var:     0.2196 | E_err:   0.007323
[2025-10-07 16:02:28] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -46.159889 | E_var:     0.2323 | E_err:   0.007530
[2025-10-07 16:02:36] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -46.164852 | E_var:     0.3344 | E_err:   0.009035
[2025-10-07 16:02:44] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -46.171438 | E_var:     0.1550 | E_err:   0.006151
[2025-10-07 16:02:52] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -46.157379 | E_var:     0.2570 | E_err:   0.007921
[2025-10-07 16:02:59] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -46.171136 | E_var:     0.2232 | E_err:   0.007381
[2025-10-07 16:03:07] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -46.164363 | E_var:     0.1798 | E_err:   0.006626
[2025-10-07 16:03:15] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -46.161326 | E_var:     0.1761 | E_err:   0.006556
[2025-10-07 16:03:23] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -46.168511 | E_var:     0.1939 | E_err:   0.006880
[2025-10-07 16:03:31] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -46.166915 | E_var:     0.2075 | E_err:   0.007117
[2025-10-07 16:03:38] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -46.169509 | E_var:     0.2348 | E_err:   0.007572
[2025-10-07 16:03:46] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -46.161534 | E_var:     0.1560 | E_err:   0.006172
[2025-10-07 16:03:54] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -46.164564 | E_var:     0.1548 | E_err:   0.006148
[2025-10-07 16:04:02] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -46.165362 | E_var:     0.2161 | E_err:   0.007263
[2025-10-07 16:04:10] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -46.164200 | E_var:     0.1687 | E_err:   0.006417
[2025-10-07 16:04:18] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -46.169962 | E_var:     0.1707 | E_err:   0.006456
[2025-10-07 16:04:25] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -46.153812 | E_var:     0.1848 | E_err:   0.006716
[2025-10-07 16:04:33] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -46.170254 | E_var:     0.1678 | E_err:   0.006400
[2025-10-07 16:04:41] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -46.168796 | E_var:     0.2009 | E_err:   0.007003
[2025-10-07 16:04:49] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -46.169558 | E_var:     0.1945 | E_err:   0.006891
[2025-10-07 16:04:57] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -46.162721 | E_var:     0.1966 | E_err:   0.006927
[2025-10-07 16:05:04] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -46.164452 | E_var:     0.1906 | E_err:   0.006822
[2025-10-07 16:05:12] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -46.160743 | E_var:     0.1588 | E_err:   0.006226
[2025-10-07 16:05:20] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -46.170707 | E_var:     0.3052 | E_err:   0.008633
[2025-10-07 16:05:28] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -46.160631 | E_var:     0.2076 | E_err:   0.007118
[2025-10-07 16:05:36] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -46.167788 | E_var:     0.1771 | E_err:   0.006575
[2025-10-07 16:05:43] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -46.170924 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 16:05:51] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -46.170032 | E_var:     0.1957 | E_err:   0.006912
[2025-10-07 16:05:59] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -46.161851 | E_var:     0.1666 | E_err:   0.006377
[2025-10-07 16:06:07] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -46.161091 | E_var:     0.2281 | E_err:   0.007463
[2025-10-07 16:06:15] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -46.173930 | E_var:     0.1480 | E_err:   0.006010
[2025-10-07 16:06:23] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -46.152143 | E_var:     0.1728 | E_err:   0.006495
[2025-10-07 16:06:30] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -46.168165 | E_var:     0.1581 | E_err:   0.006212
[2025-10-07 16:06:38] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -46.178958 | E_var:     0.2296 | E_err:   0.007487
[2025-10-07 16:06:46] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -46.166373 | E_var:     0.1743 | E_err:   0.006523
[2025-10-07 16:06:54] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -46.160770 | E_var:     0.2448 | E_err:   0.007730
[2025-10-07 16:07:02] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -46.167689 | E_var:     0.1679 | E_err:   0.006402
[2025-10-07 16:07:09] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -46.186076 | E_var:     0.1667 | E_err:   0.006380
[2025-10-07 16:07:17] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -46.173194 | E_var:     0.1918 | E_err:   0.006843
[2025-10-07 16:07:25] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -46.177139 | E_var:     0.1832 | E_err:   0.006687
[2025-10-07 16:07:33] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -46.149172 | E_var:     0.2214 | E_err:   0.007352
[2025-10-07 16:07:41] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -46.168955 | E_var:     0.1788 | E_err:   0.006607
[2025-10-07 16:07:49] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -46.162718 | E_var:     0.1937 | E_err:   0.006877
[2025-10-07 16:07:56] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -46.158562 | E_var:     0.2453 | E_err:   0.007738
[2025-10-07 16:08:04] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -46.170078 | E_var:     0.2076 | E_err:   0.007119
[2025-10-07 16:08:12] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -46.163572 | E_var:     0.1775 | E_err:   0.006583
[2025-10-07 16:08:20] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -46.166823 | E_var:     0.2368 | E_err:   0.007603
[2025-10-07 16:08:28] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -46.167634 | E_var:     0.1799 | E_err:   0.006628
[2025-10-07 16:08:35] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -46.168008 | E_var:     0.1897 | E_err:   0.006806
[2025-10-07 16:08:43] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -46.166354 | E_var:     0.1675 | E_err:   0.006395
[2025-10-07 16:08:51] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -46.159613 | E_var:     0.1623 | E_err:   0.006294
[2025-10-07 16:08:59] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -46.168776 | E_var:     0.1831 | E_err:   0.006687
[2025-10-07 16:09:07] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -46.168904 | E_var:     0.2074 | E_err:   0.007115
[2025-10-07 16:09:15] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -46.165583 | E_var:     0.1700 | E_err:   0.006442
[2025-10-07 16:09:22] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -46.177680 | E_var:     0.2636 | E_err:   0.008023
[2025-10-07 16:09:30] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -46.166725 | E_var:     0.1930 | E_err:   0.006864
[2025-10-07 16:09:38] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -46.164718 | E_var:     0.2101 | E_err:   0.007162
[2025-10-07 16:09:46] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -46.169367 | E_var:     0.2409 | E_err:   0.007669
[2025-10-07 16:09:54] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -46.160157 | E_var:     0.1666 | E_err:   0.006377
[2025-10-07 16:10:01] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -46.160825 | E_var:     0.2312 | E_err:   0.007513
[2025-10-07 16:10:09] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -46.166230 | E_var:     0.1608 | E_err:   0.006266
[2025-10-07 16:10:17] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -46.172573 | E_var:     0.1816 | E_err:   0.006658
[2025-10-07 16:10:25] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -46.175750 | E_var:     0.1861 | E_err:   0.006741
[2025-10-07 16:10:33] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -46.173085 | E_var:     0.2368 | E_err:   0.007603
[2025-10-07 16:10:40] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -46.165444 | E_var:     0.1691 | E_err:   0.006426
[2025-10-07 16:10:48] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -46.171893 | E_var:     0.1496 | E_err:   0.006044
[2025-10-07 16:10:56] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -46.160230 | E_var:     0.1928 | E_err:   0.006862
[2025-10-07 16:11:04] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -46.161164 | E_var:     0.1829 | E_err:   0.006681
[2025-10-07 16:11:12] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -46.168114 | E_var:     0.1916 | E_err:   0.006840
[2025-10-07 16:11:12] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 16:11:20] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -46.165497 | E_var:     0.2861 | E_err:   0.008358
[2025-10-07 16:11:28] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -46.156757 | E_var:     0.1742 | E_err:   0.006521
[2025-10-07 16:11:36] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -46.177107 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 16:11:43] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -46.160072 | E_var:     0.1831 | E_err:   0.006686
[2025-10-07 16:11:51] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -46.163040 | E_var:     0.2014 | E_err:   0.007012
[2025-10-07 16:11:59] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -46.174265 | E_var:     0.1810 | E_err:   0.006647
[2025-10-07 16:12:07] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -46.150761 | E_var:     0.2936 | E_err:   0.008466
[2025-10-07 16:12:15] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -46.170254 | E_var:     0.2002 | E_err:   0.006990
[2025-10-07 16:12:22] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -46.171967 | E_var:     0.1893 | E_err:   0.006799
[2025-10-07 16:12:30] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -46.160459 | E_var:     0.1873 | E_err:   0.006762
[2025-10-07 16:12:38] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -46.168224 | E_var:     0.2169 | E_err:   0.007276
[2025-10-07 16:12:46] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -46.150557 | E_var:     0.2123 | E_err:   0.007200
[2025-10-07 16:12:54] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -46.171265 | E_var:     0.1578 | E_err:   0.006207
[2025-10-07 16:13:02] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -46.175833 | E_var:     0.1646 | E_err:   0.006340
[2025-10-07 16:13:09] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -46.168208 | E_var:     0.1597 | E_err:   0.006244
[2025-10-07 16:13:17] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -46.168223 | E_var:     0.2257 | E_err:   0.007424
[2025-10-07 16:13:25] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -46.169081 | E_var:     0.1751 | E_err:   0.006537
[2025-10-07 16:13:33] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -46.164106 | E_var:     0.2000 | E_err:   0.006987
[2025-10-07 16:13:41] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -46.172674 | E_var:     0.1885 | E_err:   0.006783
[2025-10-07 16:13:48] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -46.166402 | E_var:     0.2081 | E_err:   0.007128
[2025-10-07 16:13:56] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -46.160797 | E_var:     0.1797 | E_err:   0.006623
[2025-10-07 16:14:04] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -46.150589 | E_var:     0.3069 | E_err:   0.008656
[2025-10-07 16:14:12] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -46.167544 | E_var:     0.1642 | E_err:   0.006331
[2025-10-07 16:14:20] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -46.163481 | E_var:     0.1852 | E_err:   0.006724
[2025-10-07 16:14:28] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -46.168292 | E_var:     0.1615 | E_err:   0.006280
[2025-10-07 16:14:35] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -46.170734 | E_var:     0.1591 | E_err:   0.006233
[2025-10-07 16:14:43] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -46.159520 | E_var:     0.2245 | E_err:   0.007404
[2025-10-07 16:14:51] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -46.164358 | E_var:     0.1796 | E_err:   0.006621
[2025-10-07 16:14:59] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -46.163917 | E_var:     0.1528 | E_err:   0.006108
[2025-10-07 16:15:07] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -46.162250 | E_var:     0.2797 | E_err:   0.008264
[2025-10-07 16:15:14] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -46.175933 | E_var:     0.1770 | E_err:   0.006573
[2025-10-07 16:15:22] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -46.169771 | E_var:     0.2183 | E_err:   0.007300
[2025-10-07 16:15:30] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -46.171454 | E_var:     0.1816 | E_err:   0.006659
[2025-10-07 16:15:38] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -46.166551 | E_var:     0.4157 | E_err:   0.010074
[2025-10-07 16:15:46] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -46.164844 | E_var:     0.2217 | E_err:   0.007357
[2025-10-07 16:15:54] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -46.166778 | E_var:     0.1610 | E_err:   0.006269
[2025-10-07 16:16:01] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -46.184801 | E_var:     0.2199 | E_err:   0.007326
[2025-10-07 16:16:09] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -46.163760 | E_var:     0.1907 | E_err:   0.006823
[2025-10-07 16:16:17] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -46.178910 | E_var:     0.1647 | E_err:   0.006341
[2025-10-07 16:16:25] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -46.163875 | E_var:     0.1694 | E_err:   0.006431
[2025-10-07 16:16:33] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -46.164327 | E_var:     0.1935 | E_err:   0.006874
[2025-10-07 16:16:40] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -46.181006 | E_var:     0.1941 | E_err:   0.006883
[2025-10-07 16:16:48] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -46.176712 | E_var:     0.1828 | E_err:   0.006680
[2025-10-07 16:16:56] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -46.169455 | E_var:     0.1824 | E_err:   0.006673
[2025-10-07 16:17:04] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -46.170860 | E_var:     0.1991 | E_err:   0.006972
[2025-10-07 16:17:12] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -46.168635 | E_var:     0.1953 | E_err:   0.006906
[2025-10-07 16:17:20] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -46.172782 | E_var:     0.2056 | E_err:   0.007084
[2025-10-07 16:17:28] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -46.174937 | E_var:     0.2057 | E_err:   0.007086
[2025-10-07 16:17:36] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -46.169087 | E_var:     0.1786 | E_err:   0.006603
[2025-10-07 16:17:43] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -46.158767 | E_var:     0.2188 | E_err:   0.007309
[2025-10-07 16:17:51] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -46.179031 | E_var:     0.2095 | E_err:   0.007151
[2025-10-07 16:17:59] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -46.163152 | E_var:     0.2257 | E_err:   0.007423
[2025-10-07 16:18:07] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -46.158644 | E_var:     0.2160 | E_err:   0.007261
[2025-10-07 16:18:15] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -46.168405 | E_var:     0.1730 | E_err:   0.006500
[2025-10-07 16:18:23] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -46.172087 | E_var:     0.2871 | E_err:   0.008372
[2025-10-07 16:18:30] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -46.172884 | E_var:     0.2422 | E_err:   0.007690
[2025-10-07 16:18:39] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -46.168674 | E_var:     0.1930 | E_err:   0.006864
[2025-10-07 16:18:46] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -46.176252 | E_var:     0.2198 | E_err:   0.007325
[2025-10-07 16:18:54] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -46.174050 | E_var:     0.2697 | E_err:   0.008115
[2025-10-07 16:19:02] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -46.157221 | E_var:     0.1998 | E_err:   0.006984
[2025-10-07 16:19:10] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -46.172819 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 16:19:18] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -46.161626 | E_var:     0.1959 | E_err:   0.006915
[2025-10-07 16:19:25] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -46.170251 | E_var:     0.2454 | E_err:   0.007741
[2025-10-07 16:19:33] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -46.156816 | E_var:     0.1738 | E_err:   0.006514
[2025-10-07 16:19:41] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -46.193681 | E_var:     0.4262 | E_err:   0.010201
[2025-10-07 16:19:49] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -46.179101 | E_var:     0.2088 | E_err:   0.007140
[2025-10-07 16:19:57] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -46.165214 | E_var:     0.2458 | E_err:   0.007747
[2025-10-07 16:20:05] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -46.159343 | E_var:     0.2342 | E_err:   0.007561
[2025-10-07 16:20:12] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -46.168957 | E_var:     0.2023 | E_err:   0.007028
[2025-10-07 16:20:20] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -46.161752 | E_var:     0.4757 | E_err:   0.010777
[2025-10-07 16:20:28] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -46.167934 | E_var:     0.2132 | E_err:   0.007215
[2025-10-07 16:20:36] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -46.168113 | E_var:     0.2593 | E_err:   0.007957
[2025-10-07 16:20:44] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -46.167123 | E_var:     0.2033 | E_err:   0.007045
[2025-10-07 16:20:52] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -46.160167 | E_var:     0.1625 | E_err:   0.006299
[2025-10-07 16:20:59] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -46.170754 | E_var:     0.1636 | E_err:   0.006320
[2025-10-07 16:21:07] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -46.162898 | E_var:     0.1733 | E_err:   0.006505
[2025-10-07 16:21:15] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -46.167806 | E_var:     0.2088 | E_err:   0.007139
[2025-10-07 16:21:23] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -46.166204 | E_var:     0.1800 | E_err:   0.006630
[2025-10-07 16:21:31] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -46.158717 | E_var:     0.1962 | E_err:   0.006920
[2025-10-07 16:21:39] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -46.156980 | E_var:     0.2221 | E_err:   0.007364
[2025-10-07 16:21:46] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -46.167616 | E_var:     0.1929 | E_err:   0.006862
[2025-10-07 16:21:54] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -46.171722 | E_var:     0.2167 | E_err:   0.007273
[2025-10-07 16:22:02] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -46.166610 | E_var:     0.2353 | E_err:   0.007580
[2025-10-07 16:22:10] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -46.163352 | E_var:     0.1626 | E_err:   0.006301
[2025-10-07 16:22:18] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -46.159181 | E_var:     0.2020 | E_err:   0.007022
[2025-10-07 16:22:25] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -46.164450 | E_var:     0.1599 | E_err:   0.006247
[2025-10-07 16:22:33] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -46.170489 | E_var:     0.1871 | E_err:   0.006759
[2025-10-07 16:22:41] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -46.163275 | E_var:     0.1632 | E_err:   0.006312
[2025-10-07 16:22:49] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -46.170408 | E_var:     0.2557 | E_err:   0.007901
[2025-10-07 16:22:57] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -46.166512 | E_var:     0.1723 | E_err:   0.006487
[2025-10-07 16:23:05] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -46.165693 | E_var:     0.2300 | E_err:   0.007494
[2025-10-07 16:23:12] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -46.170014 | E_var:     0.1587 | E_err:   0.006224
[2025-10-07 16:23:20] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -46.165118 | E_var:     0.2048 | E_err:   0.007070
[2025-10-07 16:23:28] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -46.167902 | E_var:     0.1646 | E_err:   0.006340
[2025-10-07 16:23:36] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -46.155755 | E_var:     0.2610 | E_err:   0.007983
[2025-10-07 16:23:44] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -46.172417 | E_var:     0.1983 | E_err:   0.006958
[2025-10-07 16:23:52] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -46.164498 | E_var:     0.1712 | E_err:   0.006464
[2025-10-07 16:23:59] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -46.162533 | E_var:     0.3338 | E_err:   0.009028
[2025-10-07 16:24:07] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -46.159347 | E_var:     0.2285 | E_err:   0.007469
[2025-10-07 16:24:15] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -46.179980 | E_var:     0.1705 | E_err:   0.006451
[2025-10-07 16:24:15] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 16:24:23] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -46.169237 | E_var:     0.1854 | E_err:   0.006728
[2025-10-07 16:24:31] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -46.169380 | E_var:     0.2276 | E_err:   0.007454
[2025-10-07 16:24:38] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -46.151384 | E_var:     0.2741 | E_err:   0.008180
[2025-10-07 16:24:46] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -46.164767 | E_var:     0.1656 | E_err:   0.006358
[2025-10-07 16:24:54] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -46.169519 | E_var:     0.1831 | E_err:   0.006687
[2025-10-07 16:25:02] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -46.180367 | E_var:     0.1762 | E_err:   0.006558
[2025-10-07 16:25:10] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -46.169348 | E_var:     0.2842 | E_err:   0.008330
[2025-10-07 16:25:18] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -46.168971 | E_var:     0.1938 | E_err:   0.006879
[2025-10-07 16:25:25] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -46.170551 | E_var:     0.1692 | E_err:   0.006427
[2025-10-07 16:25:33] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -46.173539 | E_var:     0.2343 | E_err:   0.007563
[2025-10-07 16:25:41] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -46.171313 | E_var:     0.1598 | E_err:   0.006245
[2025-10-07 16:25:49] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -46.169019 | E_var:     0.1704 | E_err:   0.006450
[2025-10-07 16:25:57] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -46.169170 | E_var:     0.2206 | E_err:   0.007339
[2025-10-07 16:26:04] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -46.162920 | E_var:     0.2261 | E_err:   0.007429
[2025-10-07 16:26:12] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -46.171013 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 16:26:20] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -46.176423 | E_var:     0.1860 | E_err:   0.006740
[2025-10-07 16:26:28] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -46.154769 | E_var:     0.3429 | E_err:   0.009149
[2025-10-07 16:26:36] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -46.156431 | E_var:     0.2086 | E_err:   0.007137
[2025-10-07 16:26:44] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -46.162160 | E_var:     0.1925 | E_err:   0.006855
[2025-10-07 16:26:51] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -46.168537 | E_var:     0.1836 | E_err:   0.006695
[2025-10-07 16:26:59] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -46.170036 | E_var:     0.1825 | E_err:   0.006674
[2025-10-07 16:27:07] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -46.175586 | E_var:     0.1634 | E_err:   0.006316
[2025-10-07 16:27:15] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -46.174720 | E_var:     0.2859 | E_err:   0.008354
[2025-10-07 16:27:23] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -46.171764 | E_var:     0.1975 | E_err:   0.006944
[2025-10-07 16:27:30] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -46.164539 | E_var:     0.2726 | E_err:   0.008158
[2025-10-07 16:27:38] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -46.170008 | E_var:     0.1906 | E_err:   0.006821
[2025-10-07 16:27:46] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -46.171457 | E_var:     0.2059 | E_err:   0.007089
[2025-10-07 16:27:54] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -46.167094 | E_var:     0.3145 | E_err:   0.008762
[2025-10-07 16:28:02] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -46.169975 | E_var:     0.1792 | E_err:   0.006614
[2025-10-07 16:28:10] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -46.159004 | E_var:     0.2338 | E_err:   0.007555
[2025-10-07 16:28:17] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -46.176140 | E_var:     0.6055 | E_err:   0.012158
[2025-10-07 16:28:25] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -46.170931 | E_var:     0.2612 | E_err:   0.007985
[2025-10-07 16:28:33] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -46.157800 | E_var:     0.1671 | E_err:   0.006388
[2025-10-07 16:28:41] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -46.156495 | E_var:     0.2734 | E_err:   0.008170
[2025-10-07 16:28:49] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -46.163029 | E_var:     0.2086 | E_err:   0.007137
[2025-10-07 16:28:57] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -46.156749 | E_var:     0.2009 | E_err:   0.007004
[2025-10-07 16:29:05] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -46.167699 | E_var:     0.2079 | E_err:   0.007125
[2025-10-07 16:29:12] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -46.172133 | E_var:     0.1738 | E_err:   0.006514
[2025-10-07 16:29:20] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -46.169305 | E_var:     0.1743 | E_err:   0.006524
[2025-10-07 16:29:28] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -46.165324 | E_var:     0.2370 | E_err:   0.007606
[2025-10-07 16:29:36] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -46.170942 | E_var:     0.1762 | E_err:   0.006559
[2025-10-07 16:29:44] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -46.163224 | E_var:     0.2146 | E_err:   0.007238
[2025-10-07 16:29:52] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -46.178396 | E_var:     0.2372 | E_err:   0.007610
[2025-10-07 16:29:59] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -46.161235 | E_var:     0.1845 | E_err:   0.006712
[2025-10-07 16:30:07] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -46.167137 | E_var:     0.1446 | E_err:   0.005942
[2025-10-07 16:30:15] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -46.169639 | E_var:     0.1659 | E_err:   0.006365
[2025-10-07 16:30:23] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -46.170332 | E_var:     0.1481 | E_err:   0.006013
[2025-10-07 16:30:31] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -46.155266 | E_var:     0.1881 | E_err:   0.006777
[2025-10-07 16:30:38] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -46.151842 | E_var:     0.1756 | E_err:   0.006547
[2025-10-07 16:30:46] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -46.165054 | E_var:     0.1520 | E_err:   0.006091
[2025-10-07 16:30:54] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -46.176855 | E_var:     0.2183 | E_err:   0.007301
[2025-10-07 16:31:02] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -46.159630 | E_var:     0.2094 | E_err:   0.007150
[2025-10-07 16:31:10] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -46.151097 | E_var:     0.5581 | E_err:   0.011673
[2025-10-07 16:31:18] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -46.168337 | E_var:     0.2256 | E_err:   0.007421
[2025-10-07 16:31:25] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -46.164888 | E_var:     0.1673 | E_err:   0.006392
[2025-10-07 16:31:33] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -46.159333 | E_var:     0.1867 | E_err:   0.006751
[2025-10-07 16:31:41] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -46.166504 | E_var:     0.1654 | E_err:   0.006355
[2025-10-07 16:31:49] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -46.152384 | E_var:     0.2457 | E_err:   0.007745
[2025-10-07 16:31:57] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -46.155186 | E_var:     0.2156 | E_err:   0.007255
[2025-10-07 16:32:05] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -46.166458 | E_var:     0.3243 | E_err:   0.008899
[2025-10-07 16:32:12] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -46.172402 | E_var:     0.1579 | E_err:   0.006210
[2025-10-07 16:32:20] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -46.164002 | E_var:     0.1828 | E_err:   0.006680
[2025-10-07 16:32:28] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -46.160658 | E_var:     0.1455 | E_err:   0.005961
[2025-10-07 16:32:36] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -46.157006 | E_var:     0.2226 | E_err:   0.007373
[2025-10-07 16:32:44] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -46.175145 | E_var:     0.1617 | E_err:   0.006284
[2025-10-07 16:32:51] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -46.176554 | E_var:     0.1855 | E_err:   0.006729
[2025-10-07 16:32:59] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -46.159767 | E_var:     0.1839 | E_err:   0.006700
[2025-10-07 16:33:07] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -46.169251 | E_var:     0.1692 | E_err:   0.006427
[2025-10-07 16:33:15] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -46.174809 | E_var:     0.1664 | E_err:   0.006373
[2025-10-07 16:33:23] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -46.168562 | E_var:     0.2714 | E_err:   0.008141
[2025-10-07 16:33:31] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -46.174543 | E_var:     0.1951 | E_err:   0.006902
[2025-10-07 16:33:38] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -46.157307 | E_var:     0.1897 | E_err:   0.006805
[2025-10-07 16:33:46] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -46.168029 | E_var:     0.1499 | E_err:   0.006051
[2025-10-07 16:33:54] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -46.184620 | E_var:     0.1707 | E_err:   0.006455
[2025-10-07 16:34:02] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -46.164413 | E_var:     0.2105 | E_err:   0.007168
[2025-10-07 16:34:10] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -46.174813 | E_var:     0.1974 | E_err:   0.006941
[2025-10-07 16:34:18] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -46.167770 | E_var:     0.1693 | E_err:   0.006429
[2025-10-07 16:34:25] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -46.163903 | E_var:     0.1879 | E_err:   0.006774
[2025-10-07 16:34:33] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -46.179194 | E_var:     0.2027 | E_err:   0.007035
[2025-10-07 16:34:41] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -46.157628 | E_var:     0.1658 | E_err:   0.006362
[2025-10-07 16:34:49] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -46.156771 | E_var:     0.2045 | E_err:   0.007066
[2025-10-07 16:34:57] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -46.171123 | E_var:     0.1608 | E_err:   0.006266
[2025-10-07 16:35:05] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -46.167606 | E_var:     0.2167 | E_err:   0.007273
[2025-10-07 16:35:12] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -46.167745 | E_var:     0.1735 | E_err:   0.006508
[2025-10-07 16:35:20] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -46.166045 | E_var:     0.2559 | E_err:   0.007905
[2025-10-07 16:35:28] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -46.162027 | E_var:     0.2392 | E_err:   0.007642
[2025-10-07 16:35:36] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -46.175397 | E_var:     0.2439 | E_err:   0.007716
[2025-10-07 16:35:44] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -46.158559 | E_var:     0.1846 | E_err:   0.006713
[2025-10-07 16:35:51] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -46.165024 | E_var:     0.2720 | E_err:   0.008149
[2025-10-07 16:35:59] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -46.170002 | E_var:     0.1898 | E_err:   0.006807
[2025-10-07 16:36:07] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -46.169329 | E_var:     0.1676 | E_err:   0.006396
[2025-10-07 16:36:15] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -46.163565 | E_var:     0.1917 | E_err:   0.006841
[2025-10-07 16:36:23] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -46.168545 | E_var:     0.1646 | E_err:   0.006339
[2025-10-07 16:36:31] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -46.163470 | E_var:     0.2078 | E_err:   0.007122
[2025-10-07 16:36:38] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -46.159816 | E_var:     0.1726 | E_err:   0.006491
[2025-10-07 16:36:46] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -46.167930 | E_var:     0.1685 | E_err:   0.006414
[2025-10-07 16:36:54] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -46.169664 | E_var:     0.2044 | E_err:   0.007064
[2025-10-07 16:37:02] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -46.175015 | E_var:     0.1956 | E_err:   0.006911
[2025-10-07 16:37:10] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -46.170713 | E_var:     0.1920 | E_err:   0.006847
[2025-10-07 16:37:17] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -46.168424 | E_var:     0.2304 | E_err:   0.007500
[2025-10-07 16:37:18] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 16:37:25] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -46.182776 | E_var:     0.1771 | E_err:   0.006575
[2025-10-07 16:37:33] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -46.158482 | E_var:     0.2100 | E_err:   0.007160
[2025-10-07 16:37:41] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -46.175406 | E_var:     0.1836 | E_err:   0.006695
[2025-10-07 16:37:49] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -46.168467 | E_var:     0.1520 | E_err:   0.006092
[2025-10-07 16:37:57] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -46.175156 | E_var:     0.1526 | E_err:   0.006105
[2025-10-07 16:38:04] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -46.164806 | E_var:     0.1724 | E_err:   0.006487
[2025-10-07 16:38:12] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -46.160395 | E_var:     0.1983 | E_err:   0.006959
[2025-10-07 16:38:20] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -46.168906 | E_var:     0.1785 | E_err:   0.006602
[2025-10-07 16:38:28] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -46.175251 | E_var:     0.2141 | E_err:   0.007229
[2025-10-07 16:38:36] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -46.172647 | E_var:     0.2223 | E_err:   0.007367
[2025-10-07 16:38:44] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -46.180444 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 16:38:51] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -46.162489 | E_var:     0.2018 | E_err:   0.007018
[2025-10-07 16:38:59] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -46.161724 | E_var:     0.2019 | E_err:   0.007020
[2025-10-07 16:39:07] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -46.177399 | E_var:     0.1787 | E_err:   0.006605
[2025-10-07 16:39:15] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -46.170044 | E_var:     0.1904 | E_err:   0.006819
[2025-10-07 16:39:23] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -46.162981 | E_var:     0.1865 | E_err:   0.006748
[2025-10-07 16:39:30] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -46.163472 | E_var:     0.2515 | E_err:   0.007836
[2025-10-07 16:39:38] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -46.168812 | E_var:     0.1746 | E_err:   0.006530
[2025-10-07 16:39:46] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -46.164773 | E_var:     0.1726 | E_err:   0.006492
[2025-10-07 16:39:54] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -46.158913 | E_var:     0.1562 | E_err:   0.006175
[2025-10-07 16:40:02] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -46.165714 | E_var:     0.1653 | E_err:   0.006353
[2025-10-07 16:40:10] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -46.165945 | E_var:     0.1626 | E_err:   0.006301
[2025-10-07 16:40:17] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -46.165188 | E_var:     0.2328 | E_err:   0.007540
[2025-10-07 16:40:25] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -46.168303 | E_var:     0.1835 | E_err:   0.006693
[2025-10-07 16:40:33] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -46.175145 | E_var:     0.1657 | E_err:   0.006360
[2025-10-07 16:40:41] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -46.163787 | E_var:     0.1777 | E_err:   0.006586
[2025-10-07 16:40:49] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -46.171780 | E_var:     0.2554 | E_err:   0.007897
[2025-10-07 16:40:56] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -46.161127 | E_var:     0.2074 | E_err:   0.007116
[2025-10-07 16:41:04] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -46.164131 | E_var:     0.2361 | E_err:   0.007593
[2025-10-07 16:41:12] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -46.166912 | E_var:     0.1890 | E_err:   0.006792
[2025-10-07 16:41:20] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -46.158697 | E_var:     0.1650 | E_err:   0.006346
[2025-10-07 16:41:28] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -46.160684 | E_var:     0.2099 | E_err:   0.007159
[2025-10-07 16:41:35] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -46.175985 | E_var:     0.1983 | E_err:   0.006958
[2025-10-07 16:41:43] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -46.163991 | E_var:     0.1769 | E_err:   0.006572
[2025-10-07 16:41:51] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -46.170003 | E_var:     0.2661 | E_err:   0.008060
[2025-10-07 16:41:59] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -46.167781 | E_var:     0.2118 | E_err:   0.007191
[2025-10-07 16:42:07] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -46.163984 | E_var:     0.2612 | E_err:   0.007986
[2025-10-07 16:42:15] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -46.179978 | E_var:     0.2838 | E_err:   0.008324
[2025-10-07 16:42:22] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -46.165244 | E_var:     0.2657 | E_err:   0.008054
[2025-10-07 16:42:30] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -46.171529 | E_var:     0.1874 | E_err:   0.006764
[2025-10-07 16:42:38] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -46.164963 | E_var:     0.1629 | E_err:   0.006306
[2025-10-07 16:42:46] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -46.167335 | E_var:     0.2129 | E_err:   0.007209
[2025-10-07 16:42:54] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -46.168445 | E_var:     0.2140 | E_err:   0.007228
[2025-10-07 16:43:01] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -46.163847 | E_var:     0.2120 | E_err:   0.007195
[2025-10-07 16:43:09] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -46.166548 | E_var:     0.1963 | E_err:   0.006923
[2025-10-07 16:43:17] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -46.157780 | E_var:     0.1588 | E_err:   0.006227
[2025-10-07 16:43:25] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -46.168697 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 16:43:33] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -46.169963 | E_var:     0.1505 | E_err:   0.006061
[2025-10-07 16:43:41] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -46.159919 | E_var:     0.1804 | E_err:   0.006637
[2025-10-07 16:43:48] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -46.163686 | E_var:     0.2253 | E_err:   0.007416
[2025-10-07 16:43:56] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -46.170638 | E_var:     0.1999 | E_err:   0.006986
[2025-10-07 16:44:04] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -46.165115 | E_var:     0.1711 | E_err:   0.006463
[2025-10-07 16:44:12] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -46.169852 | E_var:     0.1601 | E_err:   0.006251
[2025-10-07 16:44:20] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -46.167738 | E_var:     0.2038 | E_err:   0.007054
[2025-10-07 16:44:27] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -46.168432 | E_var:     0.1745 | E_err:   0.006528
[2025-10-07 16:44:35] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -46.164813 | E_var:     0.2393 | E_err:   0.007644
[2025-10-07 16:44:43] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -46.156469 | E_var:     0.1742 | E_err:   0.006522
[2025-10-07 16:44:51] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -46.168208 | E_var:     0.1925 | E_err:   0.006856
[2025-10-07 16:44:59] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -46.166942 | E_var:     0.1775 | E_err:   0.006582
[2025-10-07 16:45:07] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -46.165399 | E_var:     0.1520 | E_err:   0.006092
[2025-10-07 16:45:14] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -46.172236 | E_var:     0.1798 | E_err:   0.006625
[2025-10-07 16:45:22] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -46.168430 | E_var:     0.1556 | E_err:   0.006163
[2025-10-07 16:45:30] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -46.167470 | E_var:     0.1799 | E_err:   0.006628
[2025-10-07 16:45:38] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -46.164938 | E_var:     0.1441 | E_err:   0.005931
[2025-10-07 16:45:46] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -46.162173 | E_var:     0.2052 | E_err:   0.007078
[2025-10-07 16:45:53] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -46.164724 | E_var:     0.2517 | E_err:   0.007839
[2025-10-07 16:46:01] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -46.156958 | E_var:     0.1905 | E_err:   0.006820
[2025-10-07 16:46:09] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -46.155451 | E_var:     0.1840 | E_err:   0.006703
[2025-10-07 16:46:17] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -46.156924 | E_var:     0.2343 | E_err:   0.007563
[2025-10-07 16:46:25] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -46.169189 | E_var:     0.1554 | E_err:   0.006160
[2025-10-07 16:46:33] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -46.160911 | E_var:     0.2676 | E_err:   0.008083
[2025-10-07 16:46:40] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -46.157783 | E_var:     0.1532 | E_err:   0.006116
[2025-10-07 16:46:48] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -46.163702 | E_var:     0.2081 | E_err:   0.007128
[2025-10-07 16:46:56] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -46.154491 | E_var:     0.1867 | E_err:   0.006752
[2025-10-07 16:47:04] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -46.166557 | E_var:     0.1704 | E_err:   0.006449
[2025-10-07 16:47:12] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -46.161064 | E_var:     0.2061 | E_err:   0.007094
[2025-10-07 16:47:19] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -46.176832 | E_var:     0.2837 | E_err:   0.008322
[2025-10-07 16:47:27] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -46.162178 | E_var:     0.1778 | E_err:   0.006588
[2025-10-07 16:47:35] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -46.165043 | E_var:     0.1884 | E_err:   0.006782
[2025-10-07 16:47:43] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -46.167661 | E_var:     0.1605 | E_err:   0.006259
[2025-10-07 16:47:51] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -46.178587 | E_var:     0.1900 | E_err:   0.006812
[2025-10-07 16:47:59] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -46.169493 | E_var:     0.1672 | E_err:   0.006389
[2025-10-07 16:48:06] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -46.163102 | E_var:     0.2018 | E_err:   0.007018
[2025-10-07 16:48:14] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -46.155852 | E_var:     0.1554 | E_err:   0.006159
[2025-10-07 16:48:22] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -46.169780 | E_var:     0.1943 | E_err:   0.006887
[2025-10-07 16:48:30] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -46.178770 | E_var:     0.1684 | E_err:   0.006412
[2025-10-07 16:48:38] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -46.166033 | E_var:     0.1654 | E_err:   0.006355
[2025-10-07 16:48:45] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -46.157283 | E_var:     0.1926 | E_err:   0.006858
[2025-10-07 16:48:53] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -46.164942 | E_var:     0.2159 | E_err:   0.007260
[2025-10-07 16:49:01] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -46.160939 | E_var:     0.1689 | E_err:   0.006421
[2025-10-07 16:49:09] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -46.170852 | E_var:     0.2258 | E_err:   0.007425
[2025-10-07 16:49:17] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -46.166094 | E_var:     0.2249 | E_err:   0.007410
[2025-10-07 16:49:24] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -46.173427 | E_var:     0.3582 | E_err:   0.009352
[2025-10-07 16:49:32] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -46.165596 | E_var:     0.2047 | E_err:   0.007070
[2025-10-07 16:49:40] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -46.162880 | E_var:     0.1842 | E_err:   0.006705
[2025-10-07 16:49:48] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -46.156805 | E_var:     0.2116 | E_err:   0.007188
[2025-10-07 16:49:56] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -46.152147 | E_var:     0.1742 | E_err:   0.006522
[2025-10-07 16:50:04] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -46.169139 | E_var:     0.1853 | E_err:   0.006726
[2025-10-07 16:50:11] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -46.164528 | E_var:     0.2452 | E_err:   0.007737
[2025-10-07 16:50:19] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -46.153362 | E_var:     0.1984 | E_err:   0.006960
[2025-10-07 16:50:19] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 16:50:27] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -46.156765 | E_var:     0.1875 | E_err:   0.006765
[2025-10-07 16:50:35] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -46.161083 | E_var:     0.1681 | E_err:   0.006407
[2025-10-07 16:50:43] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -46.159567 | E_var:     0.2039 | E_err:   0.007056
[2025-10-07 16:50:51] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -46.159631 | E_var:     0.1854 | E_err:   0.006728
[2025-10-07 16:50:58] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -46.168971 | E_var:     0.2019 | E_err:   0.007021
[2025-10-07 16:51:06] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -46.171590 | E_var:     0.2275 | E_err:   0.007452
[2025-10-07 16:51:14] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -46.183980 | E_var:     0.1837 | E_err:   0.006697
[2025-10-07 16:51:22] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -46.164484 | E_var:     0.2084 | E_err:   0.007134
[2025-10-07 16:51:30] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -46.170007 | E_var:     0.1928 | E_err:   0.006861
[2025-10-07 16:51:37] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -46.168217 | E_var:     0.1667 | E_err:   0.006380
[2025-10-07 16:51:45] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -46.166211 | E_var:     0.1803 | E_err:   0.006634
[2025-10-07 16:51:53] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -46.177087 | E_var:     0.1937 | E_err:   0.006876
[2025-10-07 16:52:01] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -46.171235 | E_var:     0.1895 | E_err:   0.006802
[2025-10-07 16:52:09] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -46.163038 | E_var:     0.1908 | E_err:   0.006825
[2025-10-07 16:52:17] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -46.174523 | E_var:     0.1711 | E_err:   0.006463
[2025-10-07 16:52:24] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -46.171326 | E_var:     0.1848 | E_err:   0.006716
[2025-10-07 16:52:32] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -46.166743 | E_var:     0.1796 | E_err:   0.006621
[2025-10-07 16:52:40] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -46.160797 | E_var:     0.2057 | E_err:   0.007086
[2025-10-07 16:52:48] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -46.176677 | E_var:     0.2150 | E_err:   0.007245
[2025-10-07 16:52:56] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -46.179874 | E_var:     0.1576 | E_err:   0.006203
[2025-10-07 16:53:03] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -46.180685 | E_var:     0.1666 | E_err:   0.006378
[2025-10-07 16:53:11] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -46.166481 | E_var:     0.1684 | E_err:   0.006411
[2025-10-07 16:53:19] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -46.162269 | E_var:     0.1672 | E_err:   0.006390
[2025-10-07 16:53:27] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -46.158396 | E_var:     0.1802 | E_err:   0.006633
[2025-10-07 16:53:35] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -46.177036 | E_var:     0.2164 | E_err:   0.007268
[2025-10-07 16:53:43] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -46.158532 | E_var:     0.1817 | E_err:   0.006660
[2025-10-07 16:53:50] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -46.172758 | E_var:     0.2926 | E_err:   0.008452
[2025-10-07 16:53:58] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -46.163686 | E_var:     0.2189 | E_err:   0.007310
[2025-10-07 16:54:06] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -46.174360 | E_var:     0.1419 | E_err:   0.005885
[2025-10-07 16:54:14] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -46.186502 | E_var:     0.2191 | E_err:   0.007313
[2025-10-07 16:54:22] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -46.171003 | E_var:     0.1548 | E_err:   0.006147
[2025-10-07 16:54:29] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -46.147600 | E_var:     0.2511 | E_err:   0.007829
[2025-10-07 16:54:37] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -46.167475 | E_var:     0.2182 | E_err:   0.007299
[2025-10-07 16:54:45] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -46.163902 | E_var:     0.2165 | E_err:   0.007270
[2025-10-07 16:54:53] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -46.167071 | E_var:     0.2039 | E_err:   0.007056
[2025-10-07 16:55:01] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -46.164980 | E_var:     0.1416 | E_err:   0.005879
[2025-10-07 16:55:09] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -46.167258 | E_var:     0.1567 | E_err:   0.006185
[2025-10-07 16:55:16] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -46.167629 | E_var:     0.2215 | E_err:   0.007353
[2025-10-07 16:55:24] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -46.155342 | E_var:     0.2367 | E_err:   0.007602
[2025-10-07 16:55:32] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -46.159632 | E_var:     0.2122 | E_err:   0.007197
[2025-10-07 16:55:40] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -46.177633 | E_var:     0.2760 | E_err:   0.008209
[2025-10-07 16:55:48] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -46.173588 | E_var:     0.6615 | E_err:   0.012708
[2025-10-07 16:55:55] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -46.168027 | E_var:     0.1626 | E_err:   0.006301
[2025-10-07 16:56:03] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -46.164839 | E_var:     0.1936 | E_err:   0.006874
[2025-10-07 16:56:11] [Iter 1045/1050] R2[594/600]   | LR: 0.005006 | E:  -46.166023 | E_var:     0.1864 | E_err:   0.006745
[2025-10-07 16:56:19] [Iter 1046/1050] R2[595/600]   | LR: 0.005004 | E:  -46.159623 | E_var:     0.2019 | E_err:   0.007022
[2025-10-07 16:56:27] [Iter 1047/1050] R2[596/600]   | LR: 0.005003 | E:  -46.165952 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 16:56:35] [Iter 1048/1050] R2[597/600]   | LR: 0.005002 | E:  -46.176606 | E_var:     0.2233 | E_err:   0.007384
[2025-10-07 16:56:42] [Iter 1049/1050] R2[598/600]   | LR: 0.005001 | E:  -46.183420 | E_var:     0.1902 | E_err:   0.006815
[2025-10-07 16:56:50] [Iter 1050/1050] R2[599/600]   | LR: 0.005000 | E:  -46.173199 | E_var:     0.1880 | E_err:   0.006774
[2025-10-07 16:56:50] ======================================================================================================
[2025-10-07 16:56:50] ✅ Training completed successfully
[2025-10-07 16:56:50] Total restarts: 2
[2025-10-07 16:56:53] Final Energy: -46.17319947 ± 0.00677408
[2025-10-07 16:56:53] Final Variance: 0.187958
[2025-10-07 16:56:53] ======================================================================================================
[2025-10-07 16:56:53] ======================================================================================================
[2025-10-07 16:56:53] Training completed | Runtime: 8265.4s
[2025-10-07 16:56:56] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-07 16:56:56] ======================================================================================================
