[2025-10-03 06:00:59] ==================================================
[2025-10-03 06:00:59] GCNN for Shastry-Sutherland Model
[2025-10-03 06:00:59] ==================================================
[2025-10-03 06:00:59] System parameters:
[2025-10-03 06:00:59]   - System size: L=5, N=100
[2025-10-03 06:00:59]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-10-03 06:00:59] --------------------------------------------------
[2025-10-03 06:00:59] Model parameters:
[2025-10-03 06:00:59]   - Number of layers = 8
[2025-10-03 06:00:59]   - Number of features = 4
[2025-10-03 06:00:59]   - Total parameters = 45260
[2025-10-03 06:00:59] --------------------------------------------------
[2025-10-03 06:00:59] Training parameters:
[2025-10-03 06:00:59]   - Total iterations: 2250
[2025-10-03 06:00:59]   - Annealing cycles: 4
[2025-10-03 06:00:59]   - Initial period: 150
[2025-10-03 06:00:59]   - Period multiplier: 2.0
[2025-10-03 06:00:59]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-03 06:00:59]   - Samples: 4096
[2025-10-03 06:00:59]   - Discarded samples: 0
[2025-10-03 06:00:59]   - Chunk size: 4096
[2025-10-03 06:00:59]   - Diagonal shift: 0.15
[2025-10-03 06:00:59]   - Gradient clipping: 1.0
[2025-10-03 06:00:59]   - Checkpoint enabled: interval=200
[2025-10-03 06:00:59]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.76/model_L8F4/training/checkpoints
[2025-10-03 06:00:59] --------------------------------------------------
[2025-10-03 06:00:59] Device status:
[2025-10-03 06:00:59]   - Devices model: NVIDIA H200 NVL
[2025-10-03 06:00:59]   - Number of devices: 1
[2025-10-03 06:00:59]   - Sharding: True
[2025-10-03 06:01:47] [Iter    1/2250] R0[0/150]    | LR: 0.030000 | E:  50.480411 | E_var:     0.0938 | E_err:   0.004784
[2025-10-03 06:01:58] [Iter    2/2250] R0[2/150]    | LR: 0.029989 | E:  50.482873 | E_var:     0.1478 | E_err:   0.006007
[2025-10-03 06:02:09] [Iter    3/2250] R0[4/150]    | LR: 0.029956 | E:  50.474544 | E_var:     0.2423 | E_err:   0.007691
[2025-10-03 06:02:19] [Iter    4/2250] R0[6/150]    | LR: 0.029901 | E:  50.441144 | E_var:     0.4211 | E_err:   0.010140
[2025-10-03 06:02:30] [Iter    5/2250] R0[8/150]    | LR: 0.029825 | E:  50.376704 | E_var:     0.8538 | E_err:   0.014438
[2025-10-03 06:02:40] [Iter    6/2250] R0[10/150]   | LR: 0.029727 | E:  50.261984 | E_var:     2.0657 | E_err:   0.022457
[2025-10-03 06:02:51] [Iter    7/2250] R0[12/150]   | LR: 0.029607 | E:  49.829358 | E_var:     6.4563 | E_err:   0.039702
[2025-10-03 06:03:01] [Iter    8/2250] R0[14/150]   | LR: 0.029466 | E:  48.439558 | E_var:    23.6465 | E_err:   0.075981
[2025-10-03 06:03:12] [Iter    9/2250] R0[16/150]   | LR: 0.029305 | E:  43.075499 | E_var:    85.3109 | E_err:   0.144319
[2025-10-03 06:03:22] [Iter   10/2250] R0[18/150]   | LR: 0.029122 | E:  32.883513 | E_var:   102.8966 | E_err:   0.158497
[2025-10-03 06:03:33] [Iter   11/2250] R0[20/150]   | LR: 0.028919 | E:  24.878033 | E_var:    74.2333 | E_err:   0.134623
[2025-10-03 06:03:43] [Iter   12/2250] R0[22/150]   | LR: 0.028696 | E:  18.564766 | E_var:    74.4001 | E_err:   0.134774
[2025-10-03 06:03:54] [Iter   13/2250] R0[24/150]   | LR: 0.028454 | E:  12.739309 | E_var:    71.3170 | E_err:   0.131952
[2025-10-03 06:04:04] [Iter   14/2250] R0[26/150]   | LR: 0.028192 | E:   7.754201 | E_var:    64.9213 | E_err:   0.125896
[2025-10-03 06:04:15] [Iter   15/2250] R0[28/150]   | LR: 0.027912 | E:   3.637628 | E_var:    58.1453 | E_err:   0.119145
[2025-10-03 06:04:26] [Iter   16/2250] R0[30/150]   | LR: 0.027613 | E:   0.474246 | E_var:    48.7504 | E_err:   0.109096
[2025-10-03 06:04:36] [Iter   17/2250] R0[32/150]   | LR: 0.027296 | E:  -1.827540 | E_var:    45.0940 | E_err:   0.104925
[2025-10-03 06:04:47] [Iter   18/2250] R0[34/150]   | LR: 0.026962 | E:  -3.768615 | E_var:    42.8067 | E_err:   0.102229
[2025-10-03 06:04:57] [Iter   19/2250] R0[36/150]   | LR: 0.026612 | E:  -5.415274 | E_var:    43.4386 | E_err:   0.102981
[2025-10-03 06:05:08] [Iter   20/2250] R0[38/150]   | LR: 0.026246 | E:  -6.615038 | E_var:    36.0220 | E_err:   0.093779
[2025-10-03 06:05:18] [Iter   21/2250] R0[40/150]   | LR: 0.025864 | E:  -7.729806 | E_var:    35.8307 | E_err:   0.093529
[2025-10-03 06:05:29] [Iter   22/2250] R0[42/150]   | LR: 0.025468 | E:  -8.873725 | E_var:    34.0596 | E_err:   0.091188
[2025-10-03 06:05:39] [Iter   23/2250] R0[44/150]   | LR: 0.025057 | E:  -9.658667 | E_var:    40.8866 | E_err:   0.099910
[2025-10-03 06:05:50] [Iter   24/2250] R0[46/150]   | LR: 0.024634 | E: -10.589643 | E_var:    29.2860 | E_err:   0.084557
[2025-10-03 06:06:00] [Iter   25/2250] R0[48/150]   | LR: 0.024198 | E: -11.264948 | E_var:    37.6465 | E_err:   0.095870
[2025-10-03 06:06:11] [Iter   26/2250] R0[50/150]   | LR: 0.023750 | E: -12.040023 | E_var:    32.1222 | E_err:   0.088557
[2025-10-03 06:06:21] [Iter   27/2250] R0[52/150]   | LR: 0.023291 | E: -12.740618 | E_var:    32.5453 | E_err:   0.089138
[2025-10-03 06:06:32] [Iter   28/2250] R0[54/150]   | LR: 0.022822 | E: -13.126793 | E_var:    34.0632 | E_err:   0.091193
[2025-10-03 06:06:42] [Iter   29/2250] R0[56/150]   | LR: 0.022344 | E: -13.840153 | E_var:    42.2651 | E_err:   0.101581
[2025-10-03 06:06:53] [Iter   30/2250] R0[58/150]   | LR: 0.021857 | E: -14.416685 | E_var:    37.6632 | E_err:   0.095891
[2025-10-03 06:07:03] [Iter   31/2250] R0[60/150]   | LR: 0.021363 | E: -14.923879 | E_var:    28.7482 | E_err:   0.083777
[2025-10-03 06:07:14] [Iter   32/2250] R0[62/150]   | LR: 0.020861 | E: -15.407576 | E_var:    30.1607 | E_err:   0.085811
[2025-10-03 06:07:25] [Iter   33/2250] R0[64/150]   | LR: 0.020354 | E: -15.945685 | E_var:    29.7325 | E_err:   0.085199
[2025-10-03 06:07:35] [Iter   34/2250] R0[66/150]   | LR: 0.019842 | E: -16.363174 | E_var:    31.3562 | E_err:   0.087495
[2025-10-03 06:07:46] [Iter   35/2250] R0[68/150]   | LR: 0.019326 | E: -16.888014 | E_var:    25.0826 | E_err:   0.078254
[2025-10-03 06:07:56] [Iter   36/2250] R0[70/150]   | LR: 0.018807 | E: -17.279507 | E_var:    29.0825 | E_err:   0.084263
[2025-10-03 06:08:07] [Iter   37/2250] R0[72/150]   | LR: 0.018285 | E: -17.811687 | E_var:    29.8072 | E_err:   0.085306
[2025-10-03 06:08:17] [Iter   38/2250] R0[74/150]   | LR: 0.017762 | E: -18.240570 | E_var:    29.5354 | E_err:   0.084916
[2025-10-03 06:08:28] [Iter   39/2250] R0[76/150]   | LR: 0.017238 | E: -18.757649 | E_var:    29.2623 | E_err:   0.084523
[2025-10-03 06:08:38] [Iter   40/2250] R0[78/150]   | LR: 0.016715 | E: -19.075129 | E_var:    30.3230 | E_err:   0.086041
[2025-10-03 06:08:49] [Iter   41/2250] R0[80/150]   | LR: 0.016193 | E: -19.570639 | E_var:    25.7492 | E_err:   0.079287
[2025-10-03 06:08:59] [Iter   42/2250] R0[82/150]   | LR: 0.015674 | E: -19.977418 | E_var:    25.7365 | E_err:   0.079267
[2025-10-03 06:09:10] [Iter   43/2250] R0[84/150]   | LR: 0.015158 | E: -20.403198 | E_var:    27.4898 | E_err:   0.081923
[2025-10-03 06:09:20] [Iter   44/2250] R0[86/150]   | LR: 0.014646 | E: -20.763661 | E_var:    27.4955 | E_err:   0.081931
[2025-10-03 06:09:31] [Iter   45/2250] R0[88/150]   | LR: 0.014139 | E: -21.094510 | E_var:    31.0335 | E_err:   0.087043
[2025-10-03 06:09:41] [Iter   46/2250] R0[90/150]   | LR: 0.013637 | E: -21.381478 | E_var:    23.3211 | E_err:   0.075456
[2025-10-03 06:09:52] [Iter   47/2250] R0[92/150]   | LR: 0.013143 | E: -21.933259 | E_var:    31.0571 | E_err:   0.087076
[2025-10-03 06:10:02] [Iter   48/2250] R0[94/150]   | LR: 0.012656 | E: -22.309782 | E_var:    31.5060 | E_err:   0.087703
[2025-10-03 06:10:13] [Iter   49/2250] R0[96/150]   | LR: 0.012178 | E: -22.555563 | E_var:    24.8599 | E_err:   0.077906
[2025-10-03 06:10:23] [Iter   50/2250] R0[98/150]   | LR: 0.011709 | E: -23.109162 | E_var:    25.4549 | E_err:   0.078833
[2025-10-03 06:10:34] [Iter   51/2250] R0[100/150]  | LR: 0.011250 | E: -23.463064 | E_var:    26.2995 | E_err:   0.080130
[2025-10-03 06:10:45] [Iter   52/2250] R0[102/150]  | LR: 0.010802 | E: -23.642611 | E_var:    26.0554 | E_err:   0.079757
[2025-10-03 06:10:55] [Iter   53/2250] R0[104/150]  | LR: 0.010366 | E: -24.041911 | E_var:    37.0609 | E_err:   0.095121
[2025-10-03 06:11:06] [Iter   54/2250] R0[106/150]  | LR: 0.009943 | E: -24.320797 | E_var:    22.8275 | E_err:   0.074653
[2025-10-03 06:11:16] [Iter   55/2250] R0[108/150]  | LR: 0.009532 | E: -24.654813 | E_var:    22.1990 | E_err:   0.073619
[2025-10-03 06:11:27] [Iter   56/2250] R0[110/150]  | LR: 0.009136 | E: -25.038010 | E_var:    24.4112 | E_err:   0.077199
[2025-10-03 06:11:37] [Iter   57/2250] R0[112/150]  | LR: 0.008754 | E: -25.243955 | E_var:    24.4179 | E_err:   0.077210
[2025-10-03 06:11:48] [Iter   58/2250] R0[114/150]  | LR: 0.008388 | E: -25.642876 | E_var:    23.8591 | E_err:   0.076322
[2025-10-03 06:11:58] [Iter   59/2250] R0[116/150]  | LR: 0.008038 | E: -25.832600 | E_var:    21.9638 | E_err:   0.073227
[2025-10-03 06:12:09] [Iter   60/2250] R0[118/150]  | LR: 0.007704 | E: -26.282473 | E_var:    24.9576 | E_err:   0.078059
[2025-10-03 06:12:19] [Iter   61/2250] R0[120/150]  | LR: 0.007387 | E: -26.579145 | E_var:    24.4447 | E_err:   0.077252
[2025-10-03 06:12:30] [Iter   62/2250] R0[122/150]  | LR: 0.007088 | E: -26.859956 | E_var:    23.5732 | E_err:   0.075863
[2025-10-03 06:12:40] [Iter   63/2250] R0[124/150]  | LR: 0.006808 | E: -27.143175 | E_var:    21.7969 | E_err:   0.072949
[2025-10-03 06:12:51] [Iter   64/2250] R0[126/150]  | LR: 0.006546 | E: -27.432566 | E_var:    24.2151 | E_err:   0.076889
[2025-10-03 06:13:01] [Iter   65/2250] R0[128/150]  | LR: 0.006304 | E: -27.740434 | E_var:    23.7681 | E_err:   0.076176
[2025-10-03 06:13:12] [Iter   66/2250] R0[130/150]  | LR: 0.006081 | E: -28.124600 | E_var:    23.5536 | E_err:   0.075831
[2025-10-03 06:13:22] [Iter   67/2250] R0[132/150]  | LR: 0.005878 | E: -28.458914 | E_var:    22.1840 | E_err:   0.073594
[2025-10-03 06:13:33] [Iter   68/2250] R0[134/150]  | LR: 0.005695 | E: -28.860313 | E_var:    23.2387 | E_err:   0.075323
[2025-10-03 06:13:43] [Iter   69/2250] R0[136/150]  | LR: 0.005534 | E: -29.242940 | E_var:    22.0441 | E_err:   0.073361
[2025-10-03 06:13:54] [Iter   70/2250] R0[138/150]  | LR: 0.005393 | E: -29.530345 | E_var:    21.8927 | E_err:   0.073109
[2025-10-03 06:14:05] [Iter   71/2250] R0[140/150]  | LR: 0.005273 | E: -29.885814 | E_var:    23.0410 | E_err:   0.075002
[2025-10-03 06:14:15] [Iter   72/2250] R0[142/150]  | LR: 0.005175 | E: -30.409545 | E_var:    21.0421 | E_err:   0.071674
[2025-10-03 06:14:26] [Iter   73/2250] R0[144/150]  | LR: 0.005099 | E: -30.786767 | E_var:    20.5452 | E_err:   0.070823
[2025-10-03 06:14:36] [Iter   74/2250] R0[146/150]  | LR: 0.005044 | E: -31.067096 | E_var:    19.5314 | E_err:   0.069054
[2025-10-03 06:14:47] [Iter   75/2250] R0[148/150]  | LR: 0.005011 | E: -31.476857 | E_var:    20.0698 | E_err:   0.069999
[2025-10-03 06:14:47] 🔄 RESTART #1 | Period: 300
[2025-10-03 06:14:57] [Iter   76/2250] R1[0/300]    | LR: 0.030000 | E: -31.817735 | E_var:    18.6320 | E_err:   0.067445
[2025-10-03 06:15:08] [Iter   77/2250] R1[2/300]    | LR: 0.029997 | E: -32.137336 | E_var:    18.6104 | E_err:   0.067406
[2025-10-03 06:15:18] [Iter   78/2250] R1[4/300]    | LR: 0.029989 | E: -32.350823 | E_var:    17.8147 | E_err:   0.065949
[2025-10-03 06:15:29] [Iter   79/2250] R1[6/300]    | LR: 0.029975 | E: -32.587894 | E_var:    17.4999 | E_err:   0.065364
[2025-10-03 06:15:39] [Iter   80/2250] R1[8/300]    | LR: 0.029956 | E: -32.824916 | E_var:    17.8223 | E_err:   0.065963
[2025-10-03 06:15:50] [Iter   81/2250] R1[10/300]   | LR: 0.029932 | E: -33.066322 | E_var:    16.6618 | E_err:   0.063779
[2025-10-03 06:16:00] [Iter   82/2250] R1[12/300]   | LR: 0.029901 | E: -33.228740 | E_var:    18.6361 | E_err:   0.067452
[2025-10-03 06:16:11] [Iter   83/2250] R1[14/300]   | LR: 0.029866 | E: -33.365205 | E_var:    14.8359 | E_err:   0.060183
[2025-10-03 06:16:21] [Iter   84/2250] R1[16/300]   | LR: 0.029825 | E: -33.528674 | E_var:    14.8723 | E_err:   0.060257
[2025-10-03 06:16:32] [Iter   85/2250] R1[18/300]   | LR: 0.029779 | E: -33.745388 | E_var:    16.0228 | E_err:   0.062545
[2025-10-03 06:16:42] [Iter   86/2250] R1[20/300]   | LR: 0.029727 | E: -33.866541 | E_var:    14.5787 | E_err:   0.059659
[2025-10-03 06:16:53] [Iter   87/2250] R1[22/300]   | LR: 0.029670 | E: -34.064904 | E_var:    15.3388 | E_err:   0.061195
[2025-10-03 06:17:03] [Iter   88/2250] R1[24/300]   | LR: 0.029607 | E: -34.155075 | E_var:    15.0956 | E_err:   0.060708
[2025-10-03 06:17:14] [Iter   89/2250] R1[26/300]   | LR: 0.029540 | E: -34.337204 | E_var:    15.8156 | E_err:   0.062139
[2025-10-03 06:17:24] [Iter   90/2250] R1[28/300]   | LR: 0.029466 | E: -34.393428 | E_var:    14.7900 | E_err:   0.060090
[2025-10-03 06:17:35] [Iter   91/2250] R1[30/300]   | LR: 0.029388 | E: -34.595032 | E_var:    15.1513 | E_err:   0.060820
[2025-10-03 06:17:46] [Iter   92/2250] R1[32/300]   | LR: 0.029305 | E: -34.732819 | E_var:    13.5589 | E_err:   0.057535
[2025-10-03 06:17:56] [Iter   93/2250] R1[34/300]   | LR: 0.029216 | E: -34.855585 | E_var:    14.2608 | E_err:   0.059005
[2025-10-03 06:18:07] [Iter   94/2250] R1[36/300]   | LR: 0.029122 | E: -34.949677 | E_var:    14.7011 | E_err:   0.059909
[2025-10-03 06:18:17] [Iter   95/2250] R1[38/300]   | LR: 0.029023 | E: -35.053211 | E_var:    14.2031 | E_err:   0.058886
[2025-10-03 06:18:28] [Iter   96/2250] R1[40/300]   | LR: 0.028919 | E: -35.182053 | E_var:    14.1967 | E_err:   0.058873
[2025-10-03 06:18:38] [Iter   97/2250] R1[42/300]   | LR: 0.028810 | E: -35.296934 | E_var:    13.0367 | E_err:   0.056416
[2025-10-03 06:18:49] [Iter   98/2250] R1[44/300]   | LR: 0.028696 | E: -35.419675 | E_var:    12.8259 | E_err:   0.055958
[2025-10-03 06:18:59] [Iter   99/2250] R1[46/300]   | LR: 0.028578 | E: -35.543504 | E_var:    14.7653 | E_err:   0.060040
[2025-10-03 06:19:10] [Iter  100/2250] R1[48/300]   | LR: 0.028454 | E: -35.680702 | E_var:    12.4331 | E_err:   0.055095
[2025-10-03 06:19:20] [Iter  101/2250] R1[50/300]   | LR: 0.028325 | E: -35.786078 | E_var:    12.4148 | E_err:   0.055054
[2025-10-03 06:19:31] [Iter  102/2250] R1[52/300]   | LR: 0.028192 | E: -35.844230 | E_var:    18.6122 | E_err:   0.067409
[2025-10-03 06:19:41] [Iter  103/2250] R1[54/300]   | LR: 0.028054 | E: -36.016572 | E_var:    12.2325 | E_err:   0.054648
[2025-10-03 06:19:52] [Iter  104/2250] R1[56/300]   | LR: 0.027912 | E: -36.049358 | E_var:    12.0899 | E_err:   0.054329
[2025-10-03 06:20:02] [Iter  105/2250] R1[58/300]   | LR: 0.027764 | E: -36.300977 | E_var:    11.6579 | E_err:   0.053350
[2025-10-03 06:20:13] [Iter  106/2250] R1[60/300]   | LR: 0.027613 | E: -36.398178 | E_var:    11.9681 | E_err:   0.054055
[2025-10-03 06:20:23] [Iter  107/2250] R1[62/300]   | LR: 0.027457 | E: -36.506663 | E_var:    11.4655 | E_err:   0.052908
[2025-10-03 06:20:34] [Iter  108/2250] R1[64/300]   | LR: 0.027296 | E: -36.657260 | E_var:    11.4443 | E_err:   0.052858
[2025-10-03 06:20:44] [Iter  109/2250] R1[66/300]   | LR: 0.027131 | E: -36.833640 | E_var:    11.3299 | E_err:   0.052594
[2025-10-03 06:20:55] [Iter  110/2250] R1[68/300]   | LR: 0.026962 | E: -36.974934 | E_var:    11.3750 | E_err:   0.052698
[2025-10-03 06:21:06] [Iter  111/2250] R1[70/300]   | LR: 0.026789 | E: -37.051108 | E_var:    11.5386 | E_err:   0.053076
[2025-10-03 06:21:16] [Iter  112/2250] R1[72/300]   | LR: 0.026612 | E: -37.216371 | E_var:    10.6781 | E_err:   0.051058
[2025-10-03 06:21:27] [Iter  113/2250] R1[74/300]   | LR: 0.026431 | E: -37.364905 | E_var:    10.6027 | E_err:   0.050878
[2025-10-03 06:21:37] [Iter  114/2250] R1[76/300]   | LR: 0.026246 | E: -37.550944 | E_var:     9.9844 | E_err:   0.049372
[2025-10-03 06:21:48] [Iter  115/2250] R1[78/300]   | LR: 0.026057 | E: -37.689616 | E_var:    10.0650 | E_err:   0.049571
[2025-10-03 06:21:58] [Iter  116/2250] R1[80/300]   | LR: 0.025864 | E: -37.864021 | E_var:    10.6109 | E_err:   0.050898
[2025-10-03 06:22:09] [Iter  117/2250] R1[82/300]   | LR: 0.025668 | E: -37.979330 | E_var:     9.3993 | E_err:   0.047904
[2025-10-03 06:22:19] [Iter  118/2250] R1[84/300]   | LR: 0.025468 | E: -38.207507 | E_var:     9.1534 | E_err:   0.047273
[2025-10-03 06:22:30] [Iter  119/2250] R1[86/300]   | LR: 0.025264 | E: -38.410015 | E_var:     9.8481 | E_err:   0.049034
[2025-10-03 06:22:40] [Iter  120/2250] R1[88/300]   | LR: 0.025057 | E: -38.511068 | E_var:     9.8912 | E_err:   0.049141
[2025-10-03 06:22:51] [Iter  121/2250] R1[90/300]   | LR: 0.024847 | E: -38.807585 | E_var:     9.3672 | E_err:   0.047822
[2025-10-03 06:23:01] [Iter  122/2250] R1[92/300]   | LR: 0.024634 | E: -38.924112 | E_var:     8.4934 | E_err:   0.045537
[2025-10-03 06:23:12] [Iter  123/2250] R1[94/300]   | LR: 0.024417 | E: -39.111570 | E_var:     8.5184 | E_err:   0.045604
[2025-10-03 06:23:22] [Iter  124/2250] R1[96/300]   | LR: 0.024198 | E: -39.272493 | E_var:     8.1017 | E_err:   0.044474
[2025-10-03 06:23:33] [Iter  125/2250] R1[98/300]   | LR: 0.023975 | E: -39.479194 | E_var:     7.6508 | E_err:   0.043219
[2025-10-03 06:23:43] [Iter  126/2250] R1[100/300]  | LR: 0.023750 | E: -39.649244 | E_var:     7.4308 | E_err:   0.042593
[2025-10-03 06:23:54] [Iter  127/2250] R1[102/300]  | LR: 0.023522 | E: -39.821126 | E_var:     7.6086 | E_err:   0.043099
[2025-10-03 06:24:04] [Iter  128/2250] R1[104/300]  | LR: 0.023291 | E: -40.018814 | E_var:     6.9809 | E_err:   0.041283
[2025-10-03 06:24:15] [Iter  129/2250] R1[106/300]  | LR: 0.023058 | E: -40.123464 | E_var:     6.5833 | E_err:   0.040091
[2025-10-03 06:24:25] [Iter  130/2250] R1[108/300]  | LR: 0.022822 | E: -40.295933 | E_var:     5.6785 | E_err:   0.037234
[2025-10-03 06:24:36] [Iter  131/2250] R1[110/300]  | LR: 0.022584 | E: -40.362492 | E_var:     6.0016 | E_err:   0.038278
[2025-10-03 06:24:47] [Iter  132/2250] R1[112/300]  | LR: 0.022344 | E: -40.529036 | E_var:     5.5011 | E_err:   0.036647
[2025-10-03 06:24:57] [Iter  133/2250] R1[114/300]  | LR: 0.022102 | E: -40.679741 | E_var:     5.6232 | E_err:   0.037052
[2025-10-03 06:25:08] [Iter  134/2250] R1[116/300]  | LR: 0.021857 | E: -40.691681 | E_var:     5.2833 | E_err:   0.035915
[2025-10-03 06:25:18] [Iter  135/2250] R1[118/300]  | LR: 0.021611 | E: -40.866585 | E_var:     4.9948 | E_err:   0.034920
[2025-10-03 06:25:29] [Iter  136/2250] R1[120/300]  | LR: 0.021363 | E: -40.952743 | E_var:     4.7799 | E_err:   0.034161
[2025-10-03 06:25:39] [Iter  137/2250] R1[122/300]  | LR: 0.021113 | E: -41.098538 | E_var:     4.4196 | E_err:   0.032848
[2025-10-03 06:25:50] [Iter  138/2250] R1[124/300]  | LR: 0.020861 | E: -41.107473 | E_var:     4.3919 | E_err:   0.032745
[2025-10-03 06:26:00] [Iter  139/2250] R1[126/300]  | LR: 0.020609 | E: -41.167661 | E_var:     3.9495 | E_err:   0.031052
[2025-10-03 06:26:11] [Iter  140/2250] R1[128/300]  | LR: 0.020354 | E: -41.267165 | E_var:     3.7991 | E_err:   0.030455
[2025-10-03 06:26:21] [Iter  141/2250] R1[130/300]  | LR: 0.020099 | E: -41.274768 | E_var:     3.5526 | E_err:   0.029451
[2025-10-03 06:26:32] [Iter  142/2250] R1[132/300]  | LR: 0.019842 | E: -41.309384 | E_var:     3.4809 | E_err:   0.029152
[2025-10-03 06:26:42] [Iter  143/2250] R1[134/300]  | LR: 0.019585 | E: -41.379305 | E_var:     3.5340 | E_err:   0.029374
[2025-10-03 06:26:53] [Iter  144/2250] R1[136/300]  | LR: 0.019326 | E: -41.375015 | E_var:     3.4844 | E_err:   0.029166
[2025-10-03 06:27:03] [Iter  145/2250] R1[138/300]  | LR: 0.019067 | E: -41.470172 | E_var:     3.2389 | E_err:   0.028120
[2025-10-03 06:27:14] [Iter  146/2250] R1[140/300]  | LR: 0.018807 | E: -41.513099 | E_var:     2.9068 | E_err:   0.026640
[2025-10-03 06:27:24] [Iter  147/2250] R1[142/300]  | LR: 0.018546 | E: -41.508454 | E_var:     3.2076 | E_err:   0.027984
[2025-10-03 06:27:35] [Iter  148/2250] R1[144/300]  | LR: 0.018285 | E: -41.524623 | E_var:     2.7607 | E_err:   0.025962
[2025-10-03 06:27:45] [Iter  149/2250] R1[146/300]  | LR: 0.018023 | E: -41.576313 | E_var:     2.7853 | E_err:   0.026077
[2025-10-03 06:27:56] [Iter  150/2250] R1[148/300]  | LR: 0.017762 | E: -41.612603 | E_var:     2.7896 | E_err:   0.026097
[2025-10-03 06:28:07] [Iter  151/2250] R1[150/300]  | LR: 0.017500 | E: -41.606460 | E_var:     2.5707 | E_err:   0.025052
[2025-10-03 06:28:17] [Iter  152/2250] R1[152/300]  | LR: 0.017238 | E: -41.610235 | E_var:     2.3770 | E_err:   0.024090
[2025-10-03 06:28:28] [Iter  153/2250] R1[154/300]  | LR: 0.016977 | E: -41.649569 | E_var:     2.3320 | E_err:   0.023861
[2025-10-03 06:28:38] [Iter  154/2250] R1[156/300]  | LR: 0.016715 | E: -41.643915 | E_var:     2.5419 | E_err:   0.024911
[2025-10-03 06:28:49] [Iter  155/2250] R1[158/300]  | LR: 0.016454 | E: -41.728695 | E_var:     2.5601 | E_err:   0.025001
[2025-10-03 06:28:59] [Iter  156/2250] R1[160/300]  | LR: 0.016193 | E: -41.705254 | E_var:     2.3037 | E_err:   0.023715
[2025-10-03 06:29:10] [Iter  157/2250] R1[162/300]  | LR: 0.015933 | E: -41.698608 | E_var:     2.6548 | E_err:   0.025459
[2025-10-03 06:29:20] [Iter  158/2250] R1[164/300]  | LR: 0.015674 | E: -41.749443 | E_var:     2.1395 | E_err:   0.022855
[2025-10-03 06:29:31] [Iter  159/2250] R1[166/300]  | LR: 0.015415 | E: -41.761576 | E_var:     2.1964 | E_err:   0.023157
[2025-10-03 06:29:41] [Iter  160/2250] R1[168/300]  | LR: 0.015158 | E: -41.788055 | E_var:     1.6635 | E_err:   0.020152
[2025-10-03 06:29:52] [Iter  161/2250] R1[170/300]  | LR: 0.014901 | E: -41.767222 | E_var:     2.0960 | E_err:   0.022621
[2025-10-03 06:30:02] [Iter  162/2250] R1[172/300]  | LR: 0.014646 | E: -41.793052 | E_var:     2.0864 | E_err:   0.022570
[2025-10-03 06:30:13] [Iter  163/2250] R1[174/300]  | LR: 0.014391 | E: -41.800548 | E_var:     1.9269 | E_err:   0.021690
[2025-10-03 06:30:23] [Iter  164/2250] R1[176/300]  | LR: 0.014139 | E: -41.817824 | E_var:     2.0150 | E_err:   0.022180
[2025-10-03 06:30:34] [Iter  165/2250] R1[178/300]  | LR: 0.013887 | E: -41.883691 | E_var:     1.5455 | E_err:   0.019425
[2025-10-03 06:30:44] [Iter  166/2250] R1[180/300]  | LR: 0.013637 | E: -41.840721 | E_var:     1.8220 | E_err:   0.021091
[2025-10-03 06:30:55] [Iter  167/2250] R1[182/300]  | LR: 0.013389 | E: -41.842179 | E_var:     2.4154 | E_err:   0.024284
[2025-10-03 06:31:05] [Iter  168/2250] R1[184/300]  | LR: 0.013143 | E: -41.863593 | E_var:     1.7335 | E_err:   0.020572
[2025-10-03 06:31:16] [Iter  169/2250] R1[186/300]  | LR: 0.012898 | E: -41.821489 | E_var:     1.9499 | E_err:   0.021819
[2025-10-03 06:31:26] [Iter  170/2250] R1[188/300]  | LR: 0.012656 | E: -41.878335 | E_var:     1.5688 | E_err:   0.019571
[2025-10-03 06:31:37] [Iter  171/2250] R1[190/300]  | LR: 0.012416 | E: -41.886960 | E_var:     2.2667 | E_err:   0.023524
[2025-10-03 06:31:48] [Iter  172/2250] R1[192/300]  | LR: 0.012178 | E: -41.904269 | E_var:     1.6962 | E_err:   0.020350
[2025-10-03 06:31:58] [Iter  173/2250] R1[194/300]  | LR: 0.011942 | E: -41.901772 | E_var:     1.7619 | E_err:   0.020740
[2025-10-03 06:32:09] [Iter  174/2250] R1[196/300]  | LR: 0.011709 | E: -41.903986 | E_var:     1.5702 | E_err:   0.019579
[2025-10-03 06:32:19] [Iter  175/2250] R1[198/300]  | LR: 0.011478 | E: -41.903718 | E_var:     1.6627 | E_err:   0.020148
[2025-10-03 06:32:30] [Iter  176/2250] R1[200/300]  | LR: 0.011250 | E: -41.917682 | E_var:     1.3748 | E_err:   0.018320
[2025-10-03 06:32:40] [Iter  177/2250] R1[202/300]  | LR: 0.011025 | E: -41.949325 | E_var:     3.1190 | E_err:   0.027595
[2025-10-03 06:32:51] [Iter  178/2250] R1[204/300]  | LR: 0.010802 | E: -41.908151 | E_var:     2.2123 | E_err:   0.023240
[2025-10-03 06:33:01] [Iter  179/2250] R1[206/300]  | LR: 0.010583 | E: -41.908835 | E_var:     1.7192 | E_err:   0.020487
[2025-10-03 06:33:12] [Iter  180/2250] R1[208/300]  | LR: 0.010366 | E: -41.946219 | E_var:     1.2905 | E_err:   0.017750
[2025-10-03 06:33:22] [Iter  181/2250] R1[210/300]  | LR: 0.010153 | E: -41.924863 | E_var:     1.7553 | E_err:   0.020701
[2025-10-03 06:33:33] [Iter  182/2250] R1[212/300]  | LR: 0.009943 | E: -41.945715 | E_var:     1.3082 | E_err:   0.017871
[2025-10-03 06:33:43] [Iter  183/2250] R1[214/300]  | LR: 0.009736 | E: -41.983363 | E_var:     1.8337 | E_err:   0.021159
[2025-10-03 06:33:54] [Iter  184/2250] R1[216/300]  | LR: 0.009532 | E: -41.985001 | E_var:     1.2783 | E_err:   0.017666
[2025-10-03 06:34:04] [Iter  185/2250] R1[218/300]  | LR: 0.009332 | E: -41.960327 | E_var:     1.2266 | E_err:   0.017305
[2025-10-03 06:34:15] [Iter  186/2250] R1[220/300]  | LR: 0.009136 | E: -41.966341 | E_var:     2.1627 | E_err:   0.022978
[2025-10-03 06:34:25] [Iter  187/2250] R1[222/300]  | LR: 0.008943 | E: -42.004036 | E_var:     1.3966 | E_err:   0.018465
[2025-10-03 06:34:36] [Iter  188/2250] R1[224/300]  | LR: 0.008754 | E: -41.965513 | E_var:     1.1600 | E_err:   0.016829
[2025-10-03 06:34:46] [Iter  189/2250] R1[226/300]  | LR: 0.008569 | E: -41.975054 | E_var:     1.3288 | E_err:   0.018012
[2025-10-03 06:34:57] [Iter  190/2250] R1[228/300]  | LR: 0.008388 | E: -41.990690 | E_var:     1.4607 | E_err:   0.018884
[2025-10-03 06:35:07] [Iter  191/2250] R1[230/300]  | LR: 0.008211 | E: -41.997242 | E_var:     1.4722 | E_err:   0.018959
[2025-10-03 06:35:18] [Iter  192/2250] R1[232/300]  | LR: 0.008038 | E: -42.009164 | E_var:     1.5564 | E_err:   0.019493
[2025-10-03 06:35:29] [Iter  193/2250] R1[234/300]  | LR: 0.007869 | E: -41.998156 | E_var:     1.1382 | E_err:   0.016670
[2025-10-03 06:35:39] [Iter  194/2250] R1[236/300]  | LR: 0.007704 | E: -42.001160 | E_var:     1.2780 | E_err:   0.017664
[2025-10-03 06:35:50] [Iter  195/2250] R1[238/300]  | LR: 0.007543 | E: -42.012766 | E_var:     1.2611 | E_err:   0.017547
[2025-10-03 06:36:00] [Iter  196/2250] R1[240/300]  | LR: 0.007387 | E: -42.033995 | E_var:     1.1525 | E_err:   0.016774
[2025-10-03 06:36:11] [Iter  197/2250] R1[242/300]  | LR: 0.007236 | E: -42.046846 | E_var:     1.7250 | E_err:   0.020522
[2025-10-03 06:36:21] [Iter  198/2250] R1[244/300]  | LR: 0.007088 | E: -42.053281 | E_var:     1.0089 | E_err:   0.015695
[2025-10-03 06:36:32] [Iter  199/2250] R1[246/300]  | LR: 0.006946 | E: -42.045916 | E_var:     1.0703 | E_err:   0.016165
[2025-10-03 06:36:42] [Iter  200/2250] R1[248/300]  | LR: 0.006808 | E: -42.068163 | E_var:     1.0819 | E_err:   0.016252
[2025-10-03 06:36:42] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-03 06:36:53] [Iter  201/2250] R1[250/300]  | LR: 0.006675 | E: -42.024666 | E_var:     1.1998 | E_err:   0.017115
[2025-10-03 06:37:03] [Iter  202/2250] R1[252/300]  | LR: 0.006546 | E: -42.057459 | E_var:     0.9777 | E_err:   0.015450
[2025-10-03 06:37:14] [Iter  203/2250] R1[254/300]  | LR: 0.006422 | E: -42.056796 | E_var:     1.0876 | E_err:   0.016295
[2025-10-03 06:37:24] [Iter  204/2250] R1[256/300]  | LR: 0.006304 | E: -42.066083 | E_var:     1.0008 | E_err:   0.015631
[2025-10-03 06:37:35] [Iter  205/2250] R1[258/300]  | LR: 0.006190 | E: -42.057021 | E_var:     0.9581 | E_err:   0.015294
[2025-10-03 06:37:45] [Iter  206/2250] R1[260/300]  | LR: 0.006081 | E: -42.066334 | E_var:     0.8126 | E_err:   0.014085
[2025-10-03 06:37:56] [Iter  207/2250] R1[262/300]  | LR: 0.005977 | E: -42.077481 | E_var:     1.1304 | E_err:   0.016613
[2025-10-03 06:38:06] [Iter  208/2250] R1[264/300]  | LR: 0.005878 | E: -42.072951 | E_var:     1.5483 | E_err:   0.019442
[2025-10-03 06:38:17] [Iter  209/2250] R1[266/300]  | LR: 0.005784 | E: -42.062482 | E_var:     0.9499 | E_err:   0.015228
[2025-10-03 06:38:27] [Iter  210/2250] R1[268/300]  | LR: 0.005695 | E: -42.105688 | E_var:     1.1938 | E_err:   0.017072
[2025-10-03 06:38:38] [Iter  211/2250] R1[270/300]  | LR: 0.005612 | E: -42.105091 | E_var:     0.8553 | E_err:   0.014451
[2025-10-03 06:38:48] [Iter  212/2250] R1[272/300]  | LR: 0.005534 | E: -42.115359 | E_var:     1.3527 | E_err:   0.018173
[2025-10-03 06:38:59] [Iter  213/2250] R1[274/300]  | LR: 0.005460 | E: -42.077227 | E_var:     1.2669 | E_err:   0.017587
[2025-10-03 06:39:09] [Iter  214/2250] R1[276/300]  | LR: 0.005393 | E: -42.111439 | E_var:     1.1210 | E_err:   0.016544
[2025-10-03 06:39:20] [Iter  215/2250] R1[278/300]  | LR: 0.005330 | E: -42.105721 | E_var:     0.9267 | E_err:   0.015041
[2025-10-03 06:39:31] [Iter  216/2250] R1[280/300]  | LR: 0.005273 | E: -42.100994 | E_var:     0.9565 | E_err:   0.015281
[2025-10-03 06:39:41] [Iter  217/2250] R1[282/300]  | LR: 0.005221 | E: -42.132449 | E_var:     0.9222 | E_err:   0.015005
[2025-10-03 06:39:52] [Iter  218/2250] R1[284/300]  | LR: 0.005175 | E: -42.121834 | E_var:     0.9483 | E_err:   0.015215
[2025-10-03 06:40:02] [Iter  219/2250] R1[286/300]  | LR: 0.005134 | E: -42.098111 | E_var:     0.9461 | E_err:   0.015198
[2025-10-03 06:40:13] [Iter  220/2250] R1[288/300]  | LR: 0.005099 | E: -42.115365 | E_var:     0.8172 | E_err:   0.014125
[2025-10-03 06:40:23] [Iter  221/2250] R1[290/300]  | LR: 0.005068 | E: -42.124035 | E_var:     0.7797 | E_err:   0.013797
[2025-10-03 06:40:34] [Iter  222/2250] R1[292/300]  | LR: 0.005044 | E: -42.125065 | E_var:     1.0753 | E_err:   0.016203
[2025-10-03 06:40:44] [Iter  223/2250] R1[294/300]  | LR: 0.005025 | E: -42.111770 | E_var:     0.8287 | E_err:   0.014224
[2025-10-03 06:40:55] [Iter  224/2250] R1[296/300]  | LR: 0.005011 | E: -42.150652 | E_var:     1.4332 | E_err:   0.018706
[2025-10-03 06:41:05] [Iter  225/2250] R1[298/300]  | LR: 0.005003 | E: -42.161951 | E_var:     0.7377 | E_err:   0.013421
[2025-10-03 06:41:05] 🔄 RESTART #2 | Period: 600
[2025-10-03 06:41:16] [Iter  226/2250] R2[0/600]    | LR: 0.030000 | E: -42.147726 | E_var:     0.8273 | E_err:   0.014212
[2025-10-03 06:41:26] [Iter  227/2250] R2[2/600]    | LR: 0.029999 | E: -42.153010 | E_var:     0.8855 | E_err:   0.014703
[2025-10-03 06:41:37] [Iter  228/2250] R2[4/600]    | LR: 0.029997 | E: -42.159459 | E_var:     0.8498 | E_err:   0.014404
[2025-10-03 06:41:47] [Iter  229/2250] R2[6/600]    | LR: 0.029994 | E: -42.152529 | E_var:     0.9092 | E_err:   0.014899
[2025-10-03 06:41:58] [Iter  230/2250] R2[8/600]    | LR: 0.029989 | E: -42.167023 | E_var:     1.5090 | E_err:   0.019194
[2025-10-03 06:42:08] [Iter  231/2250] R2[10/600]   | LR: 0.029983 | E: -42.161191 | E_var:     0.8321 | E_err:   0.014253
[2025-10-03 06:42:19] [Iter  232/2250] R2[12/600]   | LR: 0.029975 | E: -42.137272 | E_var:     0.7173 | E_err:   0.013233
[2025-10-03 06:42:29] [Iter  233/2250] R2[14/600]   | LR: 0.029966 | E: -42.136192 | E_var:     0.7768 | E_err:   0.013771
[2025-10-03 06:42:40] [Iter  234/2250] R2[16/600]   | LR: 0.029956 | E: -42.166349 | E_var:     0.7225 | E_err:   0.013281
[2025-10-03 06:42:50] [Iter  235/2250] R2[18/600]   | LR: 0.029945 | E: -42.161239 | E_var:     0.8625 | E_err:   0.014511
[2025-10-03 06:43:01] [Iter  236/2250] R2[20/600]   | LR: 0.029932 | E: -42.151799 | E_var:     0.8141 | E_err:   0.014098
[2025-10-03 06:43:11] [Iter  237/2250] R2[22/600]   | LR: 0.029917 | E: -42.150320 | E_var:     0.7772 | E_err:   0.013775
[2025-10-03 06:43:22] [Iter  238/2250] R2[24/600]   | LR: 0.029901 | E: -42.168486 | E_var:     0.9386 | E_err:   0.015138
[2025-10-03 06:43:33] [Iter  239/2250] R2[26/600]   | LR: 0.029884 | E: -42.155227 | E_var:     0.8388 | E_err:   0.014310
[2025-10-03 06:43:43] [Iter  240/2250] R2[28/600]   | LR: 0.029866 | E: -42.184763 | E_var:     2.3887 | E_err:   0.024149
[2025-10-03 06:43:54] [Iter  241/2250] R2[30/600]   | LR: 0.029846 | E: -42.181282 | E_var:     0.6405 | E_err:   0.012505
[2025-10-03 06:44:04] [Iter  242/2250] R2[32/600]   | LR: 0.029825 | E: -42.177379 | E_var:     0.7653 | E_err:   0.013669
[2025-10-03 06:44:15] [Iter  243/2250] R2[34/600]   | LR: 0.029802 | E: -42.176720 | E_var:     0.7152 | E_err:   0.013214
[2025-10-03 06:44:25] [Iter  244/2250] R2[36/600]   | LR: 0.029779 | E: -42.211034 | E_var:     1.7505 | E_err:   0.020673
[2025-10-03 06:44:36] [Iter  245/2250] R2[38/600]   | LR: 0.029753 | E: -42.176399 | E_var:     0.6538 | E_err:   0.012634
[2025-10-03 06:44:46] [Iter  246/2250] R2[40/600]   | LR: 0.029727 | E: -42.173859 | E_var:     0.6134 | E_err:   0.012238
[2025-10-03 06:44:57] [Iter  247/2250] R2[42/600]   | LR: 0.029699 | E: -42.196973 | E_var:     0.9731 | E_err:   0.015413
[2025-10-03 06:45:07] [Iter  248/2250] R2[44/600]   | LR: 0.029670 | E: -42.194996 | E_var:     0.6508 | E_err:   0.012605
[2025-10-03 06:45:18] [Iter  249/2250] R2[46/600]   | LR: 0.029639 | E: -42.205937 | E_var:     0.6662 | E_err:   0.012753
[2025-10-03 06:45:28] [Iter  250/2250] R2[48/600]   | LR: 0.029607 | E: -42.193009 | E_var:     0.7728 | E_err:   0.013736
[2025-10-03 06:45:39] [Iter  251/2250] R2[50/600]   | LR: 0.029574 | E: -42.182553 | E_var:     0.7999 | E_err:   0.013975
[2025-10-03 06:45:49] [Iter  252/2250] R2[52/600]   | LR: 0.029540 | E: -42.215860 | E_var:     0.9755 | E_err:   0.015432
[2025-10-03 06:46:00] [Iter  253/2250] R2[54/600]   | LR: 0.029504 | E: -42.197845 | E_var:     0.6379 | E_err:   0.012480
[2025-10-03 06:46:10] [Iter  254/2250] R2[56/600]   | LR: 0.029466 | E: -42.199179 | E_var:     0.6096 | E_err:   0.012200
[2025-10-03 06:46:21] [Iter  255/2250] R2[58/600]   | LR: 0.029428 | E: -42.231288 | E_var:     0.6384 | E_err:   0.012484
[2025-10-03 06:46:31] [Iter  256/2250] R2[60/600]   | LR: 0.029388 | E: -42.208641 | E_var:     0.6172 | E_err:   0.012275
[2025-10-03 06:46:42] [Iter  257/2250] R2[62/600]   | LR: 0.029347 | E: -42.194730 | E_var:     0.8214 | E_err:   0.014161
[2025-10-03 06:46:52] [Iter  258/2250] R2[64/600]   | LR: 0.029305 | E: -42.216623 | E_var:     0.6658 | E_err:   0.012749
[2025-10-03 06:47:03] [Iter  259/2250] R2[66/600]   | LR: 0.029261 | E: -42.208954 | E_var:     0.5751 | E_err:   0.011849
[2025-10-03 06:47:14] [Iter  260/2250] R2[68/600]   | LR: 0.029216 | E: -42.235994 | E_var:     0.6765 | E_err:   0.012852
[2025-10-03 06:47:24] [Iter  261/2250] R2[70/600]   | LR: 0.029170 | E: -42.193811 | E_var:     0.6945 | E_err:   0.013021
[2025-10-03 06:47:35] [Iter  262/2250] R2[72/600]   | LR: 0.029122 | E: -42.214607 | E_var:     0.6575 | E_err:   0.012670
[2025-10-03 06:47:45] [Iter  263/2250] R2[74/600]   | LR: 0.029073 | E: -42.173213 | E_var:     0.9357 | E_err:   0.015114
[2025-10-03 06:47:56] [Iter  264/2250] R2[76/600]   | LR: 0.029023 | E: -42.189827 | E_var:     0.6249 | E_err:   0.012352
[2025-10-03 06:48:06] [Iter  265/2250] R2[78/600]   | LR: 0.028972 | E: -42.223496 | E_var:     0.6189 | E_err:   0.012292
[2025-10-03 06:48:17] [Iter  266/2250] R2[80/600]   | LR: 0.028919 | E: -42.234633 | E_var:     0.6550 | E_err:   0.012645
[2025-10-03 06:48:27] [Iter  267/2250] R2[82/600]   | LR: 0.028865 | E: -42.224117 | E_var:     0.9928 | E_err:   0.015569
[2025-10-03 06:48:38] [Iter  268/2250] R2[84/600]   | LR: 0.028810 | E: -42.200968 | E_var:     0.6160 | E_err:   0.012264
[2025-10-03 06:48:48] [Iter  269/2250] R2[86/600]   | LR: 0.028754 | E: -42.214369 | E_var:     0.7462 | E_err:   0.013498
[2025-10-03 06:48:59] [Iter  270/2250] R2[88/600]   | LR: 0.028696 | E: -42.225358 | E_var:     0.8399 | E_err:   0.014319
[2025-10-03 06:49:09] [Iter  271/2250] R2[90/600]   | LR: 0.028638 | E: -42.204132 | E_var:     0.6887 | E_err:   0.012967
[2025-10-03 06:49:20] [Iter  272/2250] R2[92/600]   | LR: 0.028578 | E: -42.223825 | E_var:     0.6352 | E_err:   0.012453
[2025-10-03 06:49:30] [Iter  273/2250] R2[94/600]   | LR: 0.028516 | E: -42.199356 | E_var:     0.7423 | E_err:   0.013462
[2025-10-03 06:49:41] [Iter  274/2250] R2[96/600]   | LR: 0.028454 | E: -42.219287 | E_var:     0.5932 | E_err:   0.012034
[2025-10-03 06:49:51] [Iter  275/2250] R2[98/600]   | LR: 0.028390 | E: -42.233488 | E_var:     0.7340 | E_err:   0.013387
[2025-10-03 06:50:02] [Iter  276/2250] R2[100/600]  | LR: 0.028325 | E: -42.211526 | E_var:     0.8586 | E_err:   0.014478
[2025-10-03 06:50:12] [Iter  277/2250] R2[102/600]  | LR: 0.028259 | E: -42.215183 | E_var:     0.6983 | E_err:   0.013057
[2025-10-03 06:50:23] [Iter  278/2250] R2[104/600]  | LR: 0.028192 | E: -42.217410 | E_var:     0.7057 | E_err:   0.013126
[2025-10-03 06:50:33] [Iter  279/2250] R2[106/600]  | LR: 0.028124 | E: -42.225155 | E_var:     0.7831 | E_err:   0.013827
[2025-10-03 06:50:44] [Iter  280/2250] R2[108/600]  | LR: 0.028054 | E: -42.218182 | E_var:     0.6571 | E_err:   0.012666
[2025-10-03 06:50:54] [Iter  281/2250] R2[110/600]  | LR: 0.027983 | E: -42.231089 | E_var:     0.6676 | E_err:   0.012766
[2025-10-03 06:51:05] [Iter  282/2250] R2[112/600]  | LR: 0.027912 | E: -42.222738 | E_var:     0.5322 | E_err:   0.011399
[2025-10-03 06:51:15] [Iter  283/2250] R2[114/600]  | LR: 0.027839 | E: -42.222767 | E_var:     0.6245 | E_err:   0.012348
[2025-10-03 06:51:26] [Iter  284/2250] R2[116/600]  | LR: 0.027764 | E: -42.241690 | E_var:     0.6108 | E_err:   0.012212
[2025-10-03 06:51:37] [Iter  285/2250] R2[118/600]  | LR: 0.027689 | E: -42.228769 | E_var:     0.5226 | E_err:   0.011295
[2025-10-03 06:51:47] [Iter  286/2250] R2[120/600]  | LR: 0.027613 | E: -42.241576 | E_var:     0.5900 | E_err:   0.012002
[2025-10-03 06:51:58] [Iter  287/2250] R2[122/600]  | LR: 0.027535 | E: -42.238337 | E_var:     0.6871 | E_err:   0.012952
[2025-10-03 06:52:08] [Iter  288/2250] R2[124/600]  | LR: 0.027457 | E: -42.247500 | E_var:     0.7293 | E_err:   0.013343
[2025-10-03 06:52:19] [Iter  289/2250] R2[126/600]  | LR: 0.027377 | E: -42.243174 | E_var:     0.6549 | E_err:   0.012645
[2025-10-03 06:52:29] [Iter  290/2250] R2[128/600]  | LR: 0.027296 | E: -42.236315 | E_var:     1.6096 | E_err:   0.019823
[2025-10-03 06:52:40] [Iter  291/2250] R2[130/600]  | LR: 0.027214 | E: -42.227882 | E_var:     0.5952 | E_err:   0.012054
[2025-10-03 06:52:50] [Iter  292/2250] R2[132/600]  | LR: 0.027131 | E: -42.240203 | E_var:     0.7094 | E_err:   0.013161
[2025-10-03 06:53:01] [Iter  293/2250] R2[134/600]  | LR: 0.027047 | E: -42.247325 | E_var:     0.5168 | E_err:   0.011232
[2025-10-03 06:53:11] [Iter  294/2250] R2[136/600]  | LR: 0.026962 | E: -42.263065 | E_var:     0.7576 | E_err:   0.013600
[2025-10-03 06:53:22] [Iter  295/2250] R2[138/600]  | LR: 0.026876 | E: -42.251496 | E_var:     0.5969 | E_err:   0.012072
[2025-10-03 06:53:32] [Iter  296/2250] R2[140/600]  | LR: 0.026789 | E: -42.236293 | E_var:     0.6139 | E_err:   0.012243
[2025-10-03 06:53:43] [Iter  297/2250] R2[142/600]  | LR: 0.026701 | E: -42.279250 | E_var:     0.6578 | E_err:   0.012673
[2025-10-03 06:53:53] [Iter  298/2250] R2[144/600]  | LR: 0.026612 | E: -42.260421 | E_var:     0.5791 | E_err:   0.011891
[2025-10-03 06:54:04] [Iter  299/2250] R2[146/600]  | LR: 0.026522 | E: -42.248393 | E_var:     0.5590 | E_err:   0.011683
[2025-10-03 06:54:14] [Iter  300/2250] R2[148/600]  | LR: 0.026431 | E: -42.251298 | E_var:     0.8080 | E_err:   0.014045
[2025-10-03 06:54:25] [Iter  301/2250] R2[150/600]  | LR: 0.026339 | E: -42.264278 | E_var:     0.5311 | E_err:   0.011387
[2025-10-03 06:54:35] [Iter  302/2250] R2[152/600]  | LR: 0.026246 | E: -42.257714 | E_var:     0.7065 | E_err:   0.013134
[2025-10-03 06:54:46] [Iter  303/2250] R2[154/600]  | LR: 0.026152 | E: -42.262213 | E_var:     0.5583 | E_err:   0.011675
[2025-10-03 06:54:56] [Iter  304/2250] R2[156/600]  | LR: 0.026057 | E: -42.263322 | E_var:     0.6056 | E_err:   0.012159
[2025-10-03 06:55:07] [Iter  305/2250] R2[158/600]  | LR: 0.025961 | E: -42.263625 | E_var:     0.5426 | E_err:   0.011509
[2025-10-03 06:55:17] [Iter  306/2250] R2[160/600]  | LR: 0.025864 | E: -42.262764 | E_var:     0.5724 | E_err:   0.011821
[2025-10-03 06:55:28] [Iter  307/2250] R2[162/600]  | LR: 0.025766 | E: -42.255174 | E_var:     0.7621 | E_err:   0.013640
[2025-10-03 06:55:39] [Iter  308/2250] R2[164/600]  | LR: 0.025668 | E: -42.255661 | E_var:     0.5693 | E_err:   0.011790
[2025-10-03 06:55:49] [Iter  309/2250] R2[166/600]  | LR: 0.025568 | E: -42.259471 | E_var:     0.6850 | E_err:   0.012932
[2025-10-03 06:56:00] [Iter  310/2250] R2[168/600]  | LR: 0.025468 | E: -42.267899 | E_var:     0.5460 | E_err:   0.011545
[2025-10-03 06:56:10] [Iter  311/2250] R2[170/600]  | LR: 0.025367 | E: -42.244118 | E_var:     0.5721 | E_err:   0.011818
[2025-10-03 06:56:21] [Iter  312/2250] R2[172/600]  | LR: 0.025264 | E: -42.269161 | E_var:     0.4853 | E_err:   0.010885
[2025-10-03 06:56:31] [Iter  313/2250] R2[174/600]  | LR: 0.025161 | E: -42.262167 | E_var:     0.6969 | E_err:   0.013044
[2025-10-03 06:56:42] [Iter  314/2250] R2[176/600]  | LR: 0.025057 | E: -42.262340 | E_var:     0.7598 | E_err:   0.013619
[2025-10-03 06:56:52] [Iter  315/2250] R2[178/600]  | LR: 0.024953 | E: -42.266538 | E_var:     0.5595 | E_err:   0.011687
[2025-10-03 06:57:03] [Iter  316/2250] R2[180/600]  | LR: 0.024847 | E: -42.254527 | E_var:     0.7402 | E_err:   0.013443
[2025-10-03 06:57:13] [Iter  317/2250] R2[182/600]  | LR: 0.024741 | E: -42.258059 | E_var:     0.5848 | E_err:   0.011949
[2025-10-03 06:57:24] [Iter  318/2250] R2[184/600]  | LR: 0.024634 | E: -42.263943 | E_var:     0.7645 | E_err:   0.013662
[2025-10-03 06:57:34] [Iter  319/2250] R2[186/600]  | LR: 0.024526 | E: -42.266745 | E_var:     0.5125 | E_err:   0.011185
[2025-10-03 06:57:45] [Iter  320/2250] R2[188/600]  | LR: 0.024417 | E: -42.258616 | E_var:     0.5848 | E_err:   0.011949
[2025-10-03 06:57:55] [Iter  321/2250] R2[190/600]  | LR: 0.024308 | E: -42.268448 | E_var:     1.1280 | E_err:   0.016595
[2025-10-03 06:58:06] [Iter  322/2250] R2[192/600]  | LR: 0.024198 | E: -42.259178 | E_var:     0.5541 | E_err:   0.011630
[2025-10-03 06:58:16] [Iter  323/2250] R2[194/600]  | LR: 0.024087 | E: -42.255532 | E_var:     0.6308 | E_err:   0.012410
[2025-10-03 06:58:27] [Iter  324/2250] R2[196/600]  | LR: 0.023975 | E: -42.277723 | E_var:     0.5914 | E_err:   0.012016
[2025-10-03 06:58:37] [Iter  325/2250] R2[198/600]  | LR: 0.023863 | E: -42.278532 | E_var:     0.5443 | E_err:   0.011527
[2025-10-03 06:58:48] [Iter  326/2250] R2[200/600]  | LR: 0.023750 | E: -42.262132 | E_var:     0.5551 | E_err:   0.011642
[2025-10-03 06:58:58] [Iter  327/2250] R2[202/600]  | LR: 0.023636 | E: -42.278201 | E_var:     0.5158 | E_err:   0.011222
[2025-10-03 06:59:09] [Iter  328/2250] R2[204/600]  | LR: 0.023522 | E: -42.284210 | E_var:     0.5556 | E_err:   0.011647
[2025-10-03 06:59:19] [Iter  329/2250] R2[206/600]  | LR: 0.023407 | E: -42.271994 | E_var:     0.5628 | E_err:   0.011722
[2025-10-03 06:59:30] [Iter  330/2250] R2[208/600]  | LR: 0.023291 | E: -42.270130 | E_var:     0.5546 | E_err:   0.011636
[2025-10-03 06:59:40] [Iter  331/2250] R2[210/600]  | LR: 0.023175 | E: -42.278031 | E_var:     0.4938 | E_err:   0.010980
[2025-10-03 06:59:51] [Iter  332/2250] R2[212/600]  | LR: 0.023058 | E: -42.279039 | E_var:     0.5600 | E_err:   0.011693
[2025-10-03 07:00:02] [Iter  333/2250] R2[214/600]  | LR: 0.022940 | E: -42.280409 | E_var:     0.4898 | E_err:   0.010935
[2025-10-03 07:00:12] [Iter  334/2250] R2[216/600]  | LR: 0.022822 | E: -42.257320 | E_var:     0.4967 | E_err:   0.011013
[2025-10-03 07:00:23] [Iter  335/2250] R2[218/600]  | LR: 0.022704 | E: -42.266304 | E_var:     0.5422 | E_err:   0.011506
[2025-10-03 07:00:33] [Iter  336/2250] R2[220/600]  | LR: 0.022584 | E: -42.271999 | E_var:     0.5989 | E_err:   0.012092
[2025-10-03 07:00:44] [Iter  337/2250] R2[222/600]  | LR: 0.022464 | E: -42.278936 | E_var:     0.5399 | E_err:   0.011481
[2025-10-03 07:00:54] [Iter  338/2250] R2[224/600]  | LR: 0.022344 | E: -42.276715 | E_var:     0.5932 | E_err:   0.012034
[2025-10-03 07:01:05] [Iter  339/2250] R2[226/600]  | LR: 0.022223 | E: -42.262136 | E_var:     0.5786 | E_err:   0.011885
[2025-10-03 07:01:15] [Iter  340/2250] R2[228/600]  | LR: 0.022102 | E: -42.267344 | E_var:     0.6651 | E_err:   0.012742
[2025-10-03 07:01:26] [Iter  341/2250] R2[230/600]  | LR: 0.021980 | E: -42.272152 | E_var:     0.5153 | E_err:   0.011217
[2025-10-03 07:01:36] [Iter  342/2250] R2[232/600]  | LR: 0.021857 | E: -42.276571 | E_var:     0.8160 | E_err:   0.014115
[2025-10-03 07:01:47] [Iter  343/2250] R2[234/600]  | LR: 0.021734 | E: -42.244369 | E_var:     0.5020 | E_err:   0.011071
[2025-10-03 07:01:57] [Iter  344/2250] R2[236/600]  | LR: 0.021611 | E: -42.282372 | E_var:     0.7572 | E_err:   0.013597
[2025-10-03 07:02:08] [Iter  345/2250] R2[238/600]  | LR: 0.021487 | E: -42.272396 | E_var:     0.5574 | E_err:   0.011666
[2025-10-03 07:02:18] [Iter  346/2250] R2[240/600]  | LR: 0.021363 | E: -42.284149 | E_var:     0.4944 | E_err:   0.010987
[2025-10-03 07:02:29] [Iter  347/2250] R2[242/600]  | LR: 0.021238 | E: -42.273975 | E_var:     0.5156 | E_err:   0.011220
[2025-10-03 07:02:39] [Iter  348/2250] R2[244/600]  | LR: 0.021113 | E: -42.267020 | E_var:     0.5153 | E_err:   0.011216
[2025-10-03 07:02:50] [Iter  349/2250] R2[246/600]  | LR: 0.020987 | E: -42.261808 | E_var:     0.5903 | E_err:   0.012005
[2025-10-03 07:03:00] [Iter  350/2250] R2[248/600]  | LR: 0.020861 | E: -42.258457 | E_var:     0.5506 | E_err:   0.011594
[2025-10-03 07:03:11] [Iter  351/2250] R2[250/600]  | LR: 0.020735 | E: -42.285109 | E_var:     0.5031 | E_err:   0.011082
[2025-10-03 07:03:21] [Iter  352/2250] R2[252/600]  | LR: 0.020609 | E: -42.291421 | E_var:     0.5567 | E_err:   0.011658
[2025-10-03 07:03:32] [Iter  353/2250] R2[254/600]  | LR: 0.020482 | E: -42.261752 | E_var:     0.5456 | E_err:   0.011541
[2025-10-03 07:03:43] [Iter  354/2250] R2[256/600]  | LR: 0.020354 | E: -42.278167 | E_var:     0.6060 | E_err:   0.012164
[2025-10-03 07:03:53] [Iter  355/2250] R2[258/600]  | LR: 0.020227 | E: -42.276860 | E_var:     1.3572 | E_err:   0.018203
[2025-10-03 07:04:04] [Iter  356/2250] R2[260/600]  | LR: 0.020099 | E: -42.269644 | E_var:     0.4602 | E_err:   0.010600
[2025-10-03 07:04:14] [Iter  357/2250] R2[262/600]  | LR: 0.019971 | E: -42.292665 | E_var:     0.5631 | E_err:   0.011725
[2025-10-03 07:04:25] [Iter  358/2250] R2[264/600]  | LR: 0.019842 | E: -42.271243 | E_var:     0.4935 | E_err:   0.010977
[2025-10-03 07:04:35] [Iter  359/2250] R2[266/600]  | LR: 0.019714 | E: -42.280453 | E_var:     0.5651 | E_err:   0.011746
[2025-10-03 07:04:46] [Iter  360/2250] R2[268/600]  | LR: 0.019585 | E: -42.265168 | E_var:     0.5129 | E_err:   0.011190
[2025-10-03 07:04:56] [Iter  361/2250] R2[270/600]  | LR: 0.019455 | E: -42.271313 | E_var:     0.4764 | E_err:   0.010785
[2025-10-03 07:05:07] [Iter  362/2250] R2[272/600]  | LR: 0.019326 | E: -42.299232 | E_var:     0.5224 | E_err:   0.011293
[2025-10-03 07:05:17] [Iter  363/2250] R2[274/600]  | LR: 0.019196 | E: -42.274834 | E_var:     0.4857 | E_err:   0.010890
[2025-10-03 07:05:28] [Iter  364/2250] R2[276/600]  | LR: 0.019067 | E: -42.270490 | E_var:     0.5649 | E_err:   0.011743
[2025-10-03 07:05:38] [Iter  365/2250] R2[278/600]  | LR: 0.018937 | E: -42.276444 | E_var:     0.4963 | E_err:   0.011008
[2025-10-03 07:05:49] [Iter  366/2250] R2[280/600]  | LR: 0.018807 | E: -42.281898 | E_var:     0.4934 | E_err:   0.010975
[2025-10-03 07:05:59] [Iter  367/2250] R2[282/600]  | LR: 0.018676 | E: -42.268425 | E_var:     0.5631 | E_err:   0.011725
[2025-10-03 07:06:10] [Iter  368/2250] R2[284/600]  | LR: 0.018546 | E: -42.263977 | E_var:     0.4749 | E_err:   0.010767
[2025-10-03 07:06:20] [Iter  369/2250] R2[286/600]  | LR: 0.018415 | E: -42.259735 | E_var:     0.5566 | E_err:   0.011657
[2025-10-03 07:06:31] [Iter  370/2250] R2[288/600]  | LR: 0.018285 | E: -42.291701 | E_var:     0.4599 | E_err:   0.010596
[2025-10-03 07:06:41] [Iter  371/2250] R2[290/600]  | LR: 0.018154 | E: -42.280332 | E_var:     0.5471 | E_err:   0.011557
[2025-10-03 07:06:52] [Iter  372/2250] R2[292/600]  | LR: 0.018023 | E: -42.266408 | E_var:     0.5051 | E_err:   0.011105
[2025-10-03 07:07:02] [Iter  373/2250] R2[294/600]  | LR: 0.017893 | E: -42.263740 | E_var:     0.5489 | E_err:   0.011577
[2025-10-03 07:07:13] [Iter  374/2250] R2[296/600]  | LR: 0.017762 | E: -42.270178 | E_var:     0.4221 | E_err:   0.010151
[2025-10-03 07:07:23] [Iter  375/2250] R2[298/600]  | LR: 0.017631 | E: -42.277225 | E_var:     0.7839 | E_err:   0.013834
[2025-10-03 07:07:34] [Iter  376/2250] R2[300/600]  | LR: 0.017500 | E: -42.289435 | E_var:     0.5011 | E_err:   0.011061
[2025-10-03 07:07:44] [Iter  377/2250] R2[302/600]  | LR: 0.017369 | E: -42.298277 | E_var:     0.4686 | E_err:   0.010696
[2025-10-03 07:07:55] [Iter  378/2250] R2[304/600]  | LR: 0.017238 | E: -42.299174 | E_var:     0.4540 | E_err:   0.010527
[2025-10-03 07:08:06] [Iter  379/2250] R2[306/600]  | LR: 0.017107 | E: -42.298677 | E_var:     0.8149 | E_err:   0.014105
[2025-10-03 07:08:16] [Iter  380/2250] R2[308/600]  | LR: 0.016977 | E: -42.289159 | E_var:     0.4640 | E_err:   0.010644
[2025-10-03 07:08:27] [Iter  381/2250] R2[310/600]  | LR: 0.016846 | E: -42.281794 | E_var:     0.5537 | E_err:   0.011627
[2025-10-03 07:08:37] [Iter  382/2250] R2[312/600]  | LR: 0.016715 | E: -42.307297 | E_var:     0.5124 | E_err:   0.011185
[2025-10-03 07:08:48] [Iter  383/2250] R2[314/600]  | LR: 0.016585 | E: -42.288532 | E_var:     0.6187 | E_err:   0.012290
[2025-10-03 07:08:58] [Iter  384/2250] R2[316/600]  | LR: 0.016454 | E: -42.290858 | E_var:     0.6095 | E_err:   0.012198
[2025-10-03 07:09:09] [Iter  385/2250] R2[318/600]  | LR: 0.016324 | E: -42.300473 | E_var:     0.5124 | E_err:   0.011185
[2025-10-03 07:09:19] [Iter  386/2250] R2[320/600]  | LR: 0.016193 | E: -42.269679 | E_var:     0.5537 | E_err:   0.011627
[2025-10-03 07:09:30] [Iter  387/2250] R2[322/600]  | LR: 0.016063 | E: -42.278840 | E_var:     0.5185 | E_err:   0.011251
[2025-10-03 07:09:40] [Iter  388/2250] R2[324/600]  | LR: 0.015933 | E: -42.284594 | E_var:     0.5626 | E_err:   0.011720
[2025-10-03 07:09:51] [Iter  389/2250] R2[326/600]  | LR: 0.015804 | E: -42.268958 | E_var:     0.5568 | E_err:   0.011659
[2025-10-03 07:10:01] [Iter  390/2250] R2[328/600]  | LR: 0.015674 | E: -42.282805 | E_var:     0.5907 | E_err:   0.012009
[2025-10-03 07:10:12] [Iter  391/2250] R2[330/600]  | LR: 0.015545 | E: -42.283770 | E_var:     0.4458 | E_err:   0.010432
[2025-10-03 07:10:22] [Iter  392/2250] R2[332/600]  | LR: 0.015415 | E: -42.292874 | E_var:     0.5211 | E_err:   0.011279
[2025-10-03 07:10:33] [Iter  393/2250] R2[334/600]  | LR: 0.015286 | E: -42.273338 | E_var:     0.4710 | E_err:   0.010723
[2025-10-03 07:10:43] [Iter  394/2250] R2[336/600]  | LR: 0.015158 | E: -42.289585 | E_var:     0.4926 | E_err:   0.010967
[2025-10-03 07:10:54] [Iter  395/2250] R2[338/600]  | LR: 0.015029 | E: -42.277088 | E_var:     0.4601 | E_err:   0.010599
[2025-10-03 07:11:04] [Iter  396/2250] R2[340/600]  | LR: 0.014901 | E: -42.294491 | E_var:     0.3974 | E_err:   0.009850
[2025-10-03 07:11:15] [Iter  397/2250] R2[342/600]  | LR: 0.014773 | E: -42.304868 | E_var:     0.4597 | E_err:   0.010594
[2025-10-03 07:11:25] [Iter  398/2250] R2[344/600]  | LR: 0.014646 | E: -42.297100 | E_var:     0.5271 | E_err:   0.011344
[2025-10-03 07:11:36] [Iter  399/2250] R2[346/600]  | LR: 0.014518 | E: -42.299914 | E_var:     0.4576 | E_err:   0.010570
[2025-10-03 07:11:46] [Iter  400/2250] R2[348/600]  | LR: 0.014391 | E: -42.296706 | E_var:     0.4549 | E_err:   0.010538
[2025-10-03 07:11:47] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-03 07:11:57] [Iter  401/2250] R2[350/600]  | LR: 0.014265 | E: -42.287854 | E_var:     0.5808 | E_err:   0.011908
[2025-10-03 07:12:08] [Iter  402/2250] R2[352/600]  | LR: 0.014139 | E: -42.297470 | E_var:     0.4050 | E_err:   0.009944
[2025-10-03 07:12:18] [Iter  403/2250] R2[354/600]  | LR: 0.014013 | E: -42.279934 | E_var:     0.5077 | E_err:   0.011134
[2025-10-03 07:12:29] [Iter  404/2250] R2[356/600]  | LR: 0.013887 | E: -42.311963 | E_var:     0.4834 | E_err:   0.010863
[2025-10-03 07:12:39] [Iter  405/2250] R2[358/600]  | LR: 0.013762 | E: -42.284835 | E_var:     0.5331 | E_err:   0.011408
[2025-10-03 07:12:50] [Iter  406/2250] R2[360/600]  | LR: 0.013637 | E: -42.305263 | E_var:     0.8691 | E_err:   0.014566
[2025-10-03 07:13:00] [Iter  407/2250] R2[362/600]  | LR: 0.013513 | E: -42.306065 | E_var:     0.4653 | E_err:   0.010658
[2025-10-03 07:13:11] [Iter  408/2250] R2[364/600]  | LR: 0.013389 | E: -42.265217 | E_var:     0.5371 | E_err:   0.011451
[2025-10-03 07:13:21] [Iter  409/2250] R2[366/600]  | LR: 0.013266 | E: -42.295939 | E_var:     0.4881 | E_err:   0.010916
[2025-10-03 07:13:32] [Iter  410/2250] R2[368/600]  | LR: 0.013143 | E: -42.294634 | E_var:     0.4085 | E_err:   0.009987
[2025-10-03 07:13:42] [Iter  411/2250] R2[370/600]  | LR: 0.013020 | E: -42.322474 | E_var:     0.6028 | E_err:   0.012131
[2025-10-03 07:13:53] [Iter  412/2250] R2[372/600]  | LR: 0.012898 | E: -42.292856 | E_var:     0.4336 | E_err:   0.010289
[2025-10-03 07:14:03] [Iter  413/2250] R2[374/600]  | LR: 0.012777 | E: -42.293672 | E_var:     0.4842 | E_err:   0.010873
[2025-10-03 07:14:14] [Iter  414/2250] R2[376/600]  | LR: 0.012656 | E: -42.297086 | E_var:     0.5172 | E_err:   0.011237
[2025-10-03 07:14:24] [Iter  415/2250] R2[378/600]  | LR: 0.012536 | E: -42.293914 | E_var:     0.4053 | E_err:   0.009948
[2025-10-03 07:14:35] [Iter  416/2250] R2[380/600]  | LR: 0.012416 | E: -42.292147 | E_var:     0.4853 | E_err:   0.010885
[2025-10-03 07:14:45] [Iter  417/2250] R2[382/600]  | LR: 0.012296 | E: -42.307811 | E_var:     0.4708 | E_err:   0.010721
[2025-10-03 07:14:56] [Iter  418/2250] R2[384/600]  | LR: 0.012178 | E: -42.299813 | E_var:     0.4569 | E_err:   0.010561
[2025-10-03 07:15:06] [Iter  419/2250] R2[386/600]  | LR: 0.012060 | E: -42.271289 | E_var:     0.5296 | E_err:   0.011371
[2025-10-03 07:15:17] [Iter  420/2250] R2[388/600]  | LR: 0.011942 | E: -42.299152 | E_var:     0.5173 | E_err:   0.011238
[2025-10-03 07:15:27] [Iter  421/2250] R2[390/600]  | LR: 0.011825 | E: -42.290324 | E_var:     0.5836 | E_err:   0.011937
[2025-10-03 07:15:38] [Iter  422/2250] R2[392/600]  | LR: 0.011709 | E: -42.278662 | E_var:     0.6212 | E_err:   0.012315
[2025-10-03 07:15:49] [Iter  423/2250] R2[394/600]  | LR: 0.011593 | E: -42.294574 | E_var:     0.4964 | E_err:   0.011008
[2025-10-03 07:15:59] [Iter  424/2250] R2[396/600]  | LR: 0.011478 | E: -42.294879 | E_var:     0.8628 | E_err:   0.014513
[2025-10-03 07:16:10] [Iter  425/2250] R2[398/600]  | LR: 0.011364 | E: -42.301534 | E_var:     0.4818 | E_err:   0.010846
[2025-10-03 07:16:20] [Iter  426/2250] R2[400/600]  | LR: 0.011250 | E: -42.273801 | E_var:     0.4710 | E_err:   0.010724
[2025-10-03 07:16:31] [Iter  427/2250] R2[402/600]  | LR: 0.011137 | E: -42.309045 | E_var:     0.5069 | E_err:   0.011125
[2025-10-03 07:16:41] [Iter  428/2250] R2[404/600]  | LR: 0.011025 | E: -42.293656 | E_var:     0.4898 | E_err:   0.010936
[2025-10-03 07:16:52] [Iter  429/2250] R2[406/600]  | LR: 0.010913 | E: -42.287680 | E_var:     0.5311 | E_err:   0.011387
[2025-10-03 07:17:02] [Iter  430/2250] R2[408/600]  | LR: 0.010802 | E: -42.287949 | E_var:     0.5357 | E_err:   0.011436
[2025-10-03 07:17:13] [Iter  431/2250] R2[410/600]  | LR: 0.010692 | E: -42.312480 | E_var:     0.5275 | E_err:   0.011349
[2025-10-03 07:17:23] [Iter  432/2250] R2[412/600]  | LR: 0.010583 | E: -42.282593 | E_var:     0.4861 | E_err:   0.010893
[2025-10-03 07:17:34] [Iter  433/2250] R2[414/600]  | LR: 0.010474 | E: -42.294292 | E_var:     0.4204 | E_err:   0.010131
[2025-10-03 07:17:44] [Iter  434/2250] R2[416/600]  | LR: 0.010366 | E: -42.298506 | E_var:     0.4770 | E_err:   0.010792
[2025-10-03 07:17:55] [Iter  435/2250] R2[418/600]  | LR: 0.010259 | E: -42.284381 | E_var:     0.4273 | E_err:   0.010213
[2025-10-03 07:18:05] [Iter  436/2250] R2[420/600]  | LR: 0.010153 | E: -42.281365 | E_var:     0.5024 | E_err:   0.011075
[2025-10-03 07:18:16] [Iter  437/2250] R2[422/600]  | LR: 0.010047 | E: -42.279296 | E_var:     0.5976 | E_err:   0.012079
[2025-10-03 07:18:26] [Iter  438/2250] R2[424/600]  | LR: 0.009943 | E: -42.307227 | E_var:     0.4118 | E_err:   0.010027
[2025-10-03 07:18:37] [Iter  439/2250] R2[426/600]  | LR: 0.009839 | E: -42.309502 | E_var:     0.4956 | E_err:   0.011000
[2025-10-03 07:18:47] [Iter  440/2250] R2[428/600]  | LR: 0.009736 | E: -42.290092 | E_var:     0.5650 | E_err:   0.011745
[2025-10-03 07:18:58] [Iter  441/2250] R2[430/600]  | LR: 0.009633 | E: -42.306951 | E_var:     0.7275 | E_err:   0.013327
[2025-10-03 07:19:08] [Iter  442/2250] R2[432/600]  | LR: 0.009532 | E: -42.296876 | E_var:     0.4845 | E_err:   0.010876
[2025-10-03 07:19:19] [Iter  443/2250] R2[434/600]  | LR: 0.009432 | E: -42.321350 | E_var:     0.5030 | E_err:   0.011081
[2025-10-03 07:19:29] [Iter  444/2250] R2[436/600]  | LR: 0.009332 | E: -42.293440 | E_var:     0.5088 | E_err:   0.011145
[2025-10-03 07:19:40] [Iter  445/2250] R2[438/600]  | LR: 0.009234 | E: -42.313473 | E_var:     0.4363 | E_err:   0.010321
[2025-10-03 07:19:51] [Iter  446/2250] R2[440/600]  | LR: 0.009136 | E: -42.309528 | E_var:     0.4564 | E_err:   0.010555
[2025-10-03 07:20:01] [Iter  447/2250] R2[442/600]  | LR: 0.009039 | E: -42.312080 | E_var:     0.4453 | E_err:   0.010426
[2025-10-03 07:20:12] [Iter  448/2250] R2[444/600]  | LR: 0.008943 | E: -42.291550 | E_var:     0.5271 | E_err:   0.011344
[2025-10-03 07:20:22] [Iter  449/2250] R2[446/600]  | LR: 0.008848 | E: -42.283668 | E_var:     0.4892 | E_err:   0.010928
[2025-10-03 07:20:33] [Iter  450/2250] R2[448/600]  | LR: 0.008754 | E: -42.299350 | E_var:     0.3616 | E_err:   0.009396
[2025-10-03 07:20:43] [Iter  451/2250] R2[450/600]  | LR: 0.008661 | E: -42.293874 | E_var:     0.6207 | E_err:   0.012310
[2025-10-03 07:20:54] [Iter  452/2250] R2[452/600]  | LR: 0.008569 | E: -42.309175 | E_var:     0.4486 | E_err:   0.010465
[2025-10-03 07:21:04] [Iter  453/2250] R2[454/600]  | LR: 0.008478 | E: -42.297026 | E_var:     0.5430 | E_err:   0.011514
[2025-10-03 07:21:15] [Iter  454/2250] R2[456/600]  | LR: 0.008388 | E: -42.301314 | E_var:     0.4641 | E_err:   0.010644
[2025-10-03 07:21:25] [Iter  455/2250] R2[458/600]  | LR: 0.008299 | E: -42.300953 | E_var:     0.5479 | E_err:   0.011566
[2025-10-03 07:21:36] [Iter  456/2250] R2[460/600]  | LR: 0.008211 | E: -42.298069 | E_var:     0.4187 | E_err:   0.010111
[2025-10-03 07:21:46] [Iter  457/2250] R2[462/600]  | LR: 0.008124 | E: -42.300399 | E_var:     0.4033 | E_err:   0.009923
[2025-10-03 07:21:57] [Iter  458/2250] R2[464/600]  | LR: 0.008038 | E: -42.299581 | E_var:     0.4547 | E_err:   0.010536
[2025-10-03 07:22:07] [Iter  459/2250] R2[466/600]  | LR: 0.007953 | E: -42.297878 | E_var:     0.5298 | E_err:   0.011373
[2025-10-03 07:22:18] [Iter  460/2250] R2[468/600]  | LR: 0.007869 | E: -42.303601 | E_var:     0.4590 | E_err:   0.010586
[2025-10-03 07:22:28] [Iter  461/2250] R2[470/600]  | LR: 0.007786 | E: -42.281867 | E_var:     1.2514 | E_err:   0.017479
[2025-10-03 07:22:39] [Iter  462/2250] R2[472/600]  | LR: 0.007704 | E: -42.298453 | E_var:     0.4437 | E_err:   0.010408
[2025-10-03 07:22:49] [Iter  463/2250] R2[474/600]  | LR: 0.007623 | E: -42.301494 | E_var:     0.4091 | E_err:   0.009994
[2025-10-03 07:23:00] [Iter  464/2250] R2[476/600]  | LR: 0.007543 | E: -42.307724 | E_var:     0.4146 | E_err:   0.010061
[2025-10-03 07:23:10] [Iter  465/2250] R2[478/600]  | LR: 0.007465 | E: -42.309401 | E_var:     0.4167 | E_err:   0.010086
[2025-10-03 07:23:21] [Iter  466/2250] R2[480/600]  | LR: 0.007387 | E: -42.305826 | E_var:     0.4331 | E_err:   0.010283
[2025-10-03 07:23:31] [Iter  467/2250] R2[482/600]  | LR: 0.007311 | E: -42.305870 | E_var:     0.4963 | E_err:   0.011008
[2025-10-03 07:23:42] [Iter  468/2250] R2[484/600]  | LR: 0.007236 | E: -42.306430 | E_var:     0.4522 | E_err:   0.010507
[2025-10-03 07:23:53] [Iter  469/2250] R2[486/600]  | LR: 0.007161 | E: -42.321267 | E_var:     0.5083 | E_err:   0.011140
[2025-10-03 07:24:03] [Iter  470/2250] R2[488/600]  | LR: 0.007088 | E: -42.313297 | E_var:     0.4448 | E_err:   0.010421
[2025-10-03 07:24:14] [Iter  471/2250] R2[490/600]  | LR: 0.007017 | E: -42.313547 | E_var:     0.4703 | E_err:   0.010715
[2025-10-03 07:24:24] [Iter  472/2250] R2[492/600]  | LR: 0.006946 | E: -42.298987 | E_var:     0.5809 | E_err:   0.011909
[2025-10-03 07:24:35] [Iter  473/2250] R2[494/600]  | LR: 0.006876 | E: -42.306348 | E_var:     0.3984 | E_err:   0.009863
[2025-10-03 07:24:45] [Iter  474/2250] R2[496/600]  | LR: 0.006808 | E: -42.304921 | E_var:     0.4296 | E_err:   0.010241
[2025-10-03 07:24:56] [Iter  475/2250] R2[498/600]  | LR: 0.006741 | E: -42.315242 | E_var:     0.5946 | E_err:   0.012048
[2025-10-03 07:25:06] [Iter  476/2250] R2[500/600]  | LR: 0.006675 | E: -42.309915 | E_var:     0.5006 | E_err:   0.011055
[2025-10-03 07:25:17] [Iter  477/2250] R2[502/600]  | LR: 0.006610 | E: -42.301933 | E_var:     0.4293 | E_err:   0.010238
[2025-10-03 07:25:27] [Iter  478/2250] R2[504/600]  | LR: 0.006546 | E: -42.298997 | E_var:     0.4295 | E_err:   0.010239
[2025-10-03 07:25:38] [Iter  479/2250] R2[506/600]  | LR: 0.006484 | E: -42.293155 | E_var:     0.4535 | E_err:   0.010523
[2025-10-03 07:25:48] [Iter  480/2250] R2[508/600]  | LR: 0.006422 | E: -42.314035 | E_var:     0.4591 | E_err:   0.010586
[2025-10-03 07:25:59] [Iter  481/2250] R2[510/600]  | LR: 0.006362 | E: -42.311428 | E_var:     0.4106 | E_err:   0.010013
[2025-10-03 07:26:09] [Iter  482/2250] R2[512/600]  | LR: 0.006304 | E: -42.314741 | E_var:     0.4761 | E_err:   0.010781
[2025-10-03 07:26:20] [Iter  483/2250] R2[514/600]  | LR: 0.006246 | E: -42.304133 | E_var:     0.4250 | E_err:   0.010187
[2025-10-03 07:26:30] [Iter  484/2250] R2[516/600]  | LR: 0.006190 | E: -42.308868 | E_var:     0.4196 | E_err:   0.010121
[2025-10-03 07:26:41] [Iter  485/2250] R2[518/600]  | LR: 0.006135 | E: -42.327805 | E_var:     0.5720 | E_err:   0.011817
[2025-10-03 07:26:51] [Iter  486/2250] R2[520/600]  | LR: 0.006081 | E: -42.289122 | E_var:     0.5185 | E_err:   0.011251
[2025-10-03 07:27:02] [Iter  487/2250] R2[522/600]  | LR: 0.006028 | E: -42.331563 | E_var:     0.4463 | E_err:   0.010438
[2025-10-03 07:27:12] [Iter  488/2250] R2[524/600]  | LR: 0.005977 | E: -42.307471 | E_var:     0.4440 | E_err:   0.010411
[2025-10-03 07:27:23] [Iter  489/2250] R2[526/600]  | LR: 0.005927 | E: -42.313828 | E_var:     0.5164 | E_err:   0.011229
[2025-10-03 07:27:33] [Iter  490/2250] R2[528/600]  | LR: 0.005878 | E: -42.287143 | E_var:     0.3890 | E_err:   0.009745
[2025-10-03 07:27:44] [Iter  491/2250] R2[530/600]  | LR: 0.005830 | E: -42.291854 | E_var:     0.4750 | E_err:   0.010769
[2025-10-03 07:27:55] [Iter  492/2250] R2[532/600]  | LR: 0.005784 | E: -42.306868 | E_var:     0.4309 | E_err:   0.010256
[2025-10-03 07:28:05] [Iter  493/2250] R2[534/600]  | LR: 0.005739 | E: -42.323435 | E_var:     1.1113 | E_err:   0.016472
[2025-10-03 07:28:16] [Iter  494/2250] R2[536/600]  | LR: 0.005695 | E: -42.322479 | E_var:     0.4738 | E_err:   0.010755
[2025-10-03 07:28:26] [Iter  495/2250] R2[538/600]  | LR: 0.005653 | E: -42.295764 | E_var:     0.4804 | E_err:   0.010830
[2025-10-03 07:28:37] [Iter  496/2250] R2[540/600]  | LR: 0.005612 | E: -42.303812 | E_var:     0.4739 | E_err:   0.010756
[2025-10-03 07:28:47] [Iter  497/2250] R2[542/600]  | LR: 0.005572 | E: -42.290570 | E_var:     0.5118 | E_err:   0.011178
[2025-10-03 07:28:58] [Iter  498/2250] R2[544/600]  | LR: 0.005534 | E: -42.299187 | E_var:     0.4119 | E_err:   0.010029
[2025-10-03 07:29:08] [Iter  499/2250] R2[546/600]  | LR: 0.005496 | E: -42.304643 | E_var:     0.3777 | E_err:   0.009602
[2025-10-03 07:29:19] [Iter  500/2250] R2[548/600]  | LR: 0.005460 | E: -42.295207 | E_var:     0.9495 | E_err:   0.015226
[2025-10-03 07:29:29] [Iter  501/2250] R2[550/600]  | LR: 0.005426 | E: -42.303732 | E_var:     0.3476 | E_err:   0.009212
[2025-10-03 07:29:40] [Iter  502/2250] R2[552/600]  | LR: 0.005393 | E: -42.316616 | E_var:     0.5271 | E_err:   0.011344
[2025-10-03 07:29:50] [Iter  503/2250] R2[554/600]  | LR: 0.005361 | E: -42.305051 | E_var:     0.7116 | E_err:   0.013180
[2025-10-03 07:30:01] [Iter  504/2250] R2[556/600]  | LR: 0.005330 | E: -42.302829 | E_var:     0.4714 | E_err:   0.010728
[2025-10-03 07:30:11] [Iter  505/2250] R2[558/600]  | LR: 0.005301 | E: -42.318135 | E_var:     0.4571 | E_err:   0.010564
[2025-10-03 07:30:22] [Iter  506/2250] R2[560/600]  | LR: 0.005273 | E: -42.281698 | E_var:     0.4687 | E_err:   0.010697
[2025-10-03 07:30:32] [Iter  507/2250] R2[562/600]  | LR: 0.005247 | E: -42.302104 | E_var:     0.4469 | E_err:   0.010445
[2025-10-03 07:30:43] [Iter  508/2250] R2[564/600]  | LR: 0.005221 | E: -42.325181 | E_var:     0.4639 | E_err:   0.010642
[2025-10-03 07:30:53] [Iter  509/2250] R2[566/600]  | LR: 0.005198 | E: -42.289225 | E_var:     0.5152 | E_err:   0.011215
[2025-10-03 07:31:04] [Iter  510/2250] R2[568/600]  | LR: 0.005175 | E: -42.310808 | E_var:     0.3665 | E_err:   0.009459
[2025-10-03 07:31:14] [Iter  511/2250] R2[570/600]  | LR: 0.005154 | E: -42.302242 | E_var:     0.4486 | E_err:   0.010466
[2025-10-03 07:31:25] [Iter  512/2250] R2[572/600]  | LR: 0.005134 | E: -42.307067 | E_var:     0.4083 | E_err:   0.009984
[2025-10-03 07:31:36] [Iter  513/2250] R2[574/600]  | LR: 0.005116 | E: -42.311529 | E_var:     0.4093 | E_err:   0.009997
[2025-10-03 07:31:46] [Iter  514/2250] R2[576/600]  | LR: 0.005099 | E: -42.297739 | E_var:     0.4343 | E_err:   0.010297
[2025-10-03 07:31:57] [Iter  515/2250] R2[578/600]  | LR: 0.005083 | E: -42.296478 | E_var:     0.6322 | E_err:   0.012424
[2025-10-03 07:32:07] [Iter  516/2250] R2[580/600]  | LR: 0.005068 | E: -42.311413 | E_var:     0.3919 | E_err:   0.009782
[2025-10-03 07:32:18] [Iter  517/2250] R2[582/600]  | LR: 0.005055 | E: -42.312593 | E_var:     0.4008 | E_err:   0.009892
[2025-10-03 07:32:28] [Iter  518/2250] R2[584/600]  | LR: 0.005044 | E: -42.334040 | E_var:     0.4630 | E_err:   0.010632
[2025-10-03 07:32:39] [Iter  519/2250] R2[586/600]  | LR: 0.005034 | E: -42.331138 | E_var:     0.4301 | E_err:   0.010247
[2025-10-03 07:32:49] [Iter  520/2250] R2[588/600]  | LR: 0.005025 | E: -42.313278 | E_var:     0.4257 | E_err:   0.010194
[2025-10-03 07:33:00] [Iter  521/2250] R2[590/600]  | LR: 0.005017 | E: -42.329234 | E_var:     0.3983 | E_err:   0.009861
[2025-10-03 07:33:10] [Iter  522/2250] R2[592/600]  | LR: 0.005011 | E: -42.312954 | E_var:     0.3940 | E_err:   0.009808
[2025-10-03 07:33:21] [Iter  523/2250] R2[594/600]  | LR: 0.005006 | E: -42.316407 | E_var:     0.3826 | E_err:   0.009664
[2025-10-03 07:33:31] [Iter  524/2250] R2[596/600]  | LR: 0.005003 | E: -42.294620 | E_var:     0.4024 | E_err:   0.009912
[2025-10-03 07:33:42] [Iter  525/2250] R2[598/600]  | LR: 0.005001 | E: -42.307491 | E_var:     0.4794 | E_err:   0.010819
[2025-10-03 07:33:42] 🔄 RESTART #3 | Period: 1200
[2025-10-03 07:33:52] [Iter  526/2250] R3[0/1200]   | LR: 0.030000 | E: -42.317472 | E_var:     0.3649 | E_err:   0.009439
[2025-10-03 07:34:03] [Iter  527/2250] R3[2/1200]   | LR: 0.030000 | E: -42.305372 | E_var:     0.4501 | E_err:   0.010482
[2025-10-03 07:34:13] [Iter  528/2250] R3[4/1200]   | LR: 0.029999 | E: -42.311350 | E_var:     0.4102 | E_err:   0.010007
[2025-10-03 07:34:24] [Iter  529/2250] R3[6/1200]   | LR: 0.029998 | E: -42.320902 | E_var:     0.4039 | E_err:   0.009930
[2025-10-03 07:34:34] [Iter  530/2250] R3[8/1200]   | LR: 0.029997 | E: -42.331851 | E_var:     0.4084 | E_err:   0.009986
[2025-10-03 07:34:45] [Iter  531/2250] R3[10/1200]  | LR: 0.029996 | E: -42.293368 | E_var:     0.5248 | E_err:   0.011319
[2025-10-03 07:34:55] [Iter  532/2250] R3[12/1200]  | LR: 0.029994 | E: -42.322693 | E_var:     0.4055 | E_err:   0.009950
[2025-10-03 07:35:06] [Iter  533/2250] R3[14/1200]  | LR: 0.029992 | E: -42.308928 | E_var:     0.3742 | E_err:   0.009558
[2025-10-03 07:35:16] [Iter  534/2250] R3[16/1200]  | LR: 0.029989 | E: -42.306046 | E_var:     0.4235 | E_err:   0.010168
[2025-10-03 07:35:27] [Iter  535/2250] R3[18/1200]  | LR: 0.029986 | E: -42.306670 | E_var:     0.4525 | E_err:   0.010511
[2025-10-03 07:35:38] [Iter  536/2250] R3[20/1200]  | LR: 0.029983 | E: -42.297559 | E_var:     0.3745 | E_err:   0.009562
[2025-10-03 07:35:48] [Iter  537/2250] R3[22/1200]  | LR: 0.029979 | E: -42.298385 | E_var:     0.4543 | E_err:   0.010531
[2025-10-03 07:35:59] [Iter  538/2250] R3[24/1200]  | LR: 0.029975 | E: -42.315663 | E_var:     0.4372 | E_err:   0.010332
[2025-10-03 07:36:09] [Iter  539/2250] R3[26/1200]  | LR: 0.029971 | E: -42.306764 | E_var:     0.5019 | E_err:   0.011069
[2025-10-03 07:36:20] [Iter  540/2250] R3[28/1200]  | LR: 0.029966 | E: -42.307796 | E_var:     0.3766 | E_err:   0.009589
[2025-10-03 07:36:30] [Iter  541/2250] R3[30/1200]  | LR: 0.029961 | E: -42.311441 | E_var:     0.4909 | E_err:   0.010947
[2025-10-03 07:36:41] [Iter  542/2250] R3[32/1200]  | LR: 0.029956 | E: -42.315203 | E_var:     0.5184 | E_err:   0.011250
[2025-10-03 07:36:51] [Iter  543/2250] R3[34/1200]  | LR: 0.029951 | E: -42.326350 | E_var:     0.3877 | E_err:   0.009728
[2025-10-03 07:37:02] [Iter  544/2250] R3[36/1200]  | LR: 0.029945 | E: -42.306669 | E_var:     0.3450 | E_err:   0.009178
[2025-10-03 07:37:12] [Iter  545/2250] R3[38/1200]  | LR: 0.029938 | E: -42.321543 | E_var:     0.4742 | E_err:   0.010760
[2025-10-03 07:37:23] [Iter  546/2250] R3[40/1200]  | LR: 0.029932 | E: -42.331135 | E_var:     0.4979 | E_err:   0.011025
[2025-10-03 07:37:33] [Iter  547/2250] R3[42/1200]  | LR: 0.029925 | E: -42.317962 | E_var:     0.4002 | E_err:   0.009885
[2025-10-03 07:37:44] [Iter  548/2250] R3[44/1200]  | LR: 0.029917 | E: -42.321967 | E_var:     0.4527 | E_err:   0.010513
[2025-10-03 07:37:54] [Iter  549/2250] R3[46/1200]  | LR: 0.029909 | E: -42.319923 | E_var:     0.4123 | E_err:   0.010033
[2025-10-03 07:38:05] [Iter  550/2250] R3[48/1200]  | LR: 0.029901 | E: -42.303457 | E_var:     0.4091 | E_err:   0.009994
[2025-10-03 07:38:15] [Iter  551/2250] R3[50/1200]  | LR: 0.029893 | E: -42.310163 | E_var:     0.4420 | E_err:   0.010388
[2025-10-03 07:38:26] [Iter  552/2250] R3[52/1200]  | LR: 0.029884 | E: -42.320142 | E_var:     0.3813 | E_err:   0.009648
[2025-10-03 07:38:36] [Iter  553/2250] R3[54/1200]  | LR: 0.029875 | E: -42.316814 | E_var:     0.4943 | E_err:   0.010986
[2025-10-03 07:38:47] [Iter  554/2250] R3[56/1200]  | LR: 0.029866 | E: -42.310806 | E_var:     0.3972 | E_err:   0.009847
[2025-10-03 07:38:57] [Iter  555/2250] R3[58/1200]  | LR: 0.029856 | E: -42.315623 | E_var:     0.4008 | E_err:   0.009892
[2025-10-03 07:39:08] [Iter  556/2250] R3[60/1200]  | LR: 0.029846 | E: -42.313938 | E_var:     0.3795 | E_err:   0.009625
[2025-10-03 07:39:18] [Iter  557/2250] R3[62/1200]  | LR: 0.029836 | E: -42.319280 | E_var:     1.9085 | E_err:   0.021586
[2025-10-03 07:39:29] [Iter  558/2250] R3[64/1200]  | LR: 0.029825 | E: -42.309142 | E_var:     0.4008 | E_err:   0.009891
[2025-10-03 07:39:39] [Iter  559/2250] R3[66/1200]  | LR: 0.029814 | E: -42.319796 | E_var:     0.4892 | E_err:   0.010929
[2025-10-03 07:39:50] [Iter  560/2250] R3[68/1200]  | LR: 0.029802 | E: -42.314219 | E_var:     0.4248 | E_err:   0.010183
[2025-10-03 07:40:01] [Iter  561/2250] R3[70/1200]  | LR: 0.029791 | E: -42.300241 | E_var:     0.3975 | E_err:   0.009852
[2025-10-03 07:40:11] [Iter  562/2250] R3[72/1200]  | LR: 0.029779 | E: -42.308179 | E_var:     0.4056 | E_err:   0.009951
[2025-10-03 07:40:22] [Iter  563/2250] R3[74/1200]  | LR: 0.029766 | E: -42.330532 | E_var:     0.4162 | E_err:   0.010080
[2025-10-03 07:40:32] [Iter  564/2250] R3[76/1200]  | LR: 0.029753 | E: -42.313184 | E_var:     0.4550 | E_err:   0.010539
[2025-10-03 07:40:43] [Iter  565/2250] R3[78/1200]  | LR: 0.029740 | E: -42.307022 | E_var:     0.3982 | E_err:   0.009860
[2025-10-03 07:40:53] [Iter  566/2250] R3[80/1200]  | LR: 0.029727 | E: -42.299670 | E_var:     0.4163 | E_err:   0.010082
[2025-10-03 07:41:04] [Iter  567/2250] R3[82/1200]  | LR: 0.029713 | E: -42.320216 | E_var:     0.4850 | E_err:   0.010881
[2025-10-03 07:41:14] [Iter  568/2250] R3[84/1200]  | LR: 0.029699 | E: -42.302447 | E_var:     0.5336 | E_err:   0.011414
[2025-10-03 07:41:25] [Iter  569/2250] R3[86/1200]  | LR: 0.029685 | E: -42.310884 | E_var:     0.4228 | E_err:   0.010160
[2025-10-03 07:41:35] [Iter  570/2250] R3[88/1200]  | LR: 0.029670 | E: -42.320806 | E_var:     0.3367 | E_err:   0.009067
[2025-10-03 07:41:46] [Iter  571/2250] R3[90/1200]  | LR: 0.029655 | E: -42.312188 | E_var:     0.4306 | E_err:   0.010253
[2025-10-03 07:41:56] [Iter  572/2250] R3[92/1200]  | LR: 0.029639 | E: -42.315409 | E_var:     0.3882 | E_err:   0.009735
[2025-10-03 07:42:07] [Iter  573/2250] R3[94/1200]  | LR: 0.029623 | E: -42.316008 | E_var:     0.4586 | E_err:   0.010582
[2025-10-03 07:42:17] [Iter  574/2250] R3[96/1200]  | LR: 0.029607 | E: -42.321988 | E_var:     0.5127 | E_err:   0.011188
[2025-10-03 07:42:28] [Iter  575/2250] R3[98/1200]  | LR: 0.029591 | E: -42.312761 | E_var:     0.3972 | E_err:   0.009848
[2025-10-03 07:42:38] [Iter  576/2250] R3[100/1200] | LR: 0.029574 | E: -42.292407 | E_var:     0.5117 | E_err:   0.011177
[2025-10-03 07:42:49] [Iter  577/2250] R3[102/1200] | LR: 0.029557 | E: -42.314702 | E_var:     0.4809 | E_err:   0.010836
[2025-10-03 07:42:59] [Iter  578/2250] R3[104/1200] | LR: 0.029540 | E: -42.303147 | E_var:     0.5378 | E_err:   0.011459
[2025-10-03 07:43:10] [Iter  579/2250] R3[106/1200] | LR: 0.029522 | E: -42.293893 | E_var:     0.4571 | E_err:   0.010564
[2025-10-03 07:43:20] [Iter  580/2250] R3[108/1200] | LR: 0.029504 | E: -42.302230 | E_var:     0.4628 | E_err:   0.010630
[2025-10-03 07:43:31] [Iter  581/2250] R3[110/1200] | LR: 0.029485 | E: -42.306446 | E_var:     0.4946 | E_err:   0.010989
[2025-10-03 07:43:41] [Iter  582/2250] R3[112/1200] | LR: 0.029466 | E: -42.300204 | E_var:     0.5739 | E_err:   0.011837
[2025-10-03 07:43:52] [Iter  583/2250] R3[114/1200] | LR: 0.029447 | E: -42.294879 | E_var:     0.5301 | E_err:   0.011377
[2025-10-03 07:44:03] [Iter  584/2250] R3[116/1200] | LR: 0.029428 | E: -42.312624 | E_var:     0.3899 | E_err:   0.009756
[2025-10-03 07:44:13] [Iter  585/2250] R3[118/1200] | LR: 0.029408 | E: -42.321918 | E_var:     0.3591 | E_err:   0.009364
[2025-10-03 07:44:24] [Iter  586/2250] R3[120/1200] | LR: 0.029388 | E: -42.313809 | E_var:     0.3995 | E_err:   0.009876
[2025-10-03 07:44:34] [Iter  587/2250] R3[122/1200] | LR: 0.029368 | E: -42.319124 | E_var:     0.3387 | E_err:   0.009093
[2025-10-03 07:44:45] [Iter  588/2250] R3[124/1200] | LR: 0.029347 | E: -42.311913 | E_var:     0.3709 | E_err:   0.009516
[2025-10-03 07:44:55] [Iter  589/2250] R3[126/1200] | LR: 0.029326 | E: -42.309042 | E_var:     0.4126 | E_err:   0.010036
[2025-10-03 07:45:06] [Iter  590/2250] R3[128/1200] | LR: 0.029305 | E: -42.308777 | E_var:     0.3884 | E_err:   0.009738
[2025-10-03 07:45:16] [Iter  591/2250] R3[130/1200] | LR: 0.029283 | E: -42.306504 | E_var:     0.4372 | E_err:   0.010332
[2025-10-03 07:45:27] [Iter  592/2250] R3[132/1200] | LR: 0.029261 | E: -42.306584 | E_var:     0.3862 | E_err:   0.009710
[2025-10-03 07:45:37] [Iter  593/2250] R3[134/1200] | LR: 0.029239 | E: -42.325583 | E_var:     0.4516 | E_err:   0.010500
[2025-10-03 07:45:48] [Iter  594/2250] R3[136/1200] | LR: 0.029216 | E: -42.327787 | E_var:     0.6087 | E_err:   0.012190
[2025-10-03 07:45:58] [Iter  595/2250] R3[138/1200] | LR: 0.029193 | E: -42.314187 | E_var:     0.3773 | E_err:   0.009597
[2025-10-03 07:46:09] [Iter  596/2250] R3[140/1200] | LR: 0.029170 | E: -42.319998 | E_var:     0.4476 | E_err:   0.010454
[2025-10-03 07:46:19] [Iter  597/2250] R3[142/1200] | LR: 0.029146 | E: -42.307432 | E_var:     0.3968 | E_err:   0.009843
[2025-10-03 07:46:30] [Iter  598/2250] R3[144/1200] | LR: 0.029122 | E: -42.309368 | E_var:     0.5093 | E_err:   0.011151
[2025-10-03 07:46:40] [Iter  599/2250] R3[146/1200] | LR: 0.029098 | E: -42.319910 | E_var:     0.3931 | E_err:   0.009796
[2025-10-03 07:46:51] [Iter  600/2250] R3[148/1200] | LR: 0.029073 | E: -42.315033 | E_var:     0.5186 | E_err:   0.011252
[2025-10-03 07:46:51] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-03 07:47:02] [Iter  601/2250] R3[150/1200] | LR: 0.029048 | E: -42.321722 | E_var:     0.4402 | E_err:   0.010367
[2025-10-03 07:47:12] [Iter  602/2250] R3[152/1200] | LR: 0.029023 | E: -42.310505 | E_var:     0.3545 | E_err:   0.009303
[2025-10-03 07:47:23] [Iter  603/2250] R3[154/1200] | LR: 0.028998 | E: -42.333354 | E_var:     0.4465 | E_err:   0.010440
[2025-10-03 07:47:33] [Iter  604/2250] R3[156/1200] | LR: 0.028972 | E: -42.309278 | E_var:     0.3520 | E_err:   0.009271
[2025-10-03 07:47:44] [Iter  605/2250] R3[158/1200] | LR: 0.028946 | E: -42.330767 | E_var:     0.3392 | E_err:   0.009100
[2025-10-03 07:47:54] [Iter  606/2250] R3[160/1200] | LR: 0.028919 | E: -42.304131 | E_var:     0.4026 | E_err:   0.009914
[2025-10-03 07:48:05] [Iter  607/2250] R3[162/1200] | LR: 0.028893 | E: -42.321460 | E_var:     0.4533 | E_err:   0.010520
[2025-10-03 07:48:15] [Iter  608/2250] R3[164/1200] | LR: 0.028865 | E: -42.322308 | E_var:     0.4042 | E_err:   0.009934
[2025-10-03 07:48:26] [Iter  609/2250] R3[166/1200] | LR: 0.028838 | E: -42.314428 | E_var:     0.5013 | E_err:   0.011063
[2025-10-03 07:48:36] [Iter  610/2250] R3[168/1200] | LR: 0.028810 | E: -42.316541 | E_var:     0.4258 | E_err:   0.010196
[2025-10-03 07:48:47] [Iter  611/2250] R3[170/1200] | LR: 0.028782 | E: -42.333132 | E_var:     0.3523 | E_err:   0.009275
[2025-10-03 07:48:57] [Iter  612/2250] R3[172/1200] | LR: 0.028754 | E: -42.316961 | E_var:     0.4248 | E_err:   0.010184
[2025-10-03 07:49:08] [Iter  613/2250] R3[174/1200] | LR: 0.028725 | E: -42.306389 | E_var:     0.4329 | E_err:   0.010281
[2025-10-03 07:49:18] [Iter  614/2250] R3[176/1200] | LR: 0.028696 | E: -42.326012 | E_var:     0.3935 | E_err:   0.009801
[2025-10-03 07:49:29] [Iter  615/2250] R3[178/1200] | LR: 0.028667 | E: -42.307765 | E_var:     0.4018 | E_err:   0.009904
[2025-10-03 07:49:39] [Iter  616/2250] R3[180/1200] | LR: 0.028638 | E: -42.312152 | E_var:     0.4361 | E_err:   0.010318
[2025-10-03 07:49:50] [Iter  617/2250] R3[182/1200] | LR: 0.028608 | E: -42.310718 | E_var:     0.3587 | E_err:   0.009358
[2025-10-03 07:50:00] [Iter  618/2250] R3[184/1200] | LR: 0.028578 | E: -42.339282 | E_var:     0.3420 | E_err:   0.009137
[2025-10-03 07:50:11] [Iter  619/2250] R3[186/1200] | LR: 0.028547 | E: -42.328761 | E_var:     0.5079 | E_err:   0.011135
[2025-10-03 07:50:21] [Iter  620/2250] R3[188/1200] | LR: 0.028516 | E: -42.318737 | E_var:     0.4186 | E_err:   0.010109
[2025-10-03 07:50:32] [Iter  621/2250] R3[190/1200] | LR: 0.028485 | E: -42.319000 | E_var:     0.3538 | E_err:   0.009294
[2025-10-03 07:50:43] [Iter  622/2250] R3[192/1200] | LR: 0.028454 | E: -42.309780 | E_var:     0.3585 | E_err:   0.009355
[2025-10-03 07:50:53] [Iter  623/2250] R3[194/1200] | LR: 0.028422 | E: -42.314461 | E_var:     0.4328 | E_err:   0.010279
[2025-10-03 07:51:04] [Iter  624/2250] R3[196/1200] | LR: 0.028390 | E: -42.323922 | E_var:     0.4685 | E_err:   0.010695
[2025-10-03 07:51:14] [Iter  625/2250] R3[198/1200] | LR: 0.028358 | E: -42.324198 | E_var:     0.5612 | E_err:   0.011705
[2025-10-03 07:51:25] [Iter  626/2250] R3[200/1200] | LR: 0.028325 | E: -42.314109 | E_var:     0.5067 | E_err:   0.011123
[2025-10-03 07:51:35] [Iter  627/2250] R3[202/1200] | LR: 0.028292 | E: -42.320660 | E_var:     0.3598 | E_err:   0.009373
[2025-10-03 07:51:46] [Iter  628/2250] R3[204/1200] | LR: 0.028259 | E: -42.307797 | E_var:     0.3468 | E_err:   0.009202
[2025-10-03 07:51:56] [Iter  629/2250] R3[206/1200] | LR: 0.028226 | E: -42.322011 | E_var:     0.4244 | E_err:   0.010179
[2025-10-03 07:52:07] [Iter  630/2250] R3[208/1200] | LR: 0.028192 | E: -42.311064 | E_var:     0.3955 | E_err:   0.009826
[2025-10-03 07:52:17] [Iter  631/2250] R3[210/1200] | LR: 0.028158 | E: -42.319357 | E_var:     0.3655 | E_err:   0.009446
[2025-10-03 07:52:28] [Iter  632/2250] R3[212/1200] | LR: 0.028124 | E: -42.315696 | E_var:     0.4233 | E_err:   0.010166
[2025-10-03 07:52:38] [Iter  633/2250] R3[214/1200] | LR: 0.028089 | E: -42.325354 | E_var:     0.3834 | E_err:   0.009674
[2025-10-03 07:52:49] [Iter  634/2250] R3[216/1200] | LR: 0.028054 | E: -42.320103 | E_var:     0.5730 | E_err:   0.011828
[2025-10-03 07:52:59] [Iter  635/2250] R3[218/1200] | LR: 0.028019 | E: -42.317395 | E_var:     0.4491 | E_err:   0.010471
[2025-10-03 07:53:10] [Iter  636/2250] R3[220/1200] | LR: 0.027983 | E: -42.328426 | E_var:     0.3422 | E_err:   0.009140
[2025-10-03 07:53:20] [Iter  637/2250] R3[222/1200] | LR: 0.027948 | E: -42.306553 | E_var:     0.4235 | E_err:   0.010168
[2025-10-03 07:53:31] [Iter  638/2250] R3[224/1200] | LR: 0.027912 | E: -42.315105 | E_var:     0.5255 | E_err:   0.011327
[2025-10-03 07:53:41] [Iter  639/2250] R3[226/1200] | LR: 0.027875 | E: -42.318610 | E_var:     0.3857 | E_err:   0.009703
[2025-10-03 07:53:52] [Iter  640/2250] R3[228/1200] | LR: 0.027839 | E: -42.329715 | E_var:     0.4770 | E_err:   0.010792
[2025-10-03 07:54:02] [Iter  641/2250] R3[230/1200] | LR: 0.027802 | E: -42.340755 | E_var:     0.3880 | E_err:   0.009732
[2025-10-03 07:54:13] [Iter  642/2250] R3[232/1200] | LR: 0.027764 | E: -42.296855 | E_var:     0.4175 | E_err:   0.010096
[2025-10-03 07:54:23] [Iter  643/2250] R3[234/1200] | LR: 0.027727 | E: -42.306911 | E_var:     0.4035 | E_err:   0.009925
[2025-10-03 07:54:34] [Iter  644/2250] R3[236/1200] | LR: 0.027689 | E: -42.298919 | E_var:     0.4035 | E_err:   0.009925
[2025-10-03 07:54:45] [Iter  645/2250] R3[238/1200] | LR: 0.027651 | E: -42.321881 | E_var:     0.4707 | E_err:   0.010720
[2025-10-03 07:54:55] [Iter  646/2250] R3[240/1200] | LR: 0.027613 | E: -42.315458 | E_var:     0.3333 | E_err:   0.009021
[2025-10-03 07:55:06] [Iter  647/2250] R3[242/1200] | LR: 0.027574 | E: -42.308983 | E_var:     0.3973 | E_err:   0.009849
[2025-10-03 07:55:16] [Iter  648/2250] R3[244/1200] | LR: 0.027535 | E: -42.320440 | E_var:     0.3208 | E_err:   0.008850
[2025-10-03 07:55:27] [Iter  649/2250] R3[246/1200] | LR: 0.027496 | E: -42.331373 | E_var:     0.4006 | E_err:   0.009889
[2025-10-03 07:55:37] [Iter  650/2250] R3[248/1200] | LR: 0.027457 | E: -42.316601 | E_var:     0.4182 | E_err:   0.010105
[2025-10-03 07:55:48] [Iter  651/2250] R3[250/1200] | LR: 0.027417 | E: -42.306904 | E_var:     0.3998 | E_err:   0.009880
[2025-10-03 07:55:58] [Iter  652/2250] R3[252/1200] | LR: 0.027377 | E: -42.324047 | E_var:     0.4867 | E_err:   0.010901
[2025-10-03 07:56:09] [Iter  653/2250] R3[254/1200] | LR: 0.027337 | E: -42.306918 | E_var:     0.4321 | E_err:   0.010271
[2025-10-03 07:56:19] [Iter  654/2250] R3[256/1200] | LR: 0.027296 | E: -42.318340 | E_var:     0.4367 | E_err:   0.010325
[2025-10-03 07:56:30] [Iter  655/2250] R3[258/1200] | LR: 0.027255 | E: -42.313379 | E_var:     0.3996 | E_err:   0.009878
[2025-10-03 07:56:40] [Iter  656/2250] R3[260/1200] | LR: 0.027214 | E: -42.326806 | E_var:     0.5504 | E_err:   0.011592
[2025-10-03 07:56:51] [Iter  657/2250] R3[262/1200] | LR: 0.027173 | E: -42.316646 | E_var:     0.3521 | E_err:   0.009271
[2025-10-03 07:57:01] [Iter  658/2250] R3[264/1200] | LR: 0.027131 | E: -42.315291 | E_var:     0.3311 | E_err:   0.008990
[2025-10-03 07:57:12] [Iter  659/2250] R3[266/1200] | LR: 0.027090 | E: -42.318610 | E_var:     0.3723 | E_err:   0.009534
[2025-10-03 07:57:22] [Iter  660/2250] R3[268/1200] | LR: 0.027047 | E: -42.331732 | E_var:     0.3378 | E_err:   0.009081
[2025-10-03 07:57:33] [Iter  661/2250] R3[270/1200] | LR: 0.027005 | E: -42.312339 | E_var:     0.3899 | E_err:   0.009756
[2025-10-03 07:57:43] [Iter  662/2250] R3[272/1200] | LR: 0.026962 | E: -42.330982 | E_var:     0.3528 | E_err:   0.009281
[2025-10-03 07:57:54] [Iter  663/2250] R3[274/1200] | LR: 0.026920 | E: -42.332451 | E_var:     0.3909 | E_err:   0.009770
[2025-10-03 07:58:04] [Iter  664/2250] R3[276/1200] | LR: 0.026876 | E: -42.318511 | E_var:     0.3527 | E_err:   0.009280
[2025-10-03 07:58:15] [Iter  665/2250] R3[278/1200] | LR: 0.026833 | E: -42.317830 | E_var:     0.3711 | E_err:   0.009518
[2025-10-03 07:58:26] [Iter  666/2250] R3[280/1200] | LR: 0.026789 | E: -42.307606 | E_var:     0.3434 | E_err:   0.009157
[2025-10-03 07:58:36] [Iter  667/2250] R3[282/1200] | LR: 0.026745 | E: -42.330133 | E_var:     0.4308 | E_err:   0.010256
[2025-10-03 07:58:47] [Iter  668/2250] R3[284/1200] | LR: 0.026701 | E: -42.321481 | E_var:     0.5475 | E_err:   0.011562
[2025-10-03 07:58:57] [Iter  669/2250] R3[286/1200] | LR: 0.026657 | E: -42.333469 | E_var:     0.5719 | E_err:   0.011816
[2025-10-03 07:59:08] [Iter  670/2250] R3[288/1200] | LR: 0.026612 | E: -42.314197 | E_var:     0.3810 | E_err:   0.009645
[2025-10-03 07:59:18] [Iter  671/2250] R3[290/1200] | LR: 0.026567 | E: -42.330956 | E_var:     0.4572 | E_err:   0.010565
[2025-10-03 07:59:29] [Iter  672/2250] R3[292/1200] | LR: 0.026522 | E: -42.318474 | E_var:     0.3626 | E_err:   0.009409
[2025-10-03 07:59:39] [Iter  673/2250] R3[294/1200] | LR: 0.026477 | E: -42.305418 | E_var:     0.4035 | E_err:   0.009926
[2025-10-03 07:59:50] [Iter  674/2250] R3[296/1200] | LR: 0.026431 | E: -42.336591 | E_var:     0.3458 | E_err:   0.009188
[2025-10-03 08:00:00] [Iter  675/2250] R3[298/1200] | LR: 0.026385 | E: -42.316355 | E_var:     0.3580 | E_err:   0.009350
[2025-10-03 08:00:11] [Iter  676/2250] R3[300/1200] | LR: 0.026339 | E: -42.339062 | E_var:     0.3768 | E_err:   0.009591
[2025-10-03 08:00:21] [Iter  677/2250] R3[302/1200] | LR: 0.026292 | E: -42.318438 | E_var:     0.3421 | E_err:   0.009139
[2025-10-03 08:00:32] [Iter  678/2250] R3[304/1200] | LR: 0.026246 | E: -42.322736 | E_var:     0.3531 | E_err:   0.009284
[2025-10-03 08:00:42] [Iter  679/2250] R3[306/1200] | LR: 0.026199 | E: -42.325239 | E_var:     0.3648 | E_err:   0.009437
[2025-10-03 08:00:53] [Iter  680/2250] R3[308/1200] | LR: 0.026152 | E: -42.317680 | E_var:     0.4527 | E_err:   0.010513
[2025-10-03 08:01:03] [Iter  681/2250] R3[310/1200] | LR: 0.026104 | E: -42.312702 | E_var:     0.3772 | E_err:   0.009597
[2025-10-03 08:01:14] [Iter  682/2250] R3[312/1200] | LR: 0.026057 | E: -42.328647 | E_var:     0.3757 | E_err:   0.009577
[2025-10-03 08:01:24] [Iter  683/2250] R3[314/1200] | LR: 0.026009 | E: -42.310120 | E_var:     0.4058 | E_err:   0.009953
[2025-10-03 08:01:35] [Iter  684/2250] R3[316/1200] | LR: 0.025961 | E: -42.341682 | E_var:     0.5695 | E_err:   0.011792
[2025-10-03 08:01:45] [Iter  685/2250] R3[318/1200] | LR: 0.025913 | E: -42.341720 | E_var:     0.3875 | E_err:   0.009726
[2025-10-03 08:01:56] [Iter  686/2250] R3[320/1200] | LR: 0.025864 | E: -42.300560 | E_var:     0.3804 | E_err:   0.009637
[2025-10-03 08:02:06] [Iter  687/2250] R3[322/1200] | LR: 0.025815 | E: -42.332827 | E_var:     0.4613 | E_err:   0.010612
[2025-10-03 08:02:17] [Iter  688/2250] R3[324/1200] | LR: 0.025766 | E: -42.328127 | E_var:     0.6833 | E_err:   0.012916
[2025-10-03 08:02:28] [Iter  689/2250] R3[326/1200] | LR: 0.025717 | E: -42.320210 | E_var:     0.3842 | E_err:   0.009685
[2025-10-03 08:02:38] [Iter  690/2250] R3[328/1200] | LR: 0.025668 | E: -42.316714 | E_var:     0.4187 | E_err:   0.010111
[2025-10-03 08:02:49] [Iter  691/2250] R3[330/1200] | LR: 0.025618 | E: -42.318291 | E_var:     0.3601 | E_err:   0.009377
[2025-10-03 08:02:59] [Iter  692/2250] R3[332/1200] | LR: 0.025568 | E: -42.331750 | E_var:     0.4573 | E_err:   0.010566
[2025-10-03 08:03:10] [Iter  693/2250] R3[334/1200] | LR: 0.025518 | E: -42.325343 | E_var:     0.3620 | E_err:   0.009400
[2025-10-03 08:03:20] [Iter  694/2250] R3[336/1200] | LR: 0.025468 | E: -42.325928 | E_var:     0.3812 | E_err:   0.009647
[2025-10-03 08:03:31] [Iter  695/2250] R3[338/1200] | LR: 0.025417 | E: -42.318416 | E_var:     0.4007 | E_err:   0.009890
[2025-10-03 08:03:41] [Iter  696/2250] R3[340/1200] | LR: 0.025367 | E: -42.306408 | E_var:     0.3989 | E_err:   0.009869
[2025-10-03 08:03:52] [Iter  697/2250] R3[342/1200] | LR: 0.025316 | E: -42.319997 | E_var:     0.4090 | E_err:   0.009993
[2025-10-03 08:04:02] [Iter  698/2250] R3[344/1200] | LR: 0.025264 | E: -42.320431 | E_var:     0.3552 | E_err:   0.009312
[2025-10-03 08:04:13] [Iter  699/2250] R3[346/1200] | LR: 0.025213 | E: -42.306005 | E_var:     0.4472 | E_err:   0.010449
[2025-10-03 08:04:23] [Iter  700/2250] R3[348/1200] | LR: 0.025161 | E: -42.328668 | E_var:     0.3764 | E_err:   0.009586
[2025-10-03 08:04:34] [Iter  701/2250] R3[350/1200] | LR: 0.025110 | E: -42.329851 | E_var:     0.3916 | E_err:   0.009778
[2025-10-03 08:04:44] [Iter  702/2250] R3[352/1200] | LR: 0.025057 | E: -42.313402 | E_var:     0.3295 | E_err:   0.008970
[2025-10-03 08:04:55] [Iter  703/2250] R3[354/1200] | LR: 0.025005 | E: -42.327981 | E_var:     0.4120 | E_err:   0.010030
[2025-10-03 08:05:05] [Iter  704/2250] R3[356/1200] | LR: 0.024953 | E: -42.329829 | E_var:     0.4095 | E_err:   0.009999
[2025-10-03 08:05:16] [Iter  705/2250] R3[358/1200] | LR: 0.024900 | E: -42.321228 | E_var:     0.6314 | E_err:   0.012415
[2025-10-03 08:05:26] [Iter  706/2250] R3[360/1200] | LR: 0.024847 | E: -42.322733 | E_var:     0.4283 | E_err:   0.010226
[2025-10-03 08:05:37] [Iter  707/2250] R3[362/1200] | LR: 0.024794 | E: -42.327348 | E_var:     0.4039 | E_err:   0.009930
[2025-10-03 08:05:47] [Iter  708/2250] R3[364/1200] | LR: 0.024741 | E: -42.331741 | E_var:     0.4585 | E_err:   0.010580
[2025-10-03 08:05:58] [Iter  709/2250] R3[366/1200] | LR: 0.024688 | E: -42.343075 | E_var:     0.3489 | E_err:   0.009230
[2025-10-03 08:06:08] [Iter  710/2250] R3[368/1200] | LR: 0.024634 | E: -42.315831 | E_var:     0.4791 | E_err:   0.010815
[2025-10-03 08:06:19] [Iter  711/2250] R3[370/1200] | LR: 0.024580 | E: -42.308182 | E_var:     0.3712 | E_err:   0.009519
[2025-10-03 08:06:29] [Iter  712/2250] R3[372/1200] | LR: 0.024526 | E: -42.318049 | E_var:     0.3475 | E_err:   0.009211
[2025-10-03 08:06:40] [Iter  713/2250] R3[374/1200] | LR: 0.024472 | E: -42.317843 | E_var:     0.3626 | E_err:   0.009409
[2025-10-03 08:06:51] [Iter  714/2250] R3[376/1200] | LR: 0.024417 | E: -42.321414 | E_var:     0.3554 | E_err:   0.009315
[2025-10-03 08:07:01] [Iter  715/2250] R3[378/1200] | LR: 0.024363 | E: -42.309252 | E_var:     0.4291 | E_err:   0.010235
[2025-10-03 08:07:12] [Iter  716/2250] R3[380/1200] | LR: 0.024308 | E: -42.324930 | E_var:     0.4564 | E_err:   0.010556
[2025-10-03 08:07:22] [Iter  717/2250] R3[382/1200] | LR: 0.024253 | E: -42.336600 | E_var:     0.3849 | E_err:   0.009693
[2025-10-03 08:07:33] [Iter  718/2250] R3[384/1200] | LR: 0.024198 | E: -42.307742 | E_var:     0.4239 | E_err:   0.010173
[2025-10-03 08:07:43] [Iter  719/2250] R3[386/1200] | LR: 0.024142 | E: -42.330292 | E_var:     0.4339 | E_err:   0.010293
[2025-10-03 08:07:54] [Iter  720/2250] R3[388/1200] | LR: 0.024087 | E: -42.336251 | E_var:     0.3605 | E_err:   0.009381
[2025-10-03 08:08:04] [Iter  721/2250] R3[390/1200] | LR: 0.024031 | E: -42.306589 | E_var:     0.3607 | E_err:   0.009385
[2025-10-03 08:08:15] [Iter  722/2250] R3[392/1200] | LR: 0.023975 | E: -42.328101 | E_var:     0.4059 | E_err:   0.009955
[2025-10-03 08:08:25] [Iter  723/2250] R3[394/1200] | LR: 0.023919 | E: -42.315477 | E_var:     0.5147 | E_err:   0.011209
[2025-10-03 08:08:36] [Iter  724/2250] R3[396/1200] | LR: 0.023863 | E: -42.316830 | E_var:     0.3224 | E_err:   0.008872
[2025-10-03 08:08:46] [Iter  725/2250] R3[398/1200] | LR: 0.023807 | E: -42.325561 | E_var:     0.3432 | E_err:   0.009153
[2025-10-03 08:08:57] [Iter  726/2250] R3[400/1200] | LR: 0.023750 | E: -42.334437 | E_var:     0.3747 | E_err:   0.009565
[2025-10-03 08:09:07] [Iter  727/2250] R3[402/1200] | LR: 0.023693 | E: -42.326209 | E_var:     0.4299 | E_err:   0.010245
[2025-10-03 08:09:18] [Iter  728/2250] R3[404/1200] | LR: 0.023636 | E: -42.311039 | E_var:     0.4183 | E_err:   0.010105
[2025-10-03 08:09:28] [Iter  729/2250] R3[406/1200] | LR: 0.023579 | E: -42.334218 | E_var:     0.3651 | E_err:   0.009441
[2025-10-03 08:09:39] [Iter  730/2250] R3[408/1200] | LR: 0.023522 | E: -42.318573 | E_var:     0.3751 | E_err:   0.009569
[2025-10-03 08:09:49] [Iter  731/2250] R3[410/1200] | LR: 0.023464 | E: -42.326924 | E_var:     0.3886 | E_err:   0.009741
[2025-10-03 08:10:00] [Iter  732/2250] R3[412/1200] | LR: 0.023407 | E: -42.320813 | E_var:     0.3635 | E_err:   0.009421
[2025-10-03 08:10:10] [Iter  733/2250] R3[414/1200] | LR: 0.023349 | E: -42.332046 | E_var:     0.3914 | E_err:   0.009775
[2025-10-03 08:10:21] [Iter  734/2250] R3[416/1200] | LR: 0.023291 | E: -42.325187 | E_var:     0.3657 | E_err:   0.009448
[2025-10-03 08:10:31] [Iter  735/2250] R3[418/1200] | LR: 0.023233 | E: -42.313038 | E_var:     0.3687 | E_err:   0.009487
[2025-10-03 08:10:42] [Iter  736/2250] R3[420/1200] | LR: 0.023175 | E: -42.327404 | E_var:     0.7073 | E_err:   0.013141
[2025-10-03 08:10:53] [Iter  737/2250] R3[422/1200] | LR: 0.023116 | E: -42.333699 | E_var:     0.3592 | E_err:   0.009364
[2025-10-03 08:11:03] [Iter  738/2250] R3[424/1200] | LR: 0.023058 | E: -42.321658 | E_var:     0.3991 | E_err:   0.009871
[2025-10-03 08:11:14] [Iter  739/2250] R3[426/1200] | LR: 0.022999 | E: -42.320177 | E_var:     0.3733 | E_err:   0.009546
[2025-10-03 08:11:24] [Iter  740/2250] R3[428/1200] | LR: 0.022940 | E: -42.330548 | E_var:     0.3657 | E_err:   0.009450
[2025-10-03 08:11:35] [Iter  741/2250] R3[430/1200] | LR: 0.022881 | E: -42.322189 | E_var:     0.3688 | E_err:   0.009489
[2025-10-03 08:11:45] [Iter  742/2250] R3[432/1200] | LR: 0.022822 | E: -42.317978 | E_var:     0.3323 | E_err:   0.009007
[2025-10-03 08:11:56] [Iter  743/2250] R3[434/1200] | LR: 0.022763 | E: -42.344364 | E_var:     0.3727 | E_err:   0.009539
[2025-10-03 08:12:06] [Iter  744/2250] R3[436/1200] | LR: 0.022704 | E: -42.328911 | E_var:     0.3088 | E_err:   0.008683
[2025-10-03 08:12:17] [Iter  745/2250] R3[438/1200] | LR: 0.022644 | E: -42.320175 | E_var:     0.6067 | E_err:   0.012170
[2025-10-03 08:12:27] [Iter  746/2250] R3[440/1200] | LR: 0.022584 | E: -42.325957 | E_var:     0.4273 | E_err:   0.010213
[2025-10-03 08:12:38] [Iter  747/2250] R3[442/1200] | LR: 0.022524 | E: -42.317384 | E_var:     0.4340 | E_err:   0.010293
[2025-10-03 08:12:48] [Iter  748/2250] R3[444/1200] | LR: 0.022464 | E: -42.302021 | E_var:     0.4040 | E_err:   0.009931
[2025-10-03 08:12:59] [Iter  749/2250] R3[446/1200] | LR: 0.022404 | E: -42.331524 | E_var:     0.3613 | E_err:   0.009392
[2025-10-03 08:13:09] [Iter  750/2250] R3[448/1200] | LR: 0.022344 | E: -42.328074 | E_var:     0.3083 | E_err:   0.008676
[2025-10-03 08:13:20] [Iter  751/2250] R3[450/1200] | LR: 0.022284 | E: -42.317792 | E_var:     0.4384 | E_err:   0.010346
[2025-10-03 08:13:30] [Iter  752/2250] R3[452/1200] | LR: 0.022223 | E: -42.310936 | E_var:     0.3054 | E_err:   0.008636
[2025-10-03 08:13:41] [Iter  753/2250] R3[454/1200] | LR: 0.022162 | E: -42.333300 | E_var:     0.4158 | E_err:   0.010076
[2025-10-03 08:13:51] [Iter  754/2250] R3[456/1200] | LR: 0.022102 | E: -42.319342 | E_var:     0.3587 | E_err:   0.009358
[2025-10-03 08:14:02] [Iter  755/2250] R3[458/1200] | LR: 0.022041 | E: -42.324666 | E_var:     0.3664 | E_err:   0.009458
[2025-10-03 08:14:12] [Iter  756/2250] R3[460/1200] | LR: 0.021980 | E: -42.321989 | E_var:     0.3810 | E_err:   0.009644
[2025-10-03 08:14:23] [Iter  757/2250] R3[462/1200] | LR: 0.021918 | E: -42.325333 | E_var:     0.4216 | E_err:   0.010146
[2025-10-03 08:14:34] [Iter  758/2250] R3[464/1200] | LR: 0.021857 | E: -42.335814 | E_var:     0.3291 | E_err:   0.008963
[2025-10-03 08:14:44] [Iter  759/2250] R3[466/1200] | LR: 0.021796 | E: -42.329162 | E_var:     0.3280 | E_err:   0.008948
[2025-10-03 08:14:55] [Iter  760/2250] R3[468/1200] | LR: 0.021734 | E: -42.327839 | E_var:     0.4413 | E_err:   0.010380
[2025-10-03 08:15:05] [Iter  761/2250] R3[470/1200] | LR: 0.021673 | E: -42.319573 | E_var:     0.3355 | E_err:   0.009051
[2025-10-03 08:15:16] [Iter  762/2250] R3[472/1200] | LR: 0.021611 | E: -42.337646 | E_var:     0.3636 | E_err:   0.009421
[2025-10-03 08:15:26] [Iter  763/2250] R3[474/1200] | LR: 0.021549 | E: -42.317514 | E_var:     0.3522 | E_err:   0.009273
[2025-10-03 08:15:37] [Iter  764/2250] R3[476/1200] | LR: 0.021487 | E: -42.328284 | E_var:     0.3895 | E_err:   0.009752
[2025-10-03 08:15:47] [Iter  765/2250] R3[478/1200] | LR: 0.021425 | E: -42.335097 | E_var:     0.3273 | E_err:   0.008939
[2025-10-03 08:15:58] [Iter  766/2250] R3[480/1200] | LR: 0.021363 | E: -42.346219 | E_var:     0.3917 | E_err:   0.009779
[2025-10-03 08:16:08] [Iter  767/2250] R3[482/1200] | LR: 0.021300 | E: -42.319562 | E_var:     0.7422 | E_err:   0.013461
[2025-10-03 08:16:19] [Iter  768/2250] R3[484/1200] | LR: 0.021238 | E: -42.321748 | E_var:     0.3904 | E_err:   0.009763
[2025-10-03 08:16:29] [Iter  769/2250] R3[486/1200] | LR: 0.021176 | E: -42.331825 | E_var:     0.3613 | E_err:   0.009392
[2025-10-03 08:16:40] [Iter  770/2250] R3[488/1200] | LR: 0.021113 | E: -42.337123 | E_var:     0.3527 | E_err:   0.009279
[2025-10-03 08:16:50] [Iter  771/2250] R3[490/1200] | LR: 0.021050 | E: -42.327132 | E_var:     0.3536 | E_err:   0.009291
[2025-10-03 08:17:01] [Iter  772/2250] R3[492/1200] | LR: 0.020987 | E: -42.333080 | E_var:     0.3742 | E_err:   0.009558
[2025-10-03 08:17:11] [Iter  773/2250] R3[494/1200] | LR: 0.020924 | E: -42.339858 | E_var:     0.4397 | E_err:   0.010360
[2025-10-03 08:17:22] [Iter  774/2250] R3[496/1200] | LR: 0.020861 | E: -42.328622 | E_var:     0.3631 | E_err:   0.009415
[2025-10-03 08:17:32] [Iter  775/2250] R3[498/1200] | LR: 0.020798 | E: -42.327931 | E_var:     0.3193 | E_err:   0.008829
[2025-10-03 08:17:43] [Iter  776/2250] R3[500/1200] | LR: 0.020735 | E: -42.340667 | E_var:     0.3413 | E_err:   0.009129
[2025-10-03 08:17:53] [Iter  777/2250] R3[502/1200] | LR: 0.020672 | E: -42.332865 | E_var:     0.3570 | E_err:   0.009336
[2025-10-03 08:18:04] [Iter  778/2250] R3[504/1200] | LR: 0.020609 | E: -42.331684 | E_var:     0.3489 | E_err:   0.009229
[2025-10-03 08:18:14] [Iter  779/2250] R3[506/1200] | LR: 0.020545 | E: -42.320611 | E_var:     0.3250 | E_err:   0.008908
[2025-10-03 08:18:25] [Iter  780/2250] R3[508/1200] | LR: 0.020482 | E: -42.323345 | E_var:     0.3251 | E_err:   0.008909
[2025-10-03 08:18:35] [Iter  781/2250] R3[510/1200] | LR: 0.020418 | E: -42.322775 | E_var:     0.3732 | E_err:   0.009546
[2025-10-03 08:18:46] [Iter  782/2250] R3[512/1200] | LR: 0.020354 | E: -42.317944 | E_var:     0.3660 | E_err:   0.009452
[2025-10-03 08:18:57] [Iter  783/2250] R3[514/1200] | LR: 0.020291 | E: -42.314812 | E_var:     0.3673 | E_err:   0.009470
[2025-10-03 08:19:07] [Iter  784/2250] R3[516/1200] | LR: 0.020227 | E: -42.329229 | E_var:     0.3880 | E_err:   0.009733
[2025-10-03 08:19:18] [Iter  785/2250] R3[518/1200] | LR: 0.020163 | E: -42.323352 | E_var:     0.3120 | E_err:   0.008727
[2025-10-03 08:19:28] [Iter  786/2250] R3[520/1200] | LR: 0.020099 | E: -42.325624 | E_var:     0.3213 | E_err:   0.008857
[2025-10-03 08:19:39] [Iter  787/2250] R3[522/1200] | LR: 0.020035 | E: -42.334243 | E_var:     0.3309 | E_err:   0.008988
[2025-10-03 08:19:49] [Iter  788/2250] R3[524/1200] | LR: 0.019971 | E: -42.315957 | E_var:     0.5224 | E_err:   0.011293
[2025-10-03 08:20:00] [Iter  789/2250] R3[526/1200] | LR: 0.019907 | E: -42.330653 | E_var:     0.3572 | E_err:   0.009338
[2025-10-03 08:20:10] [Iter  790/2250] R3[528/1200] | LR: 0.019842 | E: -42.330736 | E_var:     0.4780 | E_err:   0.010803
[2025-10-03 08:20:21] [Iter  791/2250] R3[530/1200] | LR: 0.019778 | E: -42.330497 | E_var:     0.4094 | E_err:   0.009998
[2025-10-03 08:20:31] [Iter  792/2250] R3[532/1200] | LR: 0.019714 | E: -42.340123 | E_var:     0.3438 | E_err:   0.009161
[2025-10-03 08:20:42] [Iter  793/2250] R3[534/1200] | LR: 0.019649 | E: -42.320386 | E_var:     0.3989 | E_err:   0.009868
[2025-10-03 08:20:52] [Iter  794/2250] R3[536/1200] | LR: 0.019585 | E: -42.317657 | E_var:     0.3758 | E_err:   0.009578
[2025-10-03 08:21:03] [Iter  795/2250] R3[538/1200] | LR: 0.019520 | E: -42.327840 | E_var:     0.3525 | E_err:   0.009277
[2025-10-03 08:21:13] [Iter  796/2250] R3[540/1200] | LR: 0.019455 | E: -42.324291 | E_var:     0.4440 | E_err:   0.010411
[2025-10-03 08:21:24] [Iter  797/2250] R3[542/1200] | LR: 0.019391 | E: -42.328468 | E_var:     0.3326 | E_err:   0.009012
[2025-10-03 08:21:34] [Iter  798/2250] R3[544/1200] | LR: 0.019326 | E: -42.347442 | E_var:     0.3797 | E_err:   0.009628
[2025-10-03 08:21:45] [Iter  799/2250] R3[546/1200] | LR: 0.019261 | E: -42.334803 | E_var:     0.3759 | E_err:   0.009580
[2025-10-03 08:21:55] [Iter  800/2250] R3[548/1200] | LR: 0.019196 | E: -42.326031 | E_var:     0.3785 | E_err:   0.009613
[2025-10-03 08:21:55] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-03 08:22:06] [Iter  801/2250] R3[550/1200] | LR: 0.019132 | E: -42.343619 | E_var:     0.3507 | E_err:   0.009254
[2025-10-03 08:22:16] [Iter  802/2250] R3[552/1200] | LR: 0.019067 | E: -42.335123 | E_var:     0.3617 | E_err:   0.009397
[2025-10-03 08:22:27] [Iter  803/2250] R3[554/1200] | LR: 0.019002 | E: -42.330867 | E_var:     0.3983 | E_err:   0.009861
[2025-10-03 08:22:38] [Iter  804/2250] R3[556/1200] | LR: 0.018937 | E: -42.332436 | E_var:     0.4475 | E_err:   0.010452
[2025-10-03 08:22:48] [Iter  805/2250] R3[558/1200] | LR: 0.018872 | E: -42.317927 | E_var:     0.3952 | E_err:   0.009822
[2025-10-03 08:22:59] [Iter  806/2250] R3[560/1200] | LR: 0.018807 | E: -42.330117 | E_var:     0.3385 | E_err:   0.009090
[2025-10-03 08:23:09] [Iter  807/2250] R3[562/1200] | LR: 0.018741 | E: -42.323688 | E_var:     0.3618 | E_err:   0.009399
[2025-10-03 08:23:20] [Iter  808/2250] R3[564/1200] | LR: 0.018676 | E: -42.322047 | E_var:     0.3679 | E_err:   0.009477
[2025-10-03 08:23:30] [Iter  809/2250] R3[566/1200] | LR: 0.018611 | E: -42.327890 | E_var:     0.6170 | E_err:   0.012274
[2025-10-03 08:23:41] [Iter  810/2250] R3[568/1200] | LR: 0.018546 | E: -42.338871 | E_var:     0.4780 | E_err:   0.010802
[2025-10-03 08:23:51] [Iter  811/2250] R3[570/1200] | LR: 0.018481 | E: -42.328719 | E_var:     0.3422 | E_err:   0.009140
[2025-10-03 08:24:02] [Iter  812/2250] R3[572/1200] | LR: 0.018415 | E: -42.321728 | E_var:     0.4314 | E_err:   0.010263
[2025-10-03 08:24:12] [Iter  813/2250] R3[574/1200] | LR: 0.018350 | E: -42.337374 | E_var:     0.3913 | E_err:   0.009774
[2025-10-03 08:24:23] [Iter  814/2250] R3[576/1200] | LR: 0.018285 | E: -42.305078 | E_var:     1.5207 | E_err:   0.019268
[2025-10-03 08:24:33] [Iter  815/2250] R3[578/1200] | LR: 0.018220 | E: -42.324481 | E_var:     1.6280 | E_err:   0.019936
[2025-10-03 08:24:44] [Iter  816/2250] R3[580/1200] | LR: 0.018154 | E: -42.319635 | E_var:     0.3540 | E_err:   0.009296
[2025-10-03 08:24:54] [Iter  817/2250] R3[582/1200] | LR: 0.018089 | E: -42.338927 | E_var:     0.3486 | E_err:   0.009225
[2025-10-03 08:25:05] [Iter  818/2250] R3[584/1200] | LR: 0.018023 | E: -42.333382 | E_var:     0.3204 | E_err:   0.008845
[2025-10-03 08:25:15] [Iter  819/2250] R3[586/1200] | LR: 0.017958 | E: -42.329664 | E_var:     0.3768 | E_err:   0.009591
[2025-10-03 08:25:26] [Iter  820/2250] R3[588/1200] | LR: 0.017893 | E: -42.329343 | E_var:     0.4192 | E_err:   0.010117
[2025-10-03 08:25:36] [Iter  821/2250] R3[590/1200] | LR: 0.017827 | E: -42.330555 | E_var:     0.4409 | E_err:   0.010375
[2025-10-03 08:25:47] [Iter  822/2250] R3[592/1200] | LR: 0.017762 | E: -42.327803 | E_var:     0.3710 | E_err:   0.009517
[2025-10-03 08:25:57] [Iter  823/2250] R3[594/1200] | LR: 0.017696 | E: -42.330084 | E_var:     0.2905 | E_err:   0.008422
[2025-10-03 08:26:08] [Iter  824/2250] R3[596/1200] | LR: 0.017631 | E: -42.331899 | E_var:     0.3344 | E_err:   0.009036
[2025-10-03 08:26:19] [Iter  825/2250] R3[598/1200] | LR: 0.017565 | E: -42.335847 | E_var:     0.3192 | E_err:   0.008827
[2025-10-03 08:26:29] [Iter  826/2250] R3[600/1200] | LR: 0.017500 | E: -42.323145 | E_var:     0.3307 | E_err:   0.008986
[2025-10-03 08:26:40] [Iter  827/2250] R3[602/1200] | LR: 0.017435 | E: -42.330790 | E_var:     0.3077 | E_err:   0.008668
[2025-10-03 08:26:50] [Iter  828/2250] R3[604/1200] | LR: 0.017369 | E: -42.315640 | E_var:     0.3360 | E_err:   0.009057
[2025-10-03 08:27:01] [Iter  829/2250] R3[606/1200] | LR: 0.017304 | E: -42.320771 | E_var:     0.3568 | E_err:   0.009333
[2025-10-03 08:27:11] [Iter  830/2250] R3[608/1200] | LR: 0.017238 | E: -42.343223 | E_var:     0.3456 | E_err:   0.009186
[2025-10-03 08:27:22] [Iter  831/2250] R3[610/1200] | LR: 0.017173 | E: -42.336001 | E_var:     0.4582 | E_err:   0.010577
[2025-10-03 08:27:32] [Iter  832/2250] R3[612/1200] | LR: 0.017107 | E: -42.324592 | E_var:     0.3510 | E_err:   0.009257
[2025-10-03 08:27:43] [Iter  833/2250] R3[614/1200] | LR: 0.017042 | E: -42.333259 | E_var:     0.3870 | E_err:   0.009720
[2025-10-03 08:27:53] [Iter  834/2250] R3[616/1200] | LR: 0.016977 | E: -42.325618 | E_var:     0.3264 | E_err:   0.008926
[2025-10-03 08:28:04] [Iter  835/2250] R3[618/1200] | LR: 0.016911 | E: -42.332919 | E_var:     0.3134 | E_err:   0.008748
[2025-10-03 08:28:14] [Iter  836/2250] R3[620/1200] | LR: 0.016846 | E: -42.335769 | E_var:     0.3751 | E_err:   0.009569
[2025-10-03 08:28:25] [Iter  837/2250] R3[622/1200] | LR: 0.016780 | E: -42.322015 | E_var:     0.2931 | E_err:   0.008459
[2025-10-03 08:28:35] [Iter  838/2250] R3[624/1200] | LR: 0.016715 | E: -42.326279 | E_var:     0.3450 | E_err:   0.009177
[2025-10-03 08:28:46] [Iter  839/2250] R3[626/1200] | LR: 0.016650 | E: -42.348204 | E_var:     0.3640 | E_err:   0.009427
[2025-10-03 08:28:56] [Iter  840/2250] R3[628/1200] | LR: 0.016585 | E: -42.311548 | E_var:     0.5701 | E_err:   0.011798
[2025-10-03 08:29:07] [Iter  841/2250] R3[630/1200] | LR: 0.016519 | E: -42.327270 | E_var:     0.2804 | E_err:   0.008274
[2025-10-03 08:29:17] [Iter  842/2250] R3[632/1200] | LR: 0.016454 | E: -42.325565 | E_var:     0.3763 | E_err:   0.009585
[2025-10-03 08:29:28] [Iter  843/2250] R3[634/1200] | LR: 0.016389 | E: -42.320426 | E_var:     0.3534 | E_err:   0.009288
[2025-10-03 08:29:38] [Iter  844/2250] R3[636/1200] | LR: 0.016324 | E: -42.343138 | E_var:     0.4055 | E_err:   0.009950
[2025-10-03 08:29:49] [Iter  845/2250] R3[638/1200] | LR: 0.016259 | E: -42.340933 | E_var:     0.3589 | E_err:   0.009360
[2025-10-03 08:30:00] [Iter  846/2250] R3[640/1200] | LR: 0.016193 | E: -42.327652 | E_var:     0.5401 | E_err:   0.011483
[2025-10-03 08:30:10] [Iter  847/2250] R3[642/1200] | LR: 0.016128 | E: -42.333983 | E_var:     0.4851 | E_err:   0.010883
[2025-10-03 08:30:21] [Iter  848/2250] R3[644/1200] | LR: 0.016063 | E: -42.340269 | E_var:     0.4884 | E_err:   0.010920
[2025-10-03 08:30:31] [Iter  849/2250] R3[646/1200] | LR: 0.015998 | E: -42.317308 | E_var:     0.5605 | E_err:   0.011698
[2025-10-03 08:30:42] [Iter  850/2250] R3[648/1200] | LR: 0.015933 | E: -42.341115 | E_var:     0.3671 | E_err:   0.009467
[2025-10-03 08:30:52] [Iter  851/2250] R3[650/1200] | LR: 0.015868 | E: -42.314890 | E_var:     0.4759 | E_err:   0.010779
[2025-10-03 08:31:03] [Iter  852/2250] R3[652/1200] | LR: 0.015804 | E: -42.336973 | E_var:     0.3095 | E_err:   0.008692
[2025-10-03 08:31:13] [Iter  853/2250] R3[654/1200] | LR: 0.015739 | E: -42.342213 | E_var:     0.3235 | E_err:   0.008887
[2025-10-03 08:31:24] [Iter  854/2250] R3[656/1200] | LR: 0.015674 | E: -42.325808 | E_var:     0.3561 | E_err:   0.009324
[2025-10-03 08:31:34] [Iter  855/2250] R3[658/1200] | LR: 0.015609 | E: -42.328787 | E_var:     0.3664 | E_err:   0.009458
[2025-10-03 08:31:45] [Iter  856/2250] R3[660/1200] | LR: 0.015545 | E: -42.339824 | E_var:     0.4240 | E_err:   0.010174
[2025-10-03 08:31:55] [Iter  857/2250] R3[662/1200] | LR: 0.015480 | E: -42.333666 | E_var:     0.4248 | E_err:   0.010184
[2025-10-03 08:32:06] [Iter  858/2250] R3[664/1200] | LR: 0.015415 | E: -42.319367 | E_var:     0.3206 | E_err:   0.008847
[2025-10-03 08:32:16] [Iter  859/2250] R3[666/1200] | LR: 0.015351 | E: -42.360420 | E_var:     0.3686 | E_err:   0.009486
[2025-10-03 08:32:27] [Iter  860/2250] R3[668/1200] | LR: 0.015286 | E: -42.345828 | E_var:     0.4372 | E_err:   0.010331
[2025-10-03 08:32:37] [Iter  861/2250] R3[670/1200] | LR: 0.015222 | E: -42.321465 | E_var:     0.3570 | E_err:   0.009336
[2025-10-03 08:32:48] [Iter  862/2250] R3[672/1200] | LR: 0.015158 | E: -42.338715 | E_var:     0.4918 | E_err:   0.010958
[2025-10-03 08:32:58] [Iter  863/2250] R3[674/1200] | LR: 0.015093 | E: -42.331148 | E_var:     0.3631 | E_err:   0.009415
[2025-10-03 08:33:09] [Iter  864/2250] R3[676/1200] | LR: 0.015029 | E: -42.347702 | E_var:     0.3301 | E_err:   0.008978
[2025-10-03 08:33:19] [Iter  865/2250] R3[678/1200] | LR: 0.014965 | E: -42.333857 | E_var:     0.4696 | E_err:   0.010708
[2025-10-03 08:33:30] [Iter  866/2250] R3[680/1200] | LR: 0.014901 | E: -42.336341 | E_var:     0.3320 | E_err:   0.009003
[2025-10-03 08:33:40] [Iter  867/2250] R3[682/1200] | LR: 0.014837 | E: -42.341723 | E_var:     0.3704 | E_err:   0.009510
[2025-10-03 08:33:51] [Iter  868/2250] R3[684/1200] | LR: 0.014773 | E: -42.334946 | E_var:     0.3015 | E_err:   0.008579
[2025-10-03 08:34:01] [Iter  869/2250] R3[686/1200] | LR: 0.014709 | E: -42.342273 | E_var:     0.3897 | E_err:   0.009754
[2025-10-03 08:34:12] [Iter  870/2250] R3[688/1200] | LR: 0.014646 | E: -42.328388 | E_var:     0.4024 | E_err:   0.009912
[2025-10-03 08:34:23] [Iter  871/2250] R3[690/1200] | LR: 0.014582 | E: -42.333907 | E_var:     0.3171 | E_err:   0.008798
[2025-10-03 08:34:33] [Iter  872/2250] R3[692/1200] | LR: 0.014518 | E: -42.329387 | E_var:     0.3246 | E_err:   0.008902
[2025-10-03 08:34:44] [Iter  873/2250] R3[694/1200] | LR: 0.014455 | E: -42.332975 | E_var:     0.4291 | E_err:   0.010236
[2025-10-03 08:34:54] [Iter  874/2250] R3[696/1200] | LR: 0.014391 | E: -42.344778 | E_var:     0.3358 | E_err:   0.009055
[2025-10-03 08:35:05] [Iter  875/2250] R3[698/1200] | LR: 0.014328 | E: -42.329804 | E_var:     0.2936 | E_err:   0.008466
[2025-10-03 08:35:15] [Iter  876/2250] R3[700/1200] | LR: 0.014265 | E: -42.311019 | E_var:     1.8967 | E_err:   0.021519
[2025-10-03 08:35:26] [Iter  877/2250] R3[702/1200] | LR: 0.014202 | E: -42.309953 | E_var:     0.3231 | E_err:   0.008881
[2025-10-03 08:35:36] [Iter  878/2250] R3[704/1200] | LR: 0.014139 | E: -42.331475 | E_var:     0.3196 | E_err:   0.008834
[2025-10-03 08:35:47] [Iter  879/2250] R3[706/1200] | LR: 0.014076 | E: -42.328563 | E_var:     0.3368 | E_err:   0.009068
[2025-10-03 08:35:57] [Iter  880/2250] R3[708/1200] | LR: 0.014013 | E: -42.319891 | E_var:     0.3777 | E_err:   0.009603
[2025-10-03 08:36:08] [Iter  881/2250] R3[710/1200] | LR: 0.013950 | E: -42.331014 | E_var:     0.3698 | E_err:   0.009501
[2025-10-03 08:36:18] [Iter  882/2250] R3[712/1200] | LR: 0.013887 | E: -42.307873 | E_var:     0.3976 | E_err:   0.009853
[2025-10-03 08:36:29] [Iter  883/2250] R3[714/1200] | LR: 0.013824 | E: -42.312749 | E_var:     0.3256 | E_err:   0.008916
[2025-10-03 08:36:39] [Iter  884/2250] R3[716/1200] | LR: 0.013762 | E: -42.341562 | E_var:     0.7833 | E_err:   0.013829
[2025-10-03 08:36:50] [Iter  885/2250] R3[718/1200] | LR: 0.013700 | E: -42.327766 | E_var:     0.3650 | E_err:   0.009440
[2025-10-03 08:37:00] [Iter  886/2250] R3[720/1200] | LR: 0.013637 | E: -42.340634 | E_var:     0.3252 | E_err:   0.008910
[2025-10-03 08:37:11] [Iter  887/2250] R3[722/1200] | LR: 0.013575 | E: -42.331423 | E_var:     0.4778 | E_err:   0.010800
[2025-10-03 08:37:21] [Iter  888/2250] R3[724/1200] | LR: 0.013513 | E: -42.322413 | E_var:     0.3409 | E_err:   0.009123
[2025-10-03 08:37:32] [Iter  889/2250] R3[726/1200] | LR: 0.013451 | E: -42.314315 | E_var:     0.3384 | E_err:   0.009089
[2025-10-03 08:37:42] [Iter  890/2250] R3[728/1200] | LR: 0.013389 | E: -42.344703 | E_var:     0.3795 | E_err:   0.009626
[2025-10-03 08:37:53] [Iter  891/2250] R3[730/1200] | LR: 0.013327 | E: -42.337331 | E_var:     0.3507 | E_err:   0.009253
[2025-10-03 08:38:03] [Iter  892/2250] R3[732/1200] | LR: 0.013266 | E: -42.321775 | E_var:     0.4336 | E_err:   0.010288
[2025-10-03 08:38:14] [Iter  893/2250] R3[734/1200] | LR: 0.013204 | E: -42.352204 | E_var:     0.4017 | E_err:   0.009903
[2025-10-03 08:38:25] [Iter  894/2250] R3[736/1200] | LR: 0.013143 | E: -42.331048 | E_var:     0.3312 | E_err:   0.008992
[2025-10-03 08:38:35] [Iter  895/2250] R3[738/1200] | LR: 0.013082 | E: -42.342689 | E_var:     0.4450 | E_err:   0.010423
[2025-10-03 08:38:46] [Iter  896/2250] R3[740/1200] | LR: 0.013020 | E: -42.326467 | E_var:     0.3131 | E_err:   0.008742
[2025-10-03 08:38:56] [Iter  897/2250] R3[742/1200] | LR: 0.012959 | E: -42.337634 | E_var:     0.2885 | E_err:   0.008393
[2025-10-03 08:39:07] [Iter  898/2250] R3[744/1200] | LR: 0.012898 | E: -42.332709 | E_var:     0.3806 | E_err:   0.009639
[2025-10-03 08:39:17] [Iter  899/2250] R3[746/1200] | LR: 0.012838 | E: -42.336671 | E_var:     0.3788 | E_err:   0.009617
[2025-10-03 08:39:28] [Iter  900/2250] R3[748/1200] | LR: 0.012777 | E: -42.341923 | E_var:     0.3447 | E_err:   0.009174
[2025-10-03 08:39:38] [Iter  901/2250] R3[750/1200] | LR: 0.012716 | E: -42.331810 | E_var:     0.3600 | E_err:   0.009375
[2025-10-03 08:39:49] [Iter  902/2250] R3[752/1200] | LR: 0.012656 | E: -42.337744 | E_var:     0.3276 | E_err:   0.008943
[2025-10-03 08:39:59] [Iter  903/2250] R3[754/1200] | LR: 0.012596 | E: -42.342799 | E_var:     0.3211 | E_err:   0.008855
[2025-10-03 08:40:10] [Iter  904/2250] R3[756/1200] | LR: 0.012536 | E: -42.323927 | E_var:     0.3551 | E_err:   0.009310
[2025-10-03 08:40:20] [Iter  905/2250] R3[758/1200] | LR: 0.012476 | E: -42.342522 | E_var:     0.3014 | E_err:   0.008578
[2025-10-03 08:40:31] [Iter  906/2250] R3[760/1200] | LR: 0.012416 | E: -42.322992 | E_var:     0.3785 | E_err:   0.009613
[2025-10-03 08:40:41] [Iter  907/2250] R3[762/1200] | LR: 0.012356 | E: -42.338938 | E_var:     0.3652 | E_err:   0.009442
[2025-10-03 08:40:52] [Iter  908/2250] R3[764/1200] | LR: 0.012296 | E: -42.335423 | E_var:     0.3683 | E_err:   0.009483
[2025-10-03 08:41:02] [Iter  909/2250] R3[766/1200] | LR: 0.012237 | E: -42.346005 | E_var:     0.3337 | E_err:   0.009026
[2025-10-03 08:41:13] [Iter  910/2250] R3[768/1200] | LR: 0.012178 | E: -42.336525 | E_var:     0.3610 | E_err:   0.009388
[2025-10-03 08:41:23] [Iter  911/2250] R3[770/1200] | LR: 0.012119 | E: -42.334119 | E_var:     0.3137 | E_err:   0.008751
[2025-10-03 08:41:34] [Iter  912/2250] R3[772/1200] | LR: 0.012060 | E: -42.334609 | E_var:     0.3582 | E_err:   0.009351
[2025-10-03 08:41:44] [Iter  913/2250] R3[774/1200] | LR: 0.012001 | E: -42.326241 | E_var:     0.4516 | E_err:   0.010500
[2025-10-03 08:41:55] [Iter  914/2250] R3[776/1200] | LR: 0.011942 | E: -42.323228 | E_var:     0.3139 | E_err:   0.008754
[2025-10-03 08:42:06] [Iter  915/2250] R3[778/1200] | LR: 0.011884 | E: -42.346407 | E_var:     1.2408 | E_err:   0.017405
[2025-10-03 08:42:16] [Iter  916/2250] R3[780/1200] | LR: 0.011825 | E: -42.327669 | E_var:     0.3952 | E_err:   0.009823
[2025-10-03 08:42:27] [Iter  917/2250] R3[782/1200] | LR: 0.011767 | E: -42.330908 | E_var:     0.2965 | E_err:   0.008508
[2025-10-03 08:42:37] [Iter  918/2250] R3[784/1200] | LR: 0.011709 | E: -42.339483 | E_var:     0.3073 | E_err:   0.008662
[2025-10-03 08:42:48] [Iter  919/2250] R3[786/1200] | LR: 0.011651 | E: -42.341862 | E_var:     0.3942 | E_err:   0.009810
[2025-10-03 08:42:58] [Iter  920/2250] R3[788/1200] | LR: 0.011593 | E: -42.339521 | E_var:     0.3825 | E_err:   0.009663
[2025-10-03 08:43:09] [Iter  921/2250] R3[790/1200] | LR: 0.011536 | E: -42.329328 | E_var:     0.3596 | E_err:   0.009370
[2025-10-03 08:43:19] [Iter  922/2250] R3[792/1200] | LR: 0.011478 | E: -42.339359 | E_var:     0.2940 | E_err:   0.008472
[2025-10-03 08:43:30] [Iter  923/2250] R3[794/1200] | LR: 0.011421 | E: -42.334014 | E_var:     0.2983 | E_err:   0.008534
[2025-10-03 08:43:40] [Iter  924/2250] R3[796/1200] | LR: 0.011364 | E: -42.341889 | E_var:     0.3296 | E_err:   0.008970
[2025-10-03 08:43:51] [Iter  925/2250] R3[798/1200] | LR: 0.011307 | E: -42.328602 | E_var:     0.3421 | E_err:   0.009139
[2025-10-03 08:44:01] [Iter  926/2250] R3[800/1200] | LR: 0.011250 | E: -42.325801 | E_var:     0.4639 | E_err:   0.010642
[2025-10-03 08:44:12] [Iter  927/2250] R3[802/1200] | LR: 0.011193 | E: -42.340166 | E_var:     0.3458 | E_err:   0.009188
[2025-10-03 08:44:22] [Iter  928/2250] R3[804/1200] | LR: 0.011137 | E: -42.347141 | E_var:     0.4095 | E_err:   0.009999
[2025-10-03 08:44:33] [Iter  929/2250] R3[806/1200] | LR: 0.011081 | E: -42.347586 | E_var:     0.3150 | E_err:   0.008770
[2025-10-03 08:44:43] [Iter  930/2250] R3[808/1200] | LR: 0.011025 | E: -42.337669 | E_var:     0.3464 | E_err:   0.009196
[2025-10-03 08:44:54] [Iter  931/2250] R3[810/1200] | LR: 0.010969 | E: -42.347746 | E_var:     0.2867 | E_err:   0.008367
[2025-10-03 08:45:04] [Iter  932/2250] R3[812/1200] | LR: 0.010913 | E: -42.337044 | E_var:     0.2680 | E_err:   0.008088
[2025-10-03 08:45:15] [Iter  933/2250] R3[814/1200] | LR: 0.010858 | E: -42.322659 | E_var:     0.3510 | E_err:   0.009257
[2025-10-03 08:45:25] [Iter  934/2250] R3[816/1200] | LR: 0.010802 | E: -42.339561 | E_var:     0.4129 | E_err:   0.010040
[2025-10-03 08:45:36] [Iter  935/2250] R3[818/1200] | LR: 0.010747 | E: -42.327687 | E_var:     0.2825 | E_err:   0.008304
[2025-10-03 08:45:46] [Iter  936/2250] R3[820/1200] | LR: 0.010692 | E: -42.328103 | E_var:     0.3472 | E_err:   0.009207
[2025-10-03 08:45:57] [Iter  937/2250] R3[822/1200] | LR: 0.010637 | E: -42.337922 | E_var:     0.6319 | E_err:   0.012420
[2025-10-03 08:46:07] [Iter  938/2250] R3[824/1200] | LR: 0.010583 | E: -42.328863 | E_var:     0.3225 | E_err:   0.008873
[2025-10-03 08:46:18] [Iter  939/2250] R3[826/1200] | LR: 0.010528 | E: -42.330578 | E_var:     0.3334 | E_err:   0.009022
[2025-10-03 08:46:29] [Iter  940/2250] R3[828/1200] | LR: 0.010474 | E: -42.336666 | E_var:     0.6616 | E_err:   0.012709
[2025-10-03 08:46:39] [Iter  941/2250] R3[830/1200] | LR: 0.010420 | E: -42.334615 | E_var:     0.4370 | E_err:   0.010329
[2025-10-03 08:46:50] [Iter  942/2250] R3[832/1200] | LR: 0.010366 | E: -42.357085 | E_var:     0.3639 | E_err:   0.009426
[2025-10-03 08:47:00] [Iter  943/2250] R3[834/1200] | LR: 0.010312 | E: -42.351365 | E_var:     0.3511 | E_err:   0.009259
[2025-10-03 08:47:11] [Iter  944/2250] R3[836/1200] | LR: 0.010259 | E: -42.334890 | E_var:     0.4782 | E_err:   0.010805
[2025-10-03 08:47:21] [Iter  945/2250] R3[838/1200] | LR: 0.010206 | E: -42.341073 | E_var:     0.3369 | E_err:   0.009069
[2025-10-03 08:47:32] [Iter  946/2250] R3[840/1200] | LR: 0.010153 | E: -42.333080 | E_var:     0.2795 | E_err:   0.008260
[2025-10-03 08:47:42] [Iter  947/2250] R3[842/1200] | LR: 0.010100 | E: -42.343506 | E_var:     0.3446 | E_err:   0.009172
[2025-10-03 08:47:53] [Iter  948/2250] R3[844/1200] | LR: 0.010047 | E: -42.340290 | E_var:     0.3523 | E_err:   0.009275
[2025-10-03 08:48:03] [Iter  949/2250] R3[846/1200] | LR: 0.009995 | E: -42.340147 | E_var:     0.3985 | E_err:   0.009863
[2025-10-03 08:48:14] [Iter  950/2250] R3[848/1200] | LR: 0.009943 | E: -42.334487 | E_var:     0.2848 | E_err:   0.008338
[2025-10-03 08:48:24] [Iter  951/2250] R3[850/1200] | LR: 0.009890 | E: -42.328682 | E_var:     0.4986 | E_err:   0.011033
[2025-10-03 08:48:35] [Iter  952/2250] R3[852/1200] | LR: 0.009839 | E: -42.336835 | E_var:     0.3863 | E_err:   0.009712
[2025-10-03 08:48:45] [Iter  953/2250] R3[854/1200] | LR: 0.009787 | E: -42.318923 | E_var:     0.3369 | E_err:   0.009069
[2025-10-03 08:48:56] [Iter  954/2250] R3[856/1200] | LR: 0.009736 | E: -42.335679 | E_var:     0.3297 | E_err:   0.008972
[2025-10-03 08:49:06] [Iter  955/2250] R3[858/1200] | LR: 0.009684 | E: -42.337831 | E_var:     0.3376 | E_err:   0.009079
[2025-10-03 08:49:17] [Iter  956/2250] R3[860/1200] | LR: 0.009633 | E: -42.320184 | E_var:     0.3634 | E_err:   0.009419
[2025-10-03 08:49:27] [Iter  957/2250] R3[862/1200] | LR: 0.009583 | E: -42.341751 | E_var:     0.3236 | E_err:   0.008888
[2025-10-03 08:49:38] [Iter  958/2250] R3[864/1200] | LR: 0.009532 | E: -42.336854 | E_var:     0.2956 | E_err:   0.008496
[2025-10-03 08:49:48] [Iter  959/2250] R3[866/1200] | LR: 0.009482 | E: -42.335538 | E_var:     0.4255 | E_err:   0.010193
[2025-10-03 08:49:59] [Iter  960/2250] R3[868/1200] | LR: 0.009432 | E: -42.325101 | E_var:     0.2968 | E_err:   0.008513
[2025-10-03 08:50:10] [Iter  961/2250] R3[870/1200] | LR: 0.009382 | E: -42.341148 | E_var:     0.3070 | E_err:   0.008658
[2025-10-03 08:50:20] [Iter  962/2250] R3[872/1200] | LR: 0.009332 | E: -42.339849 | E_var:     0.2835 | E_err:   0.008320
[2025-10-03 08:50:31] [Iter  963/2250] R3[874/1200] | LR: 0.009283 | E: -42.336998 | E_var:     0.4135 | E_err:   0.010048
[2025-10-03 08:50:41] [Iter  964/2250] R3[876/1200] | LR: 0.009234 | E: -42.343263 | E_var:     0.5828 | E_err:   0.011929
[2025-10-03 08:50:52] [Iter  965/2250] R3[878/1200] | LR: 0.009185 | E: -42.341258 | E_var:     0.3778 | E_err:   0.009604
[2025-10-03 08:51:02] [Iter  966/2250] R3[880/1200] | LR: 0.009136 | E: -42.331058 | E_var:     0.3538 | E_err:   0.009294
[2025-10-03 08:51:13] [Iter  967/2250] R3[882/1200] | LR: 0.009087 | E: -42.351306 | E_var:     0.2910 | E_err:   0.008429
[2025-10-03 08:51:23] [Iter  968/2250] R3[884/1200] | LR: 0.009039 | E: -42.343875 | E_var:     0.3234 | E_err:   0.008885
[2025-10-03 08:51:34] [Iter  969/2250] R3[886/1200] | LR: 0.008991 | E: -42.322443 | E_var:     0.3289 | E_err:   0.008962
[2025-10-03 08:51:44] [Iter  970/2250] R3[888/1200] | LR: 0.008943 | E: -42.333555 | E_var:     0.3223 | E_err:   0.008871
[2025-10-03 08:51:55] [Iter  971/2250] R3[890/1200] | LR: 0.008896 | E: -42.344672 | E_var:     0.3343 | E_err:   0.009034
[2025-10-03 08:52:05] [Iter  972/2250] R3[892/1200] | LR: 0.008848 | E: -42.330043 | E_var:     0.3327 | E_err:   0.009012
[2025-10-03 08:52:16] [Iter  973/2250] R3[894/1200] | LR: 0.008801 | E: -42.342431 | E_var:     0.3710 | E_err:   0.009517
[2025-10-03 08:52:26] [Iter  974/2250] R3[896/1200] | LR: 0.008754 | E: -42.323870 | E_var:     0.2959 | E_err:   0.008499
[2025-10-03 08:52:37] [Iter  975/2250] R3[898/1200] | LR: 0.008708 | E: -42.350817 | E_var:     0.3206 | E_err:   0.008847
[2025-10-03 08:52:47] [Iter  976/2250] R3[900/1200] | LR: 0.008661 | E: -42.347686 | E_var:     0.4173 | E_err:   0.010093
[2025-10-03 08:52:58] [Iter  977/2250] R3[902/1200] | LR: 0.008615 | E: -42.329401 | E_var:     0.5013 | E_err:   0.011063
[2025-10-03 08:53:08] [Iter  978/2250] R3[904/1200] | LR: 0.008569 | E: -42.332200 | E_var:     0.3220 | E_err:   0.008866
[2025-10-03 08:53:19] [Iter  979/2250] R3[906/1200] | LR: 0.008523 | E: -42.343293 | E_var:     0.3429 | E_err:   0.009150
[2025-10-03 08:53:29] [Iter  980/2250] R3[908/1200] | LR: 0.008478 | E: -42.344125 | E_var:     0.3100 | E_err:   0.008699
[2025-10-03 08:53:40] [Iter  981/2250] R3[910/1200] | LR: 0.008433 | E: -42.343001 | E_var:     0.3479 | E_err:   0.009216
[2025-10-03 08:53:51] [Iter  982/2250] R3[912/1200] | LR: 0.008388 | E: -42.325924 | E_var:     0.2983 | E_err:   0.008534
[2025-10-03 08:54:01] [Iter  983/2250] R3[914/1200] | LR: 0.008343 | E: -42.344124 | E_var:     0.3412 | E_err:   0.009127
[2025-10-03 08:54:12] [Iter  984/2250] R3[916/1200] | LR: 0.008299 | E: -42.318372 | E_var:     0.4940 | E_err:   0.010982
[2025-10-03 08:54:22] [Iter  985/2250] R3[918/1200] | LR: 0.008255 | E: -42.331692 | E_var:     0.2999 | E_err:   0.008557
[2025-10-03 08:54:33] [Iter  986/2250] R3[920/1200] | LR: 0.008211 | E: -42.339930 | E_var:     0.3887 | E_err:   0.009741
[2025-10-03 08:54:43] [Iter  987/2250] R3[922/1200] | LR: 0.008167 | E: -42.334700 | E_var:     0.3323 | E_err:   0.009007
[2025-10-03 08:54:54] [Iter  988/2250] R3[924/1200] | LR: 0.008124 | E: -42.335864 | E_var:     0.3591 | E_err:   0.009363
[2025-10-03 08:55:04] [Iter  989/2250] R3[926/1200] | LR: 0.008080 | E: -42.356128 | E_var:     0.2992 | E_err:   0.008547
[2025-10-03 08:55:15] [Iter  990/2250] R3[928/1200] | LR: 0.008038 | E: -42.348402 | E_var:     0.2769 | E_err:   0.008223
[2025-10-03 08:55:25] [Iter  991/2250] R3[930/1200] | LR: 0.007995 | E: -42.337497 | E_var:     0.3031 | E_err:   0.008602
[2025-10-03 08:55:36] [Iter  992/2250] R3[932/1200] | LR: 0.007953 | E: -42.335683 | E_var:     0.3171 | E_err:   0.008799
[2025-10-03 08:55:46] [Iter  993/2250] R3[934/1200] | LR: 0.007910 | E: -42.358487 | E_var:     0.3876 | E_err:   0.009728
[2025-10-03 08:55:57] [Iter  994/2250] R3[936/1200] | LR: 0.007869 | E: -42.337240 | E_var:     0.3330 | E_err:   0.009016
[2025-10-03 08:56:07] [Iter  995/2250] R3[938/1200] | LR: 0.007827 | E: -42.326327 | E_var:     0.3803 | E_err:   0.009636
[2025-10-03 08:56:18] [Iter  996/2250] R3[940/1200] | LR: 0.007786 | E: -42.325525 | E_var:     0.3066 | E_err:   0.008652
[2025-10-03 08:56:28] [Iter  997/2250] R3[942/1200] | LR: 0.007745 | E: -42.343579 | E_var:     0.2893 | E_err:   0.008404
[2025-10-03 08:56:39] [Iter  998/2250] R3[944/1200] | LR: 0.007704 | E: -42.340980 | E_var:     0.3337 | E_err:   0.009027
[2025-10-03 08:56:49] [Iter  999/2250] R3[946/1200] | LR: 0.007663 | E: -42.330697 | E_var:     0.4928 | E_err:   0.010968
[2025-10-03 08:57:00] [Iter 1000/2250] R3[948/1200] | LR: 0.007623 | E: -42.335006 | E_var:     0.3860 | E_err:   0.009707
[2025-10-03 08:57:00] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-03 08:57:10] [Iter 1001/2250] R3[950/1200] | LR: 0.007583 | E: -42.339511 | E_var:     0.3604 | E_err:   0.009380
[2025-10-03 08:57:21] [Iter 1002/2250] R3[952/1200] | LR: 0.007543 | E: -42.325874 | E_var:     0.4243 | E_err:   0.010178
[2025-10-03 08:57:32] [Iter 1003/2250] R3[954/1200] | LR: 0.007504 | E: -42.352367 | E_var:     0.3645 | E_err:   0.009433
[2025-10-03 08:57:42] [Iter 1004/2250] R3[956/1200] | LR: 0.007465 | E: -42.333552 | E_var:     0.2888 | E_err:   0.008397
[2025-10-03 08:57:53] [Iter 1005/2250] R3[958/1200] | LR: 0.007426 | E: -42.341176 | E_var:     0.3002 | E_err:   0.008561
[2025-10-03 08:58:03] [Iter 1006/2250] R3[960/1200] | LR: 0.007387 | E: -42.341408 | E_var:     0.2808 | E_err:   0.008279
[2025-10-03 08:58:14] [Iter 1007/2250] R3[962/1200] | LR: 0.007349 | E: -42.348227 | E_var:     0.3406 | E_err:   0.009119
[2025-10-03 08:58:24] [Iter 1008/2250] R3[964/1200] | LR: 0.007311 | E: -42.341175 | E_var:     0.4653 | E_err:   0.010658
[2025-10-03 08:58:35] [Iter 1009/2250] R3[966/1200] | LR: 0.007273 | E: -42.329340 | E_var:     0.3067 | E_err:   0.008653
[2025-10-03 08:58:45] [Iter 1010/2250] R3[968/1200] | LR: 0.007236 | E: -42.329118 | E_var:     0.3888 | E_err:   0.009742
[2025-10-03 08:58:56] [Iter 1011/2250] R3[970/1200] | LR: 0.007198 | E: -42.346355 | E_var:     0.3378 | E_err:   0.009082
[2025-10-03 08:59:06] [Iter 1012/2250] R3[972/1200] | LR: 0.007161 | E: -42.336503 | E_var:     0.2917 | E_err:   0.008439
[2025-10-03 08:59:17] [Iter 1013/2250] R3[974/1200] | LR: 0.007125 | E: -42.344530 | E_var:     0.3309 | E_err:   0.008989
[2025-10-03 08:59:27] [Iter 1014/2250] R3[976/1200] | LR: 0.007088 | E: -42.325958 | E_var:     0.3356 | E_err:   0.009051
[2025-10-03 08:59:38] [Iter 1015/2250] R3[978/1200] | LR: 0.007052 | E: -42.350032 | E_var:     0.3130 | E_err:   0.008742
[2025-10-03 08:59:48] [Iter 1016/2250] R3[980/1200] | LR: 0.007017 | E: -42.357231 | E_var:     0.3382 | E_err:   0.009087
[2025-10-03 08:59:59] [Iter 1017/2250] R3[982/1200] | LR: 0.006981 | E: -42.343383 | E_var:     0.2879 | E_err:   0.008384
[2025-10-03 09:00:09] [Iter 1018/2250] R3[984/1200] | LR: 0.006946 | E: -42.330815 | E_var:     0.3252 | E_err:   0.008910
[2025-10-03 09:00:20] [Iter 1019/2250] R3[986/1200] | LR: 0.006911 | E: -42.330650 | E_var:     0.3438 | E_err:   0.009162
[2025-10-03 09:00:30] [Iter 1020/2250] R3[988/1200] | LR: 0.006876 | E: -42.330907 | E_var:     0.3719 | E_err:   0.009528
[2025-10-03 09:00:41] [Iter 1021/2250] R3[990/1200] | LR: 0.006842 | E: -42.340346 | E_var:     0.2971 | E_err:   0.008516
[2025-10-03 09:00:52] [Iter 1022/2250] R3[992/1200] | LR: 0.006808 | E: -42.352711 | E_var:     0.3263 | E_err:   0.008925
[2025-10-03 09:01:02] [Iter 1023/2250] R3[994/1200] | LR: 0.006774 | E: -42.337126 | E_var:     0.2903 | E_err:   0.008419
[2025-10-03 09:01:13] [Iter 1024/2250] R3[996/1200] | LR: 0.006741 | E: -42.351342 | E_var:     4.1959 | E_err:   0.032006
[2025-10-03 09:01:23] [Iter 1025/2250] R3[998/1200] | LR: 0.006708 | E: -42.344896 | E_var:     0.2947 | E_err:   0.008482
[2025-10-03 09:01:34] [Iter 1026/2250] R3[1000/1200] | LR: 0.006675 | E: -42.348908 | E_var:     0.3201 | E_err:   0.008840
[2025-10-03 09:01:44] [Iter 1027/2250] R3[1002/1200] | LR: 0.006642 | E: -42.340548 | E_var:     0.3865 | E_err:   0.009714
[2025-10-03 09:01:55] [Iter 1028/2250] R3[1004/1200] | LR: 0.006610 | E: -42.337643 | E_var:     0.3165 | E_err:   0.008790
[2025-10-03 09:02:05] [Iter 1029/2250] R3[1006/1200] | LR: 0.006578 | E: -42.336309 | E_var:     0.3497 | E_err:   0.009240
[2025-10-03 09:02:16] [Iter 1030/2250] R3[1008/1200] | LR: 0.006546 | E: -42.338540 | E_var:     0.2790 | E_err:   0.008254
[2025-10-03 09:02:26] [Iter 1031/2250] R3[1010/1200] | LR: 0.006515 | E: -42.349483 | E_var:     0.3060 | E_err:   0.008644
[2025-10-03 09:02:37] [Iter 1032/2250] R3[1012/1200] | LR: 0.006484 | E: -42.335070 | E_var:     0.3259 | E_err:   0.008920
[2025-10-03 09:02:47] [Iter 1033/2250] R3[1014/1200] | LR: 0.006453 | E: -42.332788 | E_var:     0.3593 | E_err:   0.009366
[2025-10-03 09:02:58] [Iter 1034/2250] R3[1016/1200] | LR: 0.006422 | E: -42.319955 | E_var:     0.4552 | E_err:   0.010542
[2025-10-03 09:03:08] [Iter 1035/2250] R3[1018/1200] | LR: 0.006392 | E: -42.340697 | E_var:     0.3478 | E_err:   0.009215
[2025-10-03 09:03:19] [Iter 1036/2250] R3[1020/1200] | LR: 0.006362 | E: -42.346666 | E_var:     0.3156 | E_err:   0.008778
[2025-10-03 09:03:29] [Iter 1037/2250] R3[1022/1200] | LR: 0.006333 | E: -42.330528 | E_var:     0.3855 | E_err:   0.009702
[2025-10-03 09:03:40] [Iter 1038/2250] R3[1024/1200] | LR: 0.006304 | E: -42.318137 | E_var:     0.3566 | E_err:   0.009330
[2025-10-03 09:03:50] [Iter 1039/2250] R3[1026/1200] | LR: 0.006275 | E: -42.343417 | E_var:     0.3340 | E_err:   0.009030
[2025-10-03 09:04:01] [Iter 1040/2250] R3[1028/1200] | LR: 0.006246 | E: -42.348069 | E_var:     0.3079 | E_err:   0.008670
[2025-10-03 09:04:11] [Iter 1041/2250] R3[1030/1200] | LR: 0.006218 | E: -42.339990 | E_var:     0.3485 | E_err:   0.009224
[2025-10-03 09:04:22] [Iter 1042/2250] R3[1032/1200] | LR: 0.006190 | E: -42.332565 | E_var:     0.3721 | E_err:   0.009532
[2025-10-03 09:04:33] [Iter 1043/2250] R3[1034/1200] | LR: 0.006162 | E: -42.339619 | E_var:     0.4004 | E_err:   0.009887
[2025-10-03 09:04:43] [Iter 1044/2250] R3[1036/1200] | LR: 0.006135 | E: -42.344367 | E_var:     0.3069 | E_err:   0.008656
[2025-10-03 09:04:54] [Iter 1045/2250] R3[1038/1200] | LR: 0.006107 | E: -42.337719 | E_var:     0.3172 | E_err:   0.008800
[2025-10-03 09:05:04] [Iter 1046/2250] R3[1040/1200] | LR: 0.006081 | E: -42.352902 | E_var:     0.3359 | E_err:   0.009055
[2025-10-03 09:05:15] [Iter 1047/2250] R3[1042/1200] | LR: 0.006054 | E: -42.324495 | E_var:     0.3363 | E_err:   0.009061
[2025-10-03 09:05:25] [Iter 1048/2250] R3[1044/1200] | LR: 0.006028 | E: -42.342870 | E_var:     0.2909 | E_err:   0.008428
[2025-10-03 09:05:36] [Iter 1049/2250] R3[1046/1200] | LR: 0.006002 | E: -42.340771 | E_var:     0.3308 | E_err:   0.008987
[2025-10-03 09:05:46] [Iter 1050/2250] R3[1048/1200] | LR: 0.005977 | E: -42.357357 | E_var:     0.5155 | E_err:   0.011218
[2025-10-03 09:05:57] [Iter 1051/2250] R3[1050/1200] | LR: 0.005952 | E: -42.333721 | E_var:     0.3357 | E_err:   0.009054
[2025-10-03 09:06:07] [Iter 1052/2250] R3[1052/1200] | LR: 0.005927 | E: -42.337952 | E_var:     0.7218 | E_err:   0.013275
[2025-10-03 09:06:18] [Iter 1053/2250] R3[1054/1200] | LR: 0.005902 | E: -42.341534 | E_var:     0.4099 | E_err:   0.010004
[2025-10-03 09:06:28] [Iter 1054/2250] R3[1056/1200] | LR: 0.005878 | E: -42.331413 | E_var:     0.3183 | E_err:   0.008816
[2025-10-03 09:06:39] [Iter 1055/2250] R3[1058/1200] | LR: 0.005854 | E: -42.344428 | E_var:     0.4557 | E_err:   0.010548
[2025-10-03 09:06:49] [Iter 1056/2250] R3[1060/1200] | LR: 0.005830 | E: -42.341990 | E_var:     0.3202 | E_err:   0.008842
[2025-10-03 09:07:00] [Iter 1057/2250] R3[1062/1200] | LR: 0.005807 | E: -42.334304 | E_var:     0.2589 | E_err:   0.007951
[2025-10-03 09:07:10] [Iter 1058/2250] R3[1064/1200] | LR: 0.005784 | E: -42.334213 | E_var:     0.2892 | E_err:   0.008402
[2025-10-03 09:07:21] [Iter 1059/2250] R3[1066/1200] | LR: 0.005761 | E: -42.337327 | E_var:     0.2918 | E_err:   0.008441
[2025-10-03 09:07:31] [Iter 1060/2250] R3[1068/1200] | LR: 0.005739 | E: -42.348170 | E_var:     0.3697 | E_err:   0.009501
[2025-10-03 09:07:42] [Iter 1061/2250] R3[1070/1200] | LR: 0.005717 | E: -42.347410 | E_var:     0.4197 | E_err:   0.010122
[2025-10-03 09:07:52] [Iter 1062/2250] R3[1072/1200] | LR: 0.005695 | E: -42.340429 | E_var:     0.3076 | E_err:   0.008665
[2025-10-03 09:08:03] [Iter 1063/2250] R3[1074/1200] | LR: 0.005674 | E: -42.337396 | E_var:     0.2771 | E_err:   0.008225
[2025-10-03 09:08:14] [Iter 1064/2250] R3[1076/1200] | LR: 0.005653 | E: -42.331285 | E_var:     0.2591 | E_err:   0.007953
[2025-10-03 09:08:24] [Iter 1065/2250] R3[1078/1200] | LR: 0.005632 | E: -42.337462 | E_var:     0.2937 | E_err:   0.008468
[2025-10-03 09:08:35] [Iter 1066/2250] R3[1080/1200] | LR: 0.005612 | E: -42.349071 | E_var:     0.3350 | E_err:   0.009044
[2025-10-03 09:08:45] [Iter 1067/2250] R3[1082/1200] | LR: 0.005592 | E: -42.332639 | E_var:     0.3655 | E_err:   0.009447
[2025-10-03 09:08:56] [Iter 1068/2250] R3[1084/1200] | LR: 0.005572 | E: -42.326339 | E_var:     0.4716 | E_err:   0.010730
[2025-10-03 09:09:06] [Iter 1069/2250] R3[1086/1200] | LR: 0.005553 | E: -42.352617 | E_var:     0.3227 | E_err:   0.008876
[2025-10-03 09:09:17] [Iter 1070/2250] R3[1088/1200] | LR: 0.005534 | E: -42.342284 | E_var:     0.3800 | E_err:   0.009632
[2025-10-03 09:09:27] [Iter 1071/2250] R3[1090/1200] | LR: 0.005515 | E: -42.331885 | E_var:     0.2956 | E_err:   0.008496
[2025-10-03 09:09:38] [Iter 1072/2250] R3[1092/1200] | LR: 0.005496 | E: -42.347090 | E_var:     0.2808 | E_err:   0.008280
[2025-10-03 09:09:48] [Iter 1073/2250] R3[1094/1200] | LR: 0.005478 | E: -42.350407 | E_var:     0.3366 | E_err:   0.009065
[2025-10-03 09:09:59] [Iter 1074/2250] R3[1096/1200] | LR: 0.005460 | E: -42.330032 | E_var:     0.3366 | E_err:   0.009065
[2025-10-03 09:10:09] [Iter 1075/2250] R3[1098/1200] | LR: 0.005443 | E: -42.351235 | E_var:     0.3594 | E_err:   0.009368
[2025-10-03 09:10:20] [Iter 1076/2250] R3[1100/1200] | LR: 0.005426 | E: -42.339709 | E_var:     0.3403 | E_err:   0.009115
[2025-10-03 09:10:30] [Iter 1077/2250] R3[1102/1200] | LR: 0.005409 | E: -42.340096 | E_var:     0.3365 | E_err:   0.009063
[2025-10-03 09:10:41] [Iter 1078/2250] R3[1104/1200] | LR: 0.005393 | E: -42.341917 | E_var:     0.2845 | E_err:   0.008335
[2025-10-03 09:10:51] [Iter 1079/2250] R3[1106/1200] | LR: 0.005377 | E: -42.342201 | E_var:     0.2935 | E_err:   0.008465
[2025-10-03 09:11:02] [Iter 1080/2250] R3[1108/1200] | LR: 0.005361 | E: -42.344593 | E_var:     0.3461 | E_err:   0.009192
[2025-10-03 09:11:12] [Iter 1081/2250] R3[1110/1200] | LR: 0.005345 | E: -42.337210 | E_var:     0.2910 | E_err:   0.008429
[2025-10-03 09:11:23] [Iter 1082/2250] R3[1112/1200] | LR: 0.005330 | E: -42.331052 | E_var:     0.3740 | E_err:   0.009556
[2025-10-03 09:11:33] [Iter 1083/2250] R3[1114/1200] | LR: 0.005315 | E: -42.332178 | E_var:     0.3944 | E_err:   0.009813
[2025-10-03 09:11:44] [Iter 1084/2250] R3[1116/1200] | LR: 0.005301 | E: -42.341582 | E_var:     0.2854 | E_err:   0.008348
[2025-10-03 09:11:54] [Iter 1085/2250] R3[1118/1200] | LR: 0.005287 | E: -42.343528 | E_var:     0.3122 | E_err:   0.008730
[2025-10-03 09:12:05] [Iter 1086/2250] R3[1120/1200] | LR: 0.005273 | E: -42.332061 | E_var:     0.4433 | E_err:   0.010403
[2025-10-03 09:12:15] [Iter 1087/2250] R3[1122/1200] | LR: 0.005260 | E: -42.333955 | E_var:     0.3475 | E_err:   0.009211
[2025-10-03 09:12:26] [Iter 1088/2250] R3[1124/1200] | LR: 0.005247 | E: -42.344093 | E_var:     0.3747 | E_err:   0.009565
[2025-10-03 09:12:37] [Iter 1089/2250] R3[1126/1200] | LR: 0.005234 | E: -42.330564 | E_var:     0.2709 | E_err:   0.008133
[2025-10-03 09:12:47] [Iter 1090/2250] R3[1128/1200] | LR: 0.005221 | E: -42.335606 | E_var:     0.3155 | E_err:   0.008776
[2025-10-03 09:12:58] [Iter 1091/2250] R3[1130/1200] | LR: 0.005209 | E: -42.342789 | E_var:     0.2907 | E_err:   0.008424
[2025-10-03 09:13:08] [Iter 1092/2250] R3[1132/1200] | LR: 0.005198 | E: -42.337270 | E_var:     0.3115 | E_err:   0.008720
[2025-10-03 09:13:19] [Iter 1093/2250] R3[1134/1200] | LR: 0.005186 | E: -42.334134 | E_var:     0.2734 | E_err:   0.008171
[2025-10-03 09:13:29] [Iter 1094/2250] R3[1136/1200] | LR: 0.005175 | E: -42.349550 | E_var:     0.3815 | E_err:   0.009650
[2025-10-03 09:13:40] [Iter 1095/2250] R3[1138/1200] | LR: 0.005164 | E: -42.348234 | E_var:     0.2650 | E_err:   0.008044
[2025-10-03 09:13:50] [Iter 1096/2250] R3[1140/1200] | LR: 0.005154 | E: -42.334299 | E_var:     0.2843 | E_err:   0.008331
[2025-10-03 09:14:01] [Iter 1097/2250] R3[1142/1200] | LR: 0.005144 | E: -42.342909 | E_var:     0.3234 | E_err:   0.008885
[2025-10-03 09:14:11] [Iter 1098/2250] R3[1144/1200] | LR: 0.005134 | E: -42.323765 | E_var:     0.3490 | E_err:   0.009230
[2025-10-03 09:14:22] [Iter 1099/2250] R3[1146/1200] | LR: 0.005125 | E: -42.339685 | E_var:     0.3025 | E_err:   0.008594
[2025-10-03 09:14:32] [Iter 1100/2250] R3[1148/1200] | LR: 0.005116 | E: -42.346001 | E_var:     0.4072 | E_err:   0.009970
[2025-10-03 09:14:43] [Iter 1101/2250] R3[1150/1200] | LR: 0.005107 | E: -42.345210 | E_var:     0.3351 | E_err:   0.009046
[2025-10-03 09:14:53] [Iter 1102/2250] R3[1152/1200] | LR: 0.005099 | E: -42.337781 | E_var:     0.2905 | E_err:   0.008421
[2025-10-03 09:15:04] [Iter 1103/2250] R3[1154/1200] | LR: 0.005091 | E: -42.341543 | E_var:     0.3468 | E_err:   0.009202
[2025-10-03 09:15:14] [Iter 1104/2250] R3[1156/1200] | LR: 0.005083 | E: -42.348750 | E_var:     0.3499 | E_err:   0.009242
[2025-10-03 09:15:25] [Iter 1105/2250] R3[1158/1200] | LR: 0.005075 | E: -42.346436 | E_var:     0.3047 | E_err:   0.008625
[2025-10-03 09:15:35] [Iter 1106/2250] R3[1160/1200] | LR: 0.005068 | E: -42.331624 | E_var:     0.4402 | E_err:   0.010367
[2025-10-03 09:15:46] [Iter 1107/2250] R3[1162/1200] | LR: 0.005062 | E: -42.334165 | E_var:     0.4456 | E_err:   0.010430
[2025-10-03 09:15:56] [Iter 1108/2250] R3[1164/1200] | LR: 0.005055 | E: -42.318854 | E_var:     0.4439 | E_err:   0.010411
[2025-10-03 09:16:07] [Iter 1109/2250] R3[1166/1200] | LR: 0.005049 | E: -42.326678 | E_var:     0.3126 | E_err:   0.008736
[2025-10-03 09:16:17] [Iter 1110/2250] R3[1168/1200] | LR: 0.005044 | E: -42.332825 | E_var:     0.5475 | E_err:   0.011561
[2025-10-03 09:16:28] [Iter 1111/2250] R3[1170/1200] | LR: 0.005039 | E: -42.335677 | E_var:     0.3660 | E_err:   0.009452
[2025-10-03 09:16:38] [Iter 1112/2250] R3[1172/1200] | LR: 0.005034 | E: -42.347995 | E_var:     0.3917 | E_err:   0.009779
[2025-10-03 09:16:49] [Iter 1113/2250] R3[1174/1200] | LR: 0.005029 | E: -42.330359 | E_var:     0.3163 | E_err:   0.008788
[2025-10-03 09:17:00] [Iter 1114/2250] R3[1176/1200] | LR: 0.005025 | E: -42.332465 | E_var:     0.3283 | E_err:   0.008952
[2025-10-03 09:17:10] [Iter 1115/2250] R3[1178/1200] | LR: 0.005021 | E: -42.336075 | E_var:     0.3080 | E_err:   0.008672
[2025-10-03 09:17:21] [Iter 1116/2250] R3[1180/1200] | LR: 0.005017 | E: -42.330463 | E_var:     0.2885 | E_err:   0.008392
[2025-10-03 09:17:31] [Iter 1117/2250] R3[1182/1200] | LR: 0.005014 | E: -42.345547 | E_var:     0.3081 | E_err:   0.008672
[2025-10-03 09:17:42] [Iter 1118/2250] R3[1184/1200] | LR: 0.005011 | E: -42.325304 | E_var:     0.2882 | E_err:   0.008389
[2025-10-03 09:17:52] [Iter 1119/2250] R3[1186/1200] | LR: 0.005008 | E: -42.339570 | E_var:     0.3654 | E_err:   0.009445
[2025-10-03 09:18:03] [Iter 1120/2250] R3[1188/1200] | LR: 0.005006 | E: -42.332679 | E_var:     0.3658 | E_err:   0.009450
[2025-10-03 09:18:13] [Iter 1121/2250] R3[1190/1200] | LR: 0.005004 | E: -42.349852 | E_var:     0.2784 | E_err:   0.008244
[2025-10-03 09:18:24] [Iter 1122/2250] R3[1192/1200] | LR: 0.005003 | E: -42.327223 | E_var:     0.2861 | E_err:   0.008358
[2025-10-03 09:18:34] [Iter 1123/2250] R3[1194/1200] | LR: 0.005002 | E: -42.323836 | E_var:     0.3126 | E_err:   0.008736
[2025-10-03 09:18:45] [Iter 1124/2250] R3[1196/1200] | LR: 0.005001 | E: -42.345977 | E_var:     0.3454 | E_err:   0.009183
[2025-10-03 09:18:55] [Iter 1125/2250] R3[1198/1200] | LR: 0.005000 | E: -42.319232 | E_var:     0.3823 | E_err:   0.009661
[2025-10-03 09:18:55] 🔄 RESTART #4 | Period: 2400
[2025-10-03 09:19:06] [Iter 1126/2250] R4[0/2400]   | LR: 0.030000 | E: -42.341354 | E_var:     0.3864 | E_err:   0.009713
[2025-10-03 09:19:16] [Iter 1127/2250] R4[2/2400]   | LR: 0.030000 | E: -42.333946 | E_var:     0.3010 | E_err:   0.008573
[2025-10-03 09:19:27] [Iter 1128/2250] R4[4/2400]   | LR: 0.030000 | E: -42.346600 | E_var:     0.2914 | E_err:   0.008434
[2025-10-03 09:19:37] [Iter 1129/2250] R4[6/2400]   | LR: 0.030000 | E: -42.326725 | E_var:     0.2685 | E_err:   0.008096
[2025-10-03 09:19:48] [Iter 1130/2250] R4[8/2400]   | LR: 0.029999 | E: -42.339765 | E_var:     0.3285 | E_err:   0.008956
[2025-10-03 09:19:58] [Iter 1131/2250] R4[10/2400]  | LR: 0.029999 | E: -42.337987 | E_var:     0.2833 | E_err:   0.008316
[2025-10-03 09:20:09] [Iter 1132/2250] R4[12/2400]  | LR: 0.029998 | E: -42.354930 | E_var:     0.2726 | E_err:   0.008158
[2025-10-03 09:20:19] [Iter 1133/2250] R4[14/2400]  | LR: 0.029998 | E: -42.354600 | E_var:     0.2703 | E_err:   0.008123
[2025-10-03 09:20:30] [Iter 1134/2250] R4[16/2400]  | LR: 0.029997 | E: -42.336792 | E_var:     0.3188 | E_err:   0.008823
[2025-10-03 09:20:41] [Iter 1135/2250] R4[18/2400]  | LR: 0.029997 | E: -42.329936 | E_var:     0.3080 | E_err:   0.008671
[2025-10-03 09:20:51] [Iter 1136/2250] R4[20/2400]  | LR: 0.029996 | E: -42.336826 | E_var:     0.3261 | E_err:   0.008922
[2025-10-03 09:21:02] [Iter 1137/2250] R4[22/2400]  | LR: 0.029995 | E: -42.350760 | E_var:     0.2399 | E_err:   0.007653
[2025-10-03 09:21:12] [Iter 1138/2250] R4[24/2400]  | LR: 0.029994 | E: -42.341575 | E_var:     0.3030 | E_err:   0.008600
[2025-10-03 09:21:23] [Iter 1139/2250] R4[26/2400]  | LR: 0.029993 | E: -42.351103 | E_var:     0.2733 | E_err:   0.008169
[2025-10-03 09:21:33] [Iter 1140/2250] R4[28/2400]  | LR: 0.029992 | E: -42.346729 | E_var:     0.3399 | E_err:   0.009110
[2025-10-03 09:21:44] [Iter 1141/2250] R4[30/2400]  | LR: 0.029990 | E: -42.332995 | E_var:     0.3155 | E_err:   0.008776
[2025-10-03 09:21:54] [Iter 1142/2250] R4[32/2400]  | LR: 0.029989 | E: -42.338159 | E_var:     0.3550 | E_err:   0.009310
[2025-10-03 09:22:05] [Iter 1143/2250] R4[34/2400]  | LR: 0.029988 | E: -42.347874 | E_var:     0.3247 | E_err:   0.008904
[2025-10-03 09:22:15] [Iter 1144/2250] R4[36/2400]  | LR: 0.029986 | E: -42.323895 | E_var:     0.2870 | E_err:   0.008371
[2025-10-03 09:22:26] [Iter 1145/2250] R4[38/2400]  | LR: 0.029985 | E: -42.348377 | E_var:     0.3457 | E_err:   0.009187
[2025-10-03 09:22:36] [Iter 1146/2250] R4[40/2400]  | LR: 0.029983 | E: -42.338499 | E_var:     0.5235 | E_err:   0.011305
[2025-10-03 09:22:47] [Iter 1147/2250] R4[42/2400]  | LR: 0.029981 | E: -42.356228 | E_var:     0.3536 | E_err:   0.009291
[2025-10-03 09:22:57] [Iter 1148/2250] R4[44/2400]  | LR: 0.029979 | E: -42.356492 | E_var:     0.3570 | E_err:   0.009336
[2025-10-03 09:23:08] [Iter 1149/2250] R4[46/2400]  | LR: 0.029977 | E: -42.354828 | E_var:     0.2631 | E_err:   0.008014
[2025-10-03 09:23:18] [Iter 1150/2250] R4[48/2400]  | LR: 0.029975 | E: -42.329763 | E_var:     0.3980 | E_err:   0.009858
[2025-10-03 09:23:29] [Iter 1151/2250] R4[50/2400]  | LR: 0.029973 | E: -42.345693 | E_var:     0.3017 | E_err:   0.008582
[2025-10-03 09:23:39] [Iter 1152/2250] R4[52/2400]  | LR: 0.029971 | E: -42.340376 | E_var:     0.2659 | E_err:   0.008057
[2025-10-03 09:23:50] [Iter 1153/2250] R4[54/2400]  | LR: 0.029969 | E: -42.327979 | E_var:     0.2631 | E_err:   0.008015
[2025-10-03 09:24:00] [Iter 1154/2250] R4[56/2400]  | LR: 0.029966 | E: -42.343089 | E_var:     0.8292 | E_err:   0.014229
[2025-10-03 09:24:11] [Iter 1155/2250] R4[58/2400]  | LR: 0.029964 | E: -42.340286 | E_var:     0.2823 | E_err:   0.008302
[2025-10-03 09:24:21] [Iter 1156/2250] R4[60/2400]  | LR: 0.029961 | E: -42.352833 | E_var:     0.2805 | E_err:   0.008275
[2025-10-03 09:24:32] [Iter 1157/2250] R4[62/2400]  | LR: 0.029959 | E: -42.349448 | E_var:     0.3382 | E_err:   0.009087
[2025-10-03 09:24:43] [Iter 1158/2250] R4[64/2400]  | LR: 0.029956 | E: -42.361490 | E_var:     0.2808 | E_err:   0.008279
[2025-10-03 09:24:53] [Iter 1159/2250] R4[66/2400]  | LR: 0.029953 | E: -42.336695 | E_var:     0.3215 | E_err:   0.008859
[2025-10-03 09:25:04] [Iter 1160/2250] R4[68/2400]  | LR: 0.029951 | E: -42.349849 | E_var:     0.2858 | E_err:   0.008354
[2025-10-03 09:25:14] [Iter 1161/2250] R4[70/2400]  | LR: 0.029948 | E: -42.351971 | E_var:     0.3123 | E_err:   0.008731
[2025-10-03 09:25:25] [Iter 1162/2250] R4[72/2400]  | LR: 0.029945 | E: -42.341179 | E_var:     0.2698 | E_err:   0.008116
[2025-10-03 09:25:35] [Iter 1163/2250] R4[74/2400]  | LR: 0.029941 | E: -42.337345 | E_var:     0.3167 | E_err:   0.008794
[2025-10-03 09:25:46] [Iter 1164/2250] R4[76/2400]  | LR: 0.029938 | E: -42.343776 | E_var:     0.2955 | E_err:   0.008494
[2025-10-03 09:25:56] [Iter 1165/2250] R4[78/2400]  | LR: 0.029935 | E: -42.334631 | E_var:     0.3378 | E_err:   0.009081
[2025-10-03 09:26:07] [Iter 1166/2250] R4[80/2400]  | LR: 0.029932 | E: -42.337359 | E_var:     0.3254 | E_err:   0.008914
[2025-10-03 09:26:17] [Iter 1167/2250] R4[82/2400]  | LR: 0.029928 | E: -42.340562 | E_var:     0.2933 | E_err:   0.008462
[2025-10-03 09:26:28] [Iter 1168/2250] R4[84/2400]  | LR: 0.029925 | E: -42.342753 | E_var:     0.3601 | E_err:   0.009376
[2025-10-03 09:26:38] [Iter 1169/2250] R4[86/2400]  | LR: 0.029921 | E: -42.367572 | E_var:     0.3809 | E_err:   0.009644
[2025-10-03 09:26:49] [Iter 1170/2250] R4[88/2400]  | LR: 0.029917 | E: -42.345476 | E_var:     0.2657 | E_err:   0.008054
[2025-10-03 09:26:59] [Iter 1171/2250] R4[90/2400]  | LR: 0.029913 | E: -42.365819 | E_var:     0.3776 | E_err:   0.009601
[2025-10-03 09:27:10] [Iter 1172/2250] R4[92/2400]  | LR: 0.029909 | E: -42.350649 | E_var:     0.3054 | E_err:   0.008634
[2025-10-03 09:27:20] [Iter 1173/2250] R4[94/2400]  | LR: 0.029905 | E: -42.352338 | E_var:     0.3103 | E_err:   0.008704
[2025-10-03 09:27:31] [Iter 1174/2250] R4[96/2400]  | LR: 0.029901 | E: -42.343507 | E_var:     0.2857 | E_err:   0.008352
[2025-10-03 09:27:41] [Iter 1175/2250] R4[98/2400]  | LR: 0.029897 | E: -42.344071 | E_var:     0.3267 | E_err:   0.008931
[2025-10-03 09:27:52] [Iter 1176/2250] R4[100/2400] | LR: 0.029893 | E: -42.331809 | E_var:     0.2413 | E_err:   0.007676
[2025-10-03 09:28:02] [Iter 1177/2250] R4[102/2400] | LR: 0.029889 | E: -42.335041 | E_var:     0.2916 | E_err:   0.008437
[2025-10-03 09:28:13] [Iter 1178/2250] R4[104/2400] | LR: 0.029884 | E: -42.344530 | E_var:     0.3074 | E_err:   0.008663
[2025-10-03 09:28:23] [Iter 1179/2250] R4[106/2400] | LR: 0.029880 | E: -42.331920 | E_var:     0.2942 | E_err:   0.008475
[2025-10-03 09:28:34] [Iter 1180/2250] R4[108/2400] | LR: 0.029875 | E: -42.343729 | E_var:     0.2842 | E_err:   0.008329
[2025-10-03 09:28:45] [Iter 1181/2250] R4[110/2400] | LR: 0.029871 | E: -42.342671 | E_var:     0.3071 | E_err:   0.008659
[2025-10-03 09:28:55] [Iter 1182/2250] R4[112/2400] | LR: 0.029866 | E: -42.343320 | E_var:     0.3021 | E_err:   0.008588
[2025-10-03 09:29:06] [Iter 1183/2250] R4[114/2400] | LR: 0.029861 | E: -42.340213 | E_var:     0.4812 | E_err:   0.010839
[2025-10-03 09:29:16] [Iter 1184/2250] R4[116/2400] | LR: 0.029856 | E: -42.340369 | E_var:     0.2650 | E_err:   0.008043
[2025-10-03 09:29:27] [Iter 1185/2250] R4[118/2400] | LR: 0.029851 | E: -42.338593 | E_var:     0.3760 | E_err:   0.009581
[2025-10-03 09:29:37] [Iter 1186/2250] R4[120/2400] | LR: 0.029846 | E: -42.345970 | E_var:     0.3517 | E_err:   0.009266
[2025-10-03 09:29:48] [Iter 1187/2250] R4[122/2400] | LR: 0.029841 | E: -42.325910 | E_var:     0.3382 | E_err:   0.009086
[2025-10-03 09:29:58] [Iter 1188/2250] R4[124/2400] | LR: 0.029836 | E: -42.350729 | E_var:     0.3019 | E_err:   0.008585
[2025-10-03 09:30:09] [Iter 1189/2250] R4[126/2400] | LR: 0.029830 | E: -42.349966 | E_var:     0.2553 | E_err:   0.007894
[2025-10-03 09:30:19] [Iter 1190/2250] R4[128/2400] | LR: 0.029825 | E: -42.336739 | E_var:     0.2990 | E_err:   0.008544
[2025-10-03 09:30:30] [Iter 1191/2250] R4[130/2400] | LR: 0.029819 | E: -42.321432 | E_var:     0.3952 | E_err:   0.009823
[2025-10-03 09:30:40] [Iter 1192/2250] R4[132/2400] | LR: 0.029814 | E: -42.346524 | E_var:     0.3227 | E_err:   0.008876
[2025-10-03 09:30:51] [Iter 1193/2250] R4[134/2400] | LR: 0.029808 | E: -42.342017 | E_var:     0.2706 | E_err:   0.008128
[2025-10-03 09:31:01] [Iter 1194/2250] R4[136/2400] | LR: 0.029802 | E: -42.343045 | E_var:     0.3468 | E_err:   0.009201
[2025-10-03 09:31:12] [Iter 1195/2250] R4[138/2400] | LR: 0.029797 | E: -42.356189 | E_var:     0.3184 | E_err:   0.008817
[2025-10-03 09:31:22] [Iter 1196/2250] R4[140/2400] | LR: 0.029791 | E: -42.342247 | E_var:     0.2874 | E_err:   0.008376
[2025-10-03 09:31:33] [Iter 1197/2250] R4[142/2400] | LR: 0.029785 | E: -42.336008 | E_var:     0.3110 | E_err:   0.008714
[2025-10-03 09:31:43] [Iter 1198/2250] R4[144/2400] | LR: 0.029779 | E: -42.342823 | E_var:     0.3193 | E_err:   0.008829
[2025-10-03 09:31:54] [Iter 1199/2250] R4[146/2400] | LR: 0.029772 | E: -42.343236 | E_var:     0.2780 | E_err:   0.008239
[2025-10-03 09:32:04] [Iter 1200/2250] R4[148/2400] | LR: 0.029766 | E: -42.339710 | E_var:     0.2934 | E_err:   0.008463
[2025-10-03 09:32:04] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-10-03 09:32:15] [Iter 1201/2250] R4[150/2400] | LR: 0.029760 | E: -42.358824 | E_var:     0.4455 | E_err:   0.010429
[2025-10-03 09:32:26] [Iter 1202/2250] R4[152/2400] | LR: 0.029753 | E: -42.347215 | E_var:     0.3134 | E_err:   0.008748
[2025-10-03 09:32:36] [Iter 1203/2250] R4[154/2400] | LR: 0.029747 | E: -42.329485 | E_var:     0.4588 | E_err:   0.010584
[2025-10-03 09:32:47] [Iter 1204/2250] R4[156/2400] | LR: 0.029740 | E: -42.342191 | E_var:     0.3757 | E_err:   0.009577
[2025-10-03 09:32:57] [Iter 1205/2250] R4[158/2400] | LR: 0.029734 | E: -42.331596 | E_var:     0.3068 | E_err:   0.008655
[2025-10-03 09:33:08] [Iter 1206/2250] R4[160/2400] | LR: 0.029727 | E: -42.339426 | E_var:     0.2644 | E_err:   0.008035
[2025-10-03 09:33:18] [Iter 1207/2250] R4[162/2400] | LR: 0.029720 | E: -42.346025 | E_var:     0.3309 | E_err:   0.008988
[2025-10-03 09:33:29] [Iter 1208/2250] R4[164/2400] | LR: 0.029713 | E: -42.343520 | E_var:     0.2739 | E_err:   0.008177
[2025-10-03 09:33:39] [Iter 1209/2250] R4[166/2400] | LR: 0.029706 | E: -42.355180 | E_var:     0.3716 | E_err:   0.009525
[2025-10-03 09:33:50] [Iter 1210/2250] R4[168/2400] | LR: 0.029699 | E: -42.330471 | E_var:     0.4970 | E_err:   0.011015
[2025-10-03 09:34:00] [Iter 1211/2250] R4[170/2400] | LR: 0.029692 | E: -42.352564 | E_var:     0.3147 | E_err:   0.008766
[2025-10-03 09:34:11] [Iter 1212/2250] R4[172/2400] | LR: 0.029685 | E: -42.337759 | E_var:     0.3432 | E_err:   0.009154
[2025-10-03 09:34:21] [Iter 1213/2250] R4[174/2400] | LR: 0.029677 | E: -42.347041 | E_var:     0.3193 | E_err:   0.008829
[2025-10-03 09:34:32] [Iter 1214/2250] R4[176/2400] | LR: 0.029670 | E: -42.343965 | E_var:     0.4422 | E_err:   0.010391
[2025-10-03 09:34:42] [Iter 1215/2250] R4[178/2400] | LR: 0.029662 | E: -42.330375 | E_var:     0.3481 | E_err:   0.009219
[2025-10-03 09:34:53] [Iter 1216/2250] R4[180/2400] | LR: 0.029655 | E: -42.354257 | E_var:     0.2943 | E_err:   0.008476
[2025-10-03 09:35:03] [Iter 1217/2250] R4[182/2400] | LR: 0.029647 | E: -42.336249 | E_var:     0.4861 | E_err:   0.010894
[2025-10-03 09:35:14] [Iter 1218/2250] R4[184/2400] | LR: 0.029639 | E: -42.357765 | E_var:     0.3054 | E_err:   0.008634
[2025-10-03 09:35:24] [Iter 1219/2250] R4[186/2400] | LR: 0.029631 | E: -42.353140 | E_var:     0.2751 | E_err:   0.008195
[2025-10-03 09:35:35] [Iter 1220/2250] R4[188/2400] | LR: 0.029623 | E: -42.349929 | E_var:     0.2820 | E_err:   0.008297
[2025-10-03 09:35:45] [Iter 1221/2250] R4[190/2400] | LR: 0.029615 | E: -42.337719 | E_var:     0.3049 | E_err:   0.008627
[2025-10-03 09:35:56] [Iter 1222/2250] R4[192/2400] | LR: 0.029607 | E: -42.341882 | E_var:     0.3908 | E_err:   0.009768
[2025-10-03 09:36:06] [Iter 1223/2250] R4[194/2400] | LR: 0.029599 | E: -42.346961 | E_var:     0.4662 | E_err:   0.010668
[2025-10-03 09:36:17] [Iter 1224/2250] R4[196/2400] | LR: 0.029591 | E: -42.332982 | E_var:     0.3240 | E_err:   0.008894
[2025-10-03 09:36:28] [Iter 1225/2250] R4[198/2400] | LR: 0.029583 | E: -42.340691 | E_var:     0.3045 | E_err:   0.008622
[2025-10-03 09:36:38] [Iter 1226/2250] R4[200/2400] | LR: 0.029574 | E: -42.342678 | E_var:     0.2575 | E_err:   0.007929
[2025-10-03 09:36:49] [Iter 1227/2250] R4[202/2400] | LR: 0.029566 | E: -42.325746 | E_var:     0.3968 | E_err:   0.009842
[2025-10-03 09:36:59] [Iter 1228/2250] R4[204/2400] | LR: 0.029557 | E: -42.324864 | E_var:     0.2730 | E_err:   0.008163
[2025-10-03 09:37:10] [Iter 1229/2250] R4[206/2400] | LR: 0.029548 | E: -42.333691 | E_var:     0.3311 | E_err:   0.008991
[2025-10-03 09:37:20] [Iter 1230/2250] R4[208/2400] | LR: 0.029540 | E: -42.341371 | E_var:     0.2722 | E_err:   0.008151
[2025-10-03 09:37:31] [Iter 1231/2250] R4[210/2400] | LR: 0.029531 | E: -42.338192 | E_var:     0.3423 | E_err:   0.009142
[2025-10-03 09:37:41] [Iter 1232/2250] R4[212/2400] | LR: 0.029522 | E: -42.347538 | E_var:     0.2913 | E_err:   0.008433
[2025-10-03 09:37:52] [Iter 1233/2250] R4[214/2400] | LR: 0.029513 | E: -42.348990 | E_var:     0.3371 | E_err:   0.009072
[2025-10-03 09:38:02] [Iter 1234/2250] R4[216/2400] | LR: 0.029504 | E: -42.324041 | E_var:     0.2816 | E_err:   0.008291
[2025-10-03 09:38:13] [Iter 1235/2250] R4[218/2400] | LR: 0.029494 | E: -42.347106 | E_var:     0.3029 | E_err:   0.008600
[2025-10-03 09:38:23] [Iter 1236/2250] R4[220/2400] | LR: 0.029485 | E: -42.330572 | E_var:     0.3531 | E_err:   0.009285
[2025-10-03 09:38:34] [Iter 1237/2250] R4[222/2400] | LR: 0.029476 | E: -42.348794 | E_var:     0.6861 | E_err:   0.012942
[2025-10-03 09:38:44] [Iter 1238/2250] R4[224/2400] | LR: 0.029466 | E: -42.342412 | E_var:     0.2869 | E_err:   0.008370
[2025-10-03 09:38:55] [Iter 1239/2250] R4[226/2400] | LR: 0.029457 | E: -42.341922 | E_var:     0.3135 | E_err:   0.008748
[2025-10-03 09:39:05] [Iter 1240/2250] R4[228/2400] | LR: 0.029447 | E: -42.341673 | E_var:     0.2705 | E_err:   0.008127
[2025-10-03 09:39:16] [Iter 1241/2250] R4[230/2400] | LR: 0.029438 | E: -42.340476 | E_var:     0.4903 | E_err:   0.010940
[2025-10-03 09:39:27] [Iter 1242/2250] R4[232/2400] | LR: 0.029428 | E: -42.350321 | E_var:     0.3339 | E_err:   0.009029
[2025-10-03 09:39:37] [Iter 1243/2250] R4[234/2400] | LR: 0.029418 | E: -42.331793 | E_var:     0.3147 | E_err:   0.008765
[2025-10-03 09:39:48] [Iter 1244/2250] R4[236/2400] | LR: 0.029408 | E: -42.344745 | E_var:     0.2928 | E_err:   0.008455
[2025-10-03 09:39:58] [Iter 1245/2250] R4[238/2400] | LR: 0.029398 | E: -42.349111 | E_var:     0.2542 | E_err:   0.007878
[2025-10-03 09:40:09] [Iter 1246/2250] R4[240/2400] | LR: 0.029388 | E: -42.339741 | E_var:     0.2975 | E_err:   0.008523
[2025-10-03 09:40:19] [Iter 1247/2250] R4[242/2400] | LR: 0.029378 | E: -42.350041 | E_var:     0.2635 | E_err:   0.008021
[2025-10-03 09:40:30] [Iter 1248/2250] R4[244/2400] | LR: 0.029368 | E: -42.344198 | E_var:     0.2910 | E_err:   0.008429
[2025-10-03 09:40:40] [Iter 1249/2250] R4[246/2400] | LR: 0.029358 | E: -42.333662 | E_var:     0.3268 | E_err:   0.008932
[2025-10-03 09:40:51] [Iter 1250/2250] R4[248/2400] | LR: 0.029347 | E: -42.342881 | E_var:     0.3092 | E_err:   0.008689
[2025-10-03 09:41:01] [Iter 1251/2250] R4[250/2400] | LR: 0.029337 | E: -42.361639 | E_var:     0.3152 | E_err:   0.008773
[2025-10-03 09:41:12] [Iter 1252/2250] R4[252/2400] | LR: 0.029326 | E: -42.342368 | E_var:     0.3286 | E_err:   0.008957
[2025-10-03 09:41:22] [Iter 1253/2250] R4[254/2400] | LR: 0.029315 | E: -42.345638 | E_var:     0.2805 | E_err:   0.008275
[2025-10-03 09:41:33] [Iter 1254/2250] R4[256/2400] | LR: 0.029305 | E: -42.364738 | E_var:     0.2648 | E_err:   0.008040
[2025-10-03 09:41:43] [Iter 1255/2250] R4[258/2400] | LR: 0.029294 | E: -42.342116 | E_var:     0.3084 | E_err:   0.008678
[2025-10-03 09:41:54] [Iter 1256/2250] R4[260/2400] | LR: 0.029283 | E: -42.340615 | E_var:     0.3192 | E_err:   0.008828
[2025-10-03 09:42:04] [Iter 1257/2250] R4[262/2400] | LR: 0.029272 | E: -42.340094 | E_var:     0.3141 | E_err:   0.008758
[2025-10-03 09:42:15] [Iter 1258/2250] R4[264/2400] | LR: 0.029261 | E: -42.342693 | E_var:     0.3421 | E_err:   0.009139
[2025-10-03 09:42:25] [Iter 1259/2250] R4[266/2400] | LR: 0.029250 | E: -42.341163 | E_var:     0.2607 | E_err:   0.007978
[2025-10-03 09:42:36] [Iter 1260/2250] R4[268/2400] | LR: 0.029239 | E: -42.355862 | E_var:     0.3142 | E_err:   0.008758
[2025-10-03 09:42:46] [Iter 1261/2250] R4[270/2400] | LR: 0.029227 | E: -42.332843 | E_var:     0.2597 | E_err:   0.007963
[2025-10-03 09:42:57] [Iter 1262/2250] R4[272/2400] | LR: 0.029216 | E: -42.342448 | E_var:     0.3003 | E_err:   0.008562
[2025-10-03 09:43:08] [Iter 1263/2250] R4[274/2400] | LR: 0.029205 | E: -42.346001 | E_var:     0.2581 | E_err:   0.007939
[2025-10-03 09:43:18] [Iter 1264/2250] R4[276/2400] | LR: 0.029193 | E: -42.349220 | E_var:     0.3178 | E_err:   0.008808
[2025-10-03 09:43:29] [Iter 1265/2250] R4[278/2400] | LR: 0.029181 | E: -42.347850 | E_var:     0.3506 | E_err:   0.009252
[2025-10-03 09:43:39] [Iter 1266/2250] R4[280/2400] | LR: 0.029170 | E: -42.349594 | E_var:     0.3325 | E_err:   0.009010
[2025-10-03 09:43:50] [Iter 1267/2250] R4[282/2400] | LR: 0.029158 | E: -42.338200 | E_var:     0.3271 | E_err:   0.008936
[2025-10-03 09:44:00] [Iter 1268/2250] R4[284/2400] | LR: 0.029146 | E: -42.338141 | E_var:     0.3146 | E_err:   0.008764
[2025-10-03 09:44:11] [Iter 1269/2250] R4[286/2400] | LR: 0.029134 | E: -42.336874 | E_var:     0.3108 | E_err:   0.008711
[2025-10-03 09:44:21] [Iter 1270/2250] R4[288/2400] | LR: 0.029122 | E: -42.349512 | E_var:     0.2992 | E_err:   0.008546
[2025-10-03 09:44:32] [Iter 1271/2250] R4[290/2400] | LR: 0.029110 | E: -42.341874 | E_var:     0.2732 | E_err:   0.008167
[2025-10-03 09:44:42] [Iter 1272/2250] R4[292/2400] | LR: 0.029098 | E: -42.338885 | E_var:     0.6864 | E_err:   0.012945
[2025-10-03 09:44:53] [Iter 1273/2250] R4[294/2400] | LR: 0.029086 | E: -42.358626 | E_var:     0.4137 | E_err:   0.010050
[2025-10-03 09:45:03] [Iter 1274/2250] R4[296/2400] | LR: 0.029073 | E: -42.345573 | E_var:     0.2837 | E_err:   0.008322
[2025-10-03 09:45:14] [Iter 1275/2250] R4[298/2400] | LR: 0.029061 | E: -42.351819 | E_var:     0.2665 | E_err:   0.008066
[2025-10-03 09:45:24] [Iter 1276/2250] R4[300/2400] | LR: 0.029048 | E: -42.343922 | E_var:     0.2914 | E_err:   0.008434
[2025-10-03 09:45:35] [Iter 1277/2250] R4[302/2400] | LR: 0.029036 | E: -42.331180 | E_var:     0.3827 | E_err:   0.009666
[2025-10-03 09:45:45] [Iter 1278/2250] R4[304/2400] | LR: 0.029023 | E: -42.338568 | E_var:     0.3287 | E_err:   0.008958
[2025-10-03 09:45:56] [Iter 1279/2250] R4[306/2400] | LR: 0.029011 | E: -42.328493 | E_var:     0.4447 | E_err:   0.010419
[2025-10-03 09:46:06] [Iter 1280/2250] R4[308/2400] | LR: 0.028998 | E: -42.332578 | E_var:     0.4555 | E_err:   0.010545
[2025-10-03 09:46:17] [Iter 1281/2250] R4[310/2400] | LR: 0.028985 | E: -42.359930 | E_var:     0.8236 | E_err:   0.014180
[2025-10-03 09:46:27] [Iter 1282/2250] R4[312/2400] | LR: 0.028972 | E: -42.337712 | E_var:     0.3026 | E_err:   0.008595
[2025-10-03 09:46:38] [Iter 1283/2250] R4[314/2400] | LR: 0.028959 | E: -42.341436 | E_var:     0.3032 | E_err:   0.008603
[2025-10-03 09:46:48] [Iter 1284/2250] R4[316/2400] | LR: 0.028946 | E: -42.350176 | E_var:     0.2404 | E_err:   0.007662
[2025-10-03 09:46:59] [Iter 1285/2250] R4[318/2400] | LR: 0.028933 | E: -42.342080 | E_var:     0.3118 | E_err:   0.008724
[2025-10-03 09:47:10] [Iter 1286/2250] R4[320/2400] | LR: 0.028919 | E: -42.352271 | E_var:     0.3893 | E_err:   0.009749
[2025-10-03 09:47:20] [Iter 1287/2250] R4[322/2400] | LR: 0.028906 | E: -42.336678 | E_var:     0.3764 | E_err:   0.009586
[2025-10-03 09:47:31] [Iter 1288/2250] R4[324/2400] | LR: 0.028893 | E: -42.359523 | E_var:     0.2561 | E_err:   0.007907
[2025-10-03 09:47:41] [Iter 1289/2250] R4[326/2400] | LR: 0.028879 | E: -42.341972 | E_var:     0.3091 | E_err:   0.008687
[2025-10-03 09:47:52] [Iter 1290/2250] R4[328/2400] | LR: 0.028865 | E: -42.362028 | E_var:     0.3059 | E_err:   0.008642
[2025-10-03 09:48:02] [Iter 1291/2250] R4[330/2400] | LR: 0.028852 | E: -42.334964 | E_var:     0.3241 | E_err:   0.008895
[2025-10-03 09:48:13] [Iter 1292/2250] R4[332/2400] | LR: 0.028838 | E: -42.347209 | E_var:     0.3142 | E_err:   0.008758
[2025-10-03 09:48:23] [Iter 1293/2250] R4[334/2400] | LR: 0.028824 | E: -42.351146 | E_var:     0.3122 | E_err:   0.008730
[2025-10-03 09:48:34] [Iter 1294/2250] R4[336/2400] | LR: 0.028810 | E: -42.362608 | E_var:     0.3411 | E_err:   0.009126
[2025-10-03 09:48:44] [Iter 1295/2250] R4[338/2400] | LR: 0.028796 | E: -42.345506 | E_var:     0.2541 | E_err:   0.007876
[2025-10-03 09:48:55] [Iter 1296/2250] R4[340/2400] | LR: 0.028782 | E: -42.348463 | E_var:     0.2795 | E_err:   0.008261
[2025-10-03 09:49:05] [Iter 1297/2250] R4[342/2400] | LR: 0.028768 | E: -42.344628 | E_var:     0.2730 | E_err:   0.008163
[2025-10-03 09:49:16] [Iter 1298/2250] R4[344/2400] | LR: 0.028754 | E: -42.340813 | E_var:     0.2648 | E_err:   0.008040
[2025-10-03 09:49:26] [Iter 1299/2250] R4[346/2400] | LR: 0.028740 | E: -42.353596 | E_var:     0.8236 | E_err:   0.014180
[2025-10-03 09:49:37] [Iter 1300/2250] R4[348/2400] | LR: 0.028725 | E: -42.345123 | E_var:     0.2673 | E_err:   0.008079
[2025-10-03 09:49:47] [Iter 1301/2250] R4[350/2400] | LR: 0.028711 | E: -42.334966 | E_var:     0.2632 | E_err:   0.008016
[2025-10-03 09:49:58] [Iter 1302/2250] R4[352/2400] | LR: 0.028696 | E: -42.326305 | E_var:     0.3240 | E_err:   0.008894
[2025-10-03 09:50:08] [Iter 1303/2250] R4[354/2400] | LR: 0.028682 | E: -42.362154 | E_var:     0.2633 | E_err:   0.008018
[2025-10-03 09:50:19] [Iter 1304/2250] R4[356/2400] | LR: 0.028667 | E: -42.352658 | E_var:     0.3088 | E_err:   0.008683
[2025-10-03 09:50:29] [Iter 1305/2250] R4[358/2400] | LR: 0.028652 | E: -42.340205 | E_var:     0.2927 | E_err:   0.008453
[2025-10-03 09:50:40] [Iter 1306/2250] R4[360/2400] | LR: 0.028638 | E: -42.333074 | E_var:     0.2580 | E_err:   0.007936
[2025-10-03 09:50:50] [Iter 1307/2250] R4[362/2400] | LR: 0.028623 | E: -42.349534 | E_var:     0.3302 | E_err:   0.008979
[2025-10-03 09:51:01] [Iter 1308/2250] R4[364/2400] | LR: 0.028608 | E: -42.354200 | E_var:     0.2935 | E_err:   0.008465
[2025-10-03 09:51:12] [Iter 1309/2250] R4[366/2400] | LR: 0.028593 | E: -42.342028 | E_var:     0.3379 | E_err:   0.009083
[2025-10-03 09:51:22] [Iter 1310/2250] R4[368/2400] | LR: 0.028578 | E: -42.347681 | E_var:     0.2448 | E_err:   0.007731
[2025-10-03 09:51:33] [Iter 1311/2250] R4[370/2400] | LR: 0.028562 | E: -42.366319 | E_var:     0.3968 | E_err:   0.009842
[2025-10-03 09:51:43] [Iter 1312/2250] R4[372/2400] | LR: 0.028547 | E: -42.355017 | E_var:     0.2746 | E_err:   0.008188
[2025-10-03 09:51:54] [Iter 1313/2250] R4[374/2400] | LR: 0.028532 | E: -42.336378 | E_var:     0.3170 | E_err:   0.008798
[2025-10-03 09:52:04] [Iter 1314/2250] R4[376/2400] | LR: 0.028516 | E: -42.340990 | E_var:     0.3443 | E_err:   0.009168
[2025-10-03 09:52:15] [Iter 1315/2250] R4[378/2400] | LR: 0.028501 | E: -42.347423 | E_var:     0.2802 | E_err:   0.008271
[2025-10-03 09:52:25] [Iter 1316/2250] R4[380/2400] | LR: 0.028485 | E: -42.338718 | E_var:     0.2939 | E_err:   0.008470
[2025-10-03 09:52:36] [Iter 1317/2250] R4[382/2400] | LR: 0.028470 | E: -42.349545 | E_var:     0.2431 | E_err:   0.007703
[2025-10-03 09:52:46] [Iter 1318/2250] R4[384/2400] | LR: 0.028454 | E: -42.347116 | E_var:     0.2708 | E_err:   0.008131
[2025-10-03 09:52:57] [Iter 1319/2250] R4[386/2400] | LR: 0.028438 | E: -42.336593 | E_var:     0.2392 | E_err:   0.007641
[2025-10-03 09:53:07] [Iter 1320/2250] R4[388/2400] | LR: 0.028422 | E: -42.361681 | E_var:     0.4581 | E_err:   0.010576
[2025-10-03 09:53:18] [Iter 1321/2250] R4[390/2400] | LR: 0.028406 | E: -42.337622 | E_var:     0.2867 | E_err:   0.008367
[2025-10-03 09:53:28] [Iter 1322/2250] R4[392/2400] | LR: 0.028390 | E: -42.337010 | E_var:     0.2951 | E_err:   0.008488
[2025-10-03 09:53:39] [Iter 1323/2250] R4[394/2400] | LR: 0.028374 | E: -42.350908 | E_var:     0.2788 | E_err:   0.008250
[2025-10-03 09:53:49] [Iter 1324/2250] R4[396/2400] | LR: 0.028358 | E: -42.362270 | E_var:     0.2444 | E_err:   0.007724
[2025-10-03 09:54:00] [Iter 1325/2250] R4[398/2400] | LR: 0.028342 | E: -42.357111 | E_var:     0.3402 | E_err:   0.009114
[2025-10-03 09:54:10] [Iter 1326/2250] R4[400/2400] | LR: 0.028325 | E: -42.356917 | E_var:     0.4179 | E_err:   0.010101
[2025-10-03 09:54:21] [Iter 1327/2250] R4[402/2400] | LR: 0.028309 | E: -42.347045 | E_var:     0.3318 | E_err:   0.009000
[2025-10-03 09:54:31] [Iter 1328/2250] R4[404/2400] | LR: 0.028292 | E: -42.357406 | E_var:     0.2906 | E_err:   0.008423
[2025-10-03 09:54:42] [Iter 1329/2250] R4[406/2400] | LR: 0.028276 | E: -42.354170 | E_var:     0.3254 | E_err:   0.008913
[2025-10-03 09:54:52] [Iter 1330/2250] R4[408/2400] | LR: 0.028259 | E: -42.360271 | E_var:     0.2900 | E_err:   0.008414
[2025-10-03 09:55:03] [Iter 1331/2250] R4[410/2400] | LR: 0.028243 | E: -42.339949 | E_var:     0.3014 | E_err:   0.008578
[2025-10-03 09:55:14] [Iter 1332/2250] R4[412/2400] | LR: 0.028226 | E: -42.335301 | E_var:     4.9705 | E_err:   0.034835
[2025-10-03 09:55:24] [Iter 1333/2250] R4[414/2400] | LR: 0.028209 | E: -42.349070 | E_var:     0.3089 | E_err:   0.008684
[2025-10-03 09:55:35] [Iter 1334/2250] R4[416/2400] | LR: 0.028192 | E: -42.357012 | E_var:     0.3029 | E_err:   0.008599
[2025-10-03 09:55:45] [Iter 1335/2250] R4[418/2400] | LR: 0.028175 | E: -42.361020 | E_var:     0.6263 | E_err:   0.012366
[2025-10-03 09:55:56] [Iter 1336/2250] R4[420/2400] | LR: 0.028158 | E: -42.340004 | E_var:     0.3292 | E_err:   0.008965
[2025-10-03 09:56:06] [Iter 1337/2250] R4[422/2400] | LR: 0.028141 | E: -42.341735 | E_var:     0.2500 | E_err:   0.007813
[2025-10-03 09:56:17] [Iter 1338/2250] R4[424/2400] | LR: 0.028124 | E: -42.353904 | E_var:     0.3137 | E_err:   0.008751
[2025-10-03 09:56:27] [Iter 1339/2250] R4[426/2400] | LR: 0.028106 | E: -42.350815 | E_var:     0.2525 | E_err:   0.007852
[2025-10-03 09:56:38] [Iter 1340/2250] R4[428/2400] | LR: 0.028089 | E: -42.333111 | E_var:     0.2562 | E_err:   0.007909
[2025-10-03 09:56:48] [Iter 1341/2250] R4[430/2400] | LR: 0.028072 | E: -42.345693 | E_var:     0.3018 | E_err:   0.008584
[2025-10-03 09:56:59] [Iter 1342/2250] R4[432/2400] | LR: 0.028054 | E: -42.354925 | E_var:     0.2673 | E_err:   0.008078
[2025-10-03 09:57:09] [Iter 1343/2250] R4[434/2400] | LR: 0.028037 | E: -42.347094 | E_var:     0.3016 | E_err:   0.008580
[2025-10-03 09:57:20] [Iter 1344/2250] R4[436/2400] | LR: 0.028019 | E: -42.337651 | E_var:     0.3054 | E_err:   0.008635
[2025-10-03 09:57:30] [Iter 1345/2250] R4[438/2400] | LR: 0.028001 | E: -42.348312 | E_var:     0.3239 | E_err:   0.008893
[2025-10-03 09:57:41] [Iter 1346/2250] R4[440/2400] | LR: 0.027983 | E: -42.348659 | E_var:     0.2654 | E_err:   0.008049
[2025-10-03 09:57:51] [Iter 1347/2250] R4[442/2400] | LR: 0.027966 | E: -42.359732 | E_var:     0.2994 | E_err:   0.008550
[2025-10-03 09:58:02] [Iter 1348/2250] R4[444/2400] | LR: 0.027948 | E: -42.345850 | E_var:     0.2695 | E_err:   0.008111
[2025-10-03 09:58:12] [Iter 1349/2250] R4[446/2400] | LR: 0.027930 | E: -42.344145 | E_var:     0.2782 | E_err:   0.008242
[2025-10-03 09:58:23] [Iter 1350/2250] R4[448/2400] | LR: 0.027912 | E: -42.350425 | E_var:     0.3087 | E_err:   0.008681
[2025-10-03 09:58:33] [Iter 1351/2250] R4[450/2400] | LR: 0.027893 | E: -42.336213 | E_var:     0.5809 | E_err:   0.011909
[2025-10-03 09:58:44] [Iter 1352/2250] R4[452/2400] | LR: 0.027875 | E: -42.333290 | E_var:     0.8434 | E_err:   0.014349
[2025-10-03 09:58:55] [Iter 1353/2250] R4[454/2400] | LR: 0.027857 | E: -42.329738 | E_var:     1.1786 | E_err:   0.016963
[2025-10-03 09:59:05] [Iter 1354/2250] R4[456/2400] | LR: 0.027839 | E: -42.354415 | E_var:     0.2823 | E_err:   0.008302
[2025-10-03 09:59:16] [Iter 1355/2250] R4[458/2400] | LR: 0.027820 | E: -42.342197 | E_var:     0.3263 | E_err:   0.008926
[2025-10-03 09:59:26] [Iter 1356/2250] R4[460/2400] | LR: 0.027802 | E: -42.344052 | E_var:     0.3077 | E_err:   0.008668
[2025-10-03 09:59:37] [Iter 1357/2250] R4[462/2400] | LR: 0.027783 | E: -42.343771 | E_var:     0.3268 | E_err:   0.008933
[2025-10-03 09:59:47] [Iter 1358/2250] R4[464/2400] | LR: 0.027764 | E: -42.360477 | E_var:     0.3356 | E_err:   0.009052
[2025-10-03 09:59:58] [Iter 1359/2250] R4[466/2400] | LR: 0.027746 | E: -42.338394 | E_var:     0.3985 | E_err:   0.009864
[2025-10-03 10:00:08] [Iter 1360/2250] R4[468/2400] | LR: 0.027727 | E: -42.341319 | E_var:     0.3366 | E_err:   0.009065
[2025-10-03 10:00:19] [Iter 1361/2250] R4[470/2400] | LR: 0.027708 | E: -42.359446 | E_var:     0.3104 | E_err:   0.008705
[2025-10-03 10:00:29] [Iter 1362/2250] R4[472/2400] | LR: 0.027689 | E: -42.347106 | E_var:     0.2905 | E_err:   0.008421
[2025-10-03 10:00:40] [Iter 1363/2250] R4[474/2400] | LR: 0.027670 | E: -42.350724 | E_var:     0.2688 | E_err:   0.008102
[2025-10-03 10:00:50] [Iter 1364/2250] R4[476/2400] | LR: 0.027651 | E: -42.350711 | E_var:     0.3448 | E_err:   0.009175
[2025-10-03 10:01:01] [Iter 1365/2250] R4[478/2400] | LR: 0.027632 | E: -42.344670 | E_var:     0.3114 | E_err:   0.008719
[2025-10-03 10:01:11] [Iter 1366/2250] R4[480/2400] | LR: 0.027613 | E: -42.337466 | E_var:     0.2580 | E_err:   0.007936
[2025-10-03 10:01:22] [Iter 1367/2250] R4[482/2400] | LR: 0.027593 | E: -42.342582 | E_var:     0.2537 | E_err:   0.007871
[2025-10-03 10:01:32] [Iter 1368/2250] R4[484/2400] | LR: 0.027574 | E: -42.351840 | E_var:     0.2603 | E_err:   0.007971
[2025-10-03 10:01:43] [Iter 1369/2250] R4[486/2400] | LR: 0.027555 | E: -42.338062 | E_var:     0.2965 | E_err:   0.008508
[2025-10-03 10:01:53] [Iter 1370/2250] R4[488/2400] | LR: 0.027535 | E: -42.349071 | E_var:     0.3845 | E_err:   0.009689
[2025-10-03 10:02:04] [Iter 1371/2250] R4[490/2400] | LR: 0.027516 | E: -42.354323 | E_var:     0.2630 | E_err:   0.008012
[2025-10-03 10:02:14] [Iter 1372/2250] R4[492/2400] | LR: 0.027496 | E: -42.339795 | E_var:     0.3003 | E_err:   0.008563
[2025-10-03 10:02:25] [Iter 1373/2250] R4[494/2400] | LR: 0.027476 | E: -42.341381 | E_var:     0.2874 | E_err:   0.008377
[2025-10-03 10:02:35] [Iter 1374/2250] R4[496/2400] | LR: 0.027457 | E: -42.354085 | E_var:     0.3246 | E_err:   0.008902
[2025-10-03 10:02:46] [Iter 1375/2250] R4[498/2400] | LR: 0.027437 | E: -42.361685 | E_var:     0.3381 | E_err:   0.009085
[2025-10-03 10:02:57] [Iter 1376/2250] R4[500/2400] | LR: 0.027417 | E: -42.354843 | E_var:     0.3331 | E_err:   0.009017
[2025-10-03 10:03:07] [Iter 1377/2250] R4[502/2400] | LR: 0.027397 | E: -42.352003 | E_var:     0.3623 | E_err:   0.009405
[2025-10-03 10:03:18] [Iter 1378/2250] R4[504/2400] | LR: 0.027377 | E: -42.346500 | E_var:     0.3453 | E_err:   0.009182
[2025-10-03 10:03:28] [Iter 1379/2250] R4[506/2400] | LR: 0.027357 | E: -42.346725 | E_var:     0.3030 | E_err:   0.008602
[2025-10-03 10:03:39] [Iter 1380/2250] R4[508/2400] | LR: 0.027337 | E: -42.355376 | E_var:     0.2617 | E_err:   0.007993
[2025-10-03 10:03:49] [Iter 1381/2250] R4[510/2400] | LR: 0.027316 | E: -42.346057 | E_var:     0.3356 | E_err:   0.009052
[2025-10-03 10:04:00] [Iter 1382/2250] R4[512/2400] | LR: 0.027296 | E: -42.349583 | E_var:     0.2593 | E_err:   0.007956
[2025-10-03 10:04:10] [Iter 1383/2250] R4[514/2400] | LR: 0.027276 | E: -42.337449 | E_var:     0.2936 | E_err:   0.008466
[2025-10-03 10:04:21] [Iter 1384/2250] R4[516/2400] | LR: 0.027255 | E: -42.344536 | E_var:     0.3293 | E_err:   0.008966
[2025-10-03 10:04:31] [Iter 1385/2250] R4[518/2400] | LR: 0.027235 | E: -42.338742 | E_var:     0.3213 | E_err:   0.008857
[2025-10-03 10:04:42] [Iter 1386/2250] R4[520/2400] | LR: 0.027214 | E: -42.343626 | E_var:     0.2668 | E_err:   0.008070
[2025-10-03 10:04:52] [Iter 1387/2250] R4[522/2400] | LR: 0.027194 | E: -42.347086 | E_var:     0.2775 | E_err:   0.008231
[2025-10-03 10:05:03] [Iter 1388/2250] R4[524/2400] | LR: 0.027173 | E: -42.356031 | E_var:     0.2819 | E_err:   0.008296
[2025-10-03 10:05:13] [Iter 1389/2250] R4[526/2400] | LR: 0.027152 | E: -42.343079 | E_var:     0.4272 | E_err:   0.010212
[2025-10-03 10:05:24] [Iter 1390/2250] R4[528/2400] | LR: 0.027131 | E: -42.337555 | E_var:     0.3612 | E_err:   0.009391
[2025-10-03 10:05:34] [Iter 1391/2250] R4[530/2400] | LR: 0.027111 | E: -42.361006 | E_var:     0.2837 | E_err:   0.008322
[2025-10-03 10:05:45] [Iter 1392/2250] R4[532/2400] | LR: 0.027090 | E: -42.352786 | E_var:     0.3231 | E_err:   0.008882
[2025-10-03 10:05:55] [Iter 1393/2250] R4[534/2400] | LR: 0.027069 | E: -42.323996 | E_var:     0.2820 | E_err:   0.008297
[2025-10-03 10:06:06] [Iter 1394/2250] R4[536/2400] | LR: 0.027047 | E: -42.341614 | E_var:     0.2772 | E_err:   0.008227
[2025-10-03 10:06:16] [Iter 1395/2250] R4[538/2400] | LR: 0.027026 | E: -42.340706 | E_var:     0.5113 | E_err:   0.011172
[2025-10-03 10:06:27] [Iter 1396/2250] R4[540/2400] | LR: 0.027005 | E: -42.348481 | E_var:     0.3537 | E_err:   0.009293
[2025-10-03 10:06:38] [Iter 1397/2250] R4[542/2400] | LR: 0.026984 | E: -42.354571 | E_var:     0.3039 | E_err:   0.008614
[2025-10-03 10:06:48] [Iter 1398/2250] R4[544/2400] | LR: 0.026962 | E: -42.331885 | E_var:     0.2776 | E_err:   0.008232
[2025-10-03 10:06:59] [Iter 1399/2250] R4[546/2400] | LR: 0.026941 | E: -42.356577 | E_var:     1.0289 | E_err:   0.015849
[2025-10-03 10:07:09] [Iter 1400/2250] R4[548/2400] | LR: 0.026920 | E: -42.351741 | E_var:     0.2914 | E_err:   0.008435
[2025-10-03 10:07:09] ✓ Checkpoint saved: checkpoint_iter_001400.pkl
[2025-10-03 10:07:20] [Iter 1401/2250] R4[550/2400] | LR: 0.026898 | E: -42.351156 | E_var:     0.2554 | E_err:   0.007897
[2025-10-03 10:07:30] [Iter 1402/2250] R4[552/2400] | LR: 0.026876 | E: -42.340819 | E_var:     0.2817 | E_err:   0.008292
[2025-10-03 10:07:41] [Iter 1403/2250] R4[554/2400] | LR: 0.026855 | E: -42.349298 | E_var:     0.2997 | E_err:   0.008553
[2025-10-03 10:07:51] [Iter 1404/2250] R4[556/2400] | LR: 0.026833 | E: -42.356281 | E_var:     0.2605 | E_err:   0.007975
[2025-10-03 10:08:02] [Iter 1405/2250] R4[558/2400] | LR: 0.026811 | E: -42.337916 | E_var:     0.3087 | E_err:   0.008682
[2025-10-03 10:08:12] [Iter 1406/2250] R4[560/2400] | LR: 0.026789 | E: -42.340261 | E_var:     0.2722 | E_err:   0.008152
[2025-10-03 10:08:23] [Iter 1407/2250] R4[562/2400] | LR: 0.026767 | E: -42.368318 | E_var:     0.2821 | E_err:   0.008299
[2025-10-03 10:08:33] [Iter 1408/2250] R4[564/2400] | LR: 0.026745 | E: -42.346360 | E_var:     0.3104 | E_err:   0.008705
[2025-10-03 10:08:44] [Iter 1409/2250] R4[566/2400] | LR: 0.026723 | E: -42.334356 | E_var:     0.2414 | E_err:   0.007676
[2025-10-03 10:08:54] [Iter 1410/2250] R4[568/2400] | LR: 0.026701 | E: -42.334637 | E_var:     0.3072 | E_err:   0.008660
[2025-10-03 10:09:05] [Iter 1411/2250] R4[570/2400] | LR: 0.026679 | E: -42.362925 | E_var:     0.2646 | E_err:   0.008037
[2025-10-03 10:09:15] [Iter 1412/2250] R4[572/2400] | LR: 0.026657 | E: -42.346777 | E_var:     0.2919 | E_err:   0.008442
[2025-10-03 10:09:26] [Iter 1413/2250] R4[574/2400] | LR: 0.026634 | E: -42.345371 | E_var:     0.9917 | E_err:   0.015560
[2025-10-03 10:09:37] [Iter 1414/2250] R4[576/2400] | LR: 0.026612 | E: -42.346238 | E_var:     0.8307 | E_err:   0.014241
[2025-10-03 10:09:47] [Iter 1415/2250] R4[578/2400] | LR: 0.026590 | E: -42.318868 | E_var:     1.1701 | E_err:   0.016902
[2025-10-03 10:09:58] [Iter 1416/2250] R4[580/2400] | LR: 0.026567 | E: -42.330739 | E_var:     1.0554 | E_err:   0.016052
[2025-10-03 10:10:08] [Iter 1417/2250] R4[582/2400] | LR: 0.026545 | E: -42.332851 | E_var:     1.1011 | E_err:   0.016396
[2025-10-03 10:10:19] [Iter 1418/2250] R4[584/2400] | LR: 0.026522 | E: -42.339189 | E_var:     0.2515 | E_err:   0.007835
[2025-10-03 10:10:29] [Iter 1419/2250] R4[586/2400] | LR: 0.026499 | E: -42.368305 | E_var:     0.3403 | E_err:   0.009115
[2025-10-03 10:10:40] [Iter 1420/2250] R4[588/2400] | LR: 0.026477 | E: -42.334952 | E_var:     0.2795 | E_err:   0.008260
[2025-10-03 10:10:50] [Iter 1421/2250] R4[590/2400] | LR: 0.026454 | E: -42.353908 | E_var:     0.3064 | E_err:   0.008649
[2025-10-03 10:11:01] [Iter 1422/2250] R4[592/2400] | LR: 0.026431 | E: -42.350280 | E_var:     0.2872 | E_err:   0.008373
[2025-10-03 10:11:11] [Iter 1423/2250] R4[594/2400] | LR: 0.026408 | E: -42.352161 | E_var:     0.2367 | E_err:   0.007602
[2025-10-03 10:11:22] [Iter 1424/2250] R4[596/2400] | LR: 0.026385 | E: -42.329756 | E_var:     0.7080 | E_err:   0.013147
[2025-10-03 10:11:32] [Iter 1425/2250] R4[598/2400] | LR: 0.026362 | E: -42.349198 | E_var:     0.3358 | E_err:   0.009055
[2025-10-03 10:11:43] [Iter 1426/2250] R4[600/2400] | LR: 0.026339 | E: -42.342726 | E_var:     0.2670 | E_err:   0.008074
[2025-10-03 10:11:53] [Iter 1427/2250] R4[602/2400] | LR: 0.026316 | E: -42.350584 | E_var:     0.3512 | E_err:   0.009259
[2025-10-03 10:12:04] [Iter 1428/2250] R4[604/2400] | LR: 0.026292 | E: -42.341990 | E_var:     0.2518 | E_err:   0.007841
[2025-10-03 10:12:14] [Iter 1429/2250] R4[606/2400] | LR: 0.026269 | E: -42.347645 | E_var:     0.2512 | E_err:   0.007831
[2025-10-03 10:12:25] [Iter 1430/2250] R4[608/2400] | LR: 0.026246 | E: -42.344832 | E_var:     0.2543 | E_err:   0.007879
[2025-10-03 10:12:35] [Iter 1431/2250] R4[610/2400] | LR: 0.026222 | E: -42.360592 | E_var:     0.2992 | E_err:   0.008546
[2025-10-03 10:12:46] [Iter 1432/2250] R4[612/2400] | LR: 0.026199 | E: -42.334283 | E_var:     0.3297 | E_err:   0.008972
[2025-10-03 10:12:57] [Iter 1433/2250] R4[614/2400] | LR: 0.026175 | E: -42.360708 | E_var:     0.4092 | E_err:   0.009995
[2025-10-03 10:13:07] [Iter 1434/2250] R4[616/2400] | LR: 0.026152 | E: -42.344041 | E_var:     0.2856 | E_err:   0.008350
[2025-10-03 10:13:18] [Iter 1435/2250] R4[618/2400] | LR: 0.026128 | E: -42.361213 | E_var:     0.2584 | E_err:   0.007942
[2025-10-03 10:13:28] [Iter 1436/2250] R4[620/2400] | LR: 0.026104 | E: -42.346308 | E_var:     0.2816 | E_err:   0.008291
[2025-10-03 10:13:39] [Iter 1437/2250] R4[622/2400] | LR: 0.026081 | E: -42.348860 | E_var:     0.3022 | E_err:   0.008590
[2025-10-03 10:13:49] [Iter 1438/2250] R4[624/2400] | LR: 0.026057 | E: -42.349713 | E_var:     0.2627 | E_err:   0.008008
[2025-10-03 10:14:00] [Iter 1439/2250] R4[626/2400] | LR: 0.026033 | E: -42.361417 | E_var:     0.2862 | E_err:   0.008359
[2025-10-03 10:14:10] [Iter 1440/2250] R4[628/2400] | LR: 0.026009 | E: -42.333669 | E_var:     0.4357 | E_err:   0.010314
[2025-10-03 10:14:21] [Iter 1441/2250] R4[630/2400] | LR: 0.025985 | E: -42.357297 | E_var:     0.3335 | E_err:   0.009024
[2025-10-03 10:14:31] [Iter 1442/2250] R4[632/2400] | LR: 0.025961 | E: -42.353525 | E_var:     0.2843 | E_err:   0.008331
[2025-10-03 10:14:42] [Iter 1443/2250] R4[634/2400] | LR: 0.025937 | E: -42.334678 | E_var:     0.3941 | E_err:   0.009809
[2025-10-03 10:14:52] [Iter 1444/2250] R4[636/2400] | LR: 0.025913 | E: -42.344101 | E_var:     0.2781 | E_err:   0.008241
[2025-10-03 10:15:03] [Iter 1445/2250] R4[638/2400] | LR: 0.025888 | E: -42.346819 | E_var:     0.2768 | E_err:   0.008221
[2025-10-03 10:15:13] [Iter 1446/2250] R4[640/2400] | LR: 0.025864 | E: -42.332807 | E_var:     0.3341 | E_err:   0.009031
[2025-10-03 10:15:24] [Iter 1447/2250] R4[642/2400] | LR: 0.025840 | E: -42.361103 | E_var:     0.4051 | E_err:   0.009944
[2025-10-03 10:15:35] [Iter 1448/2250] R4[644/2400] | LR: 0.025815 | E: -42.345143 | E_var:     0.2723 | E_err:   0.008154
[2025-10-03 10:15:45] [Iter 1449/2250] R4[646/2400] | LR: 0.025791 | E: -42.344760 | E_var:     0.2691 | E_err:   0.008105
[2025-10-03 10:15:56] [Iter 1450/2250] R4[648/2400] | LR: 0.025766 | E: -42.371100 | E_var:     0.2690 | E_err:   0.008104
[2025-10-03 10:16:06] [Iter 1451/2250] R4[650/2400] | LR: 0.025742 | E: -42.340717 | E_var:     0.2777 | E_err:   0.008234
[2025-10-03 10:16:17] [Iter 1452/2250] R4[652/2400] | LR: 0.025717 | E: -42.348391 | E_var:     0.3059 | E_err:   0.008643
[2025-10-03 10:16:27] [Iter 1453/2250] R4[654/2400] | LR: 0.025693 | E: -42.337584 | E_var:     0.2907 | E_err:   0.008425
[2025-10-03 10:16:38] [Iter 1454/2250] R4[656/2400] | LR: 0.025668 | E: -42.353478 | E_var:     0.3177 | E_err:   0.008807
[2025-10-03 10:16:48] [Iter 1455/2250] R4[658/2400] | LR: 0.025643 | E: -42.346947 | E_var:     0.2793 | E_err:   0.008258
[2025-10-03 10:16:59] [Iter 1456/2250] R4[660/2400] | LR: 0.025618 | E: -42.346235 | E_var:     0.2654 | E_err:   0.008049
[2025-10-03 10:17:09] [Iter 1457/2250] R4[662/2400] | LR: 0.025593 | E: -42.349232 | E_var:     0.3594 | E_err:   0.009367
[2025-10-03 10:17:20] [Iter 1458/2250] R4[664/2400] | LR: 0.025568 | E: -42.341704 | E_var:     0.2356 | E_err:   0.007585
[2025-10-03 10:17:30] [Iter 1459/2250] R4[666/2400] | LR: 0.025543 | E: -42.347346 | E_var:     0.2829 | E_err:   0.008311
[2025-10-03 10:17:41] [Iter 1460/2250] R4[668/2400] | LR: 0.025518 | E: -42.356712 | E_var:     0.2866 | E_err:   0.008364
[2025-10-03 10:17:51] [Iter 1461/2250] R4[670/2400] | LR: 0.025493 | E: -42.344278 | E_var:     0.2947 | E_err:   0.008483
[2025-10-03 10:18:02] [Iter 1462/2250] R4[672/2400] | LR: 0.025468 | E: -42.345458 | E_var:     0.2118 | E_err:   0.007190
[2025-10-03 10:18:12] [Iter 1463/2250] R4[674/2400] | LR: 0.025443 | E: -42.344647 | E_var:     0.2556 | E_err:   0.007900
[2025-10-03 10:18:23] [Iter 1464/2250] R4[676/2400] | LR: 0.025417 | E: -42.341195 | E_var:     0.3211 | E_err:   0.008854
[2025-10-03 10:18:33] [Iter 1465/2250] R4[678/2400] | LR: 0.025392 | E: -42.341174 | E_var:     0.2781 | E_err:   0.008239
[2025-10-03 10:18:44] [Iter 1466/2250] R4[680/2400] | LR: 0.025367 | E: -42.353818 | E_var:     0.2970 | E_err:   0.008516
[2025-10-03 10:18:55] [Iter 1467/2250] R4[682/2400] | LR: 0.025341 | E: -42.338362 | E_var:     0.2830 | E_err:   0.008312
[2025-10-03 10:19:05] [Iter 1468/2250] R4[684/2400] | LR: 0.025316 | E: -42.345971 | E_var:     0.3312 | E_err:   0.008992
[2025-10-03 10:19:16] [Iter 1469/2250] R4[686/2400] | LR: 0.025290 | E: -42.358512 | E_var:     0.2992 | E_err:   0.008547
[2025-10-03 10:19:26] [Iter 1470/2250] R4[688/2400] | LR: 0.025264 | E: -42.367761 | E_var:     0.2326 | E_err:   0.007535
[2025-10-03 10:19:37] [Iter 1471/2250] R4[690/2400] | LR: 0.025239 | E: -42.342550 | E_var:     0.2845 | E_err:   0.008335
[2025-10-03 10:19:47] [Iter 1472/2250] R4[692/2400] | LR: 0.025213 | E: -42.355884 | E_var:     0.3334 | E_err:   0.009022
[2025-10-03 10:19:58] [Iter 1473/2250] R4[694/2400] | LR: 0.025187 | E: -42.348640 | E_var:     0.2587 | E_err:   0.007948
[2025-10-03 10:20:08] [Iter 1474/2250] R4[696/2400] | LR: 0.025161 | E: -42.349360 | E_var:     0.2332 | E_err:   0.007545
[2025-10-03 10:20:19] [Iter 1475/2250] R4[698/2400] | LR: 0.025135 | E: -42.342336 | E_var:     0.3140 | E_err:   0.008756
[2025-10-03 10:20:29] [Iter 1476/2250] R4[700/2400] | LR: 0.025110 | E: -42.350292 | E_var:     0.2464 | E_err:   0.007756
[2025-10-03 10:20:40] [Iter 1477/2250] R4[702/2400] | LR: 0.025084 | E: -42.347261 | E_var:     0.2348 | E_err:   0.007572
[2025-10-03 10:20:50] [Iter 1478/2250] R4[704/2400] | LR: 0.025057 | E: -42.341666 | E_var:     0.2618 | E_err:   0.007994
[2025-10-03 10:21:01] [Iter 1479/2250] R4[706/2400] | LR: 0.025031 | E: -42.358263 | E_var:     0.2640 | E_err:   0.008028
[2025-10-03 10:21:11] [Iter 1480/2250] R4[708/2400] | LR: 0.025005 | E: -42.345636 | E_var:     0.3096 | E_err:   0.008694
[2025-10-03 10:21:22] [Iter 1481/2250] R4[710/2400] | LR: 0.024979 | E: -42.343905 | E_var:     0.3802 | E_err:   0.009634
[2025-10-03 10:21:32] [Iter 1482/2250] R4[712/2400] | LR: 0.024953 | E: -42.350122 | E_var:     0.2500 | E_err:   0.007813
[2025-10-03 10:21:43] [Iter 1483/2250] R4[714/2400] | LR: 0.024927 | E: -42.351831 | E_var:     0.2660 | E_err:   0.008059
[2025-10-03 10:21:53] [Iter 1484/2250] R4[716/2400] | LR: 0.024900 | E: -42.356864 | E_var:     0.2934 | E_err:   0.008463
[2025-10-03 10:22:04] [Iter 1485/2250] R4[718/2400] | LR: 0.024874 | E: -42.353822 | E_var:     0.3142 | E_err:   0.008758
[2025-10-03 10:22:15] [Iter 1486/2250] R4[720/2400] | LR: 0.024847 | E: -42.353074 | E_var:     0.4208 | E_err:   0.010136
[2025-10-03 10:22:25] [Iter 1487/2250] R4[722/2400] | LR: 0.024821 | E: -42.354136 | E_var:     0.2371 | E_err:   0.007608
[2025-10-03 10:22:36] [Iter 1488/2250] R4[724/2400] | LR: 0.024794 | E: -42.350223 | E_var:     0.2764 | E_err:   0.008214
[2025-10-03 10:22:46] [Iter 1489/2250] R4[726/2400] | LR: 0.024768 | E: -42.330695 | E_var:     0.3081 | E_err:   0.008673
[2025-10-03 10:22:57] [Iter 1490/2250] R4[728/2400] | LR: 0.024741 | E: -42.342483 | E_var:     0.3682 | E_err:   0.009481
[2025-10-03 10:23:07] [Iter 1491/2250] R4[730/2400] | LR: 0.024714 | E: -42.347745 | E_var:     0.5882 | E_err:   0.011983
[2025-10-03 10:23:18] [Iter 1492/2250] R4[732/2400] | LR: 0.024688 | E: -42.357005 | E_var:     0.2605 | E_err:   0.007975
[2025-10-03 10:23:28] [Iter 1493/2250] R4[734/2400] | LR: 0.024661 | E: -42.350892 | E_var:     0.4284 | E_err:   0.010227
[2025-10-03 10:23:39] [Iter 1494/2250] R4[736/2400] | LR: 0.024634 | E: -42.347144 | E_var:     0.2867 | E_err:   0.008366
[2025-10-03 10:23:49] [Iter 1495/2250] R4[738/2400] | LR: 0.024607 | E: -42.351521 | E_var:     0.2356 | E_err:   0.007584
[2025-10-03 10:24:00] [Iter 1496/2250] R4[740/2400] | LR: 0.024580 | E: -42.352542 | E_var:     0.2805 | E_err:   0.008275
[2025-10-03 10:24:10] [Iter 1497/2250] R4[742/2400] | LR: 0.024553 | E: -42.337891 | E_var:     0.3341 | E_err:   0.009032
[2025-10-03 10:24:21] [Iter 1498/2250] R4[744/2400] | LR: 0.024526 | E: -42.354608 | E_var:     0.2996 | E_err:   0.008552
[2025-10-03 10:24:31] [Iter 1499/2250] R4[746/2400] | LR: 0.024499 | E: -42.351846 | E_var:     0.3119 | E_err:   0.008726
[2025-10-03 10:24:42] [Iter 1500/2250] R4[748/2400] | LR: 0.024472 | E: -42.340620 | E_var:     0.2938 | E_err:   0.008469
[2025-10-03 10:24:52] [Iter 1501/2250] R4[750/2400] | LR: 0.024445 | E: -42.349322 | E_var:     0.2531 | E_err:   0.007861
[2025-10-03 10:25:03] [Iter 1502/2250] R4[752/2400] | LR: 0.024417 | E: -42.354590 | E_var:     0.2610 | E_err:   0.007982
[2025-10-03 10:25:14] [Iter 1503/2250] R4[754/2400] | LR: 0.024390 | E: -42.344453 | E_var:     0.2760 | E_err:   0.008209
[2025-10-03 10:25:24] [Iter 1504/2250] R4[756/2400] | LR: 0.024363 | E: -42.343502 | E_var:     0.2602 | E_err:   0.007970
[2025-10-03 10:25:35] [Iter 1505/2250] R4[758/2400] | LR: 0.024335 | E: -42.339760 | E_var:     0.2458 | E_err:   0.007747
[2025-10-03 10:25:45] [Iter 1506/2250] R4[760/2400] | LR: 0.024308 | E: -42.345701 | E_var:     0.2881 | E_err:   0.008387
[2025-10-03 10:25:56] [Iter 1507/2250] R4[762/2400] | LR: 0.024281 | E: -42.354664 | E_var:     0.3353 | E_err:   0.009047
[2025-10-03 10:26:06] [Iter 1508/2250] R4[764/2400] | LR: 0.024253 | E: -42.339696 | E_var:     0.2949 | E_err:   0.008485
[2025-10-03 10:26:17] [Iter 1509/2250] R4[766/2400] | LR: 0.024225 | E: -42.351631 | E_var:     0.3190 | E_err:   0.008826
[2025-10-03 10:26:27] [Iter 1510/2250] R4[768/2400] | LR: 0.024198 | E: -42.343710 | E_var:     0.2557 | E_err:   0.007902
[2025-10-03 10:26:38] [Iter 1511/2250] R4[770/2400] | LR: 0.024170 | E: -42.356649 | E_var:     0.2590 | E_err:   0.007952
[2025-10-03 10:26:48] [Iter 1512/2250] R4[772/2400] | LR: 0.024142 | E: -42.372877 | E_var:     0.6903 | E_err:   0.012982
[2025-10-03 10:26:59] [Iter 1513/2250] R4[774/2400] | LR: 0.024115 | E: -42.348987 | E_var:     0.2561 | E_err:   0.007908
[2025-10-03 10:27:09] [Iter 1514/2250] R4[776/2400] | LR: 0.024087 | E: -42.343992 | E_var:     0.2758 | E_err:   0.008206
[2025-10-03 10:27:20] [Iter 1515/2250] R4[778/2400] | LR: 0.024059 | E: -42.343897 | E_var:     0.3088 | E_err:   0.008683
[2025-10-03 10:27:30] [Iter 1516/2250] R4[780/2400] | LR: 0.024031 | E: -42.359127 | E_var:     0.2612 | E_err:   0.007985
[2025-10-03 10:27:41] [Iter 1517/2250] R4[782/2400] | LR: 0.024003 | E: -42.345440 | E_var:     0.2701 | E_err:   0.008120
[2025-10-03 10:27:51] [Iter 1518/2250] R4[784/2400] | LR: 0.023975 | E: -42.359958 | E_var:     0.2498 | E_err:   0.007810
[2025-10-03 10:28:02] [Iter 1519/2250] R4[786/2400] | LR: 0.023947 | E: -42.347506 | E_var:     0.2636 | E_err:   0.008023
[2025-10-03 10:28:13] [Iter 1520/2250] R4[788/2400] | LR: 0.023919 | E: -42.358687 | E_var:     0.2874 | E_err:   0.008376
[2025-10-03 10:28:23] [Iter 1521/2250] R4[790/2400] | LR: 0.023891 | E: -42.355721 | E_var:     0.3136 | E_err:   0.008751
[2025-10-03 10:28:34] [Iter 1522/2250] R4[792/2400] | LR: 0.023863 | E: -42.350751 | E_var:     0.2784 | E_err:   0.008244
[2025-10-03 10:28:44] [Iter 1523/2250] R4[794/2400] | LR: 0.023835 | E: -42.366796 | E_var:     0.3869 | E_err:   0.009720
[2025-10-03 10:28:55] [Iter 1524/2250] R4[796/2400] | LR: 0.023807 | E: -42.358655 | E_var:     0.2578 | E_err:   0.007934
[2025-10-03 10:29:05] [Iter 1525/2250] R4[798/2400] | LR: 0.023778 | E: -42.357367 | E_var:     0.2694 | E_err:   0.008111
[2025-10-03 10:29:16] [Iter 1526/2250] R4[800/2400] | LR: 0.023750 | E: -42.357734 | E_var:     0.2437 | E_err:   0.007713
[2025-10-03 10:29:26] [Iter 1527/2250] R4[802/2400] | LR: 0.023722 | E: -42.357237 | E_var:     0.3226 | E_err:   0.008875
[2025-10-03 10:29:37] [Iter 1528/2250] R4[804/2400] | LR: 0.023693 | E: -42.331430 | E_var:     0.8467 | E_err:   0.014377
[2025-10-03 10:29:47] [Iter 1529/2250] R4[806/2400] | LR: 0.023665 | E: -42.355253 | E_var:     0.2959 | E_err:   0.008499
[2025-10-03 10:29:58] [Iter 1530/2250] R4[808/2400] | LR: 0.023636 | E: -42.351571 | E_var:     0.3164 | E_err:   0.008788
[2025-10-03 10:30:08] [Iter 1531/2250] R4[810/2400] | LR: 0.023608 | E: -42.357827 | E_var:     0.4046 | E_err:   0.009939
[2025-10-03 10:30:19] [Iter 1532/2250] R4[812/2400] | LR: 0.023579 | E: -42.355016 | E_var:     0.2604 | E_err:   0.007973
[2025-10-03 10:30:29] [Iter 1533/2250] R4[814/2400] | LR: 0.023551 | E: -42.352017 | E_var:     0.2335 | E_err:   0.007550
[2025-10-03 10:30:40] [Iter 1534/2250] R4[816/2400] | LR: 0.023522 | E: -42.350516 | E_var:     0.3297 | E_err:   0.008972
[2025-10-03 10:30:50] [Iter 1535/2250] R4[818/2400] | LR: 0.023493 | E: -42.350746 | E_var:     0.2770 | E_err:   0.008224
[2025-10-03 10:31:01] [Iter 1536/2250] R4[820/2400] | LR: 0.023464 | E: -42.349450 | E_var:     0.4175 | E_err:   0.010096
[2025-10-03 10:31:12] [Iter 1537/2250] R4[822/2400] | LR: 0.023436 | E: -42.343565 | E_var:     0.2639 | E_err:   0.008027
[2025-10-03 10:31:22] [Iter 1538/2250] R4[824/2400] | LR: 0.023407 | E: -42.349942 | E_var:     0.3115 | E_err:   0.008721
[2025-10-03 10:31:33] [Iter 1539/2250] R4[826/2400] | LR: 0.023378 | E: -42.352971 | E_var:     0.3195 | E_err:   0.008832
[2025-10-03 10:31:43] [Iter 1540/2250] R4[828/2400] | LR: 0.023349 | E: -42.364206 | E_var:     0.2138 | E_err:   0.007226
[2025-10-03 10:31:54] [Iter 1541/2250] R4[830/2400] | LR: 0.023320 | E: -42.335980 | E_var:     0.3435 | E_err:   0.009158
[2025-10-03 10:32:04] [Iter 1542/2250] R4[832/2400] | LR: 0.023291 | E: -42.349926 | E_var:     0.3775 | E_err:   0.009601
[2025-10-03 10:32:15] [Iter 1543/2250] R4[834/2400] | LR: 0.023262 | E: -42.352211 | E_var:     0.2668 | E_err:   0.008071
[2025-10-03 10:32:25] [Iter 1544/2250] R4[836/2400] | LR: 0.023233 | E: -42.344911 | E_var:     0.3211 | E_err:   0.008854
[2025-10-03 10:32:36] [Iter 1545/2250] R4[838/2400] | LR: 0.023204 | E: -42.345371 | E_var:     0.2662 | E_err:   0.008062
[2025-10-03 10:32:46] [Iter 1546/2250] R4[840/2400] | LR: 0.023175 | E: -42.342332 | E_var:     0.2890 | E_err:   0.008400
[2025-10-03 10:32:57] [Iter 1547/2250] R4[842/2400] | LR: 0.023146 | E: -42.350444 | E_var:     0.3567 | E_err:   0.009331
[2025-10-03 10:33:07] [Iter 1548/2250] R4[844/2400] | LR: 0.023116 | E: -42.343587 | E_var:     0.4119 | E_err:   0.010027
[2025-10-03 10:33:18] [Iter 1549/2250] R4[846/2400] | LR: 0.023087 | E: -42.372199 | E_var:     0.3781 | E_err:   0.009608
[2025-10-03 10:33:28] [Iter 1550/2250] R4[848/2400] | LR: 0.023058 | E: -42.352405 | E_var:     0.2646 | E_err:   0.008038
[2025-10-03 10:33:39] [Iter 1551/2250] R4[850/2400] | LR: 0.023029 | E: -42.354505 | E_var:     0.3196 | E_err:   0.008834
[2025-10-03 10:33:49] [Iter 1552/2250] R4[852/2400] | LR: 0.022999 | E: -42.343066 | E_var:     0.2524 | E_err:   0.007851
[2025-10-03 10:34:00] [Iter 1553/2250] R4[854/2400] | LR: 0.022970 | E: -42.341873 | E_var:     0.3629 | E_err:   0.009413
[2025-10-03 10:34:11] [Iter 1554/2250] R4[856/2400] | LR: 0.022940 | E: -42.357673 | E_var:     0.2524 | E_err:   0.007850
[2025-10-03 10:34:21] [Iter 1555/2250] R4[858/2400] | LR: 0.022911 | E: -42.346497 | E_var:     0.3335 | E_err:   0.009023
[2025-10-03 10:34:32] [Iter 1556/2250] R4[860/2400] | LR: 0.022881 | E: -42.341756 | E_var:     0.3621 | E_err:   0.009402
[2025-10-03 10:34:42] [Iter 1557/2250] R4[862/2400] | LR: 0.022852 | E: -42.348110 | E_var:     0.2959 | E_err:   0.008500
[2025-10-03 10:34:53] [Iter 1558/2250] R4[864/2400] | LR: 0.022822 | E: -42.366468 | E_var:     0.3249 | E_err:   0.008906
[2025-10-03 10:35:03] [Iter 1559/2250] R4[866/2400] | LR: 0.022793 | E: -42.349404 | E_var:     0.2878 | E_err:   0.008382
[2025-10-03 10:35:14] [Iter 1560/2250] R4[868/2400] | LR: 0.022763 | E: -42.351939 | E_var:     0.9257 | E_err:   0.015033
[2025-10-03 10:35:24] [Iter 1561/2250] R4[870/2400] | LR: 0.022733 | E: -42.348503 | E_var:     0.2951 | E_err:   0.008488
[2025-10-03 10:35:35] [Iter 1562/2250] R4[872/2400] | LR: 0.022704 | E: -42.362081 | E_var:     0.2486 | E_err:   0.007791
[2025-10-03 10:35:45] [Iter 1563/2250] R4[874/2400] | LR: 0.022674 | E: -42.354413 | E_var:     0.3526 | E_err:   0.009278
[2025-10-03 10:35:56] [Iter 1564/2250] R4[876/2400] | LR: 0.022644 | E: -42.356670 | E_var:     0.3183 | E_err:   0.008816
[2025-10-03 10:36:06] [Iter 1565/2250] R4[878/2400] | LR: 0.022614 | E: -42.347872 | E_var:     0.4004 | E_err:   0.009887
[2025-10-03 10:36:17] [Iter 1566/2250] R4[880/2400] | LR: 0.022584 | E: -42.349886 | E_var:     0.2767 | E_err:   0.008219
[2025-10-03 10:36:28] [Iter 1567/2250] R4[882/2400] | LR: 0.022554 | E: -42.350866 | E_var:     0.2668 | E_err:   0.008070
[2025-10-03 10:36:38] [Iter 1568/2250] R4[884/2400] | LR: 0.022524 | E: -42.355169 | E_var:     0.2329 | E_err:   0.007540
[2025-10-03 10:36:49] [Iter 1569/2250] R4[886/2400] | LR: 0.022494 | E: -42.326528 | E_var:     0.2524 | E_err:   0.007851
[2025-10-03 10:36:59] [Iter 1570/2250] R4[888/2400] | LR: 0.022464 | E: -42.348707 | E_var:     0.3333 | E_err:   0.009021
[2025-10-03 10:37:10] [Iter 1571/2250] R4[890/2400] | LR: 0.022434 | E: -42.329229 | E_var:     0.3490 | E_err:   0.009231
[2025-10-03 10:37:20] [Iter 1572/2250] R4[892/2400] | LR: 0.022404 | E: -42.339554 | E_var:     0.2515 | E_err:   0.007835
[2025-10-03 10:37:31] [Iter 1573/2250] R4[894/2400] | LR: 0.022374 | E: -42.354296 | E_var:     0.3238 | E_err:   0.008891
[2025-10-03 10:37:41] [Iter 1574/2250] R4[896/2400] | LR: 0.022344 | E: -42.362689 | E_var:     0.3023 | E_err:   0.008591
[2025-10-03 10:37:52] [Iter 1575/2250] R4[898/2400] | LR: 0.022314 | E: -42.336473 | E_var:     0.2853 | E_err:   0.008346
[2025-10-03 10:38:02] [Iter 1576/2250] R4[900/2400] | LR: 0.022284 | E: -42.357830 | E_var:     0.3437 | E_err:   0.009161
[2025-10-03 10:38:13] [Iter 1577/2250] R4[902/2400] | LR: 0.022253 | E: -42.360139 | E_var:     0.4333 | E_err:   0.010285
[2025-10-03 10:38:23] [Iter 1578/2250] R4[904/2400] | LR: 0.022223 | E: -42.337140 | E_var:     0.4731 | E_err:   0.010747
[2025-10-03 10:38:34] [Iter 1579/2250] R4[906/2400] | LR: 0.022193 | E: -42.345632 | E_var:     0.3155 | E_err:   0.008776
[2025-10-03 10:38:44] [Iter 1580/2250] R4[908/2400] | LR: 0.022162 | E: -42.348244 | E_var:     0.2773 | E_err:   0.008228
[2025-10-03 10:38:55] [Iter 1581/2250] R4[910/2400] | LR: 0.022132 | E: -42.356462 | E_var:     0.2268 | E_err:   0.007441
[2025-10-03 10:39:05] [Iter 1582/2250] R4[912/2400] | LR: 0.022102 | E: -42.352549 | E_var:     0.3470 | E_err:   0.009205
[2025-10-03 10:39:16] [Iter 1583/2250] R4[914/2400] | LR: 0.022071 | E: -42.354269 | E_var:     0.3831 | E_err:   0.009670
[2025-10-03 10:39:27] [Iter 1584/2250] R4[916/2400] | LR: 0.022041 | E: -42.362241 | E_var:     0.2783 | E_err:   0.008243
[2025-10-03 10:39:37] [Iter 1585/2250] R4[918/2400] | LR: 0.022010 | E: -42.346155 | E_var:     0.2568 | E_err:   0.007918
[2025-10-03 10:39:48] [Iter 1586/2250] R4[920/2400] | LR: 0.021980 | E: -42.340762 | E_var:     0.2437 | E_err:   0.007713
[2025-10-03 10:39:58] [Iter 1587/2250] R4[922/2400] | LR: 0.021949 | E: -42.333616 | E_var:     0.3417 | E_err:   0.009133
[2025-10-03 10:40:09] [Iter 1588/2250] R4[924/2400] | LR: 0.021918 | E: -42.343225 | E_var:     0.3177 | E_err:   0.008807
[2025-10-03 10:40:19] [Iter 1589/2250] R4[926/2400] | LR: 0.021888 | E: -42.357785 | E_var:     0.2864 | E_err:   0.008362
[2025-10-03 10:40:30] [Iter 1590/2250] R4[928/2400] | LR: 0.021857 | E: -42.346397 | E_var:     0.3581 | E_err:   0.009350
[2025-10-03 10:40:40] [Iter 1591/2250] R4[930/2400] | LR: 0.021826 | E: -42.349223 | E_var:     0.3030 | E_err:   0.008600
[2025-10-03 10:40:51] [Iter 1592/2250] R4[932/2400] | LR: 0.021796 | E: -42.357846 | E_var:     0.2317 | E_err:   0.007522
[2025-10-03 10:41:01] [Iter 1593/2250] R4[934/2400] | LR: 0.021765 | E: -42.348174 | E_var:     0.2989 | E_err:   0.008543
[2025-10-03 10:41:12] [Iter 1594/2250] R4[936/2400] | LR: 0.021734 | E: -42.358274 | E_var:     0.3024 | E_err:   0.008593
[2025-10-03 10:41:22] [Iter 1595/2250] R4[938/2400] | LR: 0.021703 | E: -42.356200 | E_var:     0.2404 | E_err:   0.007662
[2025-10-03 10:41:33] [Iter 1596/2250] R4[940/2400] | LR: 0.021673 | E: -42.342392 | E_var:     0.2621 | E_err:   0.008000
[2025-10-03 10:41:43] [Iter 1597/2250] R4[942/2400] | LR: 0.021642 | E: -42.345189 | E_var:     0.6707 | E_err:   0.012796
[2025-10-03 10:41:54] [Iter 1598/2250] R4[944/2400] | LR: 0.021611 | E: -42.358307 | E_var:     0.2650 | E_err:   0.008044
[2025-10-03 10:42:05] [Iter 1599/2250] R4[946/2400] | LR: 0.021580 | E: -42.336179 | E_var:     0.2569 | E_err:   0.007920
[2025-10-03 10:42:15] [Iter 1600/2250] R4[948/2400] | LR: 0.021549 | E: -42.343111 | E_var:     0.2351 | E_err:   0.007575
[2025-10-03 10:42:15] ✓ Checkpoint saved: checkpoint_iter_001600.pkl
[2025-10-03 10:42:26] [Iter 1601/2250] R4[950/2400] | LR: 0.021518 | E: -42.351295 | E_var:     0.2532 | E_err:   0.007863
[2025-10-03 10:42:36] [Iter 1602/2250] R4[952/2400] | LR: 0.021487 | E: -42.353214 | E_var:     0.2596 | E_err:   0.007960
[2025-10-03 10:42:47] [Iter 1603/2250] R4[954/2400] | LR: 0.021456 | E: -42.363721 | E_var:     0.2201 | E_err:   0.007330
[2025-10-03 10:42:57] [Iter 1604/2250] R4[956/2400] | LR: 0.021425 | E: -42.350710 | E_var:     0.2773 | E_err:   0.008228
[2025-10-03 10:43:08] [Iter 1605/2250] R4[958/2400] | LR: 0.021394 | E: -42.349757 | E_var:     0.2938 | E_err:   0.008469
[2025-10-03 10:43:18] [Iter 1606/2250] R4[960/2400] | LR: 0.021363 | E: -42.353838 | E_var:     0.2404 | E_err:   0.007661
[2025-10-03 10:43:29] [Iter 1607/2250] R4[962/2400] | LR: 0.021332 | E: -42.364700 | E_var:     0.5107 | E_err:   0.011166
[2025-10-03 10:43:39] [Iter 1608/2250] R4[964/2400] | LR: 0.021300 | E: -42.345462 | E_var:     0.2859 | E_err:   0.008355
[2025-10-03 10:43:50] [Iter 1609/2250] R4[966/2400] | LR: 0.021269 | E: -42.347746 | E_var:     0.2766 | E_err:   0.008218
[2025-10-03 10:44:00] [Iter 1610/2250] R4[968/2400] | LR: 0.021238 | E: -42.353952 | E_var:     0.2969 | E_err:   0.008514
[2025-10-03 10:44:11] [Iter 1611/2250] R4[970/2400] | LR: 0.021207 | E: -42.318821 | E_var:     1.1522 | E_err:   0.016772
[2025-10-03 10:44:22] [Iter 1612/2250] R4[972/2400] | LR: 0.021176 | E: -42.358117 | E_var:     0.2440 | E_err:   0.007718
[2025-10-03 10:44:32] [Iter 1613/2250] R4[974/2400] | LR: 0.021144 | E: -42.342795 | E_var:     0.3152 | E_err:   0.008773
[2025-10-03 10:44:43] [Iter 1614/2250] R4[976/2400] | LR: 0.021113 | E: -42.351286 | E_var:     0.2803 | E_err:   0.008273
[2025-10-03 10:44:53] [Iter 1615/2250] R4[978/2400] | LR: 0.021082 | E: -42.344051 | E_var:     0.2514 | E_err:   0.007835
[2025-10-03 10:45:04] [Iter 1616/2250] R4[980/2400] | LR: 0.021050 | E: -42.356580 | E_var:     0.2519 | E_err:   0.007841
[2025-10-03 10:45:14] [Iter 1617/2250] R4[982/2400] | LR: 0.021019 | E: -42.262974 | E_var:     3.0164 | E_err:   0.027137
[2025-10-03 10:45:25] [Iter 1618/2250] R4[984/2400] | LR: 0.020987 | E: -42.180375 | E_var:     6.2045 | E_err:   0.038920
[2025-10-03 10:45:35] [Iter 1619/2250] R4[986/2400] | LR: 0.020956 | E: -42.250363 | E_var:     3.6603 | E_err:   0.029894
[2025-10-03 10:45:46] [Iter 1620/2250] R4[988/2400] | LR: 0.020924 | E: -42.341447 | E_var:     0.3645 | E_err:   0.009433
[2025-10-03 10:45:56] [Iter 1621/2250] R4[990/2400] | LR: 0.020893 | E: -42.348988 | E_var:     0.3706 | E_err:   0.009512
[2025-10-03 10:46:07] [Iter 1622/2250] R4[992/2400] | LR: 0.020861 | E: -42.347770 | E_var:     0.2820 | E_err:   0.008297
[2025-10-03 10:46:17] [Iter 1623/2250] R4[994/2400] | LR: 0.020830 | E: -42.351308 | E_var:     0.2597 | E_err:   0.007963
[2025-10-03 10:46:28] [Iter 1624/2250] R4[996/2400] | LR: 0.020798 | E: -42.350412 | E_var:     0.2960 | E_err:   0.008501
[2025-10-03 10:46:38] [Iter 1625/2250] R4[998/2400] | LR: 0.020767 | E: -42.327284 | E_var:     0.2751 | E_err:   0.008196
[2025-10-03 10:46:49] [Iter 1626/2250] R4[1000/2400] | LR: 0.020735 | E: -42.369825 | E_var:     0.2806 | E_err:   0.008277
[2025-10-03 10:46:59] [Iter 1627/2250] R4[1002/2400] | LR: 0.020704 | E: -42.353406 | E_var:     0.2791 | E_err:   0.008254
[2025-10-03 10:47:10] [Iter 1628/2250] R4[1004/2400] | LR: 0.020672 | E: -42.350486 | E_var:     0.3258 | E_err:   0.008918
[2025-10-03 10:47:20] [Iter 1629/2250] R4[1006/2400] | LR: 0.020640 | E: -42.346785 | E_var:     0.2547 | E_err:   0.007885
[2025-10-03 10:47:31] [Iter 1630/2250] R4[1008/2400] | LR: 0.020609 | E: -42.359013 | E_var:     0.3183 | E_err:   0.008815
[2025-10-03 10:47:41] [Iter 1631/2250] R4[1010/2400] | LR: 0.020577 | E: -42.338752 | E_var:     0.2738 | E_err:   0.008176
[2025-10-03 10:47:52] [Iter 1632/2250] R4[1012/2400] | LR: 0.020545 | E: -42.351021 | E_var:     0.3536 | E_err:   0.009292
[2025-10-03 10:48:03] [Iter 1633/2250] R4[1014/2400] | LR: 0.020513 | E: -42.341340 | E_var:     0.3034 | E_err:   0.008607
[2025-10-03 10:48:13] [Iter 1634/2250] R4[1016/2400] | LR: 0.020482 | E: -42.348196 | E_var:     0.2709 | E_err:   0.008132
[2025-10-03 10:48:24] [Iter 1635/2250] R4[1018/2400] | LR: 0.020450 | E: -42.333202 | E_var:     0.3162 | E_err:   0.008786
[2025-10-03 10:48:34] [Iter 1636/2250] R4[1020/2400] | LR: 0.020418 | E: -42.345899 | E_var:     0.2448 | E_err:   0.007730
[2025-10-03 10:48:45] [Iter 1637/2250] R4[1022/2400] | LR: 0.020386 | E: -42.367993 | E_var:     0.8291 | E_err:   0.014227
[2025-10-03 10:48:55] [Iter 1638/2250] R4[1024/2400] | LR: 0.020354 | E: -42.345937 | E_var:     0.3763 | E_err:   0.009585
[2025-10-03 10:49:06] [Iter 1639/2250] R4[1026/2400] | LR: 0.020323 | E: -42.351253 | E_var:     0.2312 | E_err:   0.007513
[2025-10-03 10:49:16] [Iter 1640/2250] R4[1028/2400] | LR: 0.020291 | E: -42.350690 | E_var:     0.3242 | E_err:   0.008896
[2025-10-03 10:49:27] [Iter 1641/2250] R4[1030/2400] | LR: 0.020259 | E: -42.345411 | E_var:     0.3551 | E_err:   0.009311
[2025-10-03 10:49:37] [Iter 1642/2250] R4[1032/2400] | LR: 0.020227 | E: -42.356926 | E_var:     0.2738 | E_err:   0.008175
[2025-10-03 10:49:48] [Iter 1643/2250] R4[1034/2400] | LR: 0.020195 | E: -42.359319 | E_var:     0.2578 | E_err:   0.007933
[2025-10-03 10:49:58] [Iter 1644/2250] R4[1036/2400] | LR: 0.020163 | E: -42.345071 | E_var:     0.2842 | E_err:   0.008329
[2025-10-03 10:50:09] [Iter 1645/2250] R4[1038/2400] | LR: 0.020131 | E: -42.360025 | E_var:     0.3682 | E_err:   0.009481
[2025-10-03 10:50:19] [Iter 1646/2250] R4[1040/2400] | LR: 0.020099 | E: -42.349860 | E_var:     0.3632 | E_err:   0.009416
[2025-10-03 10:50:30] [Iter 1647/2250] R4[1042/2400] | LR: 0.020067 | E: -42.358295 | E_var:     0.2680 | E_err:   0.008089
[2025-10-03 10:50:40] [Iter 1648/2250] R4[1044/2400] | LR: 0.020035 | E: -42.354855 | E_var:     0.4022 | E_err:   0.009910
[2025-10-03 10:50:51] [Iter 1649/2250] R4[1046/2400] | LR: 0.020003 | E: -42.343806 | E_var:     0.2434 | E_err:   0.007708
[2025-10-03 10:51:01] [Iter 1650/2250] R4[1048/2400] | LR: 0.019971 | E: -42.349390 | E_var:     0.3966 | E_err:   0.009840
[2025-10-03 10:51:12] [Iter 1651/2250] R4[1050/2400] | LR: 0.019939 | E: -42.346899 | E_var:     0.2637 | E_err:   0.008024
[2025-10-03 10:51:22] [Iter 1652/2250] R4[1052/2400] | LR: 0.019907 | E: -42.347597 | E_var:     0.2653 | E_err:   0.008048
[2025-10-03 10:51:33] [Iter 1653/2250] R4[1054/2400] | LR: 0.019874 | E: -42.352987 | E_var:     0.6710 | E_err:   0.012799
[2025-10-03 10:51:44] [Iter 1654/2250] R4[1056/2400] | LR: 0.019842 | E: -42.339925 | E_var:     0.4140 | E_err:   0.010053
[2025-10-03 10:51:54] [Iter 1655/2250] R4[1058/2400] | LR: 0.019810 | E: -42.357160 | E_var:     0.2832 | E_err:   0.008315
[2025-10-03 10:52:05] [Iter 1656/2250] R4[1060/2400] | LR: 0.019778 | E: -42.352051 | E_var:     0.2631 | E_err:   0.008015
[2025-10-03 10:52:15] [Iter 1657/2250] R4[1062/2400] | LR: 0.019746 | E: -42.343648 | E_var:     0.2718 | E_err:   0.008146
[2025-10-03 10:52:26] [Iter 1658/2250] R4[1064/2400] | LR: 0.019714 | E: -42.354770 | E_var:     0.2591 | E_err:   0.007954
[2025-10-03 10:52:36] [Iter 1659/2250] R4[1066/2400] | LR: 0.019681 | E: -42.342907 | E_var:     0.2653 | E_err:   0.008047
[2025-10-03 10:52:47] [Iter 1660/2250] R4[1068/2400] | LR: 0.019649 | E: -42.356744 | E_var:     0.3144 | E_err:   0.008761
[2025-10-03 10:52:57] [Iter 1661/2250] R4[1070/2400] | LR: 0.019617 | E: -42.358612 | E_var:     0.3088 | E_err:   0.008682
[2025-10-03 10:53:08] [Iter 1662/2250] R4[1072/2400] | LR: 0.019585 | E: -42.352609 | E_var:     0.3939 | E_err:   0.009807
[2025-10-03 10:53:18] [Iter 1663/2250] R4[1074/2400] | LR: 0.019552 | E: -42.351619 | E_var:     0.4362 | E_err:   0.010320
[2025-10-03 10:53:29] [Iter 1664/2250] R4[1076/2400] | LR: 0.019520 | E: -42.350049 | E_var:     0.2448 | E_err:   0.007731
[2025-10-03 10:53:39] [Iter 1665/2250] R4[1078/2400] | LR: 0.019488 | E: -42.346603 | E_var:     0.2288 | E_err:   0.007474
[2025-10-03 10:53:50] [Iter 1666/2250] R4[1080/2400] | LR: 0.019455 | E: -42.365946 | E_var:     0.4676 | E_err:   0.010685
[2025-10-03 10:54:00] [Iter 1667/2250] R4[1082/2400] | LR: 0.019423 | E: -42.349818 | E_var:     0.2657 | E_err:   0.008054
[2025-10-03 10:54:11] [Iter 1668/2250] R4[1084/2400] | LR: 0.019391 | E: -42.340093 | E_var:     0.2850 | E_err:   0.008341
[2025-10-03 10:54:21] [Iter 1669/2250] R4[1086/2400] | LR: 0.019358 | E: -42.336668 | E_var:     0.2610 | E_err:   0.007982
[2025-10-03 10:54:32] [Iter 1670/2250] R4[1088/2400] | LR: 0.019326 | E: -42.355242 | E_var:     0.2450 | E_err:   0.007734
[2025-10-03 10:54:43] [Iter 1671/2250] R4[1090/2400] | LR: 0.019294 | E: -42.356815 | E_var:     0.2535 | E_err:   0.007867
[2025-10-03 10:54:53] [Iter 1672/2250] R4[1092/2400] | LR: 0.019261 | E: -42.359727 | E_var:     0.2453 | E_err:   0.007738
[2025-10-03 10:55:04] [Iter 1673/2250] R4[1094/2400] | LR: 0.019229 | E: -42.360700 | E_var:     0.3003 | E_err:   0.008562
[2025-10-03 10:55:14] [Iter 1674/2250] R4[1096/2400] | LR: 0.019196 | E: -42.334082 | E_var:     0.2471 | E_err:   0.007766
[2025-10-03 10:55:25] [Iter 1675/2250] R4[1098/2400] | LR: 0.019164 | E: -42.343913 | E_var:     0.3544 | E_err:   0.009302
[2025-10-03 10:55:35] [Iter 1676/2250] R4[1100/2400] | LR: 0.019132 | E: -42.351055 | E_var:     0.2482 | E_err:   0.007785
[2025-10-03 10:55:46] [Iter 1677/2250] R4[1102/2400] | LR: 0.019099 | E: -42.357755 | E_var:     0.2534 | E_err:   0.007866
[2025-10-03 10:55:56] [Iter 1678/2250] R4[1104/2400] | LR: 0.019067 | E: -42.352276 | E_var:     0.2624 | E_err:   0.008004
[2025-10-03 10:56:07] [Iter 1679/2250] R4[1106/2400] | LR: 0.019034 | E: -42.356529 | E_var:     0.2853 | E_err:   0.008346
[2025-10-03 10:56:17] [Iter 1680/2250] R4[1108/2400] | LR: 0.019002 | E: -42.363489 | E_var:     0.3146 | E_err:   0.008764
[2025-10-03 10:56:28] [Iter 1681/2250] R4[1110/2400] | LR: 0.018969 | E: -42.354319 | E_var:     0.3436 | E_err:   0.009159
[2025-10-03 10:56:38] [Iter 1682/2250] R4[1112/2400] | LR: 0.018937 | E: -42.348869 | E_var:     0.3506 | E_err:   0.009252
[2025-10-03 10:56:49] [Iter 1683/2250] R4[1114/2400] | LR: 0.018904 | E: -42.348137 | E_var:     0.2532 | E_err:   0.007862
[2025-10-03 10:57:00] [Iter 1684/2250] R4[1116/2400] | LR: 0.018872 | E: -42.349742 | E_var:     0.2573 | E_err:   0.007926
[2025-10-03 10:57:10] [Iter 1685/2250] R4[1118/2400] | LR: 0.018839 | E: -42.350400 | E_var:     0.2606 | E_err:   0.007977
[2025-10-03 10:57:21] [Iter 1686/2250] R4[1120/2400] | LR: 0.018807 | E: -42.367518 | E_var:     0.3174 | E_err:   0.008803
[2025-10-03 10:57:31] [Iter 1687/2250] R4[1122/2400] | LR: 0.018774 | E: -42.360513 | E_var:     0.2323 | E_err:   0.007531
[2025-10-03 10:57:42] [Iter 1688/2250] R4[1124/2400] | LR: 0.018741 | E: -42.354136 | E_var:     0.2780 | E_err:   0.008238
[2025-10-03 10:57:52] [Iter 1689/2250] R4[1126/2400] | LR: 0.018709 | E: -42.370142 | E_var:     0.4720 | E_err:   0.010735
[2025-10-03 10:58:03] [Iter 1690/2250] R4[1128/2400] | LR: 0.018676 | E: -42.344272 | E_var:     0.2533 | E_err:   0.007864
[2025-10-03 10:58:13] [Iter 1691/2250] R4[1130/2400] | LR: 0.018644 | E: -42.344931 | E_var:     0.3030 | E_err:   0.008601
[2025-10-03 10:58:24] [Iter 1692/2250] R4[1132/2400] | LR: 0.018611 | E: -42.340756 | E_var:     0.2493 | E_err:   0.007801
[2025-10-03 10:58:34] [Iter 1693/2250] R4[1134/2400] | LR: 0.018579 | E: -42.349173 | E_var:     0.2100 | E_err:   0.007161
[2025-10-03 10:58:45] [Iter 1694/2250] R4[1136/2400] | LR: 0.018546 | E: -42.342545 | E_var:     0.2686 | E_err:   0.008098
[2025-10-03 10:58:55] [Iter 1695/2250] R4[1138/2400] | LR: 0.018513 | E: -42.358140 | E_var:     0.2117 | E_err:   0.007190
[2025-10-03 10:59:06] [Iter 1696/2250] R4[1140/2400] | LR: 0.018481 | E: -42.345436 | E_var:     0.2544 | E_err:   0.007881
[2025-10-03 10:59:16] [Iter 1697/2250] R4[1142/2400] | LR: 0.018448 | E: -42.351249 | E_var:     0.3422 | E_err:   0.009140
[2025-10-03 10:59:27] [Iter 1698/2250] R4[1144/2400] | LR: 0.018415 | E: -42.354395 | E_var:     0.2686 | E_err:   0.008098
[2025-10-03 10:59:38] [Iter 1699/2250] R4[1146/2400] | LR: 0.018383 | E: -42.345969 | E_var:     0.4226 | E_err:   0.010158
[2025-10-03 10:59:48] [Iter 1700/2250] R4[1148/2400] | LR: 0.018350 | E: -42.357161 | E_var:     0.2645 | E_err:   0.008036
[2025-10-03 10:59:59] [Iter 1701/2250] R4[1150/2400] | LR: 0.018318 | E: -42.361765 | E_var:     0.2326 | E_err:   0.007535
[2025-10-03 11:00:09] [Iter 1702/2250] R4[1152/2400] | LR: 0.018285 | E: -42.355057 | E_var:     0.2677 | E_err:   0.008085
[2025-10-03 11:00:20] [Iter 1703/2250] R4[1154/2400] | LR: 0.018252 | E: -42.342536 | E_var:     0.2386 | E_err:   0.007633
[2025-10-03 11:00:30] [Iter 1704/2250] R4[1156/2400] | LR: 0.018220 | E: -42.352488 | E_var:     0.2982 | E_err:   0.008533
[2025-10-03 11:00:41] [Iter 1705/2250] R4[1158/2400] | LR: 0.018187 | E: -42.350781 | E_var:     0.3096 | E_err:   0.008694
[2025-10-03 11:00:51] [Iter 1706/2250] R4[1160/2400] | LR: 0.018154 | E: -42.359047 | E_var:     0.2673 | E_err:   0.008078
[2025-10-03 11:01:02] [Iter 1707/2250] R4[1162/2400] | LR: 0.018122 | E: -42.360465 | E_var:     0.2660 | E_err:   0.008059
[2025-10-03 11:01:12] [Iter 1708/2250] R4[1164/2400] | LR: 0.018089 | E: -42.344340 | E_var:     0.2588 | E_err:   0.007949
[2025-10-03 11:01:23] [Iter 1709/2250] R4[1166/2400] | LR: 0.018056 | E: -42.367317 | E_var:     0.2698 | E_err:   0.008116
[2025-10-03 11:01:33] [Iter 1710/2250] R4[1168/2400] | LR: 0.018023 | E: -42.342354 | E_var:     0.3146 | E_err:   0.008764
[2025-10-03 11:01:44] [Iter 1711/2250] R4[1170/2400] | LR: 0.017991 | E: -42.351766 | E_var:     0.2923 | E_err:   0.008448
[2025-10-03 11:01:54] [Iter 1712/2250] R4[1172/2400] | LR: 0.017958 | E: -42.362503 | E_var:     0.2908 | E_err:   0.008426
[2025-10-03 11:02:05] [Iter 1713/2250] R4[1174/2400] | LR: 0.017925 | E: -42.347769 | E_var:     0.4779 | E_err:   0.010801
[2025-10-03 11:02:16] [Iter 1714/2250] R4[1176/2400] | LR: 0.017893 | E: -42.344139 | E_var:     0.2865 | E_err:   0.008363
[2025-10-03 11:02:26] [Iter 1715/2250] R4[1178/2400] | LR: 0.017860 | E: -42.359585 | E_var:     0.2900 | E_err:   0.008415
[2025-10-03 11:02:37] [Iter 1716/2250] R4[1180/2400] | LR: 0.017827 | E: -42.351173 | E_var:     0.3700 | E_err:   0.009504
[2025-10-03 11:02:47] [Iter 1717/2250] R4[1182/2400] | LR: 0.017794 | E: -42.359889 | E_var:     0.4497 | E_err:   0.010478
[2025-10-03 11:02:58] [Iter 1718/2250] R4[1184/2400] | LR: 0.017762 | E: -42.346157 | E_var:     0.2459 | E_err:   0.007748
[2025-10-03 11:03:08] [Iter 1719/2250] R4[1186/2400] | LR: 0.017729 | E: -42.352822 | E_var:     0.2840 | E_err:   0.008327
[2025-10-03 11:03:19] [Iter 1720/2250] R4[1188/2400] | LR: 0.017696 | E: -42.340974 | E_var:     0.2484 | E_err:   0.007788
[2025-10-03 11:03:29] [Iter 1721/2250] R4[1190/2400] | LR: 0.017664 | E: -42.351850 | E_var:     0.2453 | E_err:   0.007739
[2025-10-03 11:03:40] [Iter 1722/2250] R4[1192/2400] | LR: 0.017631 | E: -42.364231 | E_var:     0.3343 | E_err:   0.009034
[2025-10-03 11:03:50] [Iter 1723/2250] R4[1194/2400] | LR: 0.017598 | E: -42.363818 | E_var:     0.2561 | E_err:   0.007907
[2025-10-03 11:04:01] [Iter 1724/2250] R4[1196/2400] | LR: 0.017565 | E: -42.351828 | E_var:     0.2459 | E_err:   0.007749
[2025-10-03 11:04:11] [Iter 1725/2250] R4[1198/2400] | LR: 0.017533 | E: -42.344620 | E_var:     0.5236 | E_err:   0.011306
[2025-10-03 11:04:22] [Iter 1726/2250] R4[1200/2400] | LR: 0.017500 | E: -42.350349 | E_var:     0.2715 | E_err:   0.008141
[2025-10-03 11:04:32] [Iter 1727/2250] R4[1202/2400] | LR: 0.017467 | E: -42.381165 | E_var:     0.2765 | E_err:   0.008216
[2025-10-03 11:04:43] [Iter 1728/2250] R4[1204/2400] | LR: 0.017435 | E: -42.358185 | E_var:     0.3215 | E_err:   0.008859
[2025-10-03 11:04:54] [Iter 1729/2250] R4[1206/2400] | LR: 0.017402 | E: -42.346038 | E_var:     0.2439 | E_err:   0.007716
[2025-10-03 11:05:04] [Iter 1730/2250] R4[1208/2400] | LR: 0.017369 | E: -42.367745 | E_var:     0.2858 | E_err:   0.008353
[2025-10-03 11:05:15] [Iter 1731/2250] R4[1210/2400] | LR: 0.017336 | E: -42.356122 | E_var:     0.2587 | E_err:   0.007947
[2025-10-03 11:05:25] [Iter 1732/2250] R4[1212/2400] | LR: 0.017304 | E: -42.340231 | E_var:     0.2461 | E_err:   0.007751
[2025-10-03 11:05:36] [Iter 1733/2250] R4[1214/2400] | LR: 0.017271 | E: -42.357361 | E_var:     0.2266 | E_err:   0.007437
[2025-10-03 11:05:46] [Iter 1734/2250] R4[1216/2400] | LR: 0.017238 | E: -42.351677 | E_var:     0.2924 | E_err:   0.008449
[2025-10-03 11:05:57] [Iter 1735/2250] R4[1218/2400] | LR: 0.017206 | E: -42.355090 | E_var:     0.3879 | E_err:   0.009731
[2025-10-03 11:06:07] [Iter 1736/2250] R4[1220/2400] | LR: 0.017173 | E: -42.355790 | E_var:     0.2423 | E_err:   0.007692
[2025-10-03 11:06:18] [Iter 1737/2250] R4[1222/2400] | LR: 0.017140 | E: -42.340102 | E_var:     0.2630 | E_err:   0.008013
[2025-10-03 11:06:28] [Iter 1738/2250] R4[1224/2400] | LR: 0.017107 | E: -42.373702 | E_var:     0.3901 | E_err:   0.009759
[2025-10-03 11:06:39] [Iter 1739/2250] R4[1226/2400] | LR: 0.017075 | E: -42.352611 | E_var:     0.3357 | E_err:   0.009053
[2025-10-03 11:06:49] [Iter 1740/2250] R4[1228/2400] | LR: 0.017042 | E: -42.345135 | E_var:     0.2816 | E_err:   0.008292
[2025-10-03 11:07:00] [Iter 1741/2250] R4[1230/2400] | LR: 0.017009 | E: -42.354901 | E_var:     0.2665 | E_err:   0.008066
[2025-10-03 11:07:10] [Iter 1742/2250] R4[1232/2400] | LR: 0.016977 | E: -42.355162 | E_var:     0.2189 | E_err:   0.007311
[2025-10-03 11:07:21] [Iter 1743/2250] R4[1234/2400] | LR: 0.016944 | E: -42.345640 | E_var:     0.4387 | E_err:   0.010349
[2025-10-03 11:07:32] [Iter 1744/2250] R4[1236/2400] | LR: 0.016911 | E: -42.359046 | E_var:     0.4265 | E_err:   0.010205
[2025-10-03 11:07:42] [Iter 1745/2250] R4[1238/2400] | LR: 0.016878 | E: -42.351021 | E_var:     0.2207 | E_err:   0.007341
[2025-10-03 11:07:53] [Iter 1746/2250] R4[1240/2400] | LR: 0.016846 | E: -42.355606 | E_var:     0.2732 | E_err:   0.008167
[2025-10-03 11:08:03] [Iter 1747/2250] R4[1242/2400] | LR: 0.016813 | E: -42.338297 | E_var:     0.3256 | E_err:   0.008916
[2025-10-03 11:08:14] [Iter 1748/2250] R4[1244/2400] | LR: 0.016780 | E: -42.349381 | E_var:     0.2995 | E_err:   0.008551
[2025-10-03 11:08:24] [Iter 1749/2250] R4[1246/2400] | LR: 0.016748 | E: -42.354332 | E_var:     0.2844 | E_err:   0.008332
[2025-10-03 11:08:35] [Iter 1750/2250] R4[1248/2400] | LR: 0.016715 | E: -42.351083 | E_var:     0.2796 | E_err:   0.008262
[2025-10-03 11:08:45] [Iter 1751/2250] R4[1250/2400] | LR: 0.016682 | E: -42.356786 | E_var:     0.3228 | E_err:   0.008878
[2025-10-03 11:08:56] [Iter 1752/2250] R4[1252/2400] | LR: 0.016650 | E: -42.352303 | E_var:     0.2911 | E_err:   0.008431
[2025-10-03 11:09:06] [Iter 1753/2250] R4[1254/2400] | LR: 0.016617 | E: -42.349607 | E_var:     0.3454 | E_err:   0.009183
[2025-10-03 11:09:17] [Iter 1754/2250] R4[1256/2400] | LR: 0.016585 | E: -42.346847 | E_var:     0.2629 | E_err:   0.008012
[2025-10-03 11:09:27] [Iter 1755/2250] R4[1258/2400] | LR: 0.016552 | E: -42.360786 | E_var:     0.3438 | E_err:   0.009161
[2025-10-03 11:09:38] [Iter 1756/2250] R4[1260/2400] | LR: 0.016519 | E: -42.336287 | E_var:     0.3179 | E_err:   0.008809
[2025-10-03 11:09:48] [Iter 1757/2250] R4[1262/2400] | LR: 0.016487 | E: -42.342441 | E_var:     0.2504 | E_err:   0.007818
[2025-10-03 11:09:59] [Iter 1758/2250] R4[1264/2400] | LR: 0.016454 | E: -42.351504 | E_var:     0.2435 | E_err:   0.007710
[2025-10-03 11:10:10] [Iter 1759/2250] R4[1266/2400] | LR: 0.016421 | E: -42.347303 | E_var:     0.6156 | E_err:   0.012260
[2025-10-03 11:10:20] [Iter 1760/2250] R4[1268/2400] | LR: 0.016389 | E: -42.353830 | E_var:     0.3159 | E_err:   0.008783
[2025-10-03 11:10:31] [Iter 1761/2250] R4[1270/2400] | LR: 0.016356 | E: -42.361595 | E_var:     0.2498 | E_err:   0.007809
[2025-10-03 11:10:41] [Iter 1762/2250] R4[1272/2400] | LR: 0.016324 | E: -42.366137 | E_var:     0.2208 | E_err:   0.007343
[2025-10-03 11:10:52] [Iter 1763/2250] R4[1274/2400] | LR: 0.016291 | E: -42.360815 | E_var:     0.2672 | E_err:   0.008077
[2025-10-03 11:11:02] [Iter 1764/2250] R4[1276/2400] | LR: 0.016259 | E: -42.340010 | E_var:     0.4661 | E_err:   0.010667
[2025-10-03 11:11:13] [Iter 1765/2250] R4[1278/2400] | LR: 0.016226 | E: -42.366949 | E_var:     0.2794 | E_err:   0.008260
[2025-10-03 11:11:23] [Iter 1766/2250] R4[1280/2400] | LR: 0.016193 | E: -42.358778 | E_var:     0.2670 | E_err:   0.008073
[2025-10-03 11:11:34] [Iter 1767/2250] R4[1282/2400] | LR: 0.016161 | E: -42.344083 | E_var:     0.3333 | E_err:   0.009020
[2025-10-03 11:11:44] [Iter 1768/2250] R4[1284/2400] | LR: 0.016128 | E: -42.370583 | E_var:     0.5105 | E_err:   0.011164
[2025-10-03 11:11:55] [Iter 1769/2250] R4[1286/2400] | LR: 0.016096 | E: -42.366857 | E_var:     0.4487 | E_err:   0.010466
[2025-10-03 11:12:05] [Iter 1770/2250] R4[1288/2400] | LR: 0.016063 | E: -42.351560 | E_var:     0.3177 | E_err:   0.008808
[2025-10-03 11:12:16] [Iter 1771/2250] R4[1290/2400] | LR: 0.016031 | E: -42.353479 | E_var:     0.2645 | E_err:   0.008036
[2025-10-03 11:12:26] [Iter 1772/2250] R4[1292/2400] | LR: 0.015998 | E: -42.348467 | E_var:     0.3250 | E_err:   0.008907
[2025-10-03 11:12:37] [Iter 1773/2250] R4[1294/2400] | LR: 0.015966 | E: -42.355137 | E_var:     0.2277 | E_err:   0.007455
[2025-10-03 11:12:47] [Iter 1774/2250] R4[1296/2400] | LR: 0.015933 | E: -42.342461 | E_var:     0.3835 | E_err:   0.009677
[2025-10-03 11:12:58] [Iter 1775/2250] R4[1298/2400] | LR: 0.015901 | E: -42.360101 | E_var:     0.2485 | E_err:   0.007789
[2025-10-03 11:13:09] [Iter 1776/2250] R4[1300/2400] | LR: 0.015868 | E: -42.353376 | E_var:     0.2812 | E_err:   0.008286
[2025-10-03 11:13:19] [Iter 1777/2250] R4[1302/2400] | LR: 0.015836 | E: -42.343345 | E_var:     0.2866 | E_err:   0.008365
[2025-10-03 11:13:30] [Iter 1778/2250] R4[1304/2400] | LR: 0.015804 | E: -42.345955 | E_var:     0.3463 | E_err:   0.009195
[2025-10-03 11:13:40] [Iter 1779/2250] R4[1306/2400] | LR: 0.015771 | E: -42.363707 | E_var:     0.2093 | E_err:   0.007148
[2025-10-03 11:13:51] [Iter 1780/2250] R4[1308/2400] | LR: 0.015739 | E: -42.360657 | E_var:     0.3149 | E_err:   0.008768
[2025-10-03 11:14:01] [Iter 1781/2250] R4[1310/2400] | LR: 0.015706 | E: -42.355959 | E_var:     0.3220 | E_err:   0.008866
[2025-10-03 11:14:12] [Iter 1782/2250] R4[1312/2400] | LR: 0.015674 | E: -42.349976 | E_var:     0.2476 | E_err:   0.007774
[2025-10-03 11:14:22] [Iter 1783/2250] R4[1314/2400] | LR: 0.015642 | E: -42.354039 | E_var:     0.2448 | E_err:   0.007730
[2025-10-03 11:14:33] [Iter 1784/2250] R4[1316/2400] | LR: 0.015609 | E: -42.362871 | E_var:     0.2825 | E_err:   0.008305
[2025-10-03 11:14:43] [Iter 1785/2250] R4[1318/2400] | LR: 0.015577 | E: -42.363355 | E_var:     0.2423 | E_err:   0.007691
[2025-10-03 11:14:54] [Iter 1786/2250] R4[1320/2400] | LR: 0.015545 | E: -42.364564 | E_var:     0.3158 | E_err:   0.008781
[2025-10-03 11:15:04] [Iter 1787/2250] R4[1322/2400] | LR: 0.015512 | E: -42.345013 | E_var:     0.2216 | E_err:   0.007356
[2025-10-03 11:15:15] [Iter 1788/2250] R4[1324/2400] | LR: 0.015480 | E: -42.357583 | E_var:     0.2195 | E_err:   0.007321
[2025-10-03 11:15:26] [Iter 1789/2250] R4[1326/2400] | LR: 0.015448 | E: -42.349666 | E_var:     0.2567 | E_err:   0.007916
[2025-10-03 11:15:36] [Iter 1790/2250] R4[1328/2400] | LR: 0.015415 | E: -42.356197 | E_var:     0.2791 | E_err:   0.008255
[2025-10-03 11:15:47] [Iter 1791/2250] R4[1330/2400] | LR: 0.015383 | E: -42.354372 | E_var:     0.2633 | E_err:   0.008018
[2025-10-03 11:15:57] [Iter 1792/2250] R4[1332/2400] | LR: 0.015351 | E: -42.341304 | E_var:     0.3360 | E_err:   0.009057
[2025-10-03 11:16:08] [Iter 1793/2250] R4[1334/2400] | LR: 0.015319 | E: -42.353846 | E_var:     0.4898 | E_err:   0.010935
[2025-10-03 11:16:18] [Iter 1794/2250] R4[1336/2400] | LR: 0.015286 | E: -42.353666 | E_var:     0.2650 | E_err:   0.008044
[2025-10-03 11:16:29] [Iter 1795/2250] R4[1338/2400] | LR: 0.015254 | E: -42.370278 | E_var:     0.3615 | E_err:   0.009394
[2025-10-03 11:16:39] [Iter 1796/2250] R4[1340/2400] | LR: 0.015222 | E: -42.354675 | E_var:     0.2863 | E_err:   0.008360
[2025-10-03 11:16:50] [Iter 1797/2250] R4[1342/2400] | LR: 0.015190 | E: -42.356584 | E_var:     0.2851 | E_err:   0.008343
[2025-10-03 11:17:00] [Iter 1798/2250] R4[1344/2400] | LR: 0.015158 | E: -42.345594 | E_var:     0.2653 | E_err:   0.008049
[2025-10-03 11:17:11] [Iter 1799/2250] R4[1346/2400] | LR: 0.015126 | E: -42.352721 | E_var:     0.2929 | E_err:   0.008456
[2025-10-03 11:17:21] [Iter 1800/2250] R4[1348/2400] | LR: 0.015093 | E: -42.370914 | E_var:     0.2884 | E_err:   0.008391
[2025-10-03 11:17:21] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-10-03 11:17:32] [Iter 1801/2250] R4[1350/2400] | LR: 0.015061 | E: -42.352892 | E_var:     0.2273 | E_err:   0.007449
[2025-10-03 11:17:42] [Iter 1802/2250] R4[1352/2400] | LR: 0.015029 | E: -42.337245 | E_var:     0.4625 | E_err:   0.010626
[2025-10-03 11:17:53] [Iter 1803/2250] R4[1354/2400] | LR: 0.014997 | E: -42.356232 | E_var:     0.2285 | E_err:   0.007469
[2025-10-03 11:18:04] [Iter 1804/2250] R4[1356/2400] | LR: 0.014965 | E: -42.354894 | E_var:     0.2740 | E_err:   0.008179
[2025-10-03 11:18:14] [Iter 1805/2250] R4[1358/2400] | LR: 0.014933 | E: -42.366184 | E_var:     0.2325 | E_err:   0.007534
[2025-10-03 11:18:25] [Iter 1806/2250] R4[1360/2400] | LR: 0.014901 | E: -42.346855 | E_var:     0.2498 | E_err:   0.007810
[2025-10-03 11:18:35] [Iter 1807/2250] R4[1362/2400] | LR: 0.014869 | E: -42.363625 | E_var:     0.2607 | E_err:   0.007978
[2025-10-03 11:18:46] [Iter 1808/2250] R4[1364/2400] | LR: 0.014837 | E: -42.348798 | E_var:     0.3905 | E_err:   0.009764
[2025-10-03 11:18:56] [Iter 1809/2250] R4[1366/2400] | LR: 0.014805 | E: -42.359440 | E_var:     0.2657 | E_err:   0.008055
[2025-10-03 11:19:07] [Iter 1810/2250] R4[1368/2400] | LR: 0.014773 | E: -42.364474 | E_var:     0.2683 | E_err:   0.008094
[2025-10-03 11:19:17] [Iter 1811/2250] R4[1370/2400] | LR: 0.014741 | E: -42.341648 | E_var:     0.4019 | E_err:   0.009905
[2025-10-03 11:19:28] [Iter 1812/2250] R4[1372/2400] | LR: 0.014709 | E: -42.340263 | E_var:     0.2594 | E_err:   0.007958
[2025-10-03 11:19:38] [Iter 1813/2250] R4[1374/2400] | LR: 0.014677 | E: -42.345963 | E_var:     0.2963 | E_err:   0.008505
[2025-10-03 11:19:49] [Iter 1814/2250] R4[1376/2400] | LR: 0.014646 | E: -42.355671 | E_var:     0.2874 | E_err:   0.008377
[2025-10-03 11:19:59] [Iter 1815/2250] R4[1378/2400] | LR: 0.014614 | E: -42.360788 | E_var:     0.2945 | E_err:   0.008480
[2025-10-03 11:20:10] [Iter 1816/2250] R4[1380/2400] | LR: 0.014582 | E: -42.364641 | E_var:     0.3961 | E_err:   0.009834
[2025-10-03 11:20:20] [Iter 1817/2250] R4[1382/2400] | LR: 0.014550 | E: -42.342369 | E_var:     0.2549 | E_err:   0.007888
[2025-10-03 11:20:31] [Iter 1818/2250] R4[1384/2400] | LR: 0.014518 | E: -42.347683 | E_var:     0.2592 | E_err:   0.007955
[2025-10-03 11:20:41] [Iter 1819/2250] R4[1386/2400] | LR: 0.014487 | E: -42.345235 | E_var:     0.2543 | E_err:   0.007880
[2025-10-03 11:20:52] [Iter 1820/2250] R4[1388/2400] | LR: 0.014455 | E: -42.362146 | E_var:     0.2630 | E_err:   0.008013
[2025-10-03 11:21:03] [Iter 1821/2250] R4[1390/2400] | LR: 0.014423 | E: -42.356668 | E_var:     0.2577 | E_err:   0.007931
[2025-10-03 11:21:13] [Iter 1822/2250] R4[1392/2400] | LR: 0.014391 | E: -42.355044 | E_var:     0.2204 | E_err:   0.007335
[2025-10-03 11:21:24] [Iter 1823/2250] R4[1394/2400] | LR: 0.014360 | E: -42.340516 | E_var:     0.3097 | E_err:   0.008695
[2025-10-03 11:21:34] [Iter 1824/2250] R4[1396/2400] | LR: 0.014328 | E: -42.356908 | E_var:     0.2329 | E_err:   0.007540
[2025-10-03 11:21:45] [Iter 1825/2250] R4[1398/2400] | LR: 0.014296 | E: -42.354339 | E_var:     0.2781 | E_err:   0.008240
[2025-10-03 11:21:55] [Iter 1826/2250] R4[1400/2400] | LR: 0.014265 | E: -42.351059 | E_var:     0.2654 | E_err:   0.008050
[2025-10-03 11:22:06] [Iter 1827/2250] R4[1402/2400] | LR: 0.014233 | E: -42.350144 | E_var:     0.2810 | E_err:   0.008283
[2025-10-03 11:22:16] [Iter 1828/2250] R4[1404/2400] | LR: 0.014202 | E: -42.350394 | E_var:     0.3292 | E_err:   0.008965
[2025-10-03 11:22:27] [Iter 1829/2250] R4[1406/2400] | LR: 0.014170 | E: -42.356057 | E_var:     0.3032 | E_err:   0.008603
[2025-10-03 11:22:37] [Iter 1830/2250] R4[1408/2400] | LR: 0.014139 | E: -42.351456 | E_var:     0.2211 | E_err:   0.007347
[2025-10-03 11:22:48] [Iter 1831/2250] R4[1410/2400] | LR: 0.014107 | E: -42.341759 | E_var:     0.2497 | E_err:   0.007808
[2025-10-03 11:22:58] [Iter 1832/2250] R4[1412/2400] | LR: 0.014076 | E: -42.373914 | E_var:     0.7065 | E_err:   0.013133
[2025-10-03 11:23:09] [Iter 1833/2250] R4[1414/2400] | LR: 0.014044 | E: -42.355977 | E_var:     0.2781 | E_err:   0.008240
[2025-10-03 11:23:19] [Iter 1834/2250] R4[1416/2400] | LR: 0.014013 | E: -42.357959 | E_var:     0.2231 | E_err:   0.007380
[2025-10-03 11:23:30] [Iter 1835/2250] R4[1418/2400] | LR: 0.013981 | E: -42.357167 | E_var:     0.2024 | E_err:   0.007030
[2025-10-03 11:23:41] [Iter 1836/2250] R4[1420/2400] | LR: 0.013950 | E: -42.362567 | E_var:     0.2705 | E_err:   0.008126
[2025-10-03 11:23:51] [Iter 1837/2250] R4[1422/2400] | LR: 0.013918 | E: -42.348456 | E_var:     0.3290 | E_err:   0.008962
[2025-10-03 11:24:02] [Iter 1838/2250] R4[1424/2400] | LR: 0.013887 | E: -42.351753 | E_var:     0.3984 | E_err:   0.009862
[2025-10-03 11:24:12] [Iter 1839/2250] R4[1426/2400] | LR: 0.013856 | E: -42.344970 | E_var:     0.2970 | E_err:   0.008515
[2025-10-03 11:24:23] [Iter 1840/2250] R4[1428/2400] | LR: 0.013824 | E: -42.358891 | E_var:     0.2426 | E_err:   0.007696
[2025-10-03 11:24:33] [Iter 1841/2250] R4[1430/2400] | LR: 0.013793 | E: -42.363291 | E_var:     0.3109 | E_err:   0.008712
[2025-10-03 11:24:44] [Iter 1842/2250] R4[1432/2400] | LR: 0.013762 | E: -42.354683 | E_var:     0.3085 | E_err:   0.008679
[2025-10-03 11:24:54] [Iter 1843/2250] R4[1434/2400] | LR: 0.013731 | E: -42.360678 | E_var:     0.2661 | E_err:   0.008060
[2025-10-03 11:25:05] [Iter 1844/2250] R4[1436/2400] | LR: 0.013700 | E: -42.352829 | E_var:     0.2945 | E_err:   0.008479
[2025-10-03 11:25:15] [Iter 1845/2250] R4[1438/2400] | LR: 0.013668 | E: -42.354443 | E_var:     0.4025 | E_err:   0.009913
[2025-10-03 11:25:26] [Iter 1846/2250] R4[1440/2400] | LR: 0.013637 | E: -42.363774 | E_var:     0.2286 | E_err:   0.007471
[2025-10-03 11:25:36] [Iter 1847/2250] R4[1442/2400] | LR: 0.013606 | E: -42.342737 | E_var:     0.3223 | E_err:   0.008870
[2025-10-03 11:25:47] [Iter 1848/2250] R4[1444/2400] | LR: 0.013575 | E: -42.349264 | E_var:     0.3002 | E_err:   0.008561
[2025-10-03 11:25:57] [Iter 1849/2250] R4[1446/2400] | LR: 0.013544 | E: -42.343233 | E_var:     0.3900 | E_err:   0.009758
[2025-10-03 11:26:08] [Iter 1850/2250] R4[1448/2400] | LR: 0.013513 | E: -42.352444 | E_var:     0.2440 | E_err:   0.007717
[2025-10-03 11:26:18] [Iter 1851/2250] R4[1450/2400] | LR: 0.013482 | E: -42.359235 | E_var:     0.2392 | E_err:   0.007642
[2025-10-03 11:26:29] [Iter 1852/2250] R4[1452/2400] | LR: 0.013451 | E: -42.364620 | E_var:     0.3304 | E_err:   0.008981
[2025-10-03 11:26:40] [Iter 1853/2250] R4[1454/2400] | LR: 0.013420 | E: -42.354069 | E_var:     0.2319 | E_err:   0.007524
[2025-10-03 11:26:50] [Iter 1854/2250] R4[1456/2400] | LR: 0.013389 | E: -42.364629 | E_var:     0.2518 | E_err:   0.007840
[2025-10-03 11:27:01] [Iter 1855/2250] R4[1458/2400] | LR: 0.013358 | E: -42.373659 | E_var:     0.9114 | E_err:   0.014917
[2025-10-03 11:27:11] [Iter 1856/2250] R4[1460/2400] | LR: 0.013327 | E: -42.357346 | E_var:     0.2892 | E_err:   0.008402
[2025-10-03 11:27:22] [Iter 1857/2250] R4[1462/2400] | LR: 0.013297 | E: -42.358734 | E_var:     0.2217 | E_err:   0.007356
[2025-10-03 11:27:32] [Iter 1858/2250] R4[1464/2400] | LR: 0.013266 | E: -42.364284 | E_var:     0.2990 | E_err:   0.008544
[2025-10-03 11:27:43] [Iter 1859/2250] R4[1466/2400] | LR: 0.013235 | E: -42.350521 | E_var:     0.2274 | E_err:   0.007451
[2025-10-03 11:27:53] [Iter 1860/2250] R4[1468/2400] | LR: 0.013204 | E: -42.373225 | E_var:     0.4455 | E_err:   0.010429
[2025-10-03 11:28:04] [Iter 1861/2250] R4[1470/2400] | LR: 0.013174 | E: -42.359832 | E_var:     0.2248 | E_err:   0.007408
[2025-10-03 11:28:14] [Iter 1862/2250] R4[1472/2400] | LR: 0.013143 | E: -42.365751 | E_var:     0.2176 | E_err:   0.007289
[2025-10-03 11:28:25] [Iter 1863/2250] R4[1474/2400] | LR: 0.013112 | E: -42.363441 | E_var:     0.2279 | E_err:   0.007460
[2025-10-03 11:28:35] [Iter 1864/2250] R4[1476/2400] | LR: 0.013082 | E: -42.350135 | E_var:     0.2797 | E_err:   0.008264
[2025-10-03 11:28:46] [Iter 1865/2250] R4[1478/2400] | LR: 0.013051 | E: -42.361019 | E_var:     0.2362 | E_err:   0.007594
[2025-10-03 11:28:56] [Iter 1866/2250] R4[1480/2400] | LR: 0.013020 | E: -42.349142 | E_var:     0.2962 | E_err:   0.008503
[2025-10-03 11:29:07] [Iter 1867/2250] R4[1482/2400] | LR: 0.012990 | E: -42.351417 | E_var:     0.2571 | E_err:   0.007923
[2025-10-03 11:29:17] [Iter 1868/2250] R4[1484/2400] | LR: 0.012959 | E: -42.365404 | E_var:     0.2638 | E_err:   0.008025
[2025-10-03 11:29:28] [Iter 1869/2250] R4[1486/2400] | LR: 0.012929 | E: -42.347873 | E_var:     0.2717 | E_err:   0.008144
[2025-10-03 11:29:38] [Iter 1870/2250] R4[1488/2400] | LR: 0.012898 | E: -42.351413 | E_var:     0.2706 | E_err:   0.008128
[2025-10-03 11:29:49] [Iter 1871/2250] R4[1490/2400] | LR: 0.012868 | E: -42.351550 | E_var:     0.3345 | E_err:   0.009036
[2025-10-03 11:29:59] [Iter 1872/2250] R4[1492/2400] | LR: 0.012838 | E: -42.342037 | E_var:     0.2332 | E_err:   0.007545
[2025-10-03 11:30:10] [Iter 1873/2250] R4[1494/2400] | LR: 0.012807 | E: -42.365501 | E_var:     0.2754 | E_err:   0.008199
[2025-10-03 11:30:21] [Iter 1874/2250] R4[1496/2400] | LR: 0.012777 | E: -42.358836 | E_var:     0.2232 | E_err:   0.007383
[2025-10-03 11:30:31] [Iter 1875/2250] R4[1498/2400] | LR: 0.012747 | E: -42.363608 | E_var:     0.2272 | E_err:   0.007447
[2025-10-03 11:30:42] [Iter 1876/2250] R4[1500/2400] | LR: 0.012716 | E: -42.361312 | E_var:     0.2849 | E_err:   0.008340
[2025-10-03 11:30:52] [Iter 1877/2250] R4[1502/2400] | LR: 0.012686 | E: -42.354163 | E_var:     0.2968 | E_err:   0.008512
[2025-10-03 11:31:03] [Iter 1878/2250] R4[1504/2400] | LR: 0.012656 | E: -42.346470 | E_var:     0.2185 | E_err:   0.007304
[2025-10-03 11:31:13] [Iter 1879/2250] R4[1506/2400] | LR: 0.012626 | E: -42.354677 | E_var:     0.4456 | E_err:   0.010430
[2025-10-03 11:31:24] [Iter 1880/2250] R4[1508/2400] | LR: 0.012596 | E: -42.361845 | E_var:     0.2351 | E_err:   0.007575
[2025-10-03 11:31:34] [Iter 1881/2250] R4[1510/2400] | LR: 0.012566 | E: -42.347230 | E_var:     0.2730 | E_err:   0.008163
[2025-10-03 11:31:45] [Iter 1882/2250] R4[1512/2400] | LR: 0.012536 | E: -42.359122 | E_var:     0.3311 | E_err:   0.008991
[2025-10-03 11:31:55] [Iter 1883/2250] R4[1514/2400] | LR: 0.012506 | E: -42.358830 | E_var:     0.2548 | E_err:   0.007888
[2025-10-03 11:32:06] [Iter 1884/2250] R4[1516/2400] | LR: 0.012476 | E: -42.375830 | E_var:     0.4583 | E_err:   0.010578
[2025-10-03 11:32:16] [Iter 1885/2250] R4[1518/2400] | LR: 0.012446 | E: -42.369938 | E_var:     0.2809 | E_err:   0.008282
[2025-10-03 11:32:27] [Iter 1886/2250] R4[1520/2400] | LR: 0.012416 | E: -42.347365 | E_var:     0.3270 | E_err:   0.008936
[2025-10-03 11:32:37] [Iter 1887/2250] R4[1522/2400] | LR: 0.012386 | E: -42.354408 | E_var:     0.2737 | E_err:   0.008175
[2025-10-03 11:32:48] [Iter 1888/2250] R4[1524/2400] | LR: 0.012356 | E: -42.362210 | E_var:     0.3772 | E_err:   0.009596
[2025-10-03 11:32:58] [Iter 1889/2250] R4[1526/2400] | LR: 0.012326 | E: -42.362123 | E_var:     0.2162 | E_err:   0.007265
[2025-10-03 11:33:09] [Iter 1890/2250] R4[1528/2400] | LR: 0.012296 | E: -42.355306 | E_var:     0.2255 | E_err:   0.007420
[2025-10-03 11:33:19] [Iter 1891/2250] R4[1530/2400] | LR: 0.012267 | E: -42.368339 | E_var:     0.3003 | E_err:   0.008563
[2025-10-03 11:33:30] [Iter 1892/2250] R4[1532/2400] | LR: 0.012237 | E: -42.350720 | E_var:     0.4928 | E_err:   0.010968
[2025-10-03 11:33:40] [Iter 1893/2250] R4[1534/2400] | LR: 0.012207 | E: -42.355604 | E_var:     0.2516 | E_err:   0.007838
[2025-10-03 11:33:51] [Iter 1894/2250] R4[1536/2400] | LR: 0.012178 | E: -42.370966 | E_var:     0.4519 | E_err:   0.010503
[2025-10-03 11:34:01] [Iter 1895/2250] R4[1538/2400] | LR: 0.012148 | E: -42.361590 | E_var:     0.3779 | E_err:   0.009606
[2025-10-03 11:34:12] [Iter 1896/2250] R4[1540/2400] | LR: 0.012119 | E: -42.363365 | E_var:     0.4437 | E_err:   0.010408
[2025-10-03 11:34:22] [Iter 1897/2250] R4[1542/2400] | LR: 0.012089 | E: -42.354713 | E_var:     0.2623 | E_err:   0.008002
[2025-10-03 11:34:33] [Iter 1898/2250] R4[1544/2400] | LR: 0.012060 | E: -42.339732 | E_var:     0.2607 | E_err:   0.007978
[2025-10-03 11:34:44] [Iter 1899/2250] R4[1546/2400] | LR: 0.012030 | E: -42.345936 | E_var:     0.2718 | E_err:   0.008145
[2025-10-03 11:34:54] [Iter 1900/2250] R4[1548/2400] | LR: 0.012001 | E: -42.362981 | E_var:     0.2695 | E_err:   0.008112
[2025-10-03 11:35:05] [Iter 1901/2250] R4[1550/2400] | LR: 0.011971 | E: -42.357249 | E_var:     0.2460 | E_err:   0.007750
[2025-10-03 11:35:15] [Iter 1902/2250] R4[1552/2400] | LR: 0.011942 | E: -42.353018 | E_var:     0.2723 | E_err:   0.008153
[2025-10-03 11:35:26] [Iter 1903/2250] R4[1554/2400] | LR: 0.011913 | E: -42.362938 | E_var:     0.2765 | E_err:   0.008216
[2025-10-03 11:35:36] [Iter 1904/2250] R4[1556/2400] | LR: 0.011884 | E: -42.352942 | E_var:     0.2262 | E_err:   0.007432
[2025-10-03 11:35:47] [Iter 1905/2250] R4[1558/2400] | LR: 0.011854 | E: -42.361997 | E_var:     0.2853 | E_err:   0.008345
[2025-10-03 11:35:57] [Iter 1906/2250] R4[1560/2400] | LR: 0.011825 | E: -42.361583 | E_var:     0.3044 | E_err:   0.008621
[2025-10-03 11:36:08] [Iter 1907/2250] R4[1562/2400] | LR: 0.011796 | E: -42.341203 | E_var:     0.2357 | E_err:   0.007586
[2025-10-03 11:36:18] [Iter 1908/2250] R4[1564/2400] | LR: 0.011767 | E: -42.355155 | E_var:     0.2761 | E_err:   0.008209
[2025-10-03 11:36:29] [Iter 1909/2250] R4[1566/2400] | LR: 0.011738 | E: -42.371184 | E_var:     0.2820 | E_err:   0.008298
[2025-10-03 11:36:39] [Iter 1910/2250] R4[1568/2400] | LR: 0.011709 | E: -42.346682 | E_var:     0.2416 | E_err:   0.007680
[2025-10-03 11:36:50] [Iter 1911/2250] R4[1570/2400] | LR: 0.011680 | E: -42.345561 | E_var:     0.3419 | E_err:   0.009136
[2025-10-03 11:37:00] [Iter 1912/2250] R4[1572/2400] | LR: 0.011651 | E: -42.364534 | E_var:     0.3322 | E_err:   0.009006
[2025-10-03 11:37:11] [Iter 1913/2250] R4[1574/2400] | LR: 0.011622 | E: -42.355140 | E_var:     0.2377 | E_err:   0.007619
[2025-10-03 11:37:21] [Iter 1914/2250] R4[1576/2400] | LR: 0.011593 | E: -42.351649 | E_var:     0.3495 | E_err:   0.009237
[2025-10-03 11:37:32] [Iter 1915/2250] R4[1578/2400] | LR: 0.011564 | E: -42.349020 | E_var:     0.2704 | E_err:   0.008126
[2025-10-03 11:37:42] [Iter 1916/2250] R4[1580/2400] | LR: 0.011536 | E: -42.359207 | E_var:     0.2438 | E_err:   0.007715
[2025-10-03 11:37:53] [Iter 1917/2250] R4[1582/2400] | LR: 0.011507 | E: -42.339861 | E_var:     0.2811 | E_err:   0.008285
[2025-10-03 11:38:03] [Iter 1918/2250] R4[1584/2400] | LR: 0.011478 | E: -42.355203 | E_var:     0.2621 | E_err:   0.008000
[2025-10-03 11:38:14] [Iter 1919/2250] R4[1586/2400] | LR: 0.011449 | E: -42.359227 | E_var:     0.2025 | E_err:   0.007031
[2025-10-03 11:38:25] [Iter 1920/2250] R4[1588/2400] | LR: 0.011421 | E: -42.354835 | E_var:     0.2859 | E_err:   0.008355
[2025-10-03 11:38:35] [Iter 1921/2250] R4[1590/2400] | LR: 0.011392 | E: -42.361037 | E_var:     0.4169 | E_err:   0.010089
[2025-10-03 11:38:46] [Iter 1922/2250] R4[1592/2400] | LR: 0.011364 | E: -42.356225 | E_var:     0.3292 | E_err:   0.008965
[2025-10-03 11:38:56] [Iter 1923/2250] R4[1594/2400] | LR: 0.011335 | E: -42.343289 | E_var:     0.5332 | E_err:   0.011410
[2025-10-03 11:39:07] [Iter 1924/2250] R4[1596/2400] | LR: 0.011307 | E: -42.363243 | E_var:     0.3262 | E_err:   0.008924
[2025-10-03 11:39:17] [Iter 1925/2250] R4[1598/2400] | LR: 0.011278 | E: -42.353811 | E_var:     0.2429 | E_err:   0.007700
[2025-10-03 11:39:28] [Iter 1926/2250] R4[1600/2400] | LR: 0.011250 | E: -42.349357 | E_var:     0.2277 | E_err:   0.007456
[2025-10-03 11:39:38] [Iter 1927/2250] R4[1602/2400] | LR: 0.011222 | E: -42.359331 | E_var:     0.2650 | E_err:   0.008044
[2025-10-03 11:39:49] [Iter 1928/2250] R4[1604/2400] | LR: 0.011193 | E: -42.356904 | E_var:     0.2396 | E_err:   0.007648
[2025-10-03 11:39:59] [Iter 1929/2250] R4[1606/2400] | LR: 0.011165 | E: -42.352040 | E_var:     0.2547 | E_err:   0.007886
[2025-10-03 11:40:10] [Iter 1930/2250] R4[1608/2400] | LR: 0.011137 | E: -42.349144 | E_var:     0.2584 | E_err:   0.007943
[2025-10-03 11:40:20] [Iter 1931/2250] R4[1610/2400] | LR: 0.011109 | E: -42.350049 | E_var:     0.2588 | E_err:   0.007948
[2025-10-03 11:40:31] [Iter 1932/2250] R4[1612/2400] | LR: 0.011081 | E: -42.359746 | E_var:     0.2024 | E_err:   0.007030
[2025-10-03 11:40:41] [Iter 1933/2250] R4[1614/2400] | LR: 0.011053 | E: -42.146991 | E_var:     4.3445 | E_err:   0.032568
[2025-10-03 11:40:52] [Iter 1934/2250] R4[1616/2400] | LR: 0.011025 | E: -42.274072 | E_var:     1.2454 | E_err:   0.017437
[2025-10-03 11:41:02] [Iter 1935/2250] R4[1618/2400] | LR: 0.010997 | E: -42.292821 | E_var:     0.8614 | E_err:   0.014502
[2025-10-03 11:41:13] [Iter 1936/2250] R4[1620/2400] | LR: 0.010969 | E: -42.324574 | E_var:     0.4081 | E_err:   0.009982
[2025-10-03 11:41:23] [Iter 1937/2250] R4[1622/2400] | LR: 0.010941 | E: -42.348936 | E_var:     0.3363 | E_err:   0.009061
[2025-10-03 11:41:34] [Iter 1938/2250] R4[1624/2400] | LR: 0.010913 | E: -42.345661 | E_var:     0.3719 | E_err:   0.009529
[2025-10-03 11:41:44] [Iter 1939/2250] R4[1626/2400] | LR: 0.010885 | E: -42.337743 | E_var:     0.2911 | E_err:   0.008431
[2025-10-03 11:41:55] [Iter 1940/2250] R4[1628/2400] | LR: 0.010858 | E: -42.348381 | E_var:     0.2535 | E_err:   0.007866
[2025-10-03 11:42:06] [Iter 1941/2250] R4[1630/2400] | LR: 0.010830 | E: -42.360206 | E_var:     0.2880 | E_err:   0.008385
[2025-10-03 11:42:16] [Iter 1942/2250] R4[1632/2400] | LR: 0.010802 | E: -42.341462 | E_var:     0.4216 | E_err:   0.010145
[2025-10-03 11:42:27] [Iter 1943/2250] R4[1634/2400] | LR: 0.010775 | E: -42.354714 | E_var:     0.2711 | E_err:   0.008136
[2025-10-03 11:42:37] [Iter 1944/2250] R4[1636/2400] | LR: 0.010747 | E: -42.367058 | E_var:     0.2901 | E_err:   0.008416
[2025-10-03 11:42:48] [Iter 1945/2250] R4[1638/2400] | LR: 0.010719 | E: -42.351508 | E_var:     0.2720 | E_err:   0.008149
[2025-10-03 11:42:58] [Iter 1946/2250] R4[1640/2400] | LR: 0.010692 | E: -42.347916 | E_var:     0.2741 | E_err:   0.008180
[2025-10-03 11:43:09] [Iter 1947/2250] R4[1642/2400] | LR: 0.010665 | E: -42.363869 | E_var:     0.2375 | E_err:   0.007615
[2025-10-03 11:43:19] [Iter 1948/2250] R4[1644/2400] | LR: 0.010637 | E: -42.373248 | E_var:     0.3198 | E_err:   0.008836
[2025-10-03 11:43:30] [Iter 1949/2250] R4[1646/2400] | LR: 0.010610 | E: -42.365829 | E_var:     0.2508 | E_err:   0.007825
[2025-10-03 11:43:40] [Iter 1950/2250] R4[1648/2400] | LR: 0.010583 | E: -42.349462 | E_var:     0.3094 | E_err:   0.008691
[2025-10-03 11:43:51] [Iter 1951/2250] R4[1650/2400] | LR: 0.010555 | E: -42.356503 | E_var:     0.2830 | E_err:   0.008312
[2025-10-03 11:44:01] [Iter 1952/2250] R4[1652/2400] | LR: 0.010528 | E: -42.344047 | E_var:     0.2297 | E_err:   0.007489
[2025-10-03 11:44:12] [Iter 1953/2250] R4[1654/2400] | LR: 0.010501 | E: -42.354656 | E_var:     0.2626 | E_err:   0.008007
[2025-10-03 11:44:22] [Iter 1954/2250] R4[1656/2400] | LR: 0.010474 | E: -42.366753 | E_var:     0.3256 | E_err:   0.008916
[2025-10-03 11:44:33] [Iter 1955/2250] R4[1658/2400] | LR: 0.010447 | E: -42.348934 | E_var:     0.2594 | E_err:   0.007957
[2025-10-03 11:44:43] [Iter 1956/2250] R4[1660/2400] | LR: 0.010420 | E: -42.370017 | E_var:     0.4063 | E_err:   0.009960
[2025-10-03 11:44:54] [Iter 1957/2250] R4[1662/2400] | LR: 0.010393 | E: -42.364303 | E_var:     0.3543 | E_err:   0.009300
[2025-10-03 11:45:04] [Iter 1958/2250] R4[1664/2400] | LR: 0.010366 | E: -42.362424 | E_var:     0.2609 | E_err:   0.007982
[2025-10-03 11:45:15] [Iter 1959/2250] R4[1666/2400] | LR: 0.010339 | E: -42.357710 | E_var:     0.2012 | E_err:   0.007008
[2025-10-03 11:45:25] [Iter 1960/2250] R4[1668/2400] | LR: 0.010312 | E: -42.349002 | E_var:     0.2278 | E_err:   0.007458
[2025-10-03 11:45:36] [Iter 1961/2250] R4[1670/2400] | LR: 0.010286 | E: -42.355448 | E_var:     0.2764 | E_err:   0.008215
[2025-10-03 11:45:46] [Iter 1962/2250] R4[1672/2400] | LR: 0.010259 | E: -42.356215 | E_var:     0.2565 | E_err:   0.007914
[2025-10-03 11:45:57] [Iter 1963/2250] R4[1674/2400] | LR: 0.010232 | E: -42.362997 | E_var:     0.2612 | E_err:   0.007986
[2025-10-03 11:46:08] [Iter 1964/2250] R4[1676/2400] | LR: 0.010206 | E: -42.356513 | E_var:     0.3784 | E_err:   0.009612
[2025-10-03 11:46:18] [Iter 1965/2250] R4[1678/2400] | LR: 0.010179 | E: -42.364373 | E_var:     0.2356 | E_err:   0.007585
[2025-10-03 11:46:29] [Iter 1966/2250] R4[1680/2400] | LR: 0.010153 | E: -42.351147 | E_var:     0.2631 | E_err:   0.008015
[2025-10-03 11:46:39] [Iter 1967/2250] R4[1682/2400] | LR: 0.010126 | E: -42.352843 | E_var:     0.2411 | E_err:   0.007672
[2025-10-03 11:46:50] [Iter 1968/2250] R4[1684/2400] | LR: 0.010100 | E: -42.361215 | E_var:     0.3423 | E_err:   0.009142
[2025-10-03 11:47:00] [Iter 1969/2250] R4[1686/2400] | LR: 0.010073 | E: -42.352738 | E_var:     0.2580 | E_err:   0.007937
[2025-10-03 11:47:11] [Iter 1970/2250] R4[1688/2400] | LR: 0.010047 | E: -42.350364 | E_var:     0.3154 | E_err:   0.008775
[2025-10-03 11:47:21] [Iter 1971/2250] R4[1690/2400] | LR: 0.010021 | E: -42.354462 | E_var:     0.6098 | E_err:   0.012201
[2025-10-03 11:47:32] [Iter 1972/2250] R4[1692/2400] | LR: 0.009995 | E: -42.352789 | E_var:     0.2613 | E_err:   0.007988
[2025-10-03 11:47:42] [Iter 1973/2250] R4[1694/2400] | LR: 0.009969 | E: -42.354021 | E_var:     0.2638 | E_err:   0.008025
[2025-10-03 11:47:53] [Iter 1974/2250] R4[1696/2400] | LR: 0.009943 | E: -42.369196 | E_var:     0.2576 | E_err:   0.007931
[2025-10-03 11:48:03] [Iter 1975/2250] R4[1698/2400] | LR: 0.009916 | E: -42.355833 | E_var:     0.2833 | E_err:   0.008316
[2025-10-03 11:48:14] [Iter 1976/2250] R4[1700/2400] | LR: 0.009890 | E: -42.348406 | E_var:     0.4791 | E_err:   0.010815
[2025-10-03 11:48:25] [Iter 1977/2250] R4[1702/2400] | LR: 0.009865 | E: -42.355401 | E_var:     0.2408 | E_err:   0.007668
[2025-10-03 11:48:35] [Iter 1978/2250] R4[1704/2400] | LR: 0.009839 | E: -42.354021 | E_var:     0.3741 | E_err:   0.009557
[2025-10-03 11:48:46] [Iter 1979/2250] R4[1706/2400] | LR: 0.009813 | E: -42.359752 | E_var:     0.3165 | E_err:   0.008791
[2025-10-03 11:48:56] [Iter 1980/2250] R4[1708/2400] | LR: 0.009787 | E: -42.353923 | E_var:     0.2704 | E_err:   0.008124
[2025-10-03 11:49:07] [Iter 1981/2250] R4[1710/2400] | LR: 0.009761 | E: -42.345792 | E_var:     0.3016 | E_err:   0.008580
[2025-10-03 11:49:17] [Iter 1982/2250] R4[1712/2400] | LR: 0.009736 | E: -42.352550 | E_var:     0.2088 | E_err:   0.007139
[2025-10-03 11:49:28] [Iter 1983/2250] R4[1714/2400] | LR: 0.009710 | E: -42.353687 | E_var:     0.2653 | E_err:   0.008048
[2025-10-03 11:49:38] [Iter 1984/2250] R4[1716/2400] | LR: 0.009684 | E: -42.348404 | E_var:     0.2405 | E_err:   0.007662
[2025-10-03 11:49:49] [Iter 1985/2250] R4[1718/2400] | LR: 0.009659 | E: -42.349974 | E_var:     0.2246 | E_err:   0.007405
[2025-10-03 11:49:59] [Iter 1986/2250] R4[1720/2400] | LR: 0.009633 | E: -42.358066 | E_var:     0.2361 | E_err:   0.007593
[2025-10-03 11:50:10] [Iter 1987/2250] R4[1722/2400] | LR: 0.009608 | E: -42.357958 | E_var:     0.2817 | E_err:   0.008293
[2025-10-03 11:50:20] [Iter 1988/2250] R4[1724/2400] | LR: 0.009583 | E: -42.364543 | E_var:     0.2433 | E_err:   0.007708
[2025-10-03 11:50:31] [Iter 1989/2250] R4[1726/2400] | LR: 0.009557 | E: -42.345587 | E_var:     0.1982 | E_err:   0.006956
[2025-10-03 11:50:41] [Iter 1990/2250] R4[1728/2400] | LR: 0.009532 | E: -42.355076 | E_var:     0.2827 | E_err:   0.008308
[2025-10-03 11:50:52] [Iter 1991/2250] R4[1730/2400] | LR: 0.009507 | E: -42.371225 | E_var:     0.3459 | E_err:   0.009190
[2025-10-03 11:51:03] [Iter 1992/2250] R4[1732/2400] | LR: 0.009482 | E: -42.342923 | E_var:     0.3375 | E_err:   0.009077
[2025-10-03 11:51:13] [Iter 1993/2250] R4[1734/2400] | LR: 0.009457 | E: -42.344858 | E_var:     0.8637 | E_err:   0.014521
[2025-10-03 11:51:24] [Iter 1994/2250] R4[1736/2400] | LR: 0.009432 | E: -42.360822 | E_var:     0.2809 | E_err:   0.008282
[2025-10-03 11:51:34] [Iter 1995/2250] R4[1738/2400] | LR: 0.009407 | E: -42.356196 | E_var:     0.2389 | E_err:   0.007638
[2025-10-03 11:51:45] [Iter 1996/2250] R4[1740/2400] | LR: 0.009382 | E: -42.359906 | E_var:     0.3330 | E_err:   0.009017
[2025-10-03 11:51:55] [Iter 1997/2250] R4[1742/2400] | LR: 0.009357 | E: -42.345894 | E_var:     0.2389 | E_err:   0.007637
[2025-10-03 11:52:06] [Iter 1998/2250] R4[1744/2400] | LR: 0.009332 | E: -42.356461 | E_var:     0.2551 | E_err:   0.007891
[2025-10-03 11:52:16] [Iter 1999/2250] R4[1746/2400] | LR: 0.009307 | E: -42.369465 | E_var:     0.2586 | E_err:   0.007946
[2025-10-03 11:52:27] [Iter 2000/2250] R4[1748/2400] | LR: 0.009283 | E: -42.374140 | E_var:     0.2578 | E_err:   0.007933
[2025-10-03 11:52:27] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-10-03 11:52:37] [Iter 2001/2250] R4[1750/2400] | LR: 0.009258 | E: -42.353646 | E_var:     0.2376 | E_err:   0.007616
[2025-10-03 11:52:48] [Iter 2002/2250] R4[1752/2400] | LR: 0.009234 | E: -42.359451 | E_var:     0.4201 | E_err:   0.010127
[2025-10-03 11:52:58] [Iter 2003/2250] R4[1754/2400] | LR: 0.009209 | E: -42.359057 | E_var:     0.2374 | E_err:   0.007612
[2025-10-03 11:53:09] [Iter 2004/2250] R4[1756/2400] | LR: 0.009185 | E: -42.356639 | E_var:     0.5038 | E_err:   0.011091
[2025-10-03 11:53:20] [Iter 2005/2250] R4[1758/2400] | LR: 0.009160 | E: -42.355128 | E_var:     0.2427 | E_err:   0.007698
[2025-10-03 11:53:30] [Iter 2006/2250] R4[1760/2400] | LR: 0.009136 | E: -42.347256 | E_var:     0.2854 | E_err:   0.008348
[2025-10-03 11:53:41] [Iter 2007/2250] R4[1762/2400] | LR: 0.009112 | E: -42.356620 | E_var:     0.2076 | E_err:   0.007119
[2025-10-03 11:53:51] [Iter 2008/2250] R4[1764/2400] | LR: 0.009087 | E: -42.345049 | E_var:     0.2271 | E_err:   0.007446
[2025-10-03 11:54:02] [Iter 2009/2250] R4[1766/2400] | LR: 0.009063 | E: -42.359008 | E_var:     0.3084 | E_err:   0.008678
[2025-10-03 11:54:12] [Iter 2010/2250] R4[1768/2400] | LR: 0.009039 | E: -42.368483 | E_var:     0.4851 | E_err:   0.010883
[2025-10-03 11:54:23] [Iter 2011/2250] R4[1770/2400] | LR: 0.009015 | E: -42.349794 | E_var:     0.3074 | E_err:   0.008664
[2025-10-03 11:54:33] [Iter 2012/2250] R4[1772/2400] | LR: 0.008991 | E: -42.369499 | E_var:     0.3042 | E_err:   0.008618
[2025-10-03 11:54:44] [Iter 2013/2250] R4[1774/2400] | LR: 0.008967 | E: -42.361875 | E_var:     0.2951 | E_err:   0.008488
[2025-10-03 11:54:54] [Iter 2014/2250] R4[1776/2400] | LR: 0.008943 | E: -42.355282 | E_var:     0.3027 | E_err:   0.008597
[2025-10-03 11:55:05] [Iter 2015/2250] R4[1778/2400] | LR: 0.008919 | E: -42.352254 | E_var:     0.2577 | E_err:   0.007931
[2025-10-03 11:55:15] [Iter 2016/2250] R4[1780/2400] | LR: 0.008896 | E: -42.357261 | E_var:     0.3275 | E_err:   0.008941
[2025-10-03 11:55:26] [Iter 2017/2250] R4[1782/2400] | LR: 0.008872 | E: -42.342253 | E_var:     0.2995 | E_err:   0.008551
[2025-10-03 11:55:36] [Iter 2018/2250] R4[1784/2400] | LR: 0.008848 | E: -42.355710 | E_var:     0.2704 | E_err:   0.008126
[2025-10-03 11:55:47] [Iter 2019/2250] R4[1786/2400] | LR: 0.008825 | E: -42.355874 | E_var:     0.3139 | E_err:   0.008755
[2025-10-03 11:55:57] [Iter 2020/2250] R4[1788/2400] | LR: 0.008801 | E: -42.345390 | E_var:     0.2371 | E_err:   0.007608
[2025-10-03 11:56:08] [Iter 2021/2250] R4[1790/2400] | LR: 0.008778 | E: -42.362236 | E_var:     0.2602 | E_err:   0.007970
[2025-10-03 11:56:19] [Iter 2022/2250] R4[1792/2400] | LR: 0.008754 | E: -42.356344 | E_var:     0.2660 | E_err:   0.008059
[2025-10-03 11:56:29] [Iter 2023/2250] R4[1794/2400] | LR: 0.008731 | E: -42.354587 | E_var:     0.2295 | E_err:   0.007485
[2025-10-03 11:56:40] [Iter 2024/2250] R4[1796/2400] | LR: 0.008708 | E: -42.353369 | E_var:     0.2157 | E_err:   0.007257
[2025-10-03 11:56:50] [Iter 2025/2250] R4[1798/2400] | LR: 0.008684 | E: -42.355923 | E_var:     0.2526 | E_err:   0.007853
[2025-10-03 11:57:01] [Iter 2026/2250] R4[1800/2400] | LR: 0.008661 | E: -42.359452 | E_var:     0.2316 | E_err:   0.007519
[2025-10-03 11:57:11] [Iter 2027/2250] R4[1802/2400] | LR: 0.008638 | E: -42.357145 | E_var:     0.2640 | E_err:   0.008028
[2025-10-03 11:57:22] [Iter 2028/2250] R4[1804/2400] | LR: 0.008615 | E: -42.356009 | E_var:     0.2460 | E_err:   0.007749
[2025-10-03 11:57:32] [Iter 2029/2250] R4[1806/2400] | LR: 0.008592 | E: -42.363273 | E_var:     0.2899 | E_err:   0.008413
[2025-10-03 11:57:43] [Iter 2030/2250] R4[1808/2400] | LR: 0.008569 | E: -42.355122 | E_var:     0.2222 | E_err:   0.007366
[2025-10-03 11:57:53] [Iter 2031/2250] R4[1810/2400] | LR: 0.008546 | E: -42.343181 | E_var:     0.2305 | E_err:   0.007502
[2025-10-03 11:58:04] [Iter 2032/2250] R4[1812/2400] | LR: 0.008523 | E: -42.364864 | E_var:     0.2642 | E_err:   0.008031
[2025-10-03 11:58:14] [Iter 2033/2250] R4[1814/2400] | LR: 0.008501 | E: -42.362888 | E_var:     0.2103 | E_err:   0.007166
[2025-10-03 11:58:25] [Iter 2034/2250] R4[1816/2400] | LR: 0.008478 | E: -42.358794 | E_var:     0.4243 | E_err:   0.010178
[2025-10-03 11:58:36] [Iter 2035/2250] R4[1818/2400] | LR: 0.008455 | E: -42.349820 | E_var:     0.3555 | E_err:   0.009316
[2025-10-03 11:58:46] [Iter 2036/2250] R4[1820/2400] | LR: 0.008433 | E: -42.355597 | E_var:     0.2345 | E_err:   0.007566
[2025-10-03 11:58:57] [Iter 2037/2250] R4[1822/2400] | LR: 0.008410 | E: -42.365059 | E_var:     0.2889 | E_err:   0.008398
[2025-10-03 11:59:07] [Iter 2038/2250] R4[1824/2400] | LR: 0.008388 | E: -42.351419 | E_var:     0.2360 | E_err:   0.007591
[2025-10-03 11:59:18] [Iter 2039/2250] R4[1826/2400] | LR: 0.008366 | E: -42.345583 | E_var:     0.2430 | E_err:   0.007703
[2025-10-03 11:59:28] [Iter 2040/2250] R4[1828/2400] | LR: 0.008343 | E: -42.370441 | E_var:     0.2865 | E_err:   0.008364
[2025-10-03 11:59:39] [Iter 2041/2250] R4[1830/2400] | LR: 0.008321 | E: -42.355966 | E_var:     0.2786 | E_err:   0.008247
[2025-10-03 11:59:49] [Iter 2042/2250] R4[1832/2400] | LR: 0.008299 | E: -42.346740 | E_var:     0.1979 | E_err:   0.006951
[2025-10-03 12:00:00] [Iter 2043/2250] R4[1834/2400] | LR: 0.008277 | E: -42.353316 | E_var:     0.3456 | E_err:   0.009186
[2025-10-03 12:00:10] [Iter 2044/2250] R4[1836/2400] | LR: 0.008255 | E: -42.363417 | E_var:     0.2805 | E_err:   0.008275
[2025-10-03 12:00:21] [Iter 2045/2250] R4[1838/2400] | LR: 0.008233 | E: -42.352937 | E_var:     0.3050 | E_err:   0.008629
[2025-10-03 12:00:31] [Iter 2046/2250] R4[1840/2400] | LR: 0.008211 | E: -42.360650 | E_var:     0.2324 | E_err:   0.007533
[2025-10-03 12:00:42] [Iter 2047/2250] R4[1842/2400] | LR: 0.008189 | E: -42.355061 | E_var:     0.2696 | E_err:   0.008113
[2025-10-03 12:00:52] [Iter 2048/2250] R4[1844/2400] | LR: 0.008167 | E: -42.353784 | E_var:     0.2777 | E_err:   0.008234
[2025-10-03 12:01:03] [Iter 2049/2250] R4[1846/2400] | LR: 0.008145 | E: -42.358859 | E_var:     0.2791 | E_err:   0.008255
[2025-10-03 12:01:14] [Iter 2050/2250] R4[1848/2400] | LR: 0.008124 | E: -42.367150 | E_var:     0.2584 | E_err:   0.007943
[2025-10-03 12:01:24] [Iter 2051/2250] R4[1850/2400] | LR: 0.008102 | E: -42.372753 | E_var:     0.2222 | E_err:   0.007365
[2025-10-03 12:01:35] [Iter 2052/2250] R4[1852/2400] | LR: 0.008080 | E: -42.353309 | E_var:     0.2403 | E_err:   0.007660
[2025-10-03 12:01:45] [Iter 2053/2250] R4[1854/2400] | LR: 0.008059 | E: -42.345543 | E_var:     0.2734 | E_err:   0.008170
[2025-10-03 12:01:56] [Iter 2054/2250] R4[1856/2400] | LR: 0.008038 | E: -42.362904 | E_var:     0.2817 | E_err:   0.008293
[2025-10-03 12:02:06] [Iter 2055/2250] R4[1858/2400] | LR: 0.008016 | E: -42.364385 | E_var:     0.3737 | E_err:   0.009552
[2025-10-03 12:02:17] [Iter 2056/2250] R4[1860/2400] | LR: 0.007995 | E: -42.352769 | E_var:     0.3120 | E_err:   0.008728
[2025-10-03 12:02:27] [Iter 2057/2250] R4[1862/2400] | LR: 0.007974 | E: -42.367077 | E_var:     0.3472 | E_err:   0.009207
[2025-10-03 12:02:38] [Iter 2058/2250] R4[1864/2400] | LR: 0.007953 | E: -42.364178 | E_var:     0.2175 | E_err:   0.007286
[2025-10-03 12:02:48] [Iter 2059/2250] R4[1866/2400] | LR: 0.007931 | E: -42.355724 | E_var:     0.2075 | E_err:   0.007117
[2025-10-03 12:02:59] [Iter 2060/2250] R4[1868/2400] | LR: 0.007910 | E: -42.350307 | E_var:     0.2823 | E_err:   0.008301
[2025-10-03 12:03:09] [Iter 2061/2250] R4[1870/2400] | LR: 0.007889 | E: -42.372355 | E_var:     0.2263 | E_err:   0.007433
[2025-10-03 12:03:20] [Iter 2062/2250] R4[1872/2400] | LR: 0.007869 | E: -42.351973 | E_var:     0.2717 | E_err:   0.008145
[2025-10-03 12:03:30] [Iter 2063/2250] R4[1874/2400] | LR: 0.007848 | E: -42.355081 | E_var:     0.4675 | E_err:   0.010683
[2025-10-03 12:03:41] [Iter 2064/2250] R4[1876/2400] | LR: 0.007827 | E: -42.358696 | E_var:     0.2719 | E_err:   0.008147
[2025-10-03 12:03:51] [Iter 2065/2250] R4[1878/2400] | LR: 0.007806 | E: -42.348328 | E_var:     0.2900 | E_err:   0.008414
[2025-10-03 12:04:02] [Iter 2066/2250] R4[1880/2400] | LR: 0.007786 | E: -42.366133 | E_var:     0.2492 | E_err:   0.007799
[2025-10-03 12:04:12] [Iter 2067/2250] R4[1882/2400] | LR: 0.007765 | E: -42.372334 | E_var:     0.2887 | E_err:   0.008395
[2025-10-03 12:04:23] [Iter 2068/2250] R4[1884/2400] | LR: 0.007745 | E: -42.358960 | E_var:     0.2419 | E_err:   0.007684
[2025-10-03 12:04:34] [Iter 2069/2250] R4[1886/2400] | LR: 0.007724 | E: -42.359686 | E_var:     0.2962 | E_err:   0.008504
[2025-10-03 12:04:44] [Iter 2070/2250] R4[1888/2400] | LR: 0.007704 | E: -42.362889 | E_var:     0.2366 | E_err:   0.007600
[2025-10-03 12:04:55] [Iter 2071/2250] R4[1890/2400] | LR: 0.007684 | E: -42.351577 | E_var:     0.4006 | E_err:   0.009890
[2025-10-03 12:05:05] [Iter 2072/2250] R4[1892/2400] | LR: 0.007663 | E: -42.357914 | E_var:     0.2361 | E_err:   0.007593
[2025-10-03 12:05:16] [Iter 2073/2250] R4[1894/2400] | LR: 0.007643 | E: -42.348705 | E_var:     0.4634 | E_err:   0.010637
[2025-10-03 12:05:26] [Iter 2074/2250] R4[1896/2400] | LR: 0.007623 | E: -42.349594 | E_var:     0.2349 | E_err:   0.007573
[2025-10-03 12:05:37] [Iter 2075/2250] R4[1898/2400] | LR: 0.007603 | E: -42.363771 | E_var:     0.2641 | E_err:   0.008030
[2025-10-03 12:05:47] [Iter 2076/2250] R4[1900/2400] | LR: 0.007583 | E: -42.357604 | E_var:     0.3565 | E_err:   0.009329
[2025-10-03 12:05:58] [Iter 2077/2250] R4[1902/2400] | LR: 0.007563 | E: -42.352742 | E_var:     0.4594 | E_err:   0.010590
[2025-10-03 12:06:08] [Iter 2078/2250] R4[1904/2400] | LR: 0.007543 | E: -42.367580 | E_var:     0.3163 | E_err:   0.008787
[2025-10-03 12:06:19] [Iter 2079/2250] R4[1906/2400] | LR: 0.007524 | E: -42.364776 | E_var:     0.2549 | E_err:   0.007889
[2025-10-03 12:06:29] [Iter 2080/2250] R4[1908/2400] | LR: 0.007504 | E: -42.359957 | E_var:     0.3566 | E_err:   0.009331
[2025-10-03 12:06:40] [Iter 2081/2250] R4[1910/2400] | LR: 0.007484 | E: -42.357684 | E_var:     0.2339 | E_err:   0.007557
[2025-10-03 12:06:50] [Iter 2082/2250] R4[1912/2400] | LR: 0.007465 | E: -42.361912 | E_var:     0.2432 | E_err:   0.007706
[2025-10-03 12:07:01] [Iter 2083/2250] R4[1914/2400] | LR: 0.007445 | E: -42.365831 | E_var:     0.2895 | E_err:   0.008407
[2025-10-03 12:07:12] [Iter 2084/2250] R4[1916/2400] | LR: 0.007426 | E: -42.361831 | E_var:     0.3132 | E_err:   0.008744
[2025-10-03 12:07:22] [Iter 2085/2250] R4[1918/2400] | LR: 0.007407 | E: -42.346353 | E_var:     0.2702 | E_err:   0.008123
[2025-10-03 12:07:33] [Iter 2086/2250] R4[1920/2400] | LR: 0.007387 | E: -42.361488 | E_var:     0.2477 | E_err:   0.007777
[2025-10-03 12:07:43] [Iter 2087/2250] R4[1922/2400] | LR: 0.007368 | E: -42.352563 | E_var:     0.2341 | E_err:   0.007560
[2025-10-03 12:07:54] [Iter 2088/2250] R4[1924/2400] | LR: 0.007349 | E: -42.354594 | E_var:     0.2612 | E_err:   0.007985
[2025-10-03 12:08:04] [Iter 2089/2250] R4[1926/2400] | LR: 0.007330 | E: -42.348904 | E_var:     0.2157 | E_err:   0.007257
[2025-10-03 12:08:15] [Iter 2090/2250] R4[1928/2400] | LR: 0.007311 | E: -42.359513 | E_var:     0.3494 | E_err:   0.009236
[2025-10-03 12:08:25] [Iter 2091/2250] R4[1930/2400] | LR: 0.007292 | E: -42.360624 | E_var:     0.2489 | E_err:   0.007796
[2025-10-03 12:08:36] [Iter 2092/2250] R4[1932/2400] | LR: 0.007273 | E: -42.375535 | E_var:     0.7952 | E_err:   0.013934
[2025-10-03 12:08:46] [Iter 2093/2250] R4[1934/2400] | LR: 0.007254 | E: -42.355386 | E_var:     0.2476 | E_err:   0.007775
[2025-10-03 12:08:57] [Iter 2094/2250] R4[1936/2400] | LR: 0.007236 | E: -42.359275 | E_var:     0.2441 | E_err:   0.007720
[2025-10-03 12:09:07] [Iter 2095/2250] R4[1938/2400] | LR: 0.007217 | E: -42.352337 | E_var:     0.2438 | E_err:   0.007714
[2025-10-03 12:09:18] [Iter 2096/2250] R4[1940/2400] | LR: 0.007198 | E: -42.350375 | E_var:     0.2356 | E_err:   0.007583
[2025-10-03 12:09:28] [Iter 2097/2250] R4[1942/2400] | LR: 0.007180 | E: -42.365611 | E_var:     0.4164 | E_err:   0.010082
[2025-10-03 12:09:39] [Iter 2098/2250] R4[1944/2400] | LR: 0.007161 | E: -42.360577 | E_var:     0.2204 | E_err:   0.007335
[2025-10-03 12:09:50] [Iter 2099/2250] R4[1946/2400] | LR: 0.007143 | E: -42.360708 | E_var:     0.3533 | E_err:   0.009287
[2025-10-03 12:10:00] [Iter 2100/2250] R4[1948/2400] | LR: 0.007125 | E: -42.368123 | E_var:     0.2381 | E_err:   0.007624
[2025-10-03 12:10:11] [Iter 2101/2250] R4[1950/2400] | LR: 0.007107 | E: -42.358577 | E_var:     0.2465 | E_err:   0.007758
[2025-10-03 12:10:21] [Iter 2102/2250] R4[1952/2400] | LR: 0.007088 | E: -42.352035 | E_var:     0.2704 | E_err:   0.008124
[2025-10-03 12:10:32] [Iter 2103/2250] R4[1954/2400] | LR: 0.007070 | E: -42.357353 | E_var:     0.2232 | E_err:   0.007382
[2025-10-03 12:10:42] [Iter 2104/2250] R4[1956/2400] | LR: 0.007052 | E: -42.363033 | E_var:     0.2611 | E_err:   0.007984
[2025-10-03 12:10:53] [Iter 2105/2250] R4[1958/2400] | LR: 0.007034 | E: -42.347750 | E_var:     0.2454 | E_err:   0.007740
[2025-10-03 12:11:03] [Iter 2106/2250] R4[1960/2400] | LR: 0.007017 | E: -42.355894 | E_var:     0.2355 | E_err:   0.007583
[2025-10-03 12:11:14] [Iter 2107/2250] R4[1962/2400] | LR: 0.006999 | E: -42.364077 | E_var:     0.2698 | E_err:   0.008115
[2025-10-03 12:11:24] [Iter 2108/2250] R4[1964/2400] | LR: 0.006981 | E: -42.353926 | E_var:     0.3390 | E_err:   0.009097
[2025-10-03 12:11:35] [Iter 2109/2250] R4[1966/2400] | LR: 0.006963 | E: -42.355374 | E_var:     0.2259 | E_err:   0.007427
[2025-10-03 12:11:45] [Iter 2110/2250] R4[1968/2400] | LR: 0.006946 | E: -42.351801 | E_var:     0.4024 | E_err:   0.009911
[2025-10-03 12:11:56] [Iter 2111/2250] R4[1970/2400] | LR: 0.006928 | E: -42.377681 | E_var:     0.2362 | E_err:   0.007593
[2025-10-03 12:12:06] [Iter 2112/2250] R4[1972/2400] | LR: 0.006911 | E: -42.372469 | E_var:     0.3257 | E_err:   0.008917
[2025-10-03 12:12:17] [Iter 2113/2250] R4[1974/2400] | LR: 0.006894 | E: -42.351749 | E_var:     0.2773 | E_err:   0.008229
[2025-10-03 12:12:27] [Iter 2114/2250] R4[1976/2400] | LR: 0.006876 | E: -42.353523 | E_var:     0.2404 | E_err:   0.007661
[2025-10-03 12:12:38] [Iter 2115/2250] R4[1978/2400] | LR: 0.006859 | E: -42.363881 | E_var:     0.2492 | E_err:   0.007800
[2025-10-03 12:12:49] [Iter 2116/2250] R4[1980/2400] | LR: 0.006842 | E: -42.358872 | E_var:     0.2393 | E_err:   0.007643
[2025-10-03 12:12:59] [Iter 2117/2250] R4[1982/2400] | LR: 0.006825 | E: -42.349008 | E_var:     0.2245 | E_err:   0.007404
[2025-10-03 12:13:10] [Iter 2118/2250] R4[1984/2400] | LR: 0.006808 | E: -42.353350 | E_var:     0.2236 | E_err:   0.007389
[2025-10-03 12:13:20] [Iter 2119/2250] R4[1986/2400] | LR: 0.006791 | E: -42.354738 | E_var:     0.3672 | E_err:   0.009468
[2025-10-03 12:13:31] [Iter 2120/2250] R4[1988/2400] | LR: 0.006774 | E: -42.366788 | E_var:     0.2261 | E_err:   0.007430
[2025-10-03 12:13:41] [Iter 2121/2250] R4[1990/2400] | LR: 0.006757 | E: -42.344562 | E_var:     0.2734 | E_err:   0.008170
[2025-10-03 12:13:52] [Iter 2122/2250] R4[1992/2400] | LR: 0.006741 | E: -42.350726 | E_var:     0.2778 | E_err:   0.008236
[2025-10-03 12:14:02] [Iter 2123/2250] R4[1994/2400] | LR: 0.006724 | E: -42.352104 | E_var:     0.3124 | E_err:   0.008733
[2025-10-03 12:14:13] [Iter 2124/2250] R4[1996/2400] | LR: 0.006708 | E: -42.356248 | E_var:     0.2715 | E_err:   0.008142
[2025-10-03 12:14:23] [Iter 2125/2250] R4[1998/2400] | LR: 0.006691 | E: -42.359959 | E_var:     0.2991 | E_err:   0.008545
[2025-10-03 12:14:34] [Iter 2126/2250] R4[2000/2400] | LR: 0.006675 | E: -42.361906 | E_var:     0.3026 | E_err:   0.008595
[2025-10-03 12:14:44] [Iter 2127/2250] R4[2002/2400] | LR: 0.006658 | E: -42.352417 | E_var:     0.2367 | E_err:   0.007601
[2025-10-03 12:14:55] [Iter 2128/2250] R4[2004/2400] | LR: 0.006642 | E: -42.363304 | E_var:     0.2858 | E_err:   0.008352
[2025-10-03 12:15:05] [Iter 2129/2250] R4[2006/2400] | LR: 0.006626 | E: -42.382519 | E_var:     0.2500 | E_err:   0.007812
[2025-10-03 12:15:16] [Iter 2130/2250] R4[2008/2400] | LR: 0.006610 | E: -42.350298 | E_var:     0.2705 | E_err:   0.008126
[2025-10-03 12:15:26] [Iter 2131/2250] R4[2010/2400] | LR: 0.006594 | E: -42.340362 | E_var:     0.2829 | E_err:   0.008311
[2025-10-03 12:15:37] [Iter 2132/2250] R4[2012/2400] | LR: 0.006578 | E: -42.371185 | E_var:     0.2171 | E_err:   0.007281
[2025-10-03 12:15:48] [Iter 2133/2250] R4[2014/2400] | LR: 0.006562 | E: -42.357979 | E_var:     0.2747 | E_err:   0.008189
[2025-10-03 12:15:58] [Iter 2134/2250] R4[2016/2400] | LR: 0.006546 | E: -42.358790 | E_var:     0.3970 | E_err:   0.009846
[2025-10-03 12:16:09] [Iter 2135/2250] R4[2018/2400] | LR: 0.006530 | E: -42.352305 | E_var:     0.1921 | E_err:   0.006849
[2025-10-03 12:16:19] [Iter 2136/2250] R4[2020/2400] | LR: 0.006515 | E: -42.362263 | E_var:     0.2645 | E_err:   0.008036
[2025-10-03 12:16:30] [Iter 2137/2250] R4[2022/2400] | LR: 0.006499 | E: -42.349860 | E_var:     0.2221 | E_err:   0.007364
[2025-10-03 12:16:40] [Iter 2138/2250] R4[2024/2400] | LR: 0.006484 | E: -42.357501 | E_var:     0.2853 | E_err:   0.008346
[2025-10-03 12:16:51] [Iter 2139/2250] R4[2026/2400] | LR: 0.006468 | E: -42.356091 | E_var:     0.2639 | E_err:   0.008027
[2025-10-03 12:17:01] [Iter 2140/2250] R4[2028/2400] | LR: 0.006453 | E: -42.362360 | E_var:     0.2409 | E_err:   0.007669
[2025-10-03 12:17:12] [Iter 2141/2250] R4[2030/2400] | LR: 0.006438 | E: -42.355362 | E_var:     0.2139 | E_err:   0.007227
[2025-10-03 12:17:22] [Iter 2142/2250] R4[2032/2400] | LR: 0.006422 | E: -42.344073 | E_var:     0.2947 | E_err:   0.008482
[2025-10-03 12:17:33] [Iter 2143/2250] R4[2034/2400] | LR: 0.006407 | E: -42.359405 | E_var:     0.2995 | E_err:   0.008551
[2025-10-03 12:17:43] [Iter 2144/2250] R4[2036/2400] | LR: 0.006392 | E: -42.358619 | E_var:     0.2627 | E_err:   0.008008
[2025-10-03 12:17:54] [Iter 2145/2250] R4[2038/2400] | LR: 0.006377 | E: -42.351389 | E_var:     0.2346 | E_err:   0.007569
[2025-10-03 12:18:04] [Iter 2146/2250] R4[2040/2400] | LR: 0.006362 | E: -42.349878 | E_var:     0.2432 | E_err:   0.007706
[2025-10-03 12:18:15] [Iter 2147/2250] R4[2042/2400] | LR: 0.006348 | E: -42.358168 | E_var:     0.2185 | E_err:   0.007304
[2025-10-03 12:18:26] [Iter 2148/2250] R4[2044/2400] | LR: 0.006333 | E: -42.357191 | E_var:     0.2467 | E_err:   0.007761
[2025-10-03 12:18:36] [Iter 2149/2250] R4[2046/2400] | LR: 0.006318 | E: -42.359324 | E_var:     0.2204 | E_err:   0.007336
[2025-10-03 12:18:47] [Iter 2150/2250] R4[2048/2400] | LR: 0.006304 | E: -42.348212 | E_var:     0.2324 | E_err:   0.007533
[2025-10-03 12:18:57] [Iter 2151/2250] R4[2050/2400] | LR: 0.006289 | E: -42.358513 | E_var:     0.2667 | E_err:   0.008069
[2025-10-03 12:19:08] [Iter 2152/2250] R4[2052/2400] | LR: 0.006275 | E: -42.356128 | E_var:     0.2764 | E_err:   0.008214
[2025-10-03 12:19:18] [Iter 2153/2250] R4[2054/2400] | LR: 0.006260 | E: -42.361357 | E_var:     0.2612 | E_err:   0.007986
[2025-10-03 12:19:29] [Iter 2154/2250] R4[2056/2400] | LR: 0.006246 | E: -42.366294 | E_var:     0.2211 | E_err:   0.007346
[2025-10-03 12:19:39] [Iter 2155/2250] R4[2058/2400] | LR: 0.006232 | E: -42.360506 | E_var:     0.2729 | E_err:   0.008163
[2025-10-03 12:19:50] [Iter 2156/2250] R4[2060/2400] | LR: 0.006218 | E: -42.350067 | E_var:     0.2369 | E_err:   0.007604
[2025-10-03 12:20:00] [Iter 2157/2250] R4[2062/2400] | LR: 0.006204 | E: -42.346440 | E_var:     0.2408 | E_err:   0.007667
[2025-10-03 12:20:11] [Iter 2158/2250] R4[2064/2400] | LR: 0.006190 | E: -42.334200 | E_var:     0.3114 | E_err:   0.008719
[2025-10-03 12:20:21] [Iter 2159/2250] R4[2066/2400] | LR: 0.006176 | E: -42.351540 | E_var:     0.2802 | E_err:   0.008271
[2025-10-03 12:20:32] [Iter 2160/2250] R4[2068/2400] | LR: 0.006162 | E: -42.351731 | E_var:     0.4627 | E_err:   0.010628
[2025-10-03 12:20:43] [Iter 2161/2250] R4[2070/2400] | LR: 0.006148 | E: -42.351720 | E_var:     0.2212 | E_err:   0.007349
[2025-10-03 12:20:53] [Iter 2162/2250] R4[2072/2400] | LR: 0.006135 | E: -42.353928 | E_var:     0.2507 | E_err:   0.007824
[2025-10-03 12:21:04] [Iter 2163/2250] R4[2074/2400] | LR: 0.006121 | E: -42.350004 | E_var:     0.2779 | E_err:   0.008237
[2025-10-03 12:21:14] [Iter 2164/2250] R4[2076/2400] | LR: 0.006107 | E: -42.360745 | E_var:     0.2430 | E_err:   0.007702
[2025-10-03 12:21:25] [Iter 2165/2250] R4[2078/2400] | LR: 0.006094 | E: -42.347135 | E_var:     0.2814 | E_err:   0.008289
[2025-10-03 12:21:35] [Iter 2166/2250] R4[2080/2400] | LR: 0.006081 | E: -42.347091 | E_var:     0.2701 | E_err:   0.008120
[2025-10-03 12:21:46] [Iter 2167/2250] R4[2082/2400] | LR: 0.006067 | E: -42.350696 | E_var:     0.2240 | E_err:   0.007396
[2025-10-03 12:21:56] [Iter 2168/2250] R4[2084/2400] | LR: 0.006054 | E: -42.365237 | E_var:     0.2588 | E_err:   0.007949
[2025-10-03 12:22:07] [Iter 2169/2250] R4[2086/2400] | LR: 0.006041 | E: -42.363344 | E_var:     0.3548 | E_err:   0.009307
[2025-10-03 12:22:17] [Iter 2170/2250] R4[2088/2400] | LR: 0.006028 | E: -42.362324 | E_var:     0.2267 | E_err:   0.007440
[2025-10-03 12:22:28] [Iter 2171/2250] R4[2090/2400] | LR: 0.006015 | E: -42.373754 | E_var:     0.4772 | E_err:   0.010794
[2025-10-03 12:22:38] [Iter 2172/2250] R4[2092/2400] | LR: 0.006002 | E: -42.363490 | E_var:     0.2543 | E_err:   0.007879
[2025-10-03 12:22:49] [Iter 2173/2250] R4[2094/2400] | LR: 0.005989 | E: -42.354476 | E_var:     0.2990 | E_err:   0.008543
[2025-10-03 12:22:59] [Iter 2174/2250] R4[2096/2400] | LR: 0.005977 | E: -42.356249 | E_var:     0.2821 | E_err:   0.008298
[2025-10-03 12:23:10] [Iter 2175/2250] R4[2098/2400] | LR: 0.005964 | E: -42.347633 | E_var:     0.2457 | E_err:   0.007745
[2025-10-03 12:23:20] [Iter 2176/2250] R4[2100/2400] | LR: 0.005952 | E: -42.358444 | E_var:     0.2314 | E_err:   0.007517
[2025-10-03 12:23:31] [Iter 2177/2250] R4[2102/2400] | LR: 0.005939 | E: -42.362533 | E_var:     0.2092 | E_err:   0.007147
[2025-10-03 12:23:41] [Iter 2178/2250] R4[2104/2400] | LR: 0.005927 | E: -42.342872 | E_var:     0.2911 | E_err:   0.008431
[2025-10-03 12:23:52] [Iter 2179/2250] R4[2106/2400] | LR: 0.005914 | E: -42.355003 | E_var:     0.2216 | E_err:   0.007356
[2025-10-03 12:24:02] [Iter 2180/2250] R4[2108/2400] | LR: 0.005902 | E: -42.366606 | E_var:     0.5010 | E_err:   0.011060
[2025-10-03 12:24:13] [Iter 2181/2250] R4[2110/2400] | LR: 0.005890 | E: -42.339487 | E_var:     0.2836 | E_err:   0.008320
[2025-10-03 12:24:24] [Iter 2182/2250] R4[2112/2400] | LR: 0.005878 | E: -42.360685 | E_var:     0.2646 | E_err:   0.008038
[2025-10-03 12:24:34] [Iter 2183/2250] R4[2114/2400] | LR: 0.005866 | E: -42.343329 | E_var:     0.5029 | E_err:   0.011080
[2025-10-03 12:24:45] [Iter 2184/2250] R4[2116/2400] | LR: 0.005854 | E: -42.365013 | E_var:     0.2618 | E_err:   0.007995
[2025-10-03 12:24:55] [Iter 2185/2250] R4[2118/2400] | LR: 0.005842 | E: -42.345134 | E_var:     0.2209 | E_err:   0.007343
[2025-10-03 12:25:06] [Iter 2186/2250] R4[2120/2400] | LR: 0.005830 | E: -42.372783 | E_var:     0.3061 | E_err:   0.008644
[2025-10-03 12:25:16] [Iter 2187/2250] R4[2122/2400] | LR: 0.005819 | E: -42.353281 | E_var:     0.5996 | E_err:   0.012099
[2025-10-03 12:25:27] [Iter 2188/2250] R4[2124/2400] | LR: 0.005807 | E: -42.355442 | E_var:     0.2714 | E_err:   0.008139
[2025-10-03 12:25:37] [Iter 2189/2250] R4[2126/2400] | LR: 0.005795 | E: -42.348796 | E_var:     0.2270 | E_err:   0.007445
[2025-10-03 12:25:48] [Iter 2190/2250] R4[2128/2400] | LR: 0.005784 | E: -42.363186 | E_var:     0.6170 | E_err:   0.012274
[2025-10-03 12:25:58] [Iter 2191/2250] R4[2130/2400] | LR: 0.005773 | E: -42.360205 | E_var:     0.2689 | E_err:   0.008103
[2025-10-03 12:26:09] [Iter 2192/2250] R4[2132/2400] | LR: 0.005761 | E: -42.356377 | E_var:     0.2815 | E_err:   0.008289
[2025-10-03 12:26:19] [Iter 2193/2250] R4[2134/2400] | LR: 0.005750 | E: -42.363192 | E_var:     0.2574 | E_err:   0.007928
[2025-10-03 12:26:30] [Iter 2194/2250] R4[2136/2400] | LR: 0.005739 | E: -42.356197 | E_var:     0.2334 | E_err:   0.007549
[2025-10-03 12:26:40] [Iter 2195/2250] R4[2138/2400] | LR: 0.005728 | E: -42.359874 | E_var:     0.2980 | E_err:   0.008530
[2025-10-03 12:26:51] [Iter 2196/2250] R4[2140/2400] | LR: 0.005717 | E: -42.338387 | E_var:     0.2651 | E_err:   0.008045
[2025-10-03 12:27:01] [Iter 2197/2250] R4[2142/2400] | LR: 0.005706 | E: -42.365684 | E_var:     0.2333 | E_err:   0.007548
[2025-10-03 12:27:12] [Iter 2198/2250] R4[2144/2400] | LR: 0.005695 | E: -42.358709 | E_var:     0.6354 | E_err:   0.012455
[2025-10-03 12:27:22] [Iter 2199/2250] R4[2146/2400] | LR: 0.005685 | E: -42.351042 | E_var:     0.2222 | E_err:   0.007365
[2025-10-03 12:27:33] [Iter 2200/2250] R4[2148/2400] | LR: 0.005674 | E: -42.361065 | E_var:     0.2336 | E_err:   0.007551
[2025-10-03 12:27:34] ✓ Checkpoint saved: checkpoint_iter_002200.pkl
[2025-10-03 12:27:44] [Iter 2201/2250] R4[2150/2400] | LR: 0.005663 | E: -42.355234 | E_var:     0.2330 | E_err:   0.007543
[2025-10-03 12:27:55] [Iter 2202/2250] R4[2152/2400] | LR: 0.005653 | E: -42.369348 | E_var:     0.2163 | E_err:   0.007267
[2025-10-03 12:28:05] [Iter 2203/2250] R4[2154/2400] | LR: 0.005642 | E: -42.354429 | E_var:     0.2573 | E_err:   0.007925
[2025-10-03 12:28:16] [Iter 2204/2250] R4[2156/2400] | LR: 0.005632 | E: -42.346186 | E_var:     0.2775 | E_err:   0.008231
[2025-10-03 12:28:26] [Iter 2205/2250] R4[2158/2400] | LR: 0.005622 | E: -42.353573 | E_var:     0.5678 | E_err:   0.011774
[2025-10-03 12:28:37] [Iter 2206/2250] R4[2160/2400] | LR: 0.005612 | E: -42.351038 | E_var:     0.2638 | E_err:   0.008025
[2025-10-03 12:28:47] [Iter 2207/2250] R4[2162/2400] | LR: 0.005602 | E: -42.360503 | E_var:     0.2083 | E_err:   0.007132
[2025-10-03 12:28:58] [Iter 2208/2250] R4[2164/2400] | LR: 0.005592 | E: -42.360669 | E_var:     0.2131 | E_err:   0.007214
[2025-10-03 12:29:08] [Iter 2209/2250] R4[2166/2400] | LR: 0.005582 | E: -42.355058 | E_var:     0.2380 | E_err:   0.007623
[2025-10-03 12:29:19] [Iter 2210/2250] R4[2168/2400] | LR: 0.005572 | E: -42.359143 | E_var:     0.2630 | E_err:   0.008013
[2025-10-03 12:29:29] [Iter 2211/2250] R4[2170/2400] | LR: 0.005562 | E: -42.346767 | E_var:     0.2130 | E_err:   0.007211
[2025-10-03 12:29:40] [Iter 2212/2250] R4[2172/2400] | LR: 0.005553 | E: -42.367192 | E_var:     0.2184 | E_err:   0.007303
[2025-10-03 12:29:50] [Iter 2213/2250] R4[2174/2400] | LR: 0.005543 | E: -42.359793 | E_var:     0.2713 | E_err:   0.008139
[2025-10-03 12:30:01] [Iter 2214/2250] R4[2176/2400] | LR: 0.005534 | E: -42.353349 | E_var:     0.2514 | E_err:   0.007835
[2025-10-03 12:30:11] [Iter 2215/2250] R4[2178/2400] | LR: 0.005524 | E: -42.336723 | E_var:     0.3036 | E_err:   0.008610
[2025-10-03 12:30:22] [Iter 2216/2250] R4[2180/2400] | LR: 0.005515 | E: -42.359737 | E_var:     0.2068 | E_err:   0.007106
[2025-10-03 12:30:32] [Iter 2217/2250] R4[2182/2400] | LR: 0.005506 | E: -42.366874 | E_var:     0.2757 | E_err:   0.008205
[2025-10-03 12:30:43] [Iter 2218/2250] R4[2184/2400] | LR: 0.005496 | E: -42.364049 | E_var:     0.3424 | E_err:   0.009143
[2025-10-03 12:30:53] [Iter 2219/2250] R4[2186/2400] | LR: 0.005487 | E: -42.355162 | E_var:     0.2448 | E_err:   0.007730
[2025-10-03 12:31:04] [Iter 2220/2250] R4[2188/2400] | LR: 0.005478 | E: -42.358955 | E_var:     0.2267 | E_err:   0.007440
[2025-10-03 12:31:14] [Iter 2221/2250] R4[2190/2400] | LR: 0.005469 | E: -42.344360 | E_var:     0.2482 | E_err:   0.007785
[2025-10-03 12:31:25] [Iter 2222/2250] R4[2192/2400] | LR: 0.005460 | E: -42.356479 | E_var:     0.2578 | E_err:   0.007933
[2025-10-03 12:31:35] [Iter 2223/2250] R4[2194/2400] | LR: 0.005452 | E: -42.361410 | E_var:     0.2581 | E_err:   0.007938
[2025-10-03 12:31:46] [Iter 2224/2250] R4[2196/2400] | LR: 0.005443 | E: -42.362320 | E_var:     0.2279 | E_err:   0.007460
[2025-10-03 12:31:57] [Iter 2225/2250] R4[2198/2400] | LR: 0.005434 | E: -42.347007 | E_var:     0.3291 | E_err:   0.008964
[2025-10-03 12:32:07] [Iter 2226/2250] R4[2200/2400] | LR: 0.005426 | E: -42.361139 | E_var:     0.2511 | E_err:   0.007829
[2025-10-03 12:32:18] [Iter 2227/2250] R4[2202/2400] | LR: 0.005417 | E: -42.364493 | E_var:     0.1821 | E_err:   0.006667
[2025-10-03 12:32:28] [Iter 2228/2250] R4[2204/2400] | LR: 0.005409 | E: -42.352885 | E_var:     0.3131 | E_err:   0.008743
[2025-10-03 12:32:39] [Iter 2229/2250] R4[2206/2400] | LR: 0.005401 | E: -42.361908 | E_var:     0.2548 | E_err:   0.007887
[2025-10-03 12:32:49] [Iter 2230/2250] R4[2208/2400] | LR: 0.005393 | E: -42.351959 | E_var:     0.3520 | E_err:   0.009271
[2025-10-03 12:33:00] [Iter 2231/2250] R4[2210/2400] | LR: 0.005385 | E: -42.356336 | E_var:     0.2347 | E_err:   0.007570
[2025-10-03 12:33:10] [Iter 2232/2250] R4[2212/2400] | LR: 0.005377 | E: -42.343914 | E_var:     1.0204 | E_err:   0.015784
[2025-10-03 12:33:21] [Iter 2233/2250] R4[2214/2400] | LR: 0.005369 | E: -42.355936 | E_var:     0.2593 | E_err:   0.007956
[2025-10-03 12:33:31] [Iter 2234/2250] R4[2216/2400] | LR: 0.005361 | E: -42.359967 | E_var:     0.2420 | E_err:   0.007687
[2025-10-03 12:33:42] [Iter 2235/2250] R4[2218/2400] | LR: 0.005353 | E: -42.360647 | E_var:     0.2887 | E_err:   0.008395
[2025-10-03 12:33:52] [Iter 2236/2250] R4[2220/2400] | LR: 0.005345 | E: -42.360820 | E_var:     0.2727 | E_err:   0.008160
[2025-10-03 12:34:03] [Iter 2237/2250] R4[2222/2400] | LR: 0.005338 | E: -42.366011 | E_var:     0.2557 | E_err:   0.007901
[2025-10-03 12:34:13] [Iter 2238/2250] R4[2224/2400] | LR: 0.005330 | E: -42.373843 | E_var:     0.3461 | E_err:   0.009193
[2025-10-03 12:34:24] [Iter 2239/2250] R4[2226/2400] | LR: 0.005323 | E: -42.349852 | E_var:     0.2379 | E_err:   0.007622
[2025-10-03 12:34:34] [Iter 2240/2250] R4[2228/2400] | LR: 0.005315 | E: -42.351924 | E_var:     0.3232 | E_err:   0.008882
[2025-10-03 12:34:45] [Iter 2241/2250] R4[2230/2400] | LR: 0.005308 | E: -42.354883 | E_var:     0.1975 | E_err:   0.006943
[2025-10-03 12:34:55] [Iter 2242/2250] R4[2232/2400] | LR: 0.005301 | E: -42.363711 | E_var:     0.3033 | E_err:   0.008606
[2025-10-03 12:35:06] [Iter 2243/2250] R4[2234/2400] | LR: 0.005294 | E: -42.360046 | E_var:     0.2468 | E_err:   0.007762
[2025-10-03 12:35:16] [Iter 2244/2250] R4[2236/2400] | LR: 0.005287 | E: -42.358096 | E_var:     0.3129 | E_err:   0.008740
[2025-10-03 12:35:27] [Iter 2245/2250] R4[2238/2400] | LR: 0.005280 | E: -42.368184 | E_var:     0.2391 | E_err:   0.007641
[2025-10-03 12:35:37] [Iter 2246/2250] R4[2240/2400] | LR: 0.005273 | E: -42.351367 | E_var:     0.2993 | E_err:   0.008548
[2025-10-03 12:35:48] [Iter 2247/2250] R4[2242/2400] | LR: 0.005266 | E: -42.361290 | E_var:     0.2209 | E_err:   0.007344
[2025-10-03 12:35:58] [Iter 2248/2250] R4[2244/2400] | LR: 0.005260 | E: -42.357475 | E_var:     0.2677 | E_err:   0.008084
[2025-10-03 12:36:09] [Iter 2249/2250] R4[2246/2400] | LR: 0.005253 | E: -42.350937 | E_var:     0.2582 | E_err:   0.007939
[2025-10-03 12:36:20] [Iter 2250/2250] R4[2248/2400] | LR: 0.005247 | E: -42.368225 | E_var:     0.2372 | E_err:   0.007609
[2025-10-03 12:36:20] ================================================================================
[2025-10-03 12:36:20] ✅ Training completed successfully
[2025-10-03 12:36:20] Total restarts: 4
[2025-10-03 12:36:23] Final Energy: -42.36822455 ± 0.00760945
[2025-10-03 12:36:23] Final Variance: 0.237174
[2025-10-03 12:36:23] ================================================================================
[2025-10-03 12:36:23] ============================================================
[2025-10-03 12:36:23] Training completed | Runtime: 23724.1s
[2025-10-03 12:36:27] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-10-03 12:36:27] ============================================================
