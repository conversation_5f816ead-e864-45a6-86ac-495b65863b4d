[2025-10-07 00:18:07] 使用checkpoint文件: results/L=5/J2=1.00/J1=0.76/model_L4F4/training/checkpoints/checkpoint_iter_000600.pkl
[2025-10-07 00:18:27] ✓ 从checkpoint加载参数: 600
[2025-10-07 00:18:27]   - 能量: -42.235540+0.000848j ± 0.011717
[2025-10-07 00:18:27] ================================================================================
[2025-10-07 00:18:27] 加载量子态: L=5, J2=1.00, J1=0.76, checkpoint=checkpoint_iter_000600
[2025-10-07 00:18:27] 使用采样数目: 1048576
[2025-10-07 00:18:27] 设置样本数为: 1048576
[2025-10-07 00:18:27] 开始生成共享样本集...
[2025-10-07 00:21:55] 样本生成完成,耗时: 208.315 秒
[2025-10-07 00:21:55] ================================================================================
[2025-10-07 00:21:55] 开始计算自旋结构因子...
[2025-10-07 00:21:55] 初始化操作符缓存...
[2025-10-07 00:21:55] 预构建所有自旋相关操作符...
[2025-10-07 00:21:56] 开始计算自旋相关函数...
[2025-10-07 00:22:05] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.599s
[2025-10-07 00:22:17] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.773s
[2025-10-07 00:22:24] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 7.145s
[2025-10-07 00:22:30] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.096s
[2025-10-07 00:22:36] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.086s
[2025-10-07 00:22:42] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.101s
[2025-10-07 00:22:48] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.104s
[2025-10-07 00:22:55] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.097s
[2025-10-07 00:23:01] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.093s
[2025-10-07 00:23:07] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.104s
[2025-10-07 00:23:13] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.097s
[2025-10-07 00:23:19] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.096s
[2025-10-07 00:23:25] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.096s
[2025-10-07 00:23:31] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.104s
[2025-10-07 00:23:37] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.104s
[2025-10-07 00:23:43] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.104s
[2025-10-07 00:23:50] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.266s
[2025-10-07 00:23:57] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 7.447s
[2025-10-07 00:24:05] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 7.422s
[2025-10-07 00:24:12] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 7.383s
[2025-10-07 00:24:19] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 7.499s
[2025-10-07 00:24:27] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 7.425s
[2025-10-07 00:24:34] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 7.373s
[2025-10-07 00:24:42] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 7.449s
[2025-10-07 00:24:49] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 7.401s
[2025-10-07 00:24:56] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 7.390s
[2025-10-07 00:25:04] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 7.476s
[2025-10-07 00:25:11] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 7.384s
[2025-10-07 00:25:19] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 7.462s
[2025-10-07 00:25:26] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 7.445s
[2025-10-07 00:25:34] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 7.392s
[2025-10-07 00:25:41] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 7.657s
[2025-10-07 00:25:49] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 7.406s
[2025-10-07 00:25:56] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 7.054s
[2025-10-07 00:26:02] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.100s
[2025-10-07 00:26:08] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.099s
[2025-10-07 00:26:14] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.084s
[2025-10-07 00:26:20] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.105s
[2025-10-07 00:26:26] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.094s
[2025-10-07 00:26:32] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.087s
[2025-10-07 00:26:39] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.206s
[2025-10-07 00:26:46] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 7.358s
[2025-10-07 00:26:53] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 7.455s
[2025-10-07 00:27:01] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 7.405s
[2025-10-07 00:27:08] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 7.354s
[2025-10-07 00:27:16] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 7.447s
[2025-10-07 00:27:23] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 7.415s
[2025-10-07 00:27:30] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 7.413s
[2025-10-07 00:27:38] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 7.483s
[2025-10-07 00:27:45] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 7.342s
[2025-10-07 00:27:53] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 7.435s
[2025-10-07 00:28:00] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 7.464s
[2025-10-07 00:28:08] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 7.346s
[2025-10-07 00:28:15] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 7.513s
[2025-10-07 00:28:22] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 7.406s
[2025-10-07 00:28:30] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 7.407s
[2025-10-07 00:28:37] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 7.454s
[2025-10-07 00:28:45] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 7.375s
[2025-10-07 00:28:51] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.141s
[2025-10-07 00:28:57] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.095s
[2025-10-07 00:29:03] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.086s
[2025-10-07 00:29:10] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.461s
[2025-10-07 00:29:17] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 7.356s
[2025-10-07 00:29:24] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 7.443s
[2025-10-07 00:29:32] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 7.472s
[2025-10-07 00:29:39] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 7.409s
[2025-10-07 00:29:47] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 7.463s
[2025-10-07 00:29:54] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 7.414s
[2025-10-07 00:30:02] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 7.454s
[2025-10-07 00:30:09] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 7.437s
[2025-10-07 00:30:16] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 7.385s
[2025-10-07 00:30:24] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 7.467s
[2025-10-07 00:30:31] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 7.393s
[2025-10-07 00:30:39] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 7.450s
[2025-10-07 00:30:46] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 7.447s
[2025-10-07 00:30:54] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 7.434s
[2025-10-07 00:31:01] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.886s
[2025-10-07 00:31:07] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.107s
[2025-10-07 00:31:13] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.082s
[2025-10-07 00:31:19] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.108s
[2025-10-07 00:31:25] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.309s
[2025-10-07 00:31:33] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 7.480s
[2025-10-07 00:31:40] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 7.387s
[2025-10-07 00:31:47] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 7.388s
[2025-10-07 00:31:55] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 7.478s
[2025-10-07 00:32:02] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 7.365s
[2025-10-07 00:32:10] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 7.383s
[2025-10-07 00:32:17] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 7.335s
[2025-10-07 00:32:23] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.097s
[2025-10-07 00:32:29] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.102s
[2025-10-07 00:32:37] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 7.391s
[2025-10-07 00:32:44] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 7.482s
[2025-10-07 00:32:50] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.269s
[2025-10-07 00:32:57] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.659s
[2025-10-07 00:33:04] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 7.352s
[2025-10-07 00:33:12] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 7.423s
[2025-10-07 00:33:19] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 7.460s
[2025-10-07 00:33:27] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 7.362s
[2025-10-07 00:33:34] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 7.461s
[2025-10-07 00:33:42] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 7.442s
[2025-10-07 00:33:42] 自旋相关函数计算完成,总耗时 706.02 秒
[2025-10-07 00:33:42] 计算傅里叶变换...
[2025-10-07 00:33:51] 自旋结构因子计算完成
[2025-10-07 00:33:52] 自旋相关函数平均误差: 0.000595
