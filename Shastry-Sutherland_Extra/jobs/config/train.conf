# ==================== 训练任务配置文件 ====================
# 此文件包含基础训练任务的所有参数配置

# ==================== 系统参数配置 ====================
# 晶格尺寸
L_VALUES="4 5"

# J2耦合强度
J2_VALUES="1.00"

# J1耦合强度（训练任务）
TRAIN_J1_VALUES="0.76"

# ==================== 训练超参数 ====================
# 学习率调度（余弦退火+热重启）
TRAIN_MAX_LR=0.03        # 最大学习率（重启时的学习率）
TRAIN_MIN_LR=0.005       # 最小学习率（周期结束时的学习率）
TRAIN_INITIAL_PERIOD=150  # 初始退火周期长度
TRAIN_PERIOD_MULT=2.0     # 周期倍增因子（每次重启后周期长度翻倍）
TRAIN_N_CYCLES=4          # 重启周期数

# 采样参数
TRAIN_N_SAMPLES=4096
TRAIN_CHUNK_SIZE=4096

# ==================== 模型参数 ====================
# 模型参数组合（格式：每个组合为"层数,特征数"，空格分隔）
# 例如："2,4" 表示 2层4特征
# 示例：TRAIN_MODEL_CONFIGS="2,4 4,5 6,8"
TRAIN_MODEL_CONFIGS="4,4 6,4 8,4"

# 模型并行数已移除，所有模型将串行执行

# 其他模型参数
TRAIN_DIAG_SHIFT=0.15
TRAIN_GRAD_CLIP=1.0

# ==================== Checkpoint配置 ====================
TRAIN_ENABLE_CHECKPOINT=true
TRAIN_CHECKPOINT_INTERVAL=200
TRAIN_RESUME_FROM_CHECKPOINT=""
TRAIN_KEEP_CHECKPOINT_HISTORY=true
